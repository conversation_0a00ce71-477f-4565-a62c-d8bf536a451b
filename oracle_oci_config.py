#!/usr/bin/env python3
"""
Oracle OCI Autonomous Database Configuration
<PERSON><PERSON><PERSON> hình kết nối Oracle Autonomous Database từ OCI cho dự án AI Assistant
"""

import os
import oracledb
from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
import cx_Oracle
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
import base64
import oci
import json

@dataclass
class OracleOCIConfig:
    """Oracle Autonomous Database OCI Configuration"""
    
    # OCI Authentication
    oci_config_file: str = os.getenv("OCI_CONFIG_FILE", "~/.oci/config")
    oci_profile: str = os.getenv("OCI_PROFILE", "DEFAULT")
    
    # Oracle Database Connection (Autonomous Database)
    oracle_user: str = os.getenv("ORACLE_USER", "ADMIN")
    oracle_password: str = os.getenv("ORACLE_PASSWORD", "")
    oracle_dsn: str = os.getenv("ORACLE_DSN", "")  # TNS connect string
    oracle_wallet_location: str = os.getenv("ORACLE_WALLET_LOCATION", "./oracle_wallet")
    oracle_wallet_password: str = os.getenv("ORACLE_WALLET_PASSWORD", "")
    
    # Autonomous Database specific
    autonomous_db_ocid: str = os.getenv("AUTONOMOUS_DB_OCID", "")
    compartment_id: str = os.getenv("OCI_COMPARTMENT_ID", "")
    
    # Connection Pool Settings
    pool_min: int = int(os.getenv("ORACLE_POOL_MIN", "2"))
    pool_max: int = int(os.getenv("ORACLE_POOL_MAX", "10"))
    pool_increment: int = int(os.getenv("ORACLE_POOL_INCREMENT", "1"))
    
    # Oracle 19C Features
    use_oracle_23ai_features: bool = False  # Set False for Oracle 19C
    enable_vector_search: bool = False      # Oracle 23ai Vector Search (not available in 19C)
    enable_json_search: bool = True         # JSON search available in 19C
    enable_full_text_search: bool = True    # Full text search available in 19C
    
    # AI Assistant specific tables
    documents_table: str = "AI_DOCUMENTS"
    embeddings_table: str = "AI_EMBEDDINGS"  
    conversations_table: str = "AI_CONVERSATIONS"
    knowledge_base_table: str = "AI_KNOWLEDGE_BASE"
    
    @property
    def connection_string(self) -> str:
        """Build Oracle connection string"""
        if self.oracle_wallet_location:
            return f"{self.oracle_user}/{self.oracle_password}@{self.oracle_dsn}"
        else:
            return f"{self.oracle_user}/{self.oracle_password}@{self.oracle_dsn}"
    
    def get_oci_client(self):
        """Get OCI Database client"""
        try:
            config = oci.config.from_file(
                file_location=os.path.expanduser(self.oci_config_file),
                profile_name=self.oci_profile
            )
            return oci.database.DatabaseClient(config)
        except Exception as e:
            print(f"❌ Error creating OCI client: {e}")
            return None
    
    def validate_connection(self) -> bool:
        """Validate Oracle connection"""
        try:
            # Set wallet location if provided
            if self.oracle_wallet_location:
                oracledb.init_oracle_client(lib_dir=None, config_dir=self.oracle_wallet_location)
            
            connection = oracledb.connect(
                user=self.oracle_user,
                password=self.oracle_password,
                dsn=self.oracle_dsn
            )
            
            cursor = connection.cursor()
            cursor.execute("SELECT 1 FROM DUAL")
            result = cursor.fetchone()
            
            cursor.close()
            connection.close()
            
            return result is not None
            
        except Exception as e:
            print(f"❌ Oracle connection validation failed: {e}")
            return False

@dataclass
class OracleAIConfig:
    """Oracle AI specific configuration for 19C"""
    
    # Since Oracle 19C doesn't have native vector search, we'll use alternatives
    use_hybrid_approach: bool = True  # Combine Oracle with external vector DB
    external_vector_db: str = "postgresql"  # or "pinecone", "weaviate"
    
    # Text processing in Oracle 19C
    enable_oracle_text: bool = True    # Oracle Text for full-text search
    enable_json_dataguide: bool = True # JSON Data Guide for semi-structured data
    
    # Embedding storage strategy for 19C
    embedding_storage_method: str = "json_clob"  # Store embeddings as JSON CLOB
    embedding_chunk_size: int = 1000  # Chunks per embedding batch
    
    # AI Tables Schema
    schema_definitions: Dict[str, str] = field(default_factory=lambda: {
        "documents": """
        CREATE TABLE AI_DOCUMENTS (
            doc_id VARCHAR2(100) PRIMARY KEY,
            title VARCHAR2(500) NOT NULL,
            content CLOB NOT NULL,
            file_type VARCHAR2(50),
            upload_date DATE DEFAULT SYSDATE,
            metadata JSON,
            content_hash VARCHAR2(64),
            status VARCHAR2(20) DEFAULT 'ACTIVE'
        )
        """,
        
        "embeddings": """
        CREATE TABLE AI_EMBEDDINGS (
            embedding_id VARCHAR2(100) PRIMARY KEY,
            doc_id VARCHAR2(100) REFERENCES AI_DOCUMENTS(doc_id),
            chunk_text CLOB NOT NULL,
            embedding_vector CLOB,  -- JSON array of float values
            chunk_index NUMBER,
            created_date DATE DEFAULT SYSDATE
        )
        """,
        
        "conversations": """
        CREATE TABLE AI_CONVERSATIONS (
            conversation_id VARCHAR2(100) PRIMARY KEY,
            user_id VARCHAR2(100),
            message_text CLOB NOT NULL,
            response_text CLOB,
            context_docs JSON,
            created_date DATE DEFAULT SYSDATE,
            session_id VARCHAR2(100)
        )
        """,
        
        "knowledge_base": """
        CREATE TABLE AI_KNOWLEDGE_BASE (
            kb_id VARCHAR2(100) PRIMARY KEY,
            category VARCHAR2(100),
            question_text CLOB NOT NULL,
            answer_text CLOB NOT NULL,
            keywords VARCHAR2(1000),
            created_date DATE DEFAULT SYSDATE,
            updated_date DATE DEFAULT SYSDATE
        )
        """
    })
    
    # Oracle Text indexes for full-text search
    text_indexes: Dict[str, str] = field(default_factory=lambda: {
        "documents_content": """
        CREATE INDEX idx_docs_content_text 
        ON AI_DOCUMENTS(content) 
        INDEXTYPE IS CTXSYS.CONTEXT
        """,
        
        "kb_questions": """
        CREATE INDEX idx_kb_questions_text 
        ON AI_KNOWLEDGE_BASE(question_text) 
        INDEXTYPE IS CTXSYS.CONTEXT
        """
    })

def get_oracle_oci_config() -> OracleOCIConfig:
    """Get Oracle OCI configuration with environment variable validation"""
    config = OracleOCIConfig()
    
    # Validate required environment variables
    required_vars = [
        "ORACLE_PASSWORD",
        "ORACLE_DSN", 
        "AUTONOMOUS_DB_OCID",
        "OCI_COMPARTMENT_ID"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️ Missing required environment variables: {missing_vars}")
        print("🔧 Set these variables:")
        for var in missing_vars:
            print(f"   export {var}=your_value")
    
    return config

def create_sample_env_file():
    """Create sample .env file for Oracle OCI configuration"""
    sample_env = """
# Oracle Autonomous Database OCI Configuration
# ============================================

# OCI Authentication
OCI_CONFIG_FILE=~/.oci/config
OCI_PROFILE=DEFAULT

# Oracle Database Connection
ORACLE_USER=ADMIN
ORACLE_PASSWORD=your_admin_password_here
ORACLE_DSN=your_autonomous_db_connection_string
ORACLE_WALLET_LOCATION=./oracle_wallet
ORACLE_WALLET_PASSWORD=your_wallet_password

# Autonomous Database Information  
AUTONOMOUS_DB_OCID=ocid1.autonomousdatabase.oc1.region.xxxxxxxxxxxxx
OCI_COMPARTMENT_ID=ocid1.compartment.oc1..xxxxxxxxxxxxx

# Connection Pool Settings
ORACLE_POOL_MIN=2
ORACLE_POOL_MAX=10
ORACLE_POOL_INCREMENT=1

# Example connection string format:
# ORACLE_DSN=your_db_name_high
# For Autonomous Database, use the connection string from the cloud console
"""
    
    with open("oracle_oci.env.sample", "w", encoding="utf-8") as f:
        f.write(sample_env)
    
    print("✅ Created oracle_oci.env.sample file")
    print("📝 Copy it to .env and update with your values")

if __name__ == "__main__":
    # Test configuration
    print("🔧 Testing Oracle OCI Configuration...")
    
    config = get_oracle_oci_config()
    ai_config = OracleAIConfig()
    
    print(f"📊 Oracle User: {config.oracle_user}")
    print(f"📊 Use Oracle Text: {ai_config.enable_oracle_text}")
    print(f"📊 Hybrid Approach: {ai_config.use_hybrid_approach}")
    
    # Create sample env file
    create_sample_env_file()
    
    # Test connection if credentials are available
    if config.oracle_password and config.oracle_dsn:
        print("🔍 Testing database connection...")
        if config.validate_connection():
            print("✅ Oracle connection successful!")
        else:
            print("❌ Oracle connection failed")
    else:
        print("⚠️ Oracle credentials not configured yet") 