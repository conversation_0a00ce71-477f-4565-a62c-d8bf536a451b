"""
Parallel Memory Optimization for Oracle Advanced Memory Pipeline
Giảm thời gian từ 18s xuống ~8-10s bằng cách chạy parallel
"""

import asyncio
import time
from typing import List, Dict, Any, Optional

class ParallelMemoryOptimizer:
    """Optimize memory operations with parallel processing"""
    
    def __init__(self, oracle_client, mem0_client, valves):
        self.oracle_client = oracle_client
        self.mem0_client = mem0_client
        self.valves = valves
    
    async def parallel_memory_search(self, user_id: str, user_message: str) -> Dict[str, Any]:
        """
        Parallel memory search - chạy Oracle và Mem0 search đồng thời
        Thay vì: Oracle (3s) + Mem0 (3s) = 6s
        Thành: max(Oracle, Mem0) = ~3s
        """
        
        print(f"🚀 Starting parallel memory search...")
        start_time = time.time()
        
        # Tạo tasks để chạy parallel
        tasks = []
        
        # Task 1: Oracle search
        if self.valves.enable_oracle_memory and self.oracle_client:
            oracle_task = asyncio.create_task(
                self._safe_oracle_search(user_id, user_message),
                name="oracle_search"
            )
            tasks.append(oracle_task)
        
        # Task 2: Mem0 search (với embedding)
        if self.valves.enable_mem0_memory and self.mem0_client:
            mem0_task = asyncio.create_task(
                self._safe_mem0_search(user_id, user_message),
                name="mem0_search"
            )
            tasks.append(mem0_task)
        
        # Task 3: Pattern retrieval (nếu có)
        if hasattr(self, 'pattern_client'):
            pattern_task = asyncio.create_task(
                self._safe_pattern_search(user_id),
                name="pattern_search"
            )
            tasks.append(pattern_task)
        
        # Chạy tất cả tasks parallel với timeout
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=self.valves.memory_search_timeout or 10
            )
            
            # Parse results
            oracle_memories = []
            mem0_memories = []
            user_patterns = {}
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    print(f"❌ Task {tasks[i].get_name()} failed: {result}")
                    continue
                    
                task_name = tasks[i].get_name()
                if task_name == "oracle_search":
                    oracle_memories = result or []
                elif task_name == "mem0_search":
                    mem0_memories = result or []
                elif task_name == "pattern_search":
                    user_patterns = result or {}
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ Parallel search completed in {duration:.2f}s")
            print(f"🏛️ Oracle memories: {len(oracle_memories)}")
            print(f"💾 Mem0 memories: {len(mem0_memories)}")
            
            return {
                'oracle_memories': oracle_memories,
                'mem0_memories': mem0_memories,
                'user_patterns': user_patterns,
                'search_duration': duration
            }
            
        except asyncio.TimeoutError:
            print(f"⏰ Parallel search timeout after {self.valves.memory_search_timeout}s")
            return {
                'oracle_memories': [],
                'mem0_memories': [],
                'user_patterns': {},
                'search_duration': self.valves.memory_search_timeout
            }
    
    async def _safe_oracle_search(self, user_id: str, user_message: str) -> List[Dict]:
        """Safe Oracle search with error handling"""
        try:
            return await self.oracle_client.search_memories(
                user_id, user_message, self.valves.max_oracle_memories
            )
        except Exception as e:
            print(f"❌ Oracle search error: {e}")
            return []
    
    async def _safe_mem0_search(self, user_id: str, user_message: str) -> List[Dict]:
        """Safe Mem0 search with error handling"""
        try:
            return await self.mem0_client.search(
                query=user_message,
                user_id=user_id,
                limit=self.valves.max_mem0_memories or 5
            )
        except Exception as e:
            print(f"❌ Mem0 search error: {e}")
            return []
    
    async def _safe_pattern_search(self, user_id: str) -> Dict:
        """Safe pattern search with error handling"""
        try:
            # Implement pattern search if available
            return {}
        except Exception as e:
            print(f"❌ Pattern search error: {e}")
            return {}
    
    async def parallel_memory_storage(self, user_id: str, user_message: str, ai_response: str) -> Dict[str, Any]:
        """
        Parallel memory storage - chạy Oracle và Mem0 storage đồng thời
        Thay vì: Oracle store (2s) + Mem0 store (2s) = 4s
        Thành: max(Oracle, Mem0) = ~2s
        """
        
        print(f"💾 Starting parallel memory storage...")
        start_time = time.time()
        
        # Tạo tasks để chạy parallel
        tasks = []
        
        # Task 1: Oracle storage
        if self.valves.enable_oracle_memory and self.oracle_client:
            oracle_store_task = asyncio.create_task(
                self._safe_oracle_storage(user_id, user_message, ai_response),
                name="oracle_storage"
            )
            tasks.append(oracle_store_task)
        
        # Task 2: Mem0 storage (với embedding generation)
        if self.valves.enable_mem0_memory and self.mem0_client:
            mem0_store_task = asyncio.create_task(
                self._safe_mem0_storage(user_id, user_message, ai_response),
                name="mem0_storage"
            )
            tasks.append(mem0_store_task)
        
        # Chạy tất cả tasks parallel
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=15  # Storage timeout
            )
            
            # Parse results
            oracle_stored = False
            mem0_stored = False
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    print(f"❌ Storage task {tasks[i].get_name()} failed: {result}")
                    continue
                    
                task_name = tasks[i].get_name()
                if task_name == "oracle_storage":
                    oracle_stored = result
                elif task_name == "mem0_storage":
                    mem0_stored = result
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ Parallel storage completed in {duration:.2f}s")
            print(f"🏛️ Oracle stored: {oracle_stored}")
            print(f"💾 Mem0 stored: {mem0_stored}")
            
            return {
                'oracle_stored': oracle_stored,
                'mem0_stored': mem0_stored,
                'storage_duration': duration
            }
            
        except asyncio.TimeoutError:
            print(f"⏰ Parallel storage timeout")
            return {
                'oracle_stored': False,
                'mem0_stored': False,
                'storage_duration': 15
            }
    
    async def _safe_oracle_storage(self, user_id: str, user_message: str, ai_response: str) -> bool:
        """Safe Oracle storage with error handling"""
        try:
            # Store user message
            await self.oracle_client.store_memory(
                user_id, f"User: {user_message}",
                memory_type="user_message"
            )
            
            # Store AI response
            await self.oracle_client.store_memory(
                user_id, f"Assistant: {ai_response}",
                memory_type="assistant_response"
            )
            
            return True
        except Exception as e:
            print(f"❌ Oracle storage error: {e}")
            return False
    
    async def _safe_mem0_storage(self, user_id: str, user_message: str, ai_response: str) -> bool:
        """Safe Mem0 storage with error handling"""
        try:
            conversation = f"User: {user_message}\nAssistant: {ai_response}"
            await self.mem0_client.add(
                messages=conversation,
                user_id=user_id
            )
            return True
        except Exception as e:
            print(f"❌ Mem0 storage error: {e}")
            return False

# Usage example trong Oracle Advanced Memory Pipeline:
"""
# Trong inlet method:
optimizer = ParallelMemoryOptimizer(self.oracle_client, self.mem0_client, self.valves)

# Thay vì sequential search:
# oracle_memories = await self.oracle_client.search_memories(...)  # 3s
# mem0_memories = await self.mem0_client.search(...)               # 3s

# Dùng parallel search:
search_results = await optimizer.parallel_memory_search(user_id, user_message)  # ~3s
oracle_memories = search_results['oracle_memories']
mem0_memories = search_results['mem0_memories']

# Trong outlet method:
# Thay vì sequential storage:
# await oracle_client.store_memory(...)  # 2s
# await mem0_client.add(...)             # 2s

# Dùng parallel storage:
storage_results = await optimizer.parallel_memory_storage(user_id, user_message, ai_response)  # ~2s
"""
