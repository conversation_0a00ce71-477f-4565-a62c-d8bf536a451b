#!/usr/bin/env python3
"""
Integrate reader mode from paywall bypass into jini_crawler for blocked websites
"""

import subprocess
import re

def integrate_reader_mode():
    """Add reader mode fallback to jini_crawler.py"""
    
    print("🔧 Integrating reader mode from paywall bypass...")
    
    # First, restore the working jini_crawler.py
    restore_result = subprocess.run([
        'docker', 'cp', 'jina_backup_1754443444/jini_crawler.py', 'jina-crawler-mcp:/app/jini_crawler.py'
    ], capture_output=True, text=True)
    
    if restore_result.returncode != 0:
        print(f"❌ Error restoring jini_crawler.py: {restore_result.stderr}")
        return False
    
    # Apply the async parallel crawling fix again
    fix_async_result = subprocess.run([
        'python3', 'fix_async_crawling.py'
    ], capture_output=True, text=True)
    
    if fix_async_result.returncode != 0:
        print(f"❌ Error applying async fix: {fix_async_result.stderr}")
        return False
    
    print("✅ Restored jini_crawler.py with async parallel crawling")
    
    # Now read the current jini_crawler.py
    result = subprocess.run([
        'docker', 'exec', 'jina-crawler-mcp', 'cat', '/app/jini_crawler.py'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ Error reading jini_crawler.py: {result.stderr}")
        return False
    
    content = result.stdout
    
    # Find the TLS bypass section and add reader mode fallback after it
    tls_pattern = r'(                except Exception as e:\n                    logger\.error\(f"❌ TLS bypass error: \{e\}"\)\n                    return None)'
    
    reader_mode_addition = r'''\1
            
            # 🚀 READER MODE FALLBACK for blocked websites (403, 429, etc.)
            if not html_content or status in [403, 429, 503]:
                logger.info(f"📖 Trying Reader Mode for blocked URL: {url}")
                try:
                    reader_content = await self._extract_with_reader_mode(url)
                    if reader_content and len(reader_content.strip()) > 200:
                        html_content = reader_content
                        status = 200
                        logger.info(f"✅ Reader Mode successful for {url}")
                    else:
                        logger.warning(f"⚠️ Reader Mode failed for {url}")
                except Exception as e:
                    logger.error(f"❌ Reader Mode error: {e}")'''
    
    # Apply the reader mode addition
    content = re.sub(tls_pattern, reader_mode_addition, content)
    
    # Add the reader mode method before the cleanup method
    reader_method = '''
    async def _extract_with_reader_mode(self, url: str) -> Optional[str]:
        """
        📖 READER MODE EXTRACTION - Aggressive content extraction for blocked sites
        
        Args:
            url: URL to extract content from
            
        Returns:
            Cleaned HTML content or None if failed
        """
        try:
            # Try with different user agents for blocked sites
            reader_headers = {
                'User-Agent': 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Referer': 'https://www.google.com/',
            }
            
            async with self.session.get(url, headers=reader_headers, timeout=30) as response:
                if response.status == 200:
                    html_content = await response.text()
                    soup = BeautifulSoup(html_content, 'html.parser')
                    
                    # Apply reader mode content extraction
                    cleaned_content = self._extract_reader_mode_content(soup)
                    
                    if len(cleaned_content.strip()) > 200:
                        logger.info(f"📖 Reader Mode extracted {len(cleaned_content)} chars from {url}")
                        return cleaned_content
                    else:
                        logger.warning(f"⚠️ Reader Mode extracted insufficient content from {url}")
                        return None
                else:
                    logger.warning(f"⚠️ Reader Mode request returned status {response.status} for {url}")
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Reader Mode extraction error for {url}: {e}")
            return None
    
    def _extract_reader_mode_content(self, soup: BeautifulSoup) -> str:
        """
        📖 AGGRESSIVE CONTENT EXTRACTION for reader mode
        Based on paywall bypass techniques
        """
        # Remove all unwanted elements
        unwanted_tags = [
            'script', 'style', 'nav', 'header', 'footer', 'aside', 'iframe', 
            'noscript', 'form', 'button', 'advertisement', 'ads', 'sidebar',
            'menu', 'navigation', 'social', 'share', 'comment', 'related'
        ]
        
        for tag in unwanted_tags:
            for element in soup.find_all(tag):
                element.decompose()
        
        # Remove elements with unwanted classes/IDs
        unwanted_patterns = [
            'ad', 'advertisement', 'social', 'share', 'comment', 'sidebar',
            'menu', 'nav', 'footer', 'header', 'popup', 'modal', 'overlay'
        ]
        
        for pattern in unwanted_patterns:
            for element in soup.find_all(attrs={'class': re.compile(pattern, re.I)}):
                element.decompose()
            for element in soup.find_all(attrs={'id': re.compile(pattern, re.I)}):
                element.decompose()
        
        # Find main content areas
        content_selectors = [
            'article', '[role="main"]', 'main', '.content', '.post', '.article',
            '.entry', '.story', '.text', '.body', '#content', '#main'
        ]
        
        main_content = None
        for selector in content_selectors:
            try:
                if selector.startswith('.') or selector.startswith('#'):
                    elements = soup.select(selector)
                else:
                    elements = soup.find_all(selector)
                
                if elements:
                    main_content = elements[0]
                    break
            except:
                continue
        
        # If no main content found, extract meaningful paragraphs
        if not main_content:
            paragraphs = soup.find_all(['p', 'div', 'span'])
            meaningful_content = []
            
            for p in paragraphs:
                text = p.get_text(strip=True)
                if len(text) > 50 and not any(unwanted in text.lower() for unwanted in ['cookie', 'subscribe', 'sign up', 'login']):
                    meaningful_content.append(str(p))
            
            return '\\n'.join(meaningful_content) if meaningful_content else str(soup.get_text())
        
        return str(main_content)
'''
    
    # Find the cleanup method and insert reader mode method before it
    cleanup_pattern = r'(\n    async def cleanup\(self\):)'
    content = re.sub(cleanup_pattern, reader_method + r'\1', content)
    
    # Write the updated content
    with open('/tmp/jini_crawler_with_reader_mode.py', 'w') as f:
        f.write(content)
    
    # Copy back to container
    copy_result = subprocess.run([
        'docker', 'cp', '/tmp/jini_crawler_with_reader_mode.py', 'jina-crawler-mcp:/app/jini_crawler.py'
    ], capture_output=True, text=True)
    
    if copy_result.returncode != 0:
        print(f"❌ Error copying updated file: {copy_result.stderr}")
        return False
    
    print("✅ Reader mode integrated into jini_crawler.py")
    return True

def main():
    """Main function"""
    print("🚀 Integrating Reader Mode for blocked websites...")
    print("🎯 Target: Sites like zhihu.com returning 403/429/503")
    print("📖 Solution: Aggressive content extraction from paywall bypass")
    print()
    
    if integrate_reader_mode():
        print("\n🎉 Reader Mode integration completed!")
        print("🔄 Restarting container...")
        
        # Restart container
        subprocess.run(['docker', 'restart', 'jina-crawler-mcp'])
        print("✅ Container restarted")
        
        print("\n📊 New fallback chain:")
        print("1. Regular request")
        print("2. TLS bypass (for Cloudflare)")
        print("3. 📖 Reader Mode (for blocked sites like zhihu.com)")
        print("🎯 Should handle 403/429/503 errors with aggressive extraction")
    else:
        print("\n❌ Reader Mode integration failed")

if __name__ == "__main__":
    main()
