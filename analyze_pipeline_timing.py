#!/usr/bin/env python3
"""
Phân tích thời gian từng bước trong pipeline dựa trên logs
"""

import re
from datetime import datetime
import subprocess

def parse_log_timestamp(log_line):
    """Parse timestamp từ log line"""
    timestamp_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})'
    match = re.search(timestamp_pattern, log_line)
    if match:
        return datetime.strptime(match.group(1), '%Y-%m-%d %H:%M:%S.%f')
    return None

def analyze_pipeline_timing():
    """Phân tích timing từ logs"""
    
    print("🔍 Analyzing Pipeline Timing from Recent Logs...")
    print("=" * 60)
    
    # Get recent logs from pipelines
    try:
        result = subprocess.run([
            'docker', 'logs', 'pipelines', '--tail', '100', '--since', '10m'
        ], capture_output=True, text=True, timeout=30)
        
        pipeline_logs = result.stdout.split('\n')
        
    except Exception as e:
        print(f"❌ Error getting pipeline logs: {e}")
        return
    
    # Get recent logs from open-webui
    try:
        result = subprocess.run([
            'docker', 'logs', 'open-webui-mcpo', '--tail', '50', '--since', '10m'
        ], capture_output=True, text=True, timeout=30)
        
        webui_logs = result.stdout.split('\n')
        
    except Exception as e:
        print(f"❌ Error getting webui logs: {e}")
        return
    
    # Analyze WebUI timing
    print("\n📊 OPEN WEBUI TIMING ANALYSIS:")
    print("-" * 40)
    
    chat_start = None
    chat_end = None
    
    for line in webui_logs:
        if 'POST /api/chat/completions' in line and '200' in line:
            timestamp = parse_log_timestamp(line)
            if timestamp:
                chat_start = timestamp
                print(f"🚀 Chat request started: {timestamp.strftime('%H:%M:%S.%f')[:-3]}")
        
        elif 'POST /api/chat/completed' in line and '200' in line:
            timestamp = parse_log_timestamp(line)
            if timestamp:
                chat_end = timestamp
                print(f"✅ Chat request completed: {timestamp.strftime('%H:%M:%S.%f')[:-3]}")
    
    if chat_start and chat_end:
        total_duration = (chat_end - chat_start).total_seconds()
        print(f"⏱️  Total chat duration: {total_duration:.2f} seconds")
    
    # Analyze Pipeline timing
    print("\n🔧 PIPELINE TIMING ANALYSIS:")
    print("-" * 40)
    
    pipeline_events = []
    
    for line in pipeline_logs:
        timestamp = parse_log_timestamp(line)
        if not timestamp:
            continue
            
        # Key pipeline events
        if 'Oracle Advanced Memory Pipeline' in line:
            pipeline_events.append((timestamp, 'Oracle Pipeline Start', line))
        elif 'Gemma 3 enhanced classification' in line:
            pipeline_events.append((timestamp, 'Classification Start', line))
        elif 'Oracle connection acquired' in line:
            pipeline_events.append((timestamp, 'Oracle Connected', line))
        elif 'SQL executed successfully' in line:
            pipeline_events.append((timestamp, 'SQL Executed', line))
        elif 'Found' in line and 'Oracle memories' in line:
            pipeline_events.append((timestamp, 'Oracle Memories Found', line))
        elif 'Embedding text' in line:
            pipeline_events.append((timestamp, 'Embedding Generation', line))
        elif 'Found' in line and 'Mem0 memories' in line:
            pipeline_events.append((timestamp, 'Mem0 Search Complete', line))
        elif 'Memory context injected' in line:
            pipeline_events.append((timestamp, 'Context Injected', line))
        elif 'Storing conversation' in line:
            pipeline_events.append((timestamp, 'Storage Start', line))
        elif 'Successfully stored' in line and 'points to Mem0' in line:
            pipeline_events.append((timestamp, 'Mem0 Storage Complete', line))
        elif 'Conversation stored in Oracle' in line:
            pipeline_events.append((timestamp, 'Oracle Storage Complete', line))
    
    # Sort events by timestamp
    pipeline_events.sort(key=lambda x: x[0])
    
    # Calculate durations between steps
    print("\n📈 STEP-BY-STEP TIMING:")
    print("-" * 40)
    
    prev_timestamp = None
    prev_event = None
    
    for timestamp, event, line in pipeline_events:
        if prev_timestamp:
            duration = (timestamp - prev_timestamp).total_seconds()
            print(f"⏱️  {prev_event} → {event}: {duration:.3f}s")
        else:
            print(f"🚀 {event}: {timestamp.strftime('%H:%M:%S.%f')[:-3]}")
        
        prev_timestamp = timestamp
        prev_event = event
    
    # Summary of major components
    print("\n🎯 BOTTLENECK ANALYSIS:")
    print("-" * 40)
    
    # Count operations
    embedding_count = len([e for e in pipeline_events if 'Embedding' in e[1]])
    oracle_ops = len([e for e in pipeline_events if 'Oracle' in e[1]])
    mem0_ops = len([e for e in pipeline_events if 'Mem0' in e[1]])
    
    print(f"🧠 Embedding operations: {embedding_count}")
    print(f"🏛️  Oracle operations: {oracle_ops}")
    print(f"💾 Mem0 operations: {mem0_ops}")
    
    # Estimate time per operation type
    if pipeline_events:
        total_pipeline_time = (pipeline_events[-1][0] - pipeline_events[0][0]).total_seconds()
        print(f"📊 Total pipeline processing: {total_pipeline_time:.2f}s")
        
        if embedding_count > 0:
            avg_embedding_time = total_pipeline_time / max(embedding_count, 1)
            print(f"⚡ Avg embedding time: {avg_embedding_time:.2f}s each")

if __name__ == "__main__":
    analyze_pipeline_timing()
