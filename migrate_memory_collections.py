#!/usr/bin/env python3
"""
Memory Migration Script for Qdrant Collections
Migrate memories from old collection to new Gemini 3072 collection
"""

import asyncio
import json
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct
import requests
import os

# Configuration
QDRANT_HOST = "localhost"
QDRANT_PORT = 6333
GEMINI_API_KEY = "AIzaSyDdXCS0Cw58rfurmaKW061IdkOy1UHK51U"

# Collections
OLD_COLLECTIONS = [
    "mem0_gemini_768",   # Main source collection (768 dimensions)
    "mem1536",           # OpenAI embeddings
    "mem768",            # Other embeddings
    "default_collection" # Default mem0 collection
]
NEW_COLLECTION = "mem0_gemini_3072"

class MemoryMigrator:
    def __init__(self):
        self.client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)
        
    def get_gemini_embedding(self, text):
        """Get embedding from Gemini API using gemini-embedding-001 (3072 dims)"""
        url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-embedding-001:embedContent"
        headers = {
            "Content-Type": "application/json",
            "x-goog-api-key": GEMINI_API_KEY
        }
        
        data = {
            "model": "models/gemini-embedding-001",
            "content": {
                "parts": [{"text": text}]
            }
        }
        
        try:
            response = requests.post(url, headers=headers, json=data)
            if response.status_code == 200:
                result = response.json()
                return result["embedding"]["values"]
            else:
                print(f"❌ Gemini API error: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"❌ Error getting embedding: {e}")
            return None

    def list_collections(self):
        """List all collections in Qdrant"""
        try:
            collections = self.client.get_collections()
            return [col.name for col in collections.collections]
        except Exception as e:
            print(f"❌ Error listing collections: {e}")
            return []

    def create_new_collection(self):
        """Create new collection for Gemini 3072 embeddings"""
        try:
            # Check if collection already exists
            existing_collections = self.list_collections()
            if NEW_COLLECTION in existing_collections:
                print(f"✅ Collection '{NEW_COLLECTION}' already exists")
                return True
                
            # Create new collection with 3072 dimensions
            self.client.create_collection(
                collection_name=NEW_COLLECTION,
                vectors_config=VectorParams(
                    size=3072,  # Gemini embedding dimension
                    distance=Distance.COSINE
                )
            )
            print(f"✅ Created new collection: {NEW_COLLECTION}")
            return True
        except Exception as e:
            print(f"❌ Error creating collection: {e}")
            return False

    def get_points_from_collection(self, collection_name):
        """Get all points from a collection"""
        try:
            # Get collection info first
            collection_info = self.client.get_collection(collection_name)
            print(f"📊 Collection '{collection_name}' has {collection_info.points_count} points")
            
            if collection_info.points_count == 0:
                return []
            
            # Scroll through all points
            points = []
            offset = None
            
            while True:
                result = self.client.scroll(
                    collection_name=collection_name,
                    limit=100,
                    offset=offset,
                    with_payload=True,
                    with_vectors=True
                )
                
                if not result[0]:  # No more points
                    break
                    
                points.extend(result[0])
                offset = result[1]  # Next offset
                
                if offset is None:  # End of collection
                    break
                    
            print(f"📥 Retrieved {len(points)} points from '{collection_name}'")
            return points
            
        except Exception as e:
            print(f"❌ Error getting points from '{collection_name}': {e}")
            return []

    def migrate_point_to_new_collection(self, point):
        """Migrate a single point to new collection with new embedding"""
        try:
            # Extract memory text from payload
            payload = point.payload
            memory_text = payload.get('data', payload.get('memory', payload.get('text', '')))
            
            if not memory_text:
                print(f"⚠️ Skipping point {point.id} - no memory text found")
                return False
            
            # Skip very short or empty content
            if len(memory_text.strip()) < 10:
                print(f"⚠️ Skipping point {point.id} - content too short")
                return False
            
            print(f"🔄 Migrating: {memory_text[:50]}...")
            
            # Get new Gemini embedding
            new_vector = self.get_gemini_embedding(memory_text)
            if not new_vector:
                print(f"❌ Failed to get embedding for point {point.id}")
                return False
            
            # Create new point with updated metadata
            new_payload = payload.copy()
            new_payload.update({
                'migrated_from': point.id,
                'migration_timestamp': asyncio.get_event_loop().time(),
                'embedding_model': 'gemini-embedding-001',
                'embedding_dimensions': 3072
            })
            
            # Insert into new collection
            self.client.upsert(
                collection_name=NEW_COLLECTION,
                points=[PointStruct(
                    id=point.id,
                    vector=new_vector,
                    payload=new_payload
                )]
            )
            
            print(f"✅ Migrated point {point.id}")
            return True
            
        except Exception as e:
            print(f"❌ Error migrating point {point.id}: {e}")
            return False

    def migrate_collection(self, source_collection):
        """Migrate all points from source collection to new collection"""
        print(f"\n🚀 Starting migration from '{source_collection}' to '{NEW_COLLECTION}'")
        
        # Get all points from source collection
        points = self.get_points_from_collection(source_collection)
        if not points:
            print(f"⚠️ No points found in '{source_collection}'")
            return 0
        
        # Migrate each point
        migrated_count = 0
        for i, point in enumerate(points):
            print(f"📝 Processing point {i+1}/{len(points)}")
            if self.migrate_point_to_new_collection(point):
                migrated_count += 1
        
        print(f"✅ Migration complete: {migrated_count}/{len(points)} points migrated")
        return migrated_count

    def run_migration(self):
        """Run the complete migration process"""
        print("🔧 Starting Memory Migration Process")
        print(f"🎯 Target collection: {NEW_COLLECTION}")
        
        # List existing collections
        existing_collections = self.list_collections()
        print(f"📋 Existing collections: {existing_collections}")
        
        # Create new collection
        if not self.create_new_collection():
            print("❌ Failed to create new collection. Aborting.")
            return
        
        # Find collections to migrate from
        collections_to_migrate = [col for col in OLD_COLLECTIONS if col in existing_collections and col != NEW_COLLECTION]
        
        if not collections_to_migrate:
            print("⚠️ No collections found to migrate from")
            return
        
        print(f"📦 Collections to migrate: {collections_to_migrate}")
        
        # Migrate from each collection
        total_migrated = 0
        for collection in collections_to_migrate:
            migrated = self.migrate_collection(collection)
            total_migrated += migrated
        
        print(f"\n🎉 Migration Summary:")
        print(f"   Total points migrated: {total_migrated}")
        print(f"   Target collection: {NEW_COLLECTION}")
        print(f"   Embedding model: gemini-embedding-001 (3072 dims)")

if __name__ == "__main__":
    migrator = MemoryMigrator()
    migrator.run_migration()