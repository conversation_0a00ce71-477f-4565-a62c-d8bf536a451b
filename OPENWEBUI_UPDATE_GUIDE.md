# 🚀 Open WebUI Update Guide

## 📋 Overview

This guide covers the updated Open WebUI deployment system with automatic version management and easy updates.

## 🔧 Available Scripts

### 1. **Deploy Open WebUI** - `deploy-openwebui-coolify.sh`
- Deploys Open WebUI with Coolify proxy integration
- Automatically detects and uses the latest stable version
- Creates backup before deployment
- Verifies deployment success

```bash
./deploy-openwebui-coolify.sh [domain] [email]
```

### 2. **Update Open WebUI** - `update-openwebui.sh`
- Updates to the latest stable version
- Creates backup before update
- Compares current vs latest version
- Automatic rollback capability

```bash
./update-openwebui.sh
```

### 3. **Check Version** - `check-openwebui-version.sh`
- Shows current vs latest version
- Container status information
- Helpful management commands

```bash
./check-openwebui-version.sh
```

## 📦 Current Configuration

### **Latest Version**: v0.6.16
- **Main Image**: `ghcr.io/open-webui/open-webui:v0.6.16`
- **Pipelines**: `ghcr.io/open-webui/pipelines:latest`

### **Key Features**:
- ✅ Coolify proxy integration
- ✅ Automatic SSL with Let's Encrypt
- ✅ RAG with Gemini embedding
- ✅ Pipeline support
- ✅ Automatic backups
- ✅ Version management

## 🔄 Update Process

### **Automatic Update**:
```bash
# Check current version
./check-openwebui-version.sh

# Update if needed
./update-openwebui.sh
```

### **Manual Update**:
```bash
# Pull latest images
docker pull ghcr.io/open-webui/open-webui:v0.6.16
docker pull ghcr.io/open-webui/pipelines:latest

# Restart services
docker compose -f deploy-openwebui-coolify.yml down
docker compose -f deploy-openwebui-coolify.yml up -d
```

## 📁 Backup System

### **Automatic Backups**:
- Created before every deployment/update
- Stored in `webui_backups/` directory
- Includes: data, configuration, compose file

### **Backup Structure**:
```
webui_backups/
├── backup_20250115_010000/
│   ├── webui-data/
│   ├── .env.traefik
│   └── deploy-openwebui-coolify.yml
└── update_backup_20250115_020000/
    ├── webui-data/
    ├── .env.traefik
    └── deploy-openwebui-coolify.yml
```

## 🛠️ Management Commands

### **Service Management**:
```bash
# View logs
docker compose -f deploy-openwebui-coolify.yml logs -f

# Stop services
docker compose -f deploy-openwebui-coolify.yml down

# Restart services
docker compose -f deploy-openwebui-coolify.yml restart

# Check status
docker compose -f deploy-openwebui-coolify.yml ps
```

### **Troubleshooting**:
```bash
# Check Coolify proxy
docker logs coolify-proxy

# Check Open WebUI logs
docker logs open-webui-coolify

# Check Pipelines logs
docker logs openwebui-pipelines-coolify
```

## 🌐 Access Information

### **Default URLs**:
- **Main Site**: https://catomaton.duckdns.org
- **API Endpoint**: https://api.catomaton.duckdns.org/pipelines
- **HTTP Redirect**: http://catomaton.duckdns.org → HTTPS

### **Configuration**:
- **Domain**: Set in `.env.traefik`
- **SSL**: Automatic via Coolify/Let's Encrypt
- **RAG**: Gemini embedding enabled
- **Signup**: Disabled by default

## 🔐 Security Features

- ✅ HTTPS enforced
- ✅ Automatic SSL certificates
- ✅ User signup disabled
- ✅ Secure API key management
- ✅ Network isolation

## 📈 Version History

| Version | Release Date | Key Features |
|---------|-------------|--------------|
| v0.6.16 | Latest | Current stable release |
| main | Rolling | Development branch |

## 🚨 Important Notes

1. **Always backup** before updates
2. **Test updates** in staging first
3. **Monitor logs** after updates
4. **Verify functionality** post-update
5. **Keep backups** for rollback capability

## 🆘 Emergency Procedures

### **Rollback Process**:
```bash
# Stop current services
docker compose -f deploy-openwebui-coolify.yml down

# Restore from backup
cp webui_backups/backup_YYYYMMDD_HHMMSS/.env.traefik .
cp webui_backups/backup_YYYYMMDD_HHMMSS/deploy-openwebui-coolify.yml .
cp -r webui_backups/backup_YYYYMMDD_HHMMSS/webui-data .

# Restart services
docker compose -f deploy-openwebui-coolify.yml up -d
```

### **Fresh Installation**:
```bash
# Clean slate
docker compose -f deploy-openwebui-coolify.yml down
docker system prune -f
rm -rf webui-data

# Redeploy
./deploy-openwebui-coolify.sh
```

---

**Last Updated**: January 15, 2025  
**Current Version**: v0.6.16  
**Next Check**: Run `./check-openwebui-version.sh` regularly