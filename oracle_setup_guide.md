# Oracle Autonomous Database Setup Guide - Chi tiết từng bước

## 🎯 Bước 1: Tạo Autonomous Database trên OCI Console

### 1.1 T<PERSON>y cập OCI Console
1. Vào https://cloud.oracle.com/
2. Click **Sign In** → **Oracle Cloud Infrastructure**
3. Nhập **Cloud Account Name** (tenancy name)
4. Login với username/password

### 1.2 Tạo Autonomous Database
1. Từ OCI Console, click **☰ Menu** → **Oracle Database** → **Autonomous Database**
2. Click **Create Autonomous Database**
3. **Điền thông tin cơ bản:**
   ```
   Display Name: AI-Assistant-DB
   Database Name: AIASSISTDB
   Workload Type: Data Warehouse (hoặc Transaction Processing)
   Deployment Type: Shared Infrastructure
   ```

4. **Cấu hình Database:**
   ```
   Database Version: 19c (hoặc mới nhất available)
   OCPU Count: 1 (có thể scale sau)
   Storage (TB): 1 (20GB minimum)
   ```

5. **<PERSON><PERSON><PERSON> hình Administrator:**
   ```
   Username: ADMIN (mặc định)
   Password: [Tạo password mạnh - ví dụ: MyStr0ngP@ssw0rd123]
   Confirm Password: [Nhập lại password]
   ```

6. **Network Access:**
   ```
   ☑️ Secure access from everywhere (recommended for testing)
   hoặc
   ☑️ Secure access from allowed IPs only (production)
   ```

7. **License:**
   ```
   ☑️ License Included (sử dụng Oracle license)
   ```

8. Click **Create Autonomous Database**

### 1.3 Chờ Database được tạo
- Trạng thái sẽ chuyển từ **PROVISIONING** → **AVAILABLE**
- Thời gian: 2-5 phút

---

## 🔑 Bước 2: Download Database Wallet

### 2.1 Download Wallet Files
1. Từ Autonomous Database details page
2. Click **DB Connection**
3. Click **Download Wallet**
4. **Wallet Type:** Instance Wallet
5. **Password:** Tạo wallet password (ví dụ: WalletP@ss123)
6. Click **Download**

### 2.2 Extract Wallet
```bash
# Tạo thư mục wallet
mkdir -p /home/<USER>/AccA/AccA/oracle_wallet

# Unzip wallet file
unzip Wallet_AIASSISTDB.zip -d /home/<USER>/AccA/AccA/oracle_wallet

# Check files
ls -la /home/<USER>/AccA/AccA/oracle_wallet
```

**Files trong wallet:**
- `tnsnames.ora` - Connection strings
- `sqlnet.ora` - Network configuration  
- `cwallet.sso` - Oracle wallet
- `ewallet.p12` - Wallet certificate
- `keystore.jks` - Java keystore
- `truststore.jks` - Trust store

---

## 📝 Bước 3: Ghi lại thông tin kết nối

### 3.1 Từ OCI Console, ghi lại:
```bash
# Database Information
AUTONOMOUS_DB_OCID: ocid1.autonomousdatabase.oc1.iad.xxxxxxxxxxxxx
COMPARTMENT_ID: ocid1.compartment.oc1..xxxxxxxxxxxxx
SERVICE_NAME: aiassistdb_high (từ tnsnames.ora)
```

### 3.2 Từ wallet files:
```bash
# Check tnsnames.ora để lấy connection string
cat /home/<USER>/AccA/AccA/oracle_wallet/tnsnames.ora

# Sẽ thấy các service như:
# aiassistdb_high = (description= ...)
# aiassistdb_medium = (description= ...)
# aiassistdb_low = (description= ...)
```

---

## ⚙️ Bước 4: Cấu hình Environment

### 4.1 Tạo file .env.oracle
```bash
cd /home/<USER>/AccA/AccA
cat > .env.oracle << 'EOF'
# Oracle Autonomous Database Configuration
# ======================================

# Database Connection
ORACLE_USER=ADMIN
ORACLE_PASSWORD=MyStr0ngP@ssw0rd123
ORACLE_DSN=aiassistdb_high
ORACLE_WALLET_LOCATION=/home/<USER>/AccA/AccA/oracle_wallet
ORACLE_WALLET_PASSWORD=WalletP@ss123

# Autonomous Database Information  
AUTONOMOUS_DB_OCID=ocid1.autonomousdatabase.oc1.iad.xxxxxxxxxxxxx
OCI_COMPARTMENT_ID=ocid1.compartment.oc1..xxxxxxxxxxxxx

# Connection Pool Settings
ORACLE_POOL_MIN=2
ORACLE_POOL_MAX=10
ORACLE_POOL_INCREMENT=1

# OCI Configuration
OCI_CONFIG_FILE=~/.oci/config
OCI_PROFILE=DEFAULT
EOF
```

### 4.2 Load environment variables
```bash
# Load vào current session
source .env.oracle

# Hoặc export manually
export ORACLE_USER=ADMIN
export ORACLE_PASSWORD=MyStr0ngP@ssw0rd123
export ORACLE_DSN=aiassistdb_high
export ORACLE_WALLET_LOCATION=/home/<USER>/AccA/AccA/oracle_wallet
export ORACLE_WALLET_PASSWORD=WalletP@ss123
```

---

## 🔧 Bước 5: Install Dependencies

### 5.1 Install system packages
```bash
sudo apt update
sudo apt install -y python3-dev python3-pip python3-venv build-essential libaio1 libaio-dev unzip wget curl
```

### 5.2 Create Python environment
```bash
cd /home/<USER>/AccA/AccA
python3 -m venv venv_oracle
source venv_oracle/bin/activate
```

### 5.3 Install Oracle packages
```bash
pip install --upgrade pip
pip install -r oracle_requirements.txt
```

---

## 📦 Bước 6: Install Oracle Instant Client (Optional)

### 6.1 Download Instant Client
```bash
# Tạo thư mục
sudo mkdir -p /opt/oracle

# Download từ Oracle website hoặc:
cd /tmp
wget https://download.oracle.com/otn_software/linux/instantclient/1917000/instantclient-basic-linux.x64-*********.0dbru.zip

# Extract
sudo unzip instantclient-basic-linux.x64-*********.0dbru.zip -d /opt/oracle

# Set permissions
sudo chmod -R 755 /opt/oracle

# Add to environment
echo 'export LD_LIBRARY_PATH=/opt/oracle/instantclient_19_17:$LD_LIBRARY_PATH' >> ~/.bashrc
source ~/.bashrc
```

---

## 🧪 Bước 7: Test Connection

### 7.1 Test với Python
```python
# test_oracle_connection.py
import oracledb
import os

# Load environment
oracle_user = os.getenv("ORACLE_USER", "ADMIN")
oracle_password = os.getenv("ORACLE_PASSWORD")
oracle_dsn = os.getenv("ORACLE_DSN")
oracle_wallet = os.getenv("ORACLE_WALLET_LOCATION")

print(f"🔍 Testing Oracle connection...")
print(f"   User: {oracle_user}")
print(f"   DSN: {oracle_dsn}")
print(f"   Wallet: {oracle_wallet}")

try:
    # Set wallet location
    oracledb.init_oracle_client(config_dir=oracle_wallet)
    
    # Connect
    connection = oracledb.connect(
        user=oracle_user,
        password=oracle_password,
        dsn=oracle_dsn
    )
    
    cursor = connection.cursor()
    cursor.execute("SELECT 'Hello from Oracle!' FROM DUAL")
    result = cursor.fetchone()
    
    print(f"✅ Connection successful!")
    print(f"   Result: {result[0]}")
    
    cursor.close()
    connection.close()
    
except Exception as e:
    print(f"❌ Connection failed: {e}")
```

### 7.2 Chạy test
```bash
cd /home/<USER>/AccA/AccA
source venv_oracle/bin/activate
source .env.oracle
python test_oracle_connection.py
```

---

## 🚀 Bước 8: Run Oracle Integration

### 8.1 Chạy setup script
```bash
chmod +x setup_oracle_integration.sh
./setup_oracle_integration.sh
```

### 8.2 Start services
```bash
# Start Oracle RAG service
source venv_oracle/bin/activate
source .env.oracle
python oracle_rag_service.py &

# Start integration backend
python integrate_oracle_backend.py &
```

### 8.3 Test API endpoints
```bash
# Test health
curl http://localhost:8025/oracle/health

# Test status  
curl http://localhost:8025/oracle/status
```

---

## 📋 Thông tin cần cung cấp - Summary

### Bạn cần có:
1. **OCI Account** với quyền tạo Autonomous Database
2. **Admin Password** cho database (tự tạo)
3. **Wallet Password** (tự tạo)  
4. **OCID values** (copy từ OCI Console):
   - Autonomous Database OCID
   - Compartment OCID

### Files cần có:
1. **Wallet files** (download từ OCI)
2. **Environment file** (.env.oracle với credentials)
3. **Python packages** (install từ oracle_requirements.txt)

### Ports sử dụng:
- **8025**: Oracle Integration Backend
- **8030**: Oracle RAG Service  
- **8010**: Existing FastAPI Backend
- **3001**: Open WebUI
- **11434**: LLAMA.CPP Server

---

## 🔧 Troubleshooting Common Issues

### Issue 1: TNS Listener Error
```
ORA-12541: TNS:no listener
```
**Solution**: Check DSN string trong .env.oracle

### Issue 2: Wallet Error  
```
ORA-28040: No matching authentication protocol
```
**Solution**: Check wallet location và password

### Issue 3: Python Package Error
```
DPI-1047: Cannot locate an Oracle Client library
```
**Solution**: Install Oracle Instant Client hoặc set LD_LIBRARY_PATH 