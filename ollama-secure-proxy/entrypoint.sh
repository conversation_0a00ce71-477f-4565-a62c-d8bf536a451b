#!/bin/sh

# Set default API key if not provided
export OLLAMA_API_KEY=${OLLAMA_API_KEY:-"ollama-secure-key-2024"}

echo "🔐 Starting Ollama Secure Proxy with API Key protection..."
echo "📡 Proxying to: http://127.0.0.1:11435"
echo "🔑 API Key: ${OLLAMA_API_KEY:0:8}..."

# Replace placeholder in nginx config
envsubst '${OLLAMA_API_KEY}' < /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf

# Test nginx config
nginx -t

# Start nginx
nginx -g 'daemon off;' 