# 🔐 Ollama Secure Proxy

Production-ready secure proxy for Ollama with API Key authentication.

## 🚀 Features

- ✅ **API Key Authentication** - All requests require valid Bearer token
- ✅ **Rate Limiting** - 20 requests/second per IP with burst capacity
- ✅ **CORS Support** - Web application friendly
- ✅ **Health Monitoring** - Built-in health check endpoint
- ✅ **Streaming Support** - Real-time response streaming
- ✅ **Production Ready** - Robust error handling and logging

## 📋 Setup

### Quick Start
```bash
# Generate secure API key
openssl rand -hex 32

# Create .env file
echo "OLLAMA_API_KEY=your-generated-key" > .env

# Start secure proxy
docker-compose up -d
```

## 🔗 Endpoints

### Health Check (No Auth)
```bash
curl http://localhost:9080/health
```

### Secure API (Requires Auth)
```bash
# List models
curl -H "Authorization: Bearer your-api-key" \
     http://localhost:9080/api/tags

# Generate text
curl -X POST http://localhost:9080/api/generate \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "qwen3:4b",
    "prompt": "Hello world",
    "stream": false
  }'
```

## 🔑 Current API Key
```
e84aa4ab76fc19c5cd7770b227641ee883ef042de2c179d9d7696e98080c8c0a
```

## 🌐 Open WebUI Configuration

**Base URL:** `http://your-server-ip:9080`  
**API Key:** `e84aa4ab76fc19c5cd7770b227641ee883ef042de2c179d9d7696e98080c8c0a`

### Steps to Connect:
1. Open WebUI Settings → Connections
2. Set External Ollama Server URL: `http://your-server-ip:9080`
3. Set API Key: `e84aa4ab76fc19c5cd7770b227641ee883ef042de2c179d9d7696e98080c8c0a`
4. Save and test connection

## 🛡️ Security Features

- **Bearer Token Authentication** - Industry standard
- **Rate Limiting** - Prevents abuse
- **Input Validation** - Secure request handling
- **CORS Headers** - Safe cross-origin requests

## 📊 Performance

- **Port:** 9080
- **Rate Limit:** 20 req/s + 30 burst
- **Timeout:** 600s for long operations
- **Buffering:** Disabled for streaming

## 🔧 Troubleshooting

### Check Status
```bash
docker logs ollama-secure-proxy
curl http://localhost:9080/health
```

### Test Authentication
```bash
# Should return 401
curl http://localhost:9080/api/tags

# Should return models list
curl -H "Authorization: Bearer your-api-key" \
     http://localhost:9080/api/tags
```

### Available Models
- `hhao/qwen2.5-coder-tools:14b` - Advanced coding assistant
- `hhao/qwen2.5-coder-tools:7b` - Balanced coding model
- `qwen3:8b` - General purpose model
- `qwen3:4b` - Fast lightweight model
- Plus 5 more models...

---

**✅ Secure Ollama API now ready for production use!** 🚀 