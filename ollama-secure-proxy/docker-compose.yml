version: '3.8'

services:
  ollama-secure-proxy:
    image: nginx:alpine
    container_name: ollama-secure-proxy
    restart: unless-stopped
    volumes:
      - ./nginx.conf.template:/etc/nginx/nginx.conf.template:ro
      - ./entrypoint.sh:/entrypoint.sh:ro
    environment:
      - OLLAMA_API_KEY=${OLLAMA_API_KEY:-ollama-secure-key-2024}
    command: ["/entrypoint.sh"]
    network_mode: "host"  # Access host's Ollama on localhost:11435
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9080/health"]
      interval: 30s
      timeout: 10s
      retries: 3 