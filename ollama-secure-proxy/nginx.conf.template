events {
    worker_connections 1024;
}

http {
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=ollama_api:10m rate=20r/s;
    
    server {
        listen 9080;
        server_name _;
        
        # Health check endpoint (no auth required)
        location /health {
            access_log off;
            return 200 "Ollama Secure Proxy OK\n";
            add_header Content-Type text/plain;
        }
        
        # Secure Ollama API proxy
        location / {
            limit_req zone=ollama_api burst=30 nodelay;
            
            # API Key authentication
            if ($http_authorization != "Bearer ${OLLAMA_API_KEY}") {
                return 401 '{"error":"Unauthorized - Valid API Key required"}';
            }
            
            # Proxy to local Ollama instance
            proxy_pass http://127.0.0.1:11435;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Long request timeout for model operations
            proxy_read_timeout 600s;
            proxy_connect_timeout 30s;
            proxy_send_timeout 600s;
            
            # Enable streaming
            proxy_buffering off;
            proxy_cache off;
            
            # CORS headers
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, DELETE' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
            
            # Handle preflight OPTIONS requests
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, DELETE';
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain; charset=utf-8';
                add_header 'Content-Length' 0;
                return 204;
            }
        }
    }
} 