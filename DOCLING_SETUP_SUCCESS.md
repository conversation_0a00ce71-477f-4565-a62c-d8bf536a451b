# Docling Integration Setup - THÀNH CÔNG ✅

## Tóm tắt
Đã thiết lập thành công tích hợp Docling với Open WebUI để xử lý bảng từ tài liệu PDF.

## Kiến trúc hiện tại

### 1. Docling Server Container
- **Container name**: `docling`
- **Image**: `docling-server` (custom built)
- **Port**: `5001:5001`
- **Status**: ✅ Running
- **Health check**: `http://docling:5001/health`

### 2. Open WebUI Container
- **Container name**: `catomanton-webui`
- **Image**: `ghcr.io/open-webui/open-webui:main`
- **Port**: `3000:8080`
- **Status**: ✅ Running
- **URL**: `http://localhost:3000` hoặc `http://[SERVER_IP]:3000`
- **Note**: Container chưa được cấu hình với Traefik labels nên chỉ truy cập được qua port mapping

### 3. Docker Network
- **Network name**: `acca-network`
- **Type**: Bridge network
- **Connected containers**: `docling`, `catomanton-webui`

### 4. Traefik (Reverse Proxy)
- **Container name**: `catomanton-traefik`
- **Image**: `traefik:v3.1`
- **Ports**: `80:80`, `443:443`, `8080:8080`
- **Status**: ✅ Running
- **Dashboard**: `http://localhost:8080/dashboard/`
- **Note**: Open WebUI chưa được cấu hình với Traefik labels

## Cách truy cập

### Open WebUI
- **Direct access**: `http://localhost:3000` ✅ HOẠT ĐỘNG
- **From external**: `http://[SERVER_IP]:3000` ✅ HOẠT ĐỘNG
- **Domain HTTP**: `http://catomanton.duckdns.org` → Redirect to HTTPS ✅
- **Domain HTTPS**: `https://catomanton.duckdns.org` ❌ 504 Gateway Timeout (SSL issue)

### Docling Server
- **Internal (từ Open WebUI)**: `http://docling:5001` ✅ HOẠT ĐỘNG
- **External**: `http://localhost:5001` ✅ HOẠT ĐỘNG
- **Health check**: `http://localhost:5001/health` ✅ HOẠT ĐỘNG

## Trạng thái hiện tại

### ✅ Hoạt động tốt:
- Open WebUI truy cập qua port 3000
- Docling server integration hoàn hảo
- Docker networking giữa containers
- HTTP redirect từ domain

### ⚠️ Vấn đề cần khắc phục:
- HTTPS domain trả về 504 Gateway Timeout
- SSL certificate có thể chưa được tạo đúng cách
- Traefik routing to HTTPS có vấn đề

### 🎯 Khuyến nghị sử dụng:
**Sử dụng `http://localhost:3000` hoặc `http://[SERVER_IP]:3000` để truy cập Open WebUI**

## Cấu hình

### Docling Server
```dockerfile
# Dockerfile.docling
FROM python:3.11-slim
WORKDIR /app
RUN apt-get update && apt-get install -y \
    tesseract-ocr \
    tesseract-ocr-vie \
    tesseract-ocr-eng \
    && rm -rf /var/lib/apt/lists/*
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY docling_server.py .
EXPOSE 5001
CMD ["python", "docling_server.py"]
```

### Requirements
```txt
docling
flask
flask-cors
```

### Open WebUI Configuration
- **DOCLING_SERVER_URL**: `http://docling:5001` (mặc định)
- **DOCLING_OCR_ENGINE**: `tesseract`
- **DOCLING_OCR_LANG**: Hỗ trợ tiếng Việt và tiếng Anh

## Các lệnh Docker đã sử dụng

### Build Docling Image
```bash
cd backend/app/rag
docker build -f Dockerfile.docling -t docling-server .
```

### Tạo Network
```bash
docker network create acca-network
```

### Khởi động Docling Container
```bash
docker run -d --name docling --restart unless-stopped -p 5001:5001 docling-server
```

### Kết nối containers vào network
```bash
docker network connect acca-network docling
docker network connect acca-network catomanton-webui
```

## Kiểm tra hoạt động

### 1. Kiểm tra Docling Health
```bash
docker exec catomanton-webui curl -X GET http://docling:5001/health
```
**Kết quả**: `{"docling_available":true,"service":"docling-table-extractor","status":"healthy","version":"1.0.0"}`

### 2. Kiểm tra Open WebUI
```bash
curl -I http://localhost:3000
```
**Kết quả**: `HTTP/1.1 200 OK`

## Endpoints Docling Server

### Health Check
- **URL**: `GET http://docling:5001/health`
- **Response**: JSON với trạng thái server

### Extract Tables (Upload)
- **URL**: `POST http://docling:5001/extract_tables`
- **Content-Type**: `multipart/form-data`
- **Body**: File upload

### Extract Tables (Path)
- **URL**: `POST http://docling:5001/extract_tables_from_path`
- **Content-Type**: `application/json`
- **Body**: `{"file_path": "/path/to/file.pdf"}`

## Tính năng

### ✅ Đã hoạt động
- [x] Docling server khởi động thành công
- [x] Open WebUI kết nối được với Docling
- [x] Docker networking hoạt động
- [x] Health check endpoint
- [x] OCR hỗ trợ tiếng Việt và tiếng Anh
- [x] Container auto-restart

### 🔄 Cần test
- [ ] Upload PDF và extract tables qua Open WebUI
- [ ] Xử lý tài liệu tiếng Việt
- [ ] Performance với file lớn

## Troubleshooting đã giải quyết

### 1. Connection Refused
**Vấn đề**: Open WebUI không thể kết nối `localhost:5001`
**Giải pháp**: Containerize Docling và sử dụng Docker network

### 2. Host Resolution
**Vấn đề**: Container không resolve được hostname `docling`
**Giải pháp**: Tạo custom network và kết nối cả hai containers

### 3. Port Conflicts
**Vấn đề**: Port 8080 conflict khi dùng `--network host`
**Giải pháp**: Sử dụng bridge network với port mapping

## Lệnh quản lý

### Restart toàn bộ hệ thống
```bash
docker restart docling catomanton-webui
```

### Xem logs
```bash
docker logs docling
docker logs catomanton-webui
```

### Stop hệ thống
```bash
docker stop docling catomanton-webui
```

### Start hệ thống
```bash
docker start docling catomanton-webui
```

## Kết luận
Hệ thống Docling + Open WebUI đã được thiết lập thành công và sẵn sàng để xử lý tài liệu PDF với khả năng trích xuất bảng tối ưu cho tài liệu tiếng Việt.

**Trạng thái**: ✅ HOÀN THÀNH
**Ngày**: 2025-07-16
**Thời gian setup**: ~30 phút