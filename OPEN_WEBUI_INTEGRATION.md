# 🔗 Open WebUI Integration Guide

## ✅ **CÁC BƯỚC ĐÃ HOÀN THÀNH**

Pipeline RAG đã được cài đặt thành công! Dưới đây là những gì đã được thực hiện:

### 📁 **Files đã copy vào container:**
- ✅ `acca_rag_document_pipeline.py` (Pipeline chính)
- ✅ `acca_rag_upload_handler.py` (Upload handler) 
- ✅ `table_processor_advanced.py` (Table processor)
- ✅ `enhanced_table_metadata.py` (Metadata generator)

### 📦 **Dependencies đã cài:**
- ✅ aiohttp, pandas, PyPDF2, python-docx, openpyxl, tabulate

### 🗄️ **Qdrant Collection:**
- ✅ Collection `acca_rag_documents` đã được tạo tự động

---

## 🎛️ **BƯỚC TIẾP THEO: CẤU HÌNH TRONG OPEN WEBUI**

### **1. T<PERSON><PERSON> cập Admin Panel**

1. Mở Open WebUI: `http://your-server:3000`
2. Đ<PERSON><PERSON> nhập với tài khoản admin
3. Click vào **Settings** (⚙️) ở góc phải 
4. Chọn **Admin Panel**

### **2. Thêm Pipeline**

1. Trong Admin Panel, chọn **Pipelines**
2. Click **"+"** để thêm pipeline mới
3. Chọn pipeline: **`acca_rag_document_pipeline`**
4. Pipeline sẽ xuất hiện trong danh sách

### **3. Cấu hình Pipeline (QUAN TRỌNG)**

Click vào **pipeline name** → **⚙️ Configure** và điền:

```json
{
  "qdrant_host": "**********",
  "qdrant_port": "6333",
  "collection_name": "acca_rag_documents",
  "openai_api_key": "YOUR_GEMINI_API_KEY_HERE",
  "openai_api_base": "https://generativelanguage.googleapis.com/v1beta/openai",
  "embedding_model": "text-embedding-004",
  "max_workers": 15,
  "rate_limit_rpm": 1400,
  "chunk_size": 400,
  "max_chunk_tokens": 1800,
  "extract_tables": true,
  "table_processing_mode": "enhanced",
  "max_relevant_chunks": 5,
  "relevance_threshold": 0.7,
  "enable_debug": true
}
```

⚠️ **QUAN TRỌNG**: Thay `YOUR_GEMINI_API_KEY_HERE` bằng API key thật của bạn!

### **4. Kích hoạt Pipeline**

1. Toggle **Enable** cho pipeline
2. Click **Save** 
3. Pipeline status sẽ hiển thị **🟢 Active**

---

## 📄 **CÁCH SỬ DỤNG**

### **A. Upload Documents**

1. Vào **Chat** interface
2. Click **📎** (attachment) 
3. Upload file: PDF, DOCX, XLSX, CSV, TXT, MD
4. Pipeline sẽ tự động:
   - Extract text content
   - Extract tables với metadata chi tiết  
   - Generate embeddings
   - Store vào Qdrant

### **B. Chat với Documents**

1. Sau khi upload, bắt đầu chat bình thường
2. Hỏi về nội dung documents:
   ```
   "Tóm tắt báo cáo tài chính Q4"
   "Doanh thu tăng trưởng bao nhiều?"
   "Cho tôi xem bảng biểu về performance"
   ```

3. Pipeline sẽ tự động:
   - Search relevant chunks trong Qdrant
   - Include table data với metadata
   - Provide context cho LLM

### **C. Tính năng đặc biệt**

- ✅ **Table-aware**: Hiểu được cấu trúc bảng biểu
- ✅ **Multi-format**: PDF, Excel, Word, CSV...
- ✅ **Async processing**: 15 workers song song
- ✅ **Smart chunking**: Token-aware cho Gemini
- ✅ **Rich metadata**: 11 loại metadata chi tiết

---

## 📊 **MONITOR & DEBUG**

### **1. Kiểm tra Pipeline Status**

```bash
# Check pipeline logs
docker logs pipelines --tail 50

# Check Qdrant collection
curl http://**********:6333/collections/acca_rag_documents
```

### **2. Debug trong Open WebUI**

1. Enable debug mode trong pipeline config
2. Check browser console (F12) khi upload
3. Pipeline logs sẽ hiển thị:
   - Document processing progress
   - Table extraction results
   - Embedding generation status
   - Storage confirmation

### **3. Performance Monitoring**

Pipeline tự động log:
```
[AccA-RAG-Enhanced] Processing document: financial_report.pdf
[AccA-RAG-Enhanced] Generated 45 chunks from 3 tables
[AccA-RAG-Enhanced-PERF] Batch completed in 2.3s (19.6 chunks/sec)
```

---

## 🚨 **TROUBLESHOOTING**

### **Issue 1: Pipeline không xuất hiện**
```bash
# Restart pipelines container
docker restart pipelines

# Check logs
docker logs pipelines --tail 20
```

### **Issue 2: API Key Error**
- Kiểm tra GEMINI_API_KEY trong pipeline config
- Test API key: `curl -H "Authorization: Bearer YOUR_KEY" https://generativelanguage.googleapis.com/v1beta/models`

### **Issue 3: Upload không hoạt động**
- Check file format support (PDF, DOCX, XLSX...)
- Enable debug mode và check logs
- Verify Qdrant connection

### **Issue 4: Slow processing**
- Reduce `max_workers` nếu server yếu
- Increase `rate_limit_rpm` nếu API limit cao
- Adjust `chunk_size` theo loại document

---

## 🎯 **BEST PRACTICES**

### **1. Document Preparation**
- ✅ PDF: Text-based (không phải scan)
- ✅ Tables: Clear borders và structure
- ✅ File size: < 50MB mỗi file
- ✅ Filename: Descriptive names

### **2. Performance Optimization**
```json
{
  "max_workers": 10,        // Reduce if low memory
  "batch_size": 25,         // Smaller batches for stability  
  "chunk_size": 300,        // Adjust based on document type
  "relevance_threshold": 0.8 // Higher for better quality
}
```

### **3. Quality Assurance**
- Test với sample documents trước
- Review extracted tables trong debug logs
- Monitor embedding success rate
- Adjust threshold dựa trên kết quả

---

## 📈 **NEXT STEPS**

### **Advanced Features**
1. **Custom Table Formats**: Add support cho format đặc biệt
2. **Multi-language**: Extend cho documents tiếng Việt
3. **Batch Upload**: Upload multiple files cùng lúc
4. **Search Analytics**: Track search patterns

### **Integration Options**
1. **API Access**: Direct REST API để upload programmatically
2. **Webhook**: Auto-process khi có file mới
3. **Scheduled Processing**: Process batch files định kỳ

---

## 🎉 **READY TO USE!**

Pipeline RAG của bạn đã sẵn sàng! 

1. ✅ **Upload documents** → Tự động process
2. ✅ **Chat bình thường** → RAG tự hoạt động  
3. ✅ **Monitor logs** → Đảm bảo quality

**Happy RAG-ing! 🚀📊**

---

## 📞 **Support**

Nếu gặp vấn đề:
1. Check troubleshooting section
2. Enable debug mode và check logs
3. Test với simple document trước
4. Verify Qdrant và Gemini API status 