"""
Document Preprocessor Service
Converts .doc/.xls to supported formats before sending to docling
"""

from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
import tempfile
import subprocess
import magic
import aiofiles
import asyncio
from pathlib import Path
import httpx
import os

app = FastAPI(title="Document Preprocessor", version="1.0.0")

DOCLING_URL = os.getenv("DOCLING_URL", "http://docling-server:5001")
LIBREOFFICE_PATH = "/usr/bin/libreoffice"

class DocumentPreprocessor:
    def __init__(self):
        self.conversion_map = {
            'application/msword': 'docx',
            'application/vnd.ms-excel': 'xlsx',
        }
    
    def detect_mime_type(self, file_path: str) -> str:
        """Detect MIME type"""
        try:
            mime = magic.Magic(mime=True)
            return mime.from_file(file_path)
        except Exception:
            ext = Path(file_path).suffix.lower()
            if ext == '.doc':
                return 'application/msword'
            elif ext == '.xls':
                return 'application/vnd.ms-excel'
            return 'application/octet-stream'
    
    async def convert_document(self, input_path: str, output_dir: str) -> str:
        """Convert document using LibreOffice"""
        mime_type = self.detect_mime_type(input_path)
        target_format = self.conversion_map.get(mime_type)
        
        if not target_format:
            raise ValueError(f"Unsupported format: {mime_type}")
        
        cmd = [
            LIBREOFFICE_PATH,
            '--headless',
            '--convert-to', target_format,
            '--outdir', output_dir,
            input_path
        ]
        
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            raise RuntimeError(f"Conversion failed: {stderr.decode()}")
        
        # Find converted file
        input_name = Path(input_path).stem
        converted_file = Path(output_dir) / f"{input_name}.{target_format}"
        
        if not converted_file.exists():
            raise RuntimeError("Converted file not found")
        
        return str(converted_file)

preprocessor = DocumentPreprocessor()

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "document-preprocessor"}

@app.post("/v1alpha/convert/file")
async def convert_file(file: UploadFile = File(...)):
    """Convert file with preprocessing support"""
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Save uploaded file
        input_path = Path(temp_dir) / file.filename
        
        async with aiofiles.open(input_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        # Check if conversion is needed
        mime_type = preprocessor.detect_mime_type(str(input_path))
        
        if mime_type in preprocessor.conversion_map:
            # Convert the file
            try:
                converted_path = await preprocessor.convert_document(
                    str(input_path), temp_dir
                )
                
                # Use converted file
                file_to_send = converted_path
                converted_filename = Path(converted_path).name
                
            except Exception as e:
                raise HTTPException(
                    status_code=500, 
                    detail=f"Conversion failed: {str(e)}"
                )
        else:
            # Use original file
            file_to_send = str(input_path)
            converted_filename = file.filename
        
        # Send to docling
        try:
            async with httpx.AsyncClient(timeout=300.0) as client:
                with open(file_to_send, 'rb') as f:
                    files = {
                        'file': (converted_filename, f, 'application/octet-stream')
                    }
                    
                    response = await client.post(
                        f"{DOCLING_URL}/v1alpha/convert/file",
                        files=files
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        
                        # Add conversion info to metadata
                        if mime_type in preprocessor.conversion_map:
                            if 'metadata' not in result:
                                result['metadata'] = {}
                            result['metadata']['preprocessed'] = {
                                'original_format': mime_type,
                                'converted_to': preprocessor.conversion_map[mime_type],
                                'original_filename': file.filename
                            }
                        
                        return result
                    else:
                        raise HTTPException(
                            status_code=response.status_code,
                            detail=f"Docling error: {response.text}"
                        )
                        
        except httpx.RequestError as e:
            raise HTTPException(
                status_code=503,
                detail=f"Failed to connect to docling: {str(e)}"
            )

@app.get("/v1alpha/convert/formats")
async def supported_formats():
    """List supported formats including legacy ones"""
    return {
        "input_formats": [
            "pdf", "docx", "xlsx", "pptx", "html", "image", 
            "csv", "md", "asciidoc", "audio",
            "doc", "xls"  # Added legacy formats
        ],
        "output_formats": ["markdown", "html", "json"],
        "preprocessing": {
            "doc": "Converted to docx via LibreOffice",
            "xls": "Converted to xlsx via LibreOffice"
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=5003)
