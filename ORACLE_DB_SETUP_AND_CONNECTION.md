# Hướng dẫn thiết lập và kết nối Oracle DB (Thin + Wallet)

Tài liệu này hướng dẫn thiết lập kết nối Oracle Autonomous Database (ADB) theo chuẩn thin mode + wallet, tích hợp với pipeline `oracle-advanced-memory` trong Open WebUI.

## 1) <PERSON><PERSON><PERSON> cầu môi trường

- Python 3.10+ (đã có sẵn trên máy chủ)
- Th<PERSON> viện `oracledb` (thin mode)
- Quyền truy cập Internet ra `adb.<region>.oraclecloud.com:1522`
- Wallet tải từ OCI Console cho ADB

Cài đặt thư viện:
```bash
pip install --upgrade oracledb
```

Kiểm tra phiên bản:
```bash
python3 -c "import oracledb; print(oracledb.__version__)"
```

## 2) Kiểm tra mạng (TLS 1522)

<PERSON><PERSON><PERSON> thực có thể bắt tay TLS đến endpoint trong vùng của DB (ví dụ ap-singapore-2):
```bash
openssl s_client -brief -connect adb.ap-singapore-2.oraclecloud.com:1522 -servername adb.ap-singapore-2.oraclecloud.com </dev/null
```
Kết quả mong đợi: `CONNECTION ESTABLISHED` và `Verification: OK`.

Nếu không OK: kiểm tra firewall outbound hoặc nhà cung cấp cloud chặn cổng 1522.

## 3) Tải và đặt Oracle Wallet

- Tải wallet từ OCI Console → Autonomous Database → DB của bạn → `DB Connection` → `Download Wallet`.
- Giải nén vào thư mục dự án, ví dụ: `oracle_wallet/Wallet_<ALIAS>`.
- Bên trong cần có các file như: `cwallet.sso`, `ewallet.p12`, `ewallet.pem`, `sqlnet.ora`, `tnsnames.ora`, `ojdbc.properties`...

Ví dụ cấu trúc:
```bash
/home/<USER>/AccA/AccA/oracle_wallet/Wallet_SWIV8HV5Y96IWO2T/
  ├─ cwallet.sso
  ├─ ewallet.p12
  ├─ ewallet.pem
  ├─ sqlnet.ora
  ├─ tnsnames.ora
  └─ ...
```

Mở `tnsnames.ora` để xác định TNS alias (ví dụ `swiv8hv5y96iwo2t_high`, `..._medium`, `..._tp`, `..._tpurgent`).

## 4) Kết nối THIN + Wallet bằng Python (khuyến nghị)

Ví dụ kiểm thử nhanh:
```python
import oracledb
wallet_dir = '/home/<USER>/AccA/AccA/oracle_wallet/Wallet_SWIV8HV5Y96IWO2T'
conn = oracledb.connect(
    user='ADMIN',
    password='YOUR_PASSWORD',
    dsn='swiv8hv5y96iwo2t_high',  # TNS alias trong tnsnames.ora
    config_dir=wallet_dir,
    wallet_location=wallet_dir,
)
cur = conn.cursor()
cur.execute("SELECT 'OK' FROM DUAL")
print(cur.fetchone())
cur.close(); conn.close()
```

Lưu ý:
- Nếu terminal yêu cầu “Enter PEM pass phrase”, đảm bảo dùng wallet autologin (`cwallet.sso`) và quyền đọc phù hợp. Trong môi trường headless hãy dùng autologin wallet (mặc định file `cwallet.sso` do OCI cung cấp).
- Không cần cài dày (thick client). Tránh `oracledb.init_oracle_client(...)` để không gặp lỗi `DPI-1047`.

## 5) Cấu hình cho Open WebUI Pipeline

File cấu hình: `webui-data/pipelines/oracle-advanced-memory/valves.json`

Thiết lập các khóa sau (thay giá trị thật của bạn):
```json
{
  "oracle_user": "ADMIN",
  "oracle_password": "<YOUR_PASSWORD>",
  "oracle_wallet_location": "/home/<USER>/AccA/AccA/oracle_wallet/Wallet_SWIV8HV5Y96IWO2T",
  "oracle_tns_alias": "swiv8hv5y96iwo2t_high",

  "oracle_dsn": "(description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.ap-singapore-2.oraclecloud.com))(connect_data=(service_name=g872ed23dd62a8b_swiv8hv5y96iwo2t_high.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))",

  "enable_oracle_memory": true,
  "enable_mem0_coordination": true,
  "enable_pattern_learning": true,
  "max_oracle_memories": 3,
  "memory_search_timeout": 3,
  "enable_debug_logging": true
}
```

Gợi ý bảo mật:
- Không commit mật khẩu thật vào repo. Dùng biến môi trường `ORACLE_USER`, `ORACLE_PASSWORD`, `ORACLE_WALLET_LOCATION`, `ORACLE_TNS_ALIAS`, `ORACLE_DSN`. Pipeline đã hỗ trợ đọc mặc định từ biến môi trường.

## 6) Khởi động/Restart Open WebUI

Sau khi cấu hình:
- Khởi động hoặc restart Open WebUI để pipeline nạp cấu hình mới.
- Trong Admin Panel → Pipelines, bật `oracle-advanced-memory`.

## 7) Kiểm tra nhanh với Pool (giống pipeline)

```python
import oracledb, json
cfg = {
  "oracle_user": "ADMIN",
  "oracle_password": "YOUR_PASSWORD",
  "oracle_wallet_location": "/home/<USER>/AccA/AccA/oracle_wallet/Wallet_SWIV8HV5Y96IWO2T",
  "oracle_tns_alias": "swiv8hv5y96iwo2t_high"
}
pool = oracledb.create_pool(
    user=cfg['oracle_user'], password=cfg['oracle_password'],
    dsn=cfg['oracle_tns_alias'],
    config_dir=cfg['oracle_wallet_location'],
    wallet_location=cfg['oracle_wallet_location'],
    min=1, max=2, increment=1
)
conn = pool.acquire(); cur = conn.cursor()
cur.execute("SELECT 'OK' FROM DUAL")
print(cur.fetchone())
cur.close(); conn.close(); pool.close()
```

## 8) Lỗi thường gặp và cách xử lý

- `DPY-6000 / DPY-6005: Listener refused connection`
  - Sai service_name trong DSN hoặc alias không đúng vùng.
  - Dùng đúng TNS alias trong `tnsnames.ora` (ví dụ `..._high`).
  - Kiểm tra kết nối TLS 1522 (mục 2).

- `DPI-1047: Cannot locate a 64-bit Oracle Client library`
  - Đang cố dùng thick client (`init_oracle_client`). Bỏ qua thick, dùng thin + wallet như hướng dẫn.

- `ORA-01017: invalid username/password`
  - Kiểm tra `oracle_user`/`oracle_password`. Thử đăng nhập từ SQL Developer/SQL*Plus để xác minh.

- Prompt `Enter PEM pass phrase`
  - Đảm bảo autologin wallet (`cwallet.sso`) có mặt và quyền truy cập đúng.
  - Hạn chế dùng `ewallet.pem` yêu cầu passphrase trong môi trường không tương tác.

## 9) Mẹo & thực hành tốt

- Luôn ưu tiên THIN + wallet + TNS alias cho ADB.
- Hạn chế dùng DSN tuple thủ công; dễ sai `service_name`.
- Quản lý secrets bằng biến môi trường / secret manager; không commit vào repo.
- Giới hạn `max_oracle_memories` để tránh phình prompt.

## 10) Phụ lục

- Kiểm tra wallet và alias:
```bash
ls -la oracle_wallet/Wallet_*/
sed -n '1,200p' oracle_wallet/Wallet_*/tnsnames.ora
```

- Ví dụ kiểm tra TLS nhiều vùng:
```bash
for r in ap-singapore-1 ap-singapore-2; do
  echo "Checking $r"; openssl s_client -brief -connect adb.$r.oraclecloud.com:1522 -servername adb.$r.oraclecloud.com </dev/null | sed -n '1,4p'; echo; done
```

---

Nếu cần, xem thêm các tài liệu liên quan trong repo:
- `ORACLE_ADVANCED_MEMORY_SYSTEM.md`
- `ORACLE_MEMORY_IMPLEMENTATION_SUMMARY.md`
- `webui-data/pipelines/oracle-advanced-memory/oracle-advanced-memory.py`

