#!/usr/bin/env python3
"""
Re-embed all documents in Open WebUI with Gemini embeddings
This fixes compatibility issues between old local embeddings and new Gemini API
"""

import sqlite3
import requests
import json
import time
import os
from typing import List, Dict, Any

# Configuration
OPEN_WEBUI_URL = "http://localhost:3001"
GEMINI_API_KEY = "AIzaSyDUnVwkztCMhNvB_kRARefQUghegpxNM"
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:embedContent"

def get_all_documents() -> List[Dict]:
    """Get all documents from ChromaDB"""
    conn = sqlite3.connect('/tmp/chroma_copy.sqlite3')
    cursor = conn.cursor()
    
    # Copy database from volume first
    os.system("docker cp $(docker ps -q -f name=acca-open-webui):/app/backend/data/vector_db/chroma.sqlite3 /tmp/chroma_copy.sqlite3")
    
    # Get all unique documents
    cursor.execute('''
        SELECT DISTINCT 
            em1.string_value as source,
            em2.string_value as content
        FROM embedding_metadata em1
        JOIN embedding_metadata em2 ON em1.seq_id = em2.seq_id
        WHERE em1.key = 'source' AND em2.key = 'document'
        LIMIT 10
    ''')
    
    documents = []
    for row in cursor.fetchall():
        if row[1]:  # Only if content exists
            documents.append({
                'source': row[0],
                'content': row[1][:1000]  # Limit content for testing
            })
    
    conn.close()
    return documents

def upload_document_to_webui(source: str, content: str) -> bool:
    """Upload document to Open WebUI knowledge base"""
    try:
        # Create a temporary file
        temp_file = f"/tmp/{source.replace('/', '_')}"
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Upload via Open WebUI API
        files = {'file': (source, open(temp_file, 'rb'), 'text/plain')}
        data = {'collection_name': 'knowledge_base'}
        
        response = requests.post(
            f"{OPEN_WEBUI_URL}/api/v1/knowledge",
            files=files,
            data=data,
            timeout=60
        )
        
        os.remove(temp_file)
        
        if response.status_code == 200:
            print(f"✅ Successfully uploaded: {source}")
            return True
        else:
            print(f"❌ Failed to upload {source}: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ Error uploading {source}: {e}")
        return False

def clear_existing_knowledge():
    """Clear existing knowledge base"""
    try:
        response = requests.delete(f"{OPEN_WEBUI_URL}/api/v1/knowledge")
        if response.status_code == 200:
            print("✅ Cleared existing knowledge base")
        else:
            print(f"⚠️  Could not clear knowledge base: {response.status_code}")
    except Exception as e:
        print(f"⚠️  Error clearing knowledge: {e}")

def main():
    print("🔄 Re-embedding documents with Gemini API")
    print("=" * 50)
    
    # Get documents from old database
    print("📄 Extracting documents from ChromaDB...")
    documents = get_all_documents()
    print(f"Found {len(documents)} documents")
    
    if not documents:
        print("❌ No documents found to re-embed")
        return
    
    # Show documents found
    print("\n📚 Documents found:")
    for i, doc in enumerate(documents, 1):
        print(f"  {i}. {doc['source']}")
        print(f"     Content preview: {doc['content'][:80]}...")
    
    # Clear existing knowledge (optional)
    print(f"\n🗑️  Clearing existing knowledge base...")
    clear_existing_knowledge()
    
    # Re-upload documents
    print(f"\n📤 Re-uploading documents...")
    success_count = 0
    
    for i, doc in enumerate(documents, 1):
        print(f"\n[{i}/{len(documents)}] Processing: {doc['source']}")
        
        if upload_document_to_webui(doc['source'], doc['content']):
            success_count += 1
        
        # Add delay to avoid rate limiting
        time.sleep(2)
    
    print(f"\n🎯 Re-embedding complete!")
    print(f"✅ Successfully processed: {success_count}/{len(documents)} documents")
    print(f"🌐 Check knowledge at: {OPEN_WEBUI_URL}/workspace/knowledge")

if __name__ == "__main__":
    main() 