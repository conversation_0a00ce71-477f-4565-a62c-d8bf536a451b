🔑 Qwen2.5-Coder API Access Information
==========================================

📍 Server Details:
   • Public IP: **************
   • API Port: 11435
   • Base URL: http://**************:11435/v1

🔑 Authentication:
   • API Key: qwen-e5209623d213d0ec3668c85f9cc44a6f
   • Model: qwen2.5-coder-7b-instruct

📋 Python Example (OpenAI Client):
===================================
import openai

client = openai.OpenAI(
    base_url="http://**************:11435/v1",
    api_key="qwen-e5209623d213d0ec3668c85f9cc44a6f"
)

response = client.chat.completions.create(
    model="qwen2.5-coder-7b-instruct",
    messages=[
        {"role": "user", "content": "Write a Python function to calculate fibonacci"}
    ],
    max_tokens=500,
    temperature=0.3
)

print(response.choices[0].message.content)

📱 cURL Example:
================
curl -X POST http://**************:11435/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer qwen-e5209623d213d0ec3668c85f9cc44a6f" \
  -d '{
    "model": "qwen2.5-coder-7b-instruct",
    "messages": [
      {"role": "user", "content": "Hello, write a simple Python function"}
    ],
    "max_tokens": 200,
    "temperature": 0.3
  }'

🛠️ Function Calling Example:
============================
{
  "model": "qwen2.5-coder-7b-instruct",
  "messages": [
    {"role": "user", "content": "Calculate the area of a circle with radius 5"}
  ],
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "calculate_area",
        "description": "Calculate circle area",
        "parameters": {
          "type": "object",
          "properties": {
            "radius": {"type": "number"}
          }
        }
      }
    }
  ],
  "max_tokens": 200
}

🎯 Model Capabilities:
======================
• Code generation in 92+ programming languages
• Function/Tool calling support
• Repository-level code understanding
• Fill-in-middle completion
• 32K context window
• Optimized for code agents (Cline, Claude Dev)
• ARM64 performance optimized

⚠️ Important Notes:
==================
• Server đang chạy local, cần config cloud firewall để access external
• Oracle Cloud: Security Lists → Add Ingress Rule port 11435
• API key được lưu secure tại server
• Performance: ~23 tokens/second trên ARM64 