-- Oracle Advanced Memory System Database Schema
-- AccA AI Assistant Platform - Advanced Memory with Thin Mode Support
-- Created: 2025-01-22

-- =============================================================================
-- CORE MEMORY TABLES
-- =============================================================================

-- User Long-term Memory Storage
CREATE TABLE USER_MEMORY (
    memory_id VARCHAR2(100) PRIMARY KEY,
    user_id VARCHAR2(100) NOT NULL,
    memory_type VARCHAR2(50) NOT NULL, -- 'conversation', 'fact', 'preference', 'context', 'pattern'
    memory_content CLOB NOT NULL,
    confidence_score NUMBER(3,2) DEFAULT 0.8, -- 0.00 to 1.00
    relevance_score NUMBER(3,2) DEFAULT 0.5,
    created_date DATE DEFAULT SYSDATE,
    last_accessed DATE DEFAULT SYSDATE,
    access_count NUMBER DEFAULT 1,
    session_id VARCHAR2(100),
    source_conversation_id VARCHAR2(100),
    tags JSON,
    metadata JSON,
    status VARCHAR2(20) DEFAULT 'ACTIVE'
);

-- User Behavioral Patterns
CREATE TABLE USER_PATTERNS (
    pattern_id VARCHAR2(100) PRIMARY KEY,
    user_id VARCHAR2(100) NOT NULL,
    pattern_type VARCHAR2(50) NOT NULL, -- 'communication_style', 'topic_interest', 'time_preference', 'response_style'
    pattern_data JSON NOT NULL,
    strength NUMBER(3,2) DEFAULT 0.5, -- Pattern strength 0.00 to 1.00
    confidence NUMBER(3,2) DEFAULT 0.5,
    created_date DATE DEFAULT SYSDATE,
    updated_date DATE DEFAULT SYSDATE,
    sample_count NUMBER DEFAULT 1,
    last_reinforced DATE DEFAULT SYSDATE
);

-- Memory Context Relationships
CREATE TABLE MEMORY_CONTEXT (
    context_id VARCHAR2(100) PRIMARY KEY,
    memory_id VARCHAR2(100) NOT NULL,
    related_memory_id VARCHAR2(100) NOT NULL,
    relationship_type VARCHAR2(50) NOT NULL, -- 'similar', 'opposite', 'causal', 'temporal', 'topical'
    relationship_strength NUMBER(3,2) DEFAULT 0.5,
    created_date DATE DEFAULT SYSDATE,
    validated CHAR(1) DEFAULT 'N',
    CONSTRAINT fk_memory_context_main FOREIGN KEY (memory_id) REFERENCES USER_MEMORY(memory_id),
    CONSTRAINT fk_memory_context_related FOREIGN KEY (related_memory_id) REFERENCES USER_MEMORY(memory_id)
);

-- User Preferences Evolution
CREATE TABLE USER_PREFERENCES (
    pref_id VARCHAR2(100) PRIMARY KEY,
    user_id VARCHAR2(100) NOT NULL,
    preference_category VARCHAR2(100) NOT NULL, -- 'response_style', 'topic_depth', 'language_level', 'interaction_mode'
    preference_value JSON NOT NULL,
    confidence NUMBER(3,2) DEFAULT 0.5,
    created_date DATE DEFAULT SYSDATE,
    updated_date DATE DEFAULT SYSDATE,
    evolution_history JSON, -- Track how preferences changed over time
    active CHAR(1) DEFAULT 'Y'
);

-- =============================================================================
-- REAL-TIME TABLES (Wallet Connection)
-- =============================================================================

-- Active Session Memory
CREATE TABLE SESSION_MEMORY (
    session_id VARCHAR2(100) PRIMARY KEY,
    user_id VARCHAR2(100) NOT NULL,
    current_context JSON,
    active_memories JSON, -- Array of relevant memory_ids
    conversation_flow JSON, -- Track conversation progression
    session_start DATE DEFAULT SYSDATE,
    last_activity DATE DEFAULT SYSDATE,
    status VARCHAR2(20) DEFAULT 'ACTIVE',
    session_metadata JSON
);

-- Memory Access Log
CREATE TABLE MEMORY_ACCESS_LOG (
    log_id VARCHAR2(100) PRIMARY KEY,
    user_id VARCHAR2(100) NOT NULL,
    memory_id VARCHAR2(100),
    access_type VARCHAR2(50) NOT NULL, -- 'read', 'write', 'update', 'recall', 'pattern_extract'
    access_timestamp DATE DEFAULT SYSDATE,
    context_data JSON,
    performance_metrics JSON, -- Response time, relevance score, etc.
    session_id VARCHAR2(100)
);

-- =============================================================================
-- DOCUMENT INTELLIGENCE TABLES
-- =============================================================================

-- Advanced Document Storage
CREATE TABLE DOCUMENT_INTELLIGENCE (
    doc_id VARCHAR2(100) PRIMARY KEY,
    user_id VARCHAR2(100) NOT NULL,
    document_content CLOB NOT NULL,
    document_metadata JSON NOT NULL, -- File type, size, upload date, etc.
    extracted_entities JSON, -- People, places, organizations, concepts
    document_summary CLOB,
    key_insights JSON,
    related_conversations JSON, -- Links to conversations about this doc
    processing_status VARCHAR2(50) DEFAULT 'PENDING',
    created_date DATE DEFAULT SYSDATE,
    last_processed DATE,
    content_hash VARCHAR2(64) -- For deduplication
);

-- Document Relationships
CREATE TABLE DOCUMENT_RELATIONSHIPS (
    rel_id VARCHAR2(100) PRIMARY KEY,
    source_doc_id VARCHAR2(100) NOT NULL,
    target_doc_id VARCHAR2(100) NOT NULL,
    relationship_type VARCHAR2(50) NOT NULL, -- 'similar', 'references', 'contradicts', 'extends'
    relationship_strength NUMBER(3,2) DEFAULT 0.5,
    created_date DATE DEFAULT SYSDATE,
    validated CHAR(1) DEFAULT 'N',
    CONSTRAINT fk_doc_rel_source FOREIGN KEY (source_doc_id) REFERENCES DOCUMENT_INTELLIGENCE(doc_id),
    CONSTRAINT fk_doc_rel_target FOREIGN KEY (target_doc_id) REFERENCES DOCUMENT_INTELLIGENCE(doc_id)
);

-- =============================================================================
-- ANALYTICS AND MONITORING TABLES
-- =============================================================================

-- Conversation Analytics
CREATE TABLE CONVERSATION_ANALYTICS (
    analytics_id VARCHAR2(100) PRIMARY KEY,
    user_id VARCHAR2(100) NOT NULL,
    session_id VARCHAR2(100),
    conversation_metrics JSON NOT NULL, -- Response time, satisfaction, engagement
    topic_distribution JSON, -- Topics discussed with weights
    sentiment_analysis JSON, -- Sentiment over time
    memory_effectiveness JSON, -- How well memories were used
    created_date DATE DEFAULT SYSDATE,
    analysis_date DATE DEFAULT SYSDATE
);

-- System Performance Metrics
CREATE TABLE SYSTEM_PERFORMANCE (
    metric_id VARCHAR2(100) PRIMARY KEY,
    metric_type VARCHAR2(50) NOT NULL, -- 'memory_search', 'pattern_extraction', 'context_injection'
    metric_data JSON NOT NULL,
    timestamp DATE DEFAULT SYSDATE,
    user_id VARCHAR2(100),
    session_id VARCHAR2(100)
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Memory search indexes
CREATE INDEX idx_user_memory_user_type ON USER_MEMORY(user_id, memory_type);
CREATE INDEX idx_user_memory_created ON USER_MEMORY(created_date DESC);
CREATE INDEX idx_user_memory_confidence ON USER_MEMORY(confidence_score DESC);
CREATE INDEX idx_user_memory_session ON USER_MEMORY(session_id);

-- Pattern indexes
CREATE INDEX idx_user_patterns_user ON USER_PATTERNS(user_id);
CREATE INDEX idx_user_patterns_type ON USER_PATTERNS(pattern_type);
CREATE INDEX idx_user_patterns_strength ON USER_PATTERNS(strength DESC);

-- Context relationship indexes
CREATE INDEX idx_memory_context_memory ON MEMORY_CONTEXT(memory_id);
CREATE INDEX idx_memory_context_related ON MEMORY_CONTEXT(related_memory_id);
CREATE INDEX idx_memory_context_type ON MEMORY_CONTEXT(relationship_type);

-- Session indexes
CREATE INDEX idx_session_memory_user ON SESSION_MEMORY(user_id);
CREATE INDEX idx_session_memory_status ON SESSION_MEMORY(status);
CREATE INDEX idx_session_memory_activity ON SESSION_MEMORY(last_activity DESC);

-- Access log indexes
CREATE INDEX idx_memory_access_user ON MEMORY_ACCESS_LOG(user_id);
CREATE INDEX idx_memory_access_timestamp ON MEMORY_ACCESS_LOG(access_timestamp DESC);
CREATE INDEX idx_memory_access_type ON MEMORY_ACCESS_LOG(access_type);

-- Document indexes
CREATE INDEX idx_document_user ON DOCUMENT_INTELLIGENCE(user_id);
CREATE INDEX idx_document_status ON DOCUMENT_INTELLIGENCE(processing_status);
CREATE INDEX idx_document_created ON DOCUMENT_INTELLIGENCE(created_date DESC);

-- =============================================================================
-- ORACLE TEXT INDEXES FOR FULL-TEXT SEARCH
-- =============================================================================

-- Full-text search on memory content
CREATE INDEX idx_memory_content_text 
ON USER_MEMORY(memory_content) 
INDEXTYPE IS CTXSYS.CONTEXT
PARAMETERS ('SYNC (ON COMMIT)');

-- Full-text search on document content
CREATE INDEX idx_document_content_text 
ON DOCUMENT_INTELLIGENCE(document_content) 
INDEXTYPE IS CTXSYS.CONTEXT
PARAMETERS ('SYNC (ON COMMIT)');

-- Full-text search on document summary
CREATE INDEX idx_document_summary_text 
ON DOCUMENT_INTELLIGENCE(document_summary) 
INDEXTYPE IS CTXSYS.CONTEXT
PARAMETERS ('SYNC (ON COMMIT)');

-- =============================================================================
-- VIEWS FOR COMMON QUERIES
-- =============================================================================

-- Active user memories with patterns
CREATE OR REPLACE VIEW v_user_active_memories AS
SELECT 
    um.memory_id,
    um.user_id,
    um.memory_type,
    um.memory_content,
    um.confidence_score,
    um.created_date,
    um.last_accessed,
    um.access_count,
    up.pattern_type,
    up.strength as pattern_strength
FROM USER_MEMORY um
LEFT JOIN USER_PATTERNS up ON um.user_id = up.user_id
WHERE um.status = 'ACTIVE'
AND um.confidence_score > 0.3;

-- User conversation analytics summary
CREATE OR REPLACE VIEW v_user_analytics_summary AS
SELECT 
    user_id,
    COUNT(*) as total_conversations,
    AVG(JSON_VALUE(conversation_metrics, '$.satisfaction_score')) as avg_satisfaction,
    AVG(JSON_VALUE(conversation_metrics, '$.response_time')) as avg_response_time,
    MAX(created_date) as last_conversation
FROM CONVERSATION_ANALYTICS
GROUP BY user_id;

-- Memory effectiveness view
CREATE OR REPLACE VIEW v_memory_effectiveness AS
SELECT 
    um.user_id,
    um.memory_type,
    COUNT(*) as memory_count,
    AVG(um.confidence_score) as avg_confidence,
    AVG(mal.performance_metrics) as avg_performance,
    SUM(um.access_count) as total_accesses
FROM USER_MEMORY um
LEFT JOIN MEMORY_ACCESS_LOG mal ON um.memory_id = mal.memory_id
WHERE um.status = 'ACTIVE'
GROUP BY um.user_id, um.memory_type;

-- =============================================================================
-- SEQUENCES FOR ID GENERATION
-- =============================================================================

CREATE SEQUENCE seq_memory_id START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_pattern_id START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_context_id START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_pref_id START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_session_id START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_log_id START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_doc_id START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_analytics_id START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_metric_id START WITH 1 INCREMENT BY 1;

-- =============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =============================================================================

-- Update last_accessed when memory is accessed
CREATE OR REPLACE TRIGGER trg_memory_access_update
    BEFORE UPDATE ON USER_MEMORY
    FOR EACH ROW
BEGIN
    :NEW.last_accessed := SYSDATE;
    :NEW.access_count := :OLD.access_count + 1;
END;
/

-- Update pattern strength when reinforced
CREATE OR REPLACE TRIGGER trg_pattern_reinforcement
    BEFORE UPDATE ON USER_PATTERNS
    FOR EACH ROW
BEGIN
    :NEW.updated_date := SYSDATE;
    :NEW.last_reinforced := SYSDATE;
    -- Increase strength slightly when pattern is reinforced
    IF :NEW.sample_count > :OLD.sample_count THEN
        :NEW.strength := LEAST(1.0, :OLD.strength + 0.1);
    END IF;
END;
/

-- Update session activity
CREATE OR REPLACE TRIGGER trg_session_activity_update
    BEFORE UPDATE ON SESSION_MEMORY
    FOR EACH ROW
BEGIN
    :NEW.last_activity := SYSDATE;
END;
/

-- =============================================================================
-- INITIAL DATA AND CONFIGURATION
-- =============================================================================

-- Insert default system patterns
INSERT INTO USER_PATTERNS (pattern_id, user_id, pattern_type, pattern_data, strength, confidence)
VALUES ('SYS_DEFAULT_001', 'SYSTEM', 'communication_style', 
        JSON('{"description": "Default balanced communication", "formality": "medium", "detail_level": "moderate"}'),
        0.5, 0.8);

INSERT INTO USER_PATTERNS (pattern_id, user_id, pattern_type, pattern_data, strength, confidence)
VALUES ('SYS_DEFAULT_002', 'SYSTEM', 'response_style', 
        JSON('{"length": "medium", "examples": true, "technical_depth": "adaptive"}'),
        0.5, 0.8);

-- Commit all changes
COMMIT;

-- =============================================================================
-- GRANTS AND PERMISSIONS
-- =============================================================================

-- Grant necessary permissions for the application user
-- GRANT SELECT, INSERT, UPDATE, DELETE ON USER_MEMORY TO acca_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON USER_PATTERNS TO acca_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON MEMORY_CONTEXT TO acca_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON USER_PREFERENCES TO acca_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON SESSION_MEMORY TO acca_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON MEMORY_ACCESS_LOG TO acca_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON DOCUMENT_INTELLIGENCE TO acca_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON DOCUMENT_RELATIONSHIPS TO acca_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON CONVERSATION_ANALYTICS TO acca_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON SYSTEM_PERFORMANCE TO acca_app_user;

-- Grant access to views
-- GRANT SELECT ON v_user_active_memories TO acca_app_user;
-- GRANT SELECT ON v_user_analytics_summary TO acca_app_user;
-- GRANT SELECT ON v_memory_effectiveness TO acca_app_user;

-- Grant sequence usage
-- GRANT SELECT ON seq_memory_id TO acca_app_user;
-- GRANT SELECT ON seq_pattern_id TO acca_app_user;
-- GRANT SELECT ON seq_context_id TO acca_app_user;
-- GRANT SELECT ON seq_pref_id TO acca_app_user;
-- GRANT SELECT ON seq_session_id TO acca_app_user;
-- GRANT SELECT ON seq_log_id TO acca_app_user;
-- GRANT SELECT ON seq_doc_id TO acca_app_user;
-- GRANT SELECT ON seq_analytics_id TO acca_app_user;
-- GRANT SELECT ON seq_metric_id TO acca_app_user;

-- =============================================================================
-- VERIFICATION QUERIES
-- =============================================================================

-- Verify table creation
SELECT table_name, num_rows 
FROM user_tables 
WHERE table_name IN (
    'USER_MEMORY', 'USER_PATTERNS', 'MEMORY_CONTEXT', 'USER_PREFERENCES',
    'SESSION_MEMORY', 'MEMORY_ACCESS_LOG', 'DOCUMENT_INTELLIGENCE',
    'DOCUMENT_RELATIONSHIPS', 'CONVERSATION_ANALYTICS', 'SYSTEM_PERFORMANCE'
)
ORDER BY table_name;

-- Verify indexes
SELECT index_name, table_name, uniqueness
FROM user_indexes
WHERE table_name IN (
    'USER_MEMORY', 'USER_PATTERNS', 'MEMORY_CONTEXT', 'USER_PREFERENCES',
    'SESSION_MEMORY', 'MEMORY_ACCESS_LOG', 'DOCUMENT_INTELLIGENCE'
)
ORDER BY table_name, index_name;

-- Verify Oracle Text indexes
SELECT index_name, table_name, index_type
FROM user_indexes
WHERE index_type = 'DOMAIN'
ORDER BY table_name, index_name;

PROMPT 'Oracle Advanced Memory System Schema created successfully!'
PROMPT 'Tables: 10 | Indexes: 20+ | Views: 3 | Sequences: 9 | Triggers: 3'
PROMPT 'Ready for AccA AI Assistant Advanced Memory Integration!'