# 🎯 Hướng Dẫn Sử Dụng RAG Tối Ưu - Từng Bước

## 🚀 Bước 1: <PERSON><PERSON><PERSON> và Cấu Hình Admin

### 1.1 Đăng Nhập Open WebUI
```
URL: http://localhost:3001
User: <EMAIL> (hoặc tài khoản admin của bạn)
```

### 1.2 Vào Settings Admin
1. Click vào **avatar/profile** ở góc dưới trái
2. Chọn **Settings**
3. Chọn tab **Admin Settings**
4. Chọn **Documents** từ menu bên trái

### 1.3 Cấu Hình Documents Settings (Quan Trọng!)

#### **Content Extraction:**
```yaml
Content Extraction Engine: Tika ✅
Tika Server URL: http://tika:9998 ✅
```

#### **Text Splitting:**
```yaml
Text Splitter: Token (Tiktoken) ✅  # KHÔNG phải Character!
Chunk Size: 500 ✅                  # tokens, không phải characters
Chunk Overlap: 100 ✅               # 20% overlap tối ưu
```

#### **Embedding:**
```yaml
Embedding Model Engine: Ollama ✅
Embedding Model: nomic-embed-text:latest ✅
Embedding Batch Size: 1 ✅
```

#### **Retrieval (Tính Năng Chính!):**
```yaml
Hybrid Search: ✅ ENABLED           # Quan trọng nhất!
Top K: 20 ✅                        # Số documents ban đầu
Top K Reranker: 10 ✅               # Sau khi re-rank
Relevance Threshold: 0.1 ✅         # Ngưỡng relevance
Re-ranking Model: BAAI/bge-reranker-v2-m3 ✅
```

### 1.4 Lưu Cấu Hình
- Click **Save** ở cuối trang
- Chờ thông báo "Settings saved successfully"

---

## 📚 Bước 2: Tạo Knowledge Base

### 2.1 Vào Workspace
1. Click **Workspace** ở sidebar trái
2. Click tab **Knowledge** ở thanh trên

### 2.2 Tạo Knowledge Base Mới
1. Click nút **"+"** bên phải
2. Điền thông tin:
   ```
   Name: Test RAG Optimized
   Purpose: Demo optimized RAG with Tika + Hybrid Search
   Visibility: Private (hoặc Public tùy bạn)
   ```
3. Click **Create Knowledge**

### 2.3 Upload Documents
1. Click nút **"+"** bên cạnh search bar
2. Chọn **Upload File**
3. Upload file `test_rag_document.txt` (đã tạo bởi demo script)
4. Chờ processing hoàn thành (Tika sẽ xử lý)

**📊 Theo dõi Processing:**
- Bạn sẽ thấy progress bar
- Status: "Processing..." → "Completed"
- File sẽ xuất hiện trong danh sách với icon xanh

---

## 🤖 Bước 3: Tạo RAG-Enabled Model

### 3.1 Vào Models Section
1. Click **Workspace** → **Models**
2. Click nút **"+"** để tạo model mới

### 3.2 Cấu Hình Model
```yaml
Name: Llama + Optimized RAG
Base Model: llama3.1:8b (hoặc model 7B-14B khác)
Knowledge Source: Test RAG Optimized ✅  # Chọn KB vừa tạo
System Prompt: (xem template bên dưới)
```

### 3.3 System Prompt Tối Ưu
```markdown
You are an AI assistant with access to optimized RAG knowledge retrieval.

## Response Guidelines:
1. **Always use retrieved context** when available
2. **Cite sources** with relevance scores 
3. **State clearly** if information is not in knowledge base
4. **Prioritize accuracy** over completeness

## Context Analysis:
- High confidence: Relevance score ≥ 0.8
- Medium confidence: Relevance score 0.5-0.8  
- Low confidence: Relevance score < 0.5

## Citation Format:
Sources: [Document](Relevance: 0.XX)

Remember: You're using hybrid search + re-ranking for optimal retrieval.
```

### 3.4 Lưu Model
- Click **Save & Create**
- Model sẽ xuất hiện trong danh sách

---

## 🧪 Bước 4: Test RAG System

### 4.1 Tạo Chat Mới
1. Click **New Chat**
2. Chọn model **"Llama + Optimized RAG"** vừa tạo

### 4.2 Test Questions
Thử các câu hỏi sau để test different aspects:

#### **Test 1: Factual Information**
```
What is the company vacation policy?
```
**Expected:** Chính xác trích xuất "20 days per year, 2 weeks advance notice"

#### **Test 2: Technical Information**  
```
How do I configure the API?
```
**Expected:** Liệt kê 3 bước với API_KEY, config.json, authentication

#### **Test 3: Contact Information**
```
What are the office hours and contact information?
```
**Expected:** "Monday to Friday 9 AM to 5 PM" + email addresses

#### **Test 4: Performance Metrics**
```
What are the current performance metrics?
```
**Expected:** Response time, uptime, satisfaction scores

### 4.3 Kiểm Tra Quality Indicators

**✅ Dấu hiệu RAG hoạt động tốt:**
- Response có citations với relevance scores
- Thông tin chính xác từ document
- Không hallucination
- Sources được liệt kê rõ ràng

**❌ Dấu hiệu cần điều chỉnh:**
- Không có citations
- Thông tin không chính xác
- Response quá chung chung
- Relevance scores thấp (<0.5)

---

## 🔧 Bước 5: Fine-tuning Parameters

### 5.1 Điều Chỉnh Theo Use Case

#### **For Precise Factual Queries:**
```yaml
Chunk Size: 300 tokens
Top K: 25
Relevance Threshold: 0.15
```

#### **For Complex Reasoning:**
```yaml  
Chunk Size: 800 tokens
Top K: 15
Relevance Threshold: 0.1
```

#### **For Legal/Compliance Docs:**
```yaml
Chunk Size: 200 tokens  
Top K: 30
Relevance Threshold: 0.2
```

### 5.2 Monitor Performance
1. Check relevance scores trong citations
2. User feedback về accuracy
3. Response time (nếu chậm → giảm Top K)

---

## 🌐 Bước 6: Advanced Features

### 6.1 Multi-Source RAG
1. Enable **Web Search** trong chat interface
2. Toggle ⚡ button để bật/tắt web search
3. Kết hợp local knowledge + internet info

### 6.2 Document Management
```
# Upload thêm documents
- PDF files (Tika sẽ extract text)
- DOCX files  
- PowerPoint presentations
- Text files

# Organize knowledge bases
- Tạo separate KBs cho different domains
- Company Policies KB
- Technical Documentation KB  
- FAQ KB
```

### 6.3 Advanced Prompting
```markdown
Use this template for specialized use cases:

Context: {retrieved_context}
Relevance Scores: {relevance_scores}
Question: {user_question}

Instructions:
1. Analyze context relevance (ignore scores < 0.5)
2. Provide answer based only on high-confidence context
3. If conflicting information, note the differences
4. Always cite sources with scores

Answer:
```

---

## 🎯 Expected Results

### **So với RAG cũ, bạn sẽ thấy:**

| Aspect | Before | After (Optimized) |
|--------|--------|------------------|
| **Accuracy** | 70-80% | 85-95% |
| **Relevance** | Mixed results | Consistently high |
| **Source Citation** | Basic | With confidence scores |
| **Processing** | Character-based | Token-based (better) |
| **Search** | Dense only | Hybrid (dense+sparse) |
| **Document Support** | Limited | PDF/DOCX/PPT via Tika |

### **Performance Benchmarks:**
- Response time: < 3 seconds
- Relevance score: > 0.7 average  
- Citation accuracy: > 90%
- User satisfaction: Significant improvement

---

## 🚨 Troubleshooting

### **Issue: No citations trong response**
```bash
# Check embedding model
ollama list | grep nomic-embed-text

# Check Tika service  
curl http://localhost:9998/version

# Verify knowledge base has documents
# Go to Workspace > Knowledge > [Your KB]
```

### **Issue: Low relevance scores**
```yaml
# Giảm threshold
Relevance Threshold: 0.05

# Tăng Top K
Top K: 30

# Check document quality (có đủ content không?)
```

### **Issue: Slow response**
```yaml
# Giảm Top K
Top K: 15
Top K Reranker: 8

# Check system resources
docker stats
```

---

## 🎉 Next Level: Production Tips

### **1. Content Strategy**
- Organize documents theo topics
- Use consistent naming conventions
- Regular content updates
- Quality control process

### **2. Performance Optimization**  
- Monitor system metrics
- A/B test different parameters
- User feedback collection
- Continuous improvement

### **3. Security & Privacy**
- All data stays local
- No external API calls (except for web search)
- User access controls
- Audit trails

**🎯 Mục tiêu cuối cùng:** Hệ thống RAG local hoàn toàn private, accurate, và scalable cho production use! 