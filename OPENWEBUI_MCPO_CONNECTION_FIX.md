# 🔧 OpenWebUI-MCPO Connection Fix Guide

## 🚨 VẤN ĐỀ ĐÃ XÁC ĐỊNH

**Root Cause**: OpenWebUI hiện tại **KHÔNG CÓ CẤU HÌNH MCP** để kết nối với MCPO servers.

### Phân Tích Chi Tiết:
- ✅ **MCPO servers đang hoạt động bình thường** (12 servers trên port 8000)
- ✅ **MCPO API accessible** với authentication
- ❌ **OpenWebUI thiếu MCP environment variables**
- ❌ **Không có MCP servers configuration**
- ❌ **Không có MCPO connection settings**

## 🎯 GIẢI PHÁP

### 🚀 Giải Pháp 1: Quick Fix (Khuyến nghị)

Chạy script tự động khắc phục:

```bash
cd /home/<USER>/AccA/AccA
./fix_openwebui_mcpo_connection.sh
```

Script sẽ:
1. Phân tích vấn đề hiện tại
2. Đ<PERSON><PERSON> ra 3 tùy chọn khắc phục
3. Tự động cấu hình OpenWebUI với MCP support
4. Verify kết nối

### 🔧 Giải Pháp 2: Manual Configuration

#### Step 1: Cập nhật OpenWebUI Container

```bash
# Stop current container
docker stop open-webui

# Start with MCP support
docker run -d \
    --name open-webui \
    --restart unless-stopped \
    -p 3000:8080 \
    -v acca_open_webui_data:/app/backend/data \
    -v $(pwd)/mem0-owui/config/mcp_servers.json:/app/backend/data/mcp_servers.json:ro \
    -e ENABLE_MCP_SERVERS=true \
    -e MCP_SERVERS_CONFIG_PATH=/app/backend/data/mcp_servers.json \
    -e MCPO_BASE_URL=http://host.docker.internal:8000 \
    -e MCPO_API_KEY=acca-enhanced-rag-mcp-key-2025 \
    -e WEBUI_NAME="AccA OpenWebUI with MCP Support" \
    --add-host host.docker.internal:host-gateway \
    --network acca-network \
    ghcr.io/open-webui/open-webui:latest
```

#### Step 2: Verify Configuration

```bash
# Check container status
docker ps | grep open-webui

# Check MCP environment variables
docker exec open-webui env | grep -E "(MCP|MCPO)"

# Test OpenWebUI access
curl -I http://localhost:3000
```

### 🐳 Giải Pháp 3: Docker Compose Deployment

Sử dụng cấu hình Docker Compose đã tối ưu:

```bash
# Deploy new stack with MCP support
docker-compose -f docker-compose-openwebui-mcpo.yml up -d

# Check services
docker-compose -f docker-compose-openwebui-mcpo.yml ps
```

## 📋 CÁC FILE ĐÃ TẠO

### 1. **MCP Configuration for OpenWebUI**
- **File**: [`mem0-owui/config/mcp_servers.json`](mem0-owui/config/mcp_servers.json:1)
- **Purpose**: Cấu hình MCP servers cho OpenWebUI
- **Format**: Tuân thủ chuẩn OpenWebUI MCP configuration

### 2. **Docker Compose with MCP Support**
- **File**: [`docker-compose-openwebui-mcpo.yml`](docker-compose-openwebui-mcpo.yml:1)
- **Purpose**: Deploy OpenWebUI với MCP integration
- **Features**: Full MCP support, MCPO integration, pipelines

### 3. **Automated Fix Script**
- **File**: [`fix_openwebui_mcpo_connection.sh`](fix_openwebui_mcpo_connection.sh:1)
- **Purpose**: Tự động khắc phục vấn đề kết nối
- **Features**: Interactive, multiple options, verification

### 4. **Optimized MCPO Configuration**
- **File**: [`mem0-owui/config/mcpo_config_optimized.json`](mem0-owui/config/mcpo_config_optimized.json:1)
- **Purpose**: Cấu hình MCPO tối ưu với mô tả chi tiết

## 🔍 VERIFICATION STEPS

### 1. Check MCPO Server Status
```bash
# Test MCPO API
curl -s http://localhost:8000/openapi.json | jq '.info'

# Test with authentication
curl -X POST "http://localhost:8000/time_utilities/get_current_time" \
  -H "Authorization: Bearer acca-enhanced-rag-mcp-key-2025" \
  -d '{}'
```

### 2. Check OpenWebUI MCP Integration
```bash
# Check MCP environment variables
docker exec open-webui env | grep -E "(MCP|MCPO)"

# Check MCP configuration file
docker exec open-webui cat /app/backend/data/mcp_servers.json
```

### 3. Test End-to-End Connection
1. Access OpenWebUI: http://localhost:3000
2. Go to **Admin Panel** → **Settings** → **Tools**
3. Look for MCP servers in available tools
4. Test MCP server functionality

## 🚨 TROUBLESHOOTING

### Issue 1: OpenWebUI không khởi động
```bash
# Check logs
docker logs open-webui

# Check port conflicts
lsof -i :3000

# Restart container
docker restart open-webui
```

### Issue 2: MCP servers không hiển thị
```bash
# Verify MCP config file exists
docker exec open-webui ls -la /app/backend/data/mcp_servers.json

# Check MCP environment variables
docker exec open-webui env | grep MCP

# Restart with correct configuration
./fix_openwebui_mcpo_connection.sh
```

### Issue 3: MCPO không accessible từ container
```bash
# Test host.docker.internal resolution
docker exec open-webui nslookup host.docker.internal

# Test MCPO connectivity from container
docker exec open-webui curl -I http://host.docker.internal:8000

# Check network configuration
docker network ls
docker network inspect acca-network
```

## 📊 EXPECTED RESULTS

Sau khi khắc phục thành công:

### ✅ OpenWebUI Interface
- MCP servers xuất hiện trong **Tools** section
- Có thể sử dụng MCP tools trong conversations
- Admin panel hiển thị MCP configuration

### ✅ API Integration
- OpenWebUI có thể gọi MCPO endpoints
- Authentication hoạt động với API key
- All 12 MCP servers accessible

### ✅ Functionality Test
```bash
# Test time utilities via OpenWebUI
# Should work in chat: "What time is it?"

# Test Vietnamese language processing
# Should work in chat: "Phân tích cảm xúc của câu này: Tôi rất vui"

# Test document processing
# Should work: Upload document and ask for analysis
```

## 🎯 NEXT STEPS

1. **Run the fix script**: `./fix_openwebui_mcpo_connection.sh`
2. **Choose appropriate option** (1 recommended for quick fix)
3. **Verify functionality** through OpenWebUI interface
4. **Test MCP tools** in conversations
5. **Monitor logs** for any issues

## 📞 SUPPORT

### Log Locations
- **OpenWebUI logs**: `docker logs open-webui`
- **MCPO logs**: `/home/<USER>/AccA/AccA/mem0-owui/logs/mcpo_optimized.log`
- **Fix script logs**: Output during execution

### Configuration Files
- **MCP servers**: [`mem0-owui/config/mcp_servers.json`](mem0-owui/config/mcp_servers.json:1)
- **MCPO config**: [`mem0-owui/config/mcpo_config_optimized.json`](mem0-owui/config/mcpo_config_optimized.json:1)
- **Docker compose**: [`docker-compose-openwebui-mcpo.yml`](docker-compose-openwebui-mcpo.yml:1)

### Quick Commands
```bash
# Check overall status
docker ps | grep -E "(open-webui|mcpo)"

# Test MCPO connectivity
curl -s http://localhost:8000/openapi.json | jq '.info.title'

# Restart everything
./fix_openwebui_mcpo_connection.sh
```

---

**Status**: ✅ Ready to fix OpenWebUI-MCPO connection  
**Last Updated**: 2025-07-31  
**Compatibility**: OpenWebUI latest, MCPO 0.0.16, MCP 1.12.0