"""
Docker Integration Script for Open WebUI RAG Replacement
=======================================================

This script provides Docker-specific integration for deploying the enhanced RAG system
directly into running Open WebUI containers.
"""

import os
import sys
import json
import logging
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, List, Optional

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

class DockerOpenWebUIIntegrator:
    """
    Docker-specific integrator for Open WebUI RAG replacement.
    """
    
    def __init__(self, container_name: str = "open-webui-ssl"):
        self.container_name = container_name
        self.container_app_path = "/app"
        self.enhanced_rag_path = f"{self.container_app_path}/backend/open_webui/enhanced_rag"
        
        # Files to copy into container
        self.rag_files = [
            "rag_optimization_system.py",
            "openwebui_rag_replacement.py", 
            "openwebui_integration_adapter.py",
            "rag_benchmark_system.py",
            "rag_pipeline_integration.py",
            "requirements_rag_optimization.txt"
        ]
    
    def check_container_running(self) -> bool:
        """Check if the Open WebUI container is running."""
        try:
            result = subprocess.run([
                "docker", "ps", "--filter", f"name={self.container_name}", "--format", "{{.Names}}"
            ], capture_output=True, text=True, check=True)
            
            return self.container_name in result.stdout
        except subprocess.CalledProcessError:
            return False
    
    def copy_files_to_container(self) -> bool:
        """Copy enhanced RAG files to the container."""
        log.info("Copying enhanced RAG files to container...")
        
        try:
            # Create enhanced_rag directory in container
            subprocess.run([
                "docker", "exec", self.container_name, 
                "mkdir", "-p", self.enhanced_rag_path
            ], check=True)
            
            # Copy each file
            for file_name in self.rag_files:
                if Path(file_name).exists():
                    subprocess.run([
                        "docker", "cp", file_name, 
                        f"{self.container_name}:{self.enhanced_rag_path}/{file_name}"
                    ], check=True)
                    log.info(f"Copied {file_name}")
                else:
                    log.warning(f"File not found: {file_name}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            log.error(f"Failed to copy files: {e}")
            return False
    
    def install_dependencies_in_container(self) -> bool:
        """Install Python dependencies in the container."""
        log.info("Installing dependencies in container...")
        
        try:
            # Install from requirements file
            subprocess.run([
                "docker", "exec", self.container_name,
                "pip", "install", "-r", f"{self.enhanced_rag_path}/requirements_rag_optimization.txt"
            ], check=True)
            
            log.info("Dependencies installed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            log.error(f"Failed to install dependencies: {e}")
            return False
    
    def create_integration_script(self) -> str:
        """Create the integration script to run inside the container."""
        script_content = f'''#!/usr/bin/env python3
"""
Container Integration Script
"""
import sys
import os
import shutil
from pathlib import Path

# Add enhanced_rag to Python path
sys.path.insert(0, "{self.enhanced_rag_path}")

def backup_original_files():
    """Backup original Open WebUI files."""
    backup_dir = Path("{self.container_app_path}/backup_original")
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = [
        "backend/open_webui/routers/knowledge.py",
        "backend/open_webui/routers/retrieval.py",
        "backend/open_webui/main.py"
    ]
    
    for file_path in files_to_backup:
        source = Path("{self.container_app_path}") / file_path
        if source.exists():
            dest = backup_dir / file_path
            dest.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(source, dest)
            print(f"Backed up: {{file_path}}")

def modify_main_py():
    """Modify main.py to initialize enhanced RAG."""
    main_py_path = Path("{self.container_app_path}/backend/open_webui/main.py")
    
    with open(main_py_path, "r") as f:
        content = f.read()
    
    # Add enhanced RAG initialization
    enhanced_rag_init = '''
# Enhanced RAG System Integration
try:
    from open_webui.enhanced_rag.openwebui_integration_adapter import (
        initialize_enhanced_rag_integration,
        shutdown_enhanced_rag_integration
    )
    
    @app.on_event("startup")
    async def initialize_enhanced_rag():
        """Initialize enhanced RAG system."""
        success = initialize_enhanced_rag_integration()
        if success:
            print("✅ Enhanced RAG system initialized successfully")
        else:
            print("❌ Enhanced RAG system initialization failed")
    
    @app.on_event("shutdown") 
    async def shutdown_enhanced_rag():
        """Shutdown enhanced RAG system."""
        shutdown_enhanced_rag_integration()
        print("Enhanced RAG system shutdown")
        
except ImportError as e:
    print(f"Enhanced RAG system not available: {{e}}")
'''
    
    if "Enhanced RAG System Integration" not in content:
        if "if __name__ == \\"__main__\\":" in content:
            content = content.replace(
                "if __name__ == \\"__main__\\":",
                enhanced_rag_init + "\\n\\nif __name__ == \\"__main__\\":"
            )
        else:
            content += enhanced_rag_init
        
        with open(main_py_path, "w") as f:
            f.write(content)
        
        print("Modified main.py successfully")

def add_enhanced_routes():
    """Add enhanced RAG routes to knowledge.py."""
    knowledge_py_path = Path("{self.container_app_path}/backend/open_webui/routers/knowledge.py")
    
    with open(knowledge_py_path, "r") as f:
        content = f.read()
    
    enhanced_import = '''
# Enhanced RAG System
try:
    from open_webui.enhanced_rag.openwebui_rag_replacement import router as enhanced_rag_router
    router.include_router(enhanced_rag_router, prefix="/enhanced", tags=["enhanced-rag"])
except ImportError:
    pass
'''
    
    if "Enhanced RAG System" not in content:
        # Insert after imports
        lines = content.split('\\n')
        import_end = 0
        for i, line in enumerate(lines):
            if line.startswith('from ') or line.startswith('import '):
                import_end = i
        
        lines.insert(import_end + 1, enhanced_import)
        content = '\\n'.join(lines)
        
        with open(knowledge_py_path, "w") as f:
            f.write(content)
        
        print("Added enhanced RAG routes successfully")

def main():
    print("Starting container integration...")
    backup_original_files()
    modify_main_py()
    add_enhanced_routes()
    print("Container integration completed!")

if __name__ == "__main__":
    main()
'''
        
        return script_content
    
    def run_integration_in_container(self) -> bool:
        """Run the integration script inside the container."""
        log.info("Running integration script in container...")
        
        try:
            # Create temporary script file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(self.create_integration_script())
                script_path = f.name
            
            # Copy script to container
            subprocess.run([
                "docker", "cp", script_path,
                f"{self.container_name}:{self.enhanced_rag_path}/integrate.py"
            ], check=True)
            
            # Run script in container
            subprocess.run([
                "docker", "exec", self.container_name,
                "python3", f"{self.enhanced_rag_path}/integrate.py"
            ], check=True)
            
            # Clean up
            os.unlink(script_path)
            
            log.info("Integration script completed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            log.error(f"Integration script failed: {e}")
            return False
    
    def create_config_file(self) -> bool:
        """Create configuration file in container."""
        log.info("Creating configuration file...")
        
        config = {
            "enhanced_rag": {
                "enabled": True,
                "fallback_to_original": True,
                "log_performance_metrics": True,
                "vietnamese_processing": True,
                "intelligent_chunking": True,
                "hybrid_search": True,
                "citation_enhancement": True
            },
            "chunking": {
                "max_chunk_size": 1000,
                "overlap_size": 200,
                "respect_sentence_boundaries": True,
                "context_aware": True
            },
            "retrieval": {
                "top_k_default": 5,
                "confidence_threshold": 0.7,
                "reranking_enabled": True,
                "hybrid_search_weight": 0.7
            },
            "embedding": {
                "model_name": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
                "cache_embeddings": True,
                "batch_size": 32
            }
        }
        
        try:
            # Create config file locally
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(config, f, indent=2)
                config_path = f.name
            
            # Copy to container
            subprocess.run([
                "docker", "cp", config_path,
                f"{self.container_name}:{self.enhanced_rag_path}/enhanced_rag_config.json"
            ], check=True)
            
            # Clean up
            os.unlink(config_path)
            
            log.info("Configuration file created successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            log.error(f"Failed to create config file: {e}")
            return False
    
    def restart_container(self) -> bool:
        """Restart the Open WebUI container."""
        log.info("Restarting container...")
        
        try:
            subprocess.run(["docker", "restart", self.container_name], check=True)
            log.info("Container restarted successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            log.error(f"Failed to restart container: {e}")
            return False
    
    def verify_integration(self) -> bool:
        """Verify the integration is working."""
        log.info("Verifying integration...")
        
        try:
            # Wait a bit for container to start
            import time
            time.sleep(10)
            
            # Check if enhanced RAG endpoints are available
            result = subprocess.run([
                "docker", "exec", self.container_name,
                "curl", "-s", "http://localhost:8080/api/v1/knowledge/enhanced/health"
            ], capture_output=True, text=True)
            
            if "healthy" in result.stdout:
                log.info("Integration verified successfully")
                return True
            else:
                log.warning("Health check endpoint not responding as expected")
                return False
                
        except subprocess.CalledProcessError as e:
            log.error(f"Verification failed: {e}")
            return False
    
    def integrate(self) -> bool:
        """Run the complete integration process."""
        log.info("Starting Docker integration for Open WebUI RAG replacement...")
        
        steps = [
            ("Checking container", self.check_container_running),
            ("Copying files", self.copy_files_to_container),
            ("Installing dependencies", self.install_dependencies_in_container),
            ("Running integration", self.run_integration_in_container),
            ("Creating config", self.create_config_file),
            ("Restarting container", self.restart_container),
            ("Verifying integration", self.verify_integration)
        ]
        
        for step_name, step_func in steps:
            log.info(f"Executing: {step_name}")
            if not step_func():
                log.error(f"Integration failed at step: {step_name}")
                return False
            log.info(f"Completed: {step_name}")
        
        log.info("🎉 Docker integration completed successfully!")
        log.info("Enhanced RAG system is now active in your Open WebUI container.")
        
        return True
    
    def rollback(self) -> bool:
        """Rollback the integration."""
        log.info("Rolling back integration...")
        
        try:
            # Restore original files from backup
            subprocess.run([
                "docker", "exec", self.container_name,
                "cp", "-r", f"{self.container_app_path}/backup_original/.", 
                f"{self.container_app_path}/"
            ], check=True)
            
            # Remove enhanced_rag directory
            subprocess.run([
                "docker", "exec", self.container_name,
                "rm", "-rf", self.enhanced_rag_path
            ], check=True)
            
            # Restart container
            subprocess.run(["docker", "restart", self.container_name], check=True)
            
            log.info("Rollback completed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            log.error(f"Rollback failed: {e}")
            return False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Docker Integration for Open WebUI RAG Replacement")
    parser.add_argument("--container", default="open-webui-ssl", help="Container name")
    parser.add_argument("--rollback", action="store_true", help="Rollback integration")
    parser.add_argument("--verify", action="store_true", help="Verify integration only")
    
    args = parser.parse_args()
    
    integrator = DockerOpenWebUIIntegrator(args.container)
    
    if args.rollback:
        success = integrator.rollback()
        if success:
            print("✅ Rollback completed successfully")
        else:
            print("❌ Rollback failed")
        return
    
    if args.verify:
        success = integrator.verify_integration()
        if success:
            print("✅ Integration verified successfully")
        else:
            print("❌ Integration verification failed")
        return
    
    # Run integration
    success = integrator.integrate()
    if success:
        print("✅ Docker integration completed successfully")
        print("\n📋 Next steps:")
        print("1. Check container logs: docker logs open-webui-ssl")
        print("2. Test the Knowledge feature in Open WebUI")
        print("3. Verify enhanced RAG: curl http://localhost:3000/api/v1/knowledge/enhanced/health")
    else:
        print("❌ Docker integration failed")
        sys.exit(1)

if __name__ == "__main__":
    main()