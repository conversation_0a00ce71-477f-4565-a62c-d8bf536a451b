# 🚀 Ollama API Backend - Coolify Deployment Package

[![Platform](https://img.shields.io/badge/Platform-X86__64%20%7C%20ARM64-blue)](https://ollama.ai)
[![Docker](https://img.shields.io/badge/Docker-Compose-blue)](https://docs.docker.com/compose/)
[![Coolify](https://img.shields.io/badge/Coolify-Compatible-green)](https://coolify.io)

## 📋 Tổng quan

Package deployment hoàn chỉnh cho Ollama API Backend trên nền tảng Coolify, hỗ trợ cả X86_64 và ARM64. Cung cấp API tương thích OpenAI với Nginx proxy, rate limiting, và monitoring.

## 🏗️ Kiến trúc

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Proxy   │───▶│  Ollama Backend │───▶│  Model Storage  │
│  (Rate Limit)   │    │   (API Server)  │    │   (Volumes)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

**Thành phần:**
- **Nginx**: Reverse proxy với CORS, rate limiting, health checks
- **Ollama**: Backend AI với API tương thích OpenAI
- **Volume**: Persistent storage cho models

## 📊 Tình trạng hệ thống hiện tại

**💾 Dung lượng đĩa:**
- **Tổng**: 193GB
- **Đã sử dụng**: 144GB (75%)
- **Còn trống**: 50GB ✅

**🐳 Docker usage:**
- **Images**: 17.21GB (có thể giải phóng 5.39GB)
- **Containers**: 41.19MB
- **Volumes**: 38.71GB (đã được tối ưu)

## 🚀 Triển khai nhanh

### 1. Tải lên Coolify

```bash
# Tạo package
tar -czf ollama-api-backend.tar.gz .

# Upload lên Coolify hoặc push lên Git
```

### 2. Cấu hình trong Coolify

**Environment Variables:**
```env
DOMAIN=api.yourdomain.com
OLLAMA_PORT=11434
API_PORT=8080
DOCKER_PLATFORM=linux/arm64  # hoặc linux/amd64
```

### 3. Deploy

- Tạo project mới trong Coolify
- Chọn "Docker Compose"
- Upload package hoặc connect Git repo
- Deploy!

## 🔧 Cấu hình nâng cao

### Performance tuning cho ARM64/X86

```yaml
environment:
  - OLLAMA_NUM_PARALLEL=2        # Giảm cho ARM64
  - OLLAMA_MAX_LOADED_MODELS=2   # Tiết kiệm RAM
  - OLLAMA_HOST=0.0.0.0
```

### Models được khuyến nghị

**ARM64 (hiện tại):**
- `llama3.2:1b` (1.3GB) - Nhanh, nhẹ
- `gemma2:2b` (1.6GB) - Cân bằng
- `qwen2.5:7b` (4.7GB) - Code-focused

**X86_64:**
- `qwen2.5-coder:7b` (4.7GB) - Tối ưu coding
- `codellama:7b` (3.8GB) - Meta's code model
- `deepseek-coder:6.7b` (3.7GB) - DeepSeek

## 📡 API Endpoints

### Health Check
```bash
curl http://your-domain.com/health
# Response: "healthy"
```

### List Models
```bash
curl http://your-domain.com/api/tags
```

### Chat Completion (OpenAI Compatible)
```bash
curl -X POST http://your-domain.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "llama3.2:1b",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

### Native Ollama API
```bash
curl -X POST http://your-domain.com/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "llama3.2:1b",
    "prompt": "Hello, world!",
    "stream": false
  }'
```

## 🛠️ Quản lý Models

```bash
# Vào container
docker exec -it ollama-backend bash

# Cài đặt model
./scripts/manage-models.sh install llama3.2:1b

# Models được khuyến nghị
./scripts/manage-models.sh recommended

# Liệt kê models
./scripts/manage-models.sh list

# Test model
./scripts/manage-models.sh test llama3.2:1b
```

## 📈 Monitoring & Troubleshooting

### Resource Monitoring
```bash
# Container stats
docker stats ollama-backend

# Model memory usage
curl http://localhost:11434/api/ps

# Disk usage
df -h
```

### Common Issues

**High Memory Usage:**
```bash
# Giảm models song song
OLLAMA_MAX_LOADED_MODELS=1

# Sử dụng models nhỏ hơn
ollama pull llama3.2:1b  # thay vì 7b
```

**Slow Response:**
```bash
# Giảm parallelism
OLLAMA_NUM_PARALLEL=1

# Monitor CPU
htop
```

## 🔒 Bảo mật

- ✅ Rate limiting (10 req/s)
- ✅ CORS headers
- ✅ Health check endpoints
- ⚠️ Thêm authentication nếu cần
- ⚠️ Sử dụng HTTPS trong production

## 📁 Cấu trúc Files

```
ollama-api-backend/
├── docker-compose.yml       # Main deployment config
├── .coolify.yml            # Coolify specific config
├── config/
│   └── nginx.conf          # Nginx configuration
├── scripts/
│   └── manage-models.sh    # Model management script
├── examples/
│   ├── python_client.py    # Python client example
│   └── javascript_client.js # Node.js client example
├── DEPLOYMENT.md           # Detailed deployment guide
└── README.md              # This file
```

## 🎯 Next Steps

1. **Deploy to Coolify** ✅
2. **Install recommended models** 📥
3. **Configure domain & SSL** 🔒
4. **Monitor performance** 📊
5. **Add authentication** 🔐

## 🤝 Support

- **Ollama Docs**: https://ollama.ai/docs
- **Coolify Docs**: https://coolify.io/docs
- **Docker Compose**: https://docs.docker.com/compose/

---

**💡 Tip**: Với 50GB trống, bạn có thể cài đặt 5-10 models khác nhau tùy thuộc vào kích thước! 