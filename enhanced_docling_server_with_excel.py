#!/usr/bin/env python3
"""
Enhanced Docling Server với Excel Support
Hỗ trợ đầy đủ Excel files (.xlsx, .xls) cùng với các format khác
"""

import os
import tempfile
import logging
import traceback
from pathlib import Path
from flask import Flask, request, jsonify
from flask_cors import CORS

# Excel processing imports
try:
    import pandas as pd
    import openpyxl
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

# Docling imports
try:
    from docling.document_converter import DocumentConverter
    from docling.datamodel.base_models import InputFormat
    from docling.datamodel.pipeline_options import PdfPipelineOptions, TesseractOcrOptions
    from docling.document_converter import PdfFormatOption
    DOCLING_AVAILABLE = True
except ImportError:
    DOCLING_AVAILABLE = False

# Other format imports
try:
    from docx import Document as DocxDocument
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, origins=["*"])

class EnhancedDoclingServer:
    """Enhanced Docling Server với Excel support"""
    
    def __init__(self):
        self.supported_formats = {
            # Excel formats
            '.xlsx': 'excel',
            '.xls': 'excel',
            # Docling formats
            '.pdf': 'docling',
            '.png': 'docling', 
            '.jpg': 'docling',
            '.jpeg': 'docling',
            # Other formats
            '.docx': 'docx',
            '.txt': 'text',
            '.md': 'markdown',
            '.csv': 'csv',
            '.html': 'html',
            '.json': 'json'
        }
        
        logger.info("🚀 Enhanced Docling Server with Excel support initialized")
        logger.info(f"📋 Supported formats: {list(self.supported_formats.keys())}")
        self._log_capabilities()
    
    def _log_capabilities(self):
        """Log available processing capabilities"""
        capabilities = {
            'Excel (pandas + openpyxl)': EXCEL_AVAILABLE,
            'Docling (PDF/Images)': DOCLING_AVAILABLE,
            'DOCX (python-docx)': DOCX_AVAILABLE
        }
        
        for cap, available in capabilities.items():
            status = "✅" if available else "❌"
            logger.info(f"  {status} {cap}")
    
    def process_file(self, file_path: str, form_data: dict) -> dict:
        """Process file và trả về format đúng cho Open WebUI"""
        try:
            file_ext = Path(file_path).suffix.lower()
            file_size = os.path.getsize(file_path)
            filename = Path(file_path).name
            
            logger.info(f"📄 Processing: {filename} ({file_ext}, {file_size} bytes)")
            
            if file_ext not in self.supported_formats:
                return self._create_error_response(
                    f"Unsupported file format: {file_ext}. Supported: {list(self.supported_formats.keys())}",
                    filename
                )
            
            processor_type = self.supported_formats[file_ext]
            
            # Route to appropriate processor
            if processor_type == 'excel':
                return self._process_excel_file(file_path, form_data)
            elif processor_type == 'docling':
                return self._process_with_docling(file_path, form_data)
            elif processor_type == 'docx':
                return self._process_docx_file(file_path, form_data)
            elif processor_type == 'text' or processor_type == 'markdown':
                return self._process_text_file(file_path, form_data)
            elif processor_type == 'csv':
                return self._process_csv_file(file_path, form_data)
            else:
                return self._process_fallback(file_path, form_data)
                
        except Exception as e:
            logger.error(f"❌ Error processing {file_path}: {e}")
            logger.error(traceback.format_exc())
            return self._create_error_response(str(e), Path(file_path).name)
    
    def _process_excel_file(self, file_path: str, form_data: dict) -> dict:
        """Process Excel files (.xlsx, .xls)"""
        filename = Path(file_path).name
        logger.info(f"📊 Processing Excel file: {filename}")
        
        if not EXCEL_AVAILABLE:
            return self._create_error_response(
                "Excel processing not available. Please install: pip install pandas openpyxl xlrd",
                filename
            )
        
        try:
            # Read Excel file with all sheets
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            logger.info(f"📋 Found {len(sheet_names)} sheets: {sheet_names}")
            
            all_text_parts = []
            all_tables = []
            table_counter = 0
            
            for sheet_name in sheet_names:
                try:
                    # Read sheet
                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    
                    if df.empty:
                        logger.info(f"⚠️ Sheet '{sheet_name}' is empty, skipping")
                        continue
                    
                    # Add sheet header to text
                    sheet_header = f"\n## Sheet: {sheet_name}\n"
                    all_text_parts.append(sheet_header)
                    
                    # Convert to text representation
                    text_content = df.to_string(index=False, max_rows=1000)
                    all_text_parts.append(text_content)
                    
                    # Create table representation
                    # Convert DataFrame to list of lists for table processing
                    table_data = [df.columns.tolist()] + df.values.tolist()
                    
                    # Convert to markdown table
                    markdown_table = self._table_data_to_markdown(table_data)
                    
                    # Add to tables list
                    table_info = {
                        'table_id': table_counter,
                        'sheet_name': sheet_name,
                        'rows': len(df),
                        'columns': len(df.columns),
                        'content': table_data,
                        'markdown': markdown_table,
                        'confidence': 1.0  # Excel data is always accurate
                    }
                    
                    all_tables.append(table_info)
                    table_counter += 1
                    
                    logger.info(f"✅ Processed sheet '{sheet_name}': {len(df)} rows, {len(df.columns)} columns")
                    
                except Exception as sheet_error:
                    logger.error(f"❌ Error processing sheet '{sheet_name}': {sheet_error}")
                    all_text_parts.append(f"\n## Sheet: {sheet_name}\nError processing sheet: {sheet_error}\n")
            
            # Combine all text content
            combined_text = "\n".join(all_text_parts)
            
            if not combined_text.strip():
                combined_text = f"Excel file '{filename}' processed but no readable content found."
            
            logger.info(f"✅ Excel processing completed: {len(combined_text)} chars, {len(all_tables)} tables")
            
            return self._create_success_response(
                combined_text, 
                all_tables, 
                filename, 
                "excel",
                extra_metadata={
                    'sheets_count': len(sheet_names),
                    'sheets_names': sheet_names
                }
            )
            
        except Exception as e:
            logger.error(f"❌ Excel processing failed: {e}")
            return self._create_error_response(f"Excel processing error: {e}", filename)
    
    def _process_csv_file(self, file_path: str, form_data: dict) -> dict:
        """Process CSV files"""
        filename = Path(file_path).name
        logger.info(f"📊 Processing CSV file: {filename}")
        
        if not EXCEL_AVAILABLE:
            return self._process_text_file(file_path, form_data)  # Fallback to text
        
        try:
            # Try different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252']
            df = None
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    logger.info(f"✅ CSV loaded with encoding: {encoding}")
                    break
                except UnicodeDecodeError:
                    continue
            
            if df is None:
                return self._process_text_file(file_path, form_data)  # Fallback
            
            # Convert to text
            text_content = df.to_string(index=False)
            
            # Create table
            table_data = [df.columns.tolist()] + df.values.tolist()
            markdown_table = self._table_data_to_markdown(table_data)
            
            tables = [{
                'table_id': 0,
                'rows': len(df),
                'columns': len(df.columns),
                'content': table_data,
                'markdown': markdown_table,
                'confidence': 1.0
            }]
            
            logger.info(f"✅ CSV processed: {len(df)} rows, {len(df.columns)} columns")
            
            return self._create_success_response(text_content, tables, filename, "csv")
            
        except Exception as e:
            logger.error(f"❌ CSV processing failed: {e}")
            return self._process_text_file(file_path, form_data)  # Fallback
    
    def _process_with_docling(self, file_path: str, form_data: dict) -> dict:
        """Process with Docling (PDF, images)"""
        filename = Path(file_path).name
        
        if not DOCLING_AVAILABLE:
            return self._create_error_response("Docling not available", filename)
        
        try:
            logger.info(f"🔄 Processing with Docling: {filename}")
            
            # Get OCR settings from form data
            ocr_langs = form_data.get('ocr_lang', 'eng').split(',')
            ocr_engine = form_data.get('ocr_engine', 'tesseract')
            do_ocr = form_data.get('do_picture_description', 'True').lower() == 'true'
            
            # Configure OCR
            ocr_options = TesseractOcrOptions(lang=ocr_langs)
            
            # Pipeline options
            pipeline_options = PdfPipelineOptions(
                do_ocr=do_ocr,
                do_table_structure=True,
                ocr_options=ocr_options if do_ocr else None
            )
            
            # Format options
            format_options = {
                InputFormat.PDF: PdfFormatOption(pipeline_options=pipeline_options),
                InputFormat.IMAGE: PdfFormatOption(pipeline_options=pipeline_options)
            }
            
            # Convert document
            converter = DocumentConverter(format_options=format_options)
            result = converter.convert(file_path)
            
            # Extract content and tables
            text_content = ""
            tables = []
            
            if hasattr(result, 'document') and result.document:
                doc = result.document
                
                # Extract text content
                try:
                    text_content = doc.export_to_markdown()
                    logger.info(f"✅ Extracted text: {len(text_content)} characters")
                except Exception as e:
                    logger.warning(f"Failed to export to markdown: {e}")
                    text_content = str(doc)
                
                # Extract tables
                if hasattr(doc, 'tables') and doc.tables:
                    logger.info(f"Found {len(doc.tables)} tables")
                    for i, table in enumerate(doc.tables):
                        try:
                            table_md = self._table_to_markdown(table)
                            if table_md:
                                tables.append({
                                    'table_id': i,
                                    'content': str(table),
                                    'markdown': table_md,
                                    'confidence': getattr(table, 'confidence', 0.95)
                                })
                        except Exception as e:
                            logger.warning(f"Failed to process table {i}: {e}")
            
            return self._create_success_response(text_content, tables, filename, "docling")
            
        except Exception as e:
            logger.error(f"❌ Docling processing failed: {e}")
            return self._create_error_response(f"Docling error: {str(e)}", filename)
    
    def _process_docx_file(self, file_path: str, form_data: dict) -> dict:
        """Process DOCX files"""
        filename = Path(file_path).name
        logger.info(f"📝 Processing DOCX: {filename}")
        
        if not DOCX_AVAILABLE:
            return self._process_fallback(file_path, form_data)
        
        try:
            doc = DocxDocument(file_path)
            
            # Extract text
            paragraphs = []
            for para in doc.paragraphs:
                if para.text.strip():
                    paragraphs.append(para.text.strip())
            text_content = '\n\n'.join(paragraphs)
            
            # Extract tables
            tables = []
            for i, table in enumerate(doc.tables):
                table_data = []
                for row in table.rows:
                    row_data = [cell.text.strip() for cell in row.cells]
                    table_data.append(row_data)
                
                if table_data:
                    markdown_table = self._table_data_to_markdown(table_data)
                    tables.append({
                        'table_id': i,
                        'content': table_data,
                        'markdown': markdown_table,
                        'confidence': 0.95
                    })
            
            logger.info(f"✅ DOCX processed: {len(text_content)} chars, {len(tables)} tables")
            
            return self._create_success_response(text_content, tables, filename, "docx")
            
        except Exception as e:
            logger.error(f"❌ DOCX processing failed: {e}")
            return self._create_error_response(f"DOCX error: {e}", filename)
    
    def _process_text_file(self, file_path: str, form_data: dict) -> dict:
        """Process plain text files"""
        filename = Path(file_path).name
        logger.info(f"📄 Processing text file: {filename}")
        
        try:
            # Try different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252']
            text_content = None
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        text_content = f.read()
                    logger.info(f"✅ Text loaded with encoding: {encoding}")
                    break
                except UnicodeDecodeError:
                    continue
            
            if text_content is None:
                text_content = "Error: Could not decode text file with any supported encoding"
            
            tables = []  # Text files usually don't have structured tables
            
            logger.info(f"✅ Text processed: {len(text_content)} characters")
            
            return self._create_success_response(text_content, tables, filename, "text")
            
        except Exception as e:
            logger.error(f"❌ Text processing failed: {e}")
            return self._create_error_response(f"Text error: {e}", filename)
    
    def _process_fallback(self, file_path: str, form_data: dict) -> dict:
        """Fallback processing when other methods fail"""
        filename = Path(file_path).name
        logger.info(f"📄 Fallback processing: {filename}")
        
        try:
            # Try to read as text with error handling
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                text_content = f.read()
            
            if not text_content.strip():
                text_content = f"File '{filename}' processed but content could not be extracted or file appears to be empty."
            
            return self._create_success_response(text_content, [], filename, "fallback")
            
        except Exception as e:
            return self._create_error_response(f"Fallback processing failed: {e}", filename)
    
    def _table_to_markdown(self, table_item) -> str:
        """Convert table to markdown"""
        try:
            if hasattr(table_item, 'export_to_markdown') and callable(table_item.export_to_markdown):
                return str(table_item.export_to_markdown())
            elif hasattr(table_item, 'to_markdown') and callable(table_item.to_markdown):
                return str(table_item.to_markdown())
            return str(table_item)
        except Exception as e:
            logger.error(f"Error converting table to markdown: {e}")
            return str(table_item)
    
    def _table_data_to_markdown(self, table_data: list) -> str:
        """Convert table data to markdown format"""
        if not table_data or len(table_data) == 0:
            return ""
        
        try:
            markdown_lines = []
            
            # Header row
            if len(table_data) > 0:
                header_cells = [str(cell).replace('|', '\\|') for cell in table_data[0]]
                header = "| " + " | ".join(header_cells) + " |"
                separator = "|" + "|".join([" --- " for _ in table_data[0]]) + "|"
                markdown_lines.append(header)
                markdown_lines.append(separator)
                
                # Data rows
                for row in table_data[1:]:
                    row_cells = [str(cell).replace('|', '\\|') for cell in row]
                    row_md = "| " + " | ".join(row_cells) + " |"
                    markdown_lines.append(row_md)
            
            return "\n".join(markdown_lines)
        except Exception as e:
            logger.error(f"Error converting table data to markdown: {e}")
            return str(table_data)
    
    def _create_success_response(self, text_content: str, tables: list,
                               filename: str, processor: str, extra_metadata: dict = None) -> dict:
        """Create successful response với format đúng cho Open WebUI"""
        metadata = {
            'filename': filename,
            'processor': processor,
            'text_length': len(text_content),
            'table_count': len(tables)
        }
        
        if extra_metadata:
            metadata.update(extra_metadata)
        
        return {
            'text': text_content,
            'tables': tables,
            'metadata': metadata
        }
    
    def _create_error_response(self, error_msg: str, filename: str) -> dict:
        """Create error response"""
        return {
            'error': error_msg,
            'text': f'Error processing {filename}: {error_msg}',
            'tables': [],
            'metadata': {
                'filename': filename,
                'error': True
            }
        }

# Initialize processor
processor = EnhancedDoclingServer()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'enhanced-docling-server-with-excel',
        'version': '1.0.0',
        'capabilities': {
            'excel': EXCEL_AVAILABLE,
            'docling': DOCLING_AVAILABLE,
            'docx': DOCX_AVAILABLE
        },
        'supported_formats': list(processor.supported_formats.keys())
    })

@app.route('/v1/convert/file', methods=['POST'])
@app.route('/v1alpha/convert/file', methods=['POST'])
def convert_file():
    """Convert file - Open WebUI compatible với Excel support"""
    logger.info(f"📥 Received request on {request.path}")
    
    try:
        # Check for file in different possible field names
        file_obj = None
        for field_name in ['files', 'file']:
            if field_name in request.files:
                file_obj = request.files[field_name]
                logger.info(f"✅ Found file in field: {field_name}")
                break
        
        if not file_obj or file_obj.filename == '':
            return jsonify({
                'error': 'No file provided',
                'text': '',
                'tables': []
            }), 400
        
        logger.info(f"🔄 Processing file: {file_obj.filename}")
        
        # Get form data
        form_data = {
            'image_export_mode': request.form.get('image_export_mode', 'placeholder'),
            'table_mode': request.form.get('table_mode', 'accurate'),
            'do_picture_description': request.form.get('do_picture_description', 'True'),
            'ocr_engine': request.form.get('ocr_engine', 'tesseract'),
            'ocr_lang': request.form.get('ocr_lang', 'eng')
        }
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file_obj.filename).suffix) as tmp_file:
            file_obj.save(tmp_file.name)
            tmp_file_path = tmp_file.name
        
        try:
            # Process file
            result = processor.process_file(tmp_file_path, form_data)
            logger.info(f"✅ Processing completed for: {file_obj.filename}")
            return jsonify(result)
        finally:
            # Clean up temporary file
            try:
                os.unlink(tmp_file_path)
            except:
                pass
    
    except Exception as e:
        logger.error(f"❌ Error in convert_file endpoint: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'error': str(e),
            'text': f'Server error: {str(e)}',
            'tables': []
        }), 500

if __name__ == '__main__':
    print("🚀 Enhanced Docling Server with Excel Support")
    print("=" * 60)
    print(f"📊 Excel Support: {EXCEL_AVAILABLE}")
    print(f"📄 Docling Support: {DOCLING_AVAILABLE}")
    print(f"📝 DOCX Support: {DOCX_AVAILABLE}")
    print("=" * 60)
    print("📋 Supported Formats:")
    for fmt, processor_type in processor.supported_formats.items():
        print(f"  {fmt} -> {processor_type}")
    print("=" * 60)
    print("🌐 Server: http://localhost:5001")
    print("📊 Endpoints: /v1/convert/file, /v1alpha/convert/file")
    print("🔍 Health Check: /health")
    print("=" * 60)
    
    app.run(host='0.0.0.0', port=5001, debug=False)