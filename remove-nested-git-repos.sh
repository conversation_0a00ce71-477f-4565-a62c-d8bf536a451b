#!/bin/bash

# REMOVE NESTED GIT REPOS: Remove nested git repositories from main repository tracking
set -e

echo "🧹 Removing nested git repositories from main repository tracking..."

# Check if .git_disabled exists
if [ ! -d ".git_disabled" ]; then
    echo "❌ Error: .git_disabled directory not found"
    exit 1
fi

# Temporarily enable git repository
echo "🔌 Enabling git repository..."
mv .git_disabled .git

# Function to safely remove directory from git tracking
remove_from_git_tracking() {
    local dir=$1
    if [ -d "$dir" ]; then
        echo "🗑️  Removing $dir from git tracking..."
        # Remove from git tracking but keep local files
        git rm --cached "$dir" -r 2>/dev/null || true
        git reset HEAD "$dir" 2>/dev/null || true
    else
        echo "⚠️  Directory $dir not found, skipping..."
    fi
}

# Remove each nested git repository from git tracking
remove_from_git_tracking "docling-serve"
remove_from_git_tracking "mcp-integration/servers/pandas_mcp"
remove_from_git_tracking "mem0-owui"
remove_from_git_tracking "mem0-owui/mcp-integration/servers/pandas_mcp"
remove_from_git_tracking "mem0-owui/pandas_mcp"

# Add updated .gitignore to git
echo "📝 Adding updated .gitignore to git..."
git add .gitignore

# Commit the changes
echo "💾 Committing changes..."
git commit -m "🧹 Remove nested git repositories from main repository tracking

- Remove docling-serve/ from git tracking (contains its own .git)
- Remove mcp-integration/servers/pandas_mcp/ from git tracking (contains its own .git)
- Remove mem0-owui/ from git tracking (contains its own .git)
- Remove mem0-owui/mcp-integration/servers/pandas_mcp/ from git tracking (contains its own .git)
- Remove mem0-owui/pandas_mcp/ from git tracking (contains its own .git)
- Keep local files intact
- Update .gitignore to prevent future tracking of nested git repos"

# Disable git repository again
echo "🔌 Disabling git repository..."
mv .git .git_disabled

echo ""
echo "✅ Nested git repositories removed from main repository tracking!"
echo ""
echo "📊 Current status:"
echo "• ✅ Nested git repositories are no longer tracked by main repository"
echo "• ✅ Local files are preserved"
echo "• ✅ .gitignore should be updated to prevent future tracking"
echo ""
echo "🔧 To verify the changes, you can:"
echo "   1. Rename .git_disabled back to .git temporarily"
echo "   2. Run 'git status' to see that nested repositories are no longer listed"
echo "   3. Rename .git back to .git_disabled"