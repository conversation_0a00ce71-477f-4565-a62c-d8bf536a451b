#!/usr/bin/env python3
"""
Ollama API Configuration for Qwen 2.5 Coder Tools
Optimized for external tool agents and Cline integration
"""

import requests
import json
import sys

def test_coder_tools_api():
    """Test qwen2.5-coder-tools API"""
    
    print("🚀 Qwen 2.5 Coder Tools API Test")
    print("=" * 50)
    
    base_url = "http://localhost:11435"
    
    # Test basic API connection
    try:
        response = requests.get(f"{base_url}/api/tags")
        response.raise_for_status()
        models = response.json()
        
        coder_models = [m for m in models.get('models', []) 
                       if 'qwen2.5-coder-tools' in m.get('name', '')]
        
        print(f"✅ Found {len(coder_models)} coder tools models:")
        for model in coder_models:
            name = model.get('name', 'Unknown')
            size = model.get('size', 0) / (1024**3)
            print(f"   • {name} ({size:.1f} GB)")
            
    except Exception as e:
        print(f"❌ API connection failed: {e}")
        return
    
    # Test code generation
    print(f"\n🧪 Testing code generation...")
    
    test_payload = {
        "model": "hhao/qwen2.5-coder-tools:7b",
        "prompt": "Write a Python function to calculate factorial",
        "stream": False,
        "options": {
            "num_ctx": 32768,
            "temperature": 0.1
        }
    }
    
    try:
        response = requests.post(f"{base_url}/api/generate", json=test_payload)
        response.raise_for_status()
        result = response.json()
        
        if 'response' in result:
            print("✅ Code generation: SUCCESS")
            preview = result['response'][:200] + "..."
            print(f"   Preview: {preview}")
        else:
            print("❌ Code generation: No response")
            
    except Exception as e:
        print(f"❌ Code generation failed: {e}")
    
    # Test chat with context
    print(f"\n🧪 Testing chat with extended context...")
    
    chat_payload = {
        "model": "hhao/qwen2.5-coder-tools:7b",
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful coding assistant with tool access."
            },
            {
                "role": "user", 
                "content": "Explain the difference between REST and GraphQL APIs"
            }
        ],
        "stream": False,
        "options": {
            "num_ctx": 64000,  # Extended context
            "temperature": 0.1
        }
    }
    
    try:
        response = requests.post(f"{base_url}/api/chat", json=chat_payload)
        response.raise_for_status()
        result = response.json()
        
        if 'message' in result and 'content' in result['message']:
            print("✅ Extended context chat: SUCCESS")
            preview = result['message']['content'][:200] + "..."
            print(f"   Preview: {preview}")
        else:
            print("❌ Extended context chat: No response")
            
    except Exception as e:
        print(f"❌ Extended context chat failed: {e}")

def show_integration_examples():
    """Show integration examples for external agents"""
    
    print("\n🔧 Integration Examples for External Tool Agents")
    print("=" * 60)
    
    print("1️⃣ Cline (Claude Dev) Configuration:")
    cline_config = {
        "model": "hhao/qwen2.5-coder-tools:7b",
        "apiEndpoint": "http://**************:11435/api/chat",
        "contextLength": 32768,
        "temperature": 0.1,
        "systemPrompt": "You are a helpful coding assistant specialized in development tasks."
    }
    print(json.dumps(cline_config, indent=2))
    
    print("\n2️⃣ cURL Examples:")
    curl_examples = '''
# Code generation
curl -X POST http://localhost:11435/api/generate \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "hhao/qwen2.5-coder-tools:7b",
    "prompt": "Create a Python FastAPI endpoint for user authentication",
    "options": {"num_ctx": 32768, "temperature": 0.1}
  }'

# Chat with tools
curl -X POST http://localhost:11435/api/chat \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "hhao/qwen2.5-coder-tools:14b",
    "messages": [
      {"role": "user", "content": "Help me debug this Python code"}
    ],
    "options": {"num_ctx": 64000}
  }'
'''
    print(curl_examples)
    
    print("3️⃣ Python Client Example:")
    python_example = '''
import requests

class QwenCoderClient:
    def __init__(self, base_url="http://localhost:11435"):
        self.base_url = base_url
        
    def generate_code(self, prompt, model="7b", context=32768):
        payload = {
            "model": f"hhao/qwen2.5-coder-tools:{model}",
            "prompt": prompt,
            "stream": False,
            "options": {"num_ctx": context, "temperature": 0.1}
        }
        
        response = requests.post(f"{self.base_url}/api/generate", json=payload)
        return response.json()

# Usage
client = QwenCoderClient()
result = client.generate_code("Write a database connection class")
print(result["response"])
'''
    print(python_example)

def main():
    """Main function"""
    
    # Test API functionality
    test_coder_tools_api()
    
    # Show integration examples
    show_integration_examples()
    
    print("\n✨ Qwen 2.5 Coder Tools Configuration Complete!")
    print("🌐 API Endpoint: http://**************:11435")
    print("🔧 Models available: 7B and 14B")
    print("📚 Context: 32k native, extendable to 128k")
    print("🛠️  Features: Tool calling, Insert mode, Cline compatible")
    
    print("\n🎯 Key Advantages:")
    print("   • Enhanced tool calling for agent integration")
    print("   • Optimized for Cline/Claude Dev workflows")  
    print("   • Better code completion and analysis")
    print("   • Support for external tool agent protocols")

if __name__ == "__main__":
    main() 