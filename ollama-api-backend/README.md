# Ollama API Backend for Coolify

Production-ready Ollama API backend with Nginx proxy, optimized for Coolify deployment.

## 🚀 Quick Deploy

### Via Coolify Git Repository

1. **Create new service in Coolify**
2. **Connect Git repository** with this code
3. **Set deployment method** to Docker Compose
4. **Deploy!**

Coolify will automatically:
- Pull the latest Ollama image
- Set up Nginx proxy with CORS and rate limiting
- Create persistent volume for models
- Configure health checks

## 🔧 Configuration

### Default Settings
- **Ollama**: Port 11434 (internal)
- **Nginx Proxy**: Port 80 (external)
- **Volume**: `ollama_data` for model persistence
- **GPU**: Auto-detection (NVIDIA)
- **Security**: API Key authentication required

### Environment Variables
**Required in Coolify:**

```env
# API Key for authentication (REQUIRED)
OLLAMA_API_KEY=your-secret-api-key-here

# Optional Ollama settings
OLLAMA_HOST=0.0.0.0
OLLAMA_KEEP_ALIVE=24h
```

### Generate Secure API Key
```bash
# Generate a random API key
openssl rand -hex 32
```

## 📋 API Endpoints

### Health Check
```bash
GET /health
```

### Ollama API (Proxied with Authentication)
```bash
# List models (with API key)
curl -H "Authorization: Bearer your-api-key" http://your-domain/api/tags

# Generate completion
curl -X POST http://your-domain/api/generate \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "llama3.2:1b",
    "prompt": "Hello world"
  }'

# Chat completion
curl -X POST http://your-domain/api/chat \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "llama3.2:1b",
    "messages": [
      {"role": "user", "content": "Hello!"}
    ]
  }'
```

## 🎯 Model Management

### Install Models
```bash
# Via API (with authentication)
curl -X POST http://your-domain/api/pull \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"name": "llama3.2:1b"}'

# Via container exec (no auth needed)
docker exec ollama-backend ollama pull llama3.2:1b
```

### Recommended Models for X86
- `llama3.2:1b` - Fast, lightweight
- `qwen2.5:3b` - Good balance
- `gemma2:2b` - Small but capable
- `phi3:mini` - Microsoft's efficient model

## 🛡️ Security Features

- **Rate Limiting**: 10 requests/second per IP
- **CORS**: Configured for web applications
- **Health Checks**: Automatic service monitoring
- **Proxy Isolation**: Backend not directly exposed

## 🔧 Troubleshooting

### Check Service Status
```bash
# Via Coolify logs or:
docker logs ollama-backend
docker logs ollama-proxy
```

### Test API
```bash
# Health check (no auth needed)
curl http://your-domain/health

# List models (requires API key)
curl -H "Authorization: Bearer your-api-key" http://your-domain/api/tags
```

### Performance Tuning
- Increase rate limits in `nginx.conf`
- Adjust `OLLAMA_KEEP_ALIVE` for memory management
- Monitor GPU usage if available

## 🔗 Connect with Open WebUI

### Configuration for Open WebUI
1. **Set Ollama Base URL**: `http://your-domain`
2. **Set API Key**: Use the same `OLLAMA_API_KEY` value
3. **Enable External API**: Check in Open WebUI settings

### Environment Variables for Open WebUI
```env
OLLAMA_BASE_URL=http://your-domain
OLLAMA_API_KEY=your-secret-api-key-here
```

## 📁 Project Structure
```
ollama-api-backend/
├── docker-compose.yml     # Main deployment config
├── nginx.conf.template    # Proxy configuration template
├── entrypoint.sh         # Nginx startup script
├── env.example           # Environment variables example
└── README.md             # This file
```

## 🌐 Integration Examples

### Python
```python
import requests

headers = {
    'Authorization': 'Bearer your-api-key',
    'Content-Type': 'application/json'
}

response = requests.post('http://your-domain/api/generate', 
    headers=headers,
    json={
        'model': 'llama3.2:1b',
        'prompt': 'Hello world',
        'stream': False
    }
)
print(response.json())
```

### JavaScript
```javascript
const response = await fetch('http://your-domain/api/generate', {
  method: 'POST',
  headers: { 
    'Authorization': 'Bearer your-api-key',
    'Content-Type': 'application/json' 
  },
  body: JSON.stringify({
    model: 'llama3.2:1b',
    prompt: 'Hello world',
    stream: false
  })
});
const data = await response.json();
console.log(data);
```

---

**Ready to deploy on Coolify! 🚀** 