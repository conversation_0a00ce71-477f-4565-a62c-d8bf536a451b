version: '3.8'

services:
  ollama:
    image: ollama/ollama:latest
    container_name: ollama-backend
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_KEEP_ALIVE=24h
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  nginx:
    image: nginx:alpine
    container_name: ollama-proxy
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf.template:/etc/nginx/nginx.conf.template:ro
      - ./entrypoint.sh:/entrypoint.sh:ro
    environment:
      - OLLAMA_API_KEY=${OLLAMA_API_KEY:-your-secret-api-key-here}
    depends_on:
      - ollama
    command: ["/entrypoint.sh"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  ollama_data:
    driver: local
