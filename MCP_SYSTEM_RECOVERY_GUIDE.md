# 🔧 HƯỚNG DẪN KHÔI PHỤC HỆ THỐNG MCP

## ⚠️ Vấn đề hiện tại:
- <PERSON>ô<PERSON> qua các MCP tools hoạt động bình thường
- Hôm nay sau khi thay đổi cấu hình, tất cả MCP tools đều "Connection failed"
- Cần khôi phục lại cấu hình MCP ban đầu

## ✅ Giải pháp khôi phục:

### 1. **Container MCP ban đầu:**
```bash
# Tên container: mcpo-container (không phải mcpo-container-host)
# Port: 5000 (không phải 8000)
# Command: mcpo --host 0.0.0.0 --port 5000
# Network: acca-network
```

### 2. **Lệnh khôi phục:**
```bash
# Dừng các container MCP hiện tại
docker stop mcpo-container-host mcpo-server 2>/dev/null
docker rm -f mcpo-container-host mcpo-server 2>/dev/null

# Khởi động lại container MCP ban đầu
docker run -d \
  --name mcpo-container \
  --network acca-network \
  -p 5000:5000 \
  -v $(pwd)/mcp-integration:/app \
  -w /app \
  mcpo-server \
  mcpo --host 0.0.0.0 --port 5000 --api-key acca-enhanced-rag-mcp-key-2025 --config config/mcpo_config_docker.json --cors-allow-origins "*"
```

### 3. **Cấu hình Open WebUI:**
Các MCP tools ban đầu kết nối tới:
```
http://mcpo-container:5000/weather_service
http://mcpo-container:5000/time_utilities
http://mcpo-container:5000/wikipedia
http://mcpo-container:5000/github
http://mcpo-container:5000/brave_search
http://mcpo-container:5000/gemini_search_engine
http://mcpo-container:5000/mem0_system
```

### 4. **Thêm Jina-Crawler MCP:**
Sau khi khôi phục hệ thống MCP ban đầu, có thể thêm jina-crawler:
```
http://mcpo-container:5000/jina_crawler
```

## 🔍 Kiểm tra sau khôi phục:

### Test kết nối:
```bash
# Test MCPO container
curl http://localhost:5000/weather_service
curl http://localhost:5000/time_utilities
curl http://localhost:5000/wikipedia

# Test jina-crawler (nếu đã thêm)
curl -X POST http://localhost:5000/jina_crawler/health_check \
  -H "Content-Type: application/json" -d '{}'
```

### Kiểm tra container:
```bash
docker ps --filter "name=mcpo-container"
docker logs mcpo-container --tail 10
```

## 📋 Cấu hình chính xác cho Open WebUI:

### Jina-Crawler MCP:
- **URL**: `http://mcpo-container:5000/jina_crawler`
- **Name**: `Jina Crawler`
- **Description**: `AI-powered web crawler with SmolDocling-256M-preview`
- **Auth**: Bearer (để trống)
- **Visibility**: Public

## 🎯 Lưu ý quan trọng:

1. **Container name**: `mcpo-container` (không phải `mcpo-container-host`)
2. **Port**: `5000` (không phải `8000`)
3. **Network**: `acca-network` (cùng với Open WebUI)
4. **Command**: `mcpo` (không phải `python -m uvicorn`)

## 🔧 Troubleshooting:

Nếu vẫn có vấn đề:
1. Kiểm tra jina-crawler service: `sudo systemctl status jina-crawler`
2. Kiểm tra network: `docker network ls`
3. Kiểm tra logs: `docker logs mcpo-container`
4. Test từng endpoint riêng lẻ

**Mục tiêu**: Khôi phục lại hệ thống MCP hoạt động như hôm qua, sau đó thêm jina-crawler vào.