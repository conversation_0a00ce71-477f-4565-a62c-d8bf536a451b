#!/bin/bash

echo "🔧 FINAL FIX - DOCX PROCESSING"
echo "================================"

# Kill old docling servers on wrong ports
echo "🛑 Cleaning up old servers..."
sudo pkill -f "docling_server.py" 2>/dev/null || true
sudo pkill -f "port.*5000.*docling" 2>/dev/null || true

# Verify Universal Docling Server is running on port 5001
echo ""
echo "🔍 Checking Universal Docling Server (port 5001)..."
if curl -s http://localhost:5001/health > /dev/null; then
    echo "   ✅ Universal Docling Server is running on port 5001"
    echo "   📋 Supports: PDF, DOCX, TXT, JSON, XML, CSV, XLSX, PPTX, HTML, MD, etc."
else
    echo "   ❌ Universal Docling Server not running! Starting..."
    python3 universal_docling_server_complete.py &
    sleep 5
    if curl -s http://localhost:5001/health > /dev/null; then
        echo "   ✅ Universal Docling Server started successfully"
    else
        echo "   ❌ Failed to start Universal Docling Server"
        exit 1
    fi
fi

# Set environment variables for Open WebUI
echo ""
echo "⚙️  Setting environment variables for Open WebUI..."
export RAG_CONTENT_EXTRACTION_ENGINE=docling
export DOCLING_SERVER_URL=http://localhost:5001
export RAG_CONTENT_EXTRACTION_TIMEOUT=120
export ENABLE_RAG_HYBRID_SEARCH=true

echo "   ✅ RAG_CONTENT_EXTRACTION_ENGINE=docling"
echo "   ✅ DOCLING_SERVER_URL=http://localhost:5001"
echo "   ✅ RAG_CONTENT_EXTRACTION_TIMEOUT=120"
echo "   ✅ ENABLE_RAG_HYBRID_SEARCH=true"

# Save to .env file for persistence
cat > .env.docling_final << EOF
RAG_CONTENT_EXTRACTION_ENGINE=docling
DOCLING_SERVER_URL=http://localhost:5001
RAG_CONTENT_EXTRACTION_TIMEOUT=120
ENABLE_RAG_HYBRID_SEARCH=true
EOF

echo "   ✅ Environment variables saved to .env.docling_final"

echo ""
echo "================================"
echo "🎉 SETUP COMPLETED!"
echo "================================"
echo ""
echo "📋 System Status:"
echo "   ✅ Universal Docling Server: http://localhost:5001 (RUNNING)"
echo "   ⚠️  Open WebUI: Needs manual restart with new environment"
echo ""
echo "🚀 To restart Open WebUI with fixed DOCX processing:"
echo ""
echo "   source .env.docling_final"
echo "   open-webui serve --port 3000"
echo ""
echo "🧪 After restart, test by uploading:"
echo "   📄 149_2001_QD-BTC_48964.docx"
echo "   📄 Any TXT, JSON, XML, CSV files"
echo ""
echo "✅ LLM will now read file content instead of just showing filename!"
echo "================================"