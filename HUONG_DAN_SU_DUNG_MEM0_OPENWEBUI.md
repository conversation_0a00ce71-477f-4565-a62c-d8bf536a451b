# Hướng Dẫn Sử Dụng Mem0 Memory System trong Open WebUI

## 🎯 Tổng Quan
Mem0 đã được tích hợp vào Open WebUI thông qua Functions/Tools, cho phép bạn:
- Lưu trữ và quản lý memory
- Tì<PERSON> kiếm thông tin đã lưu
- Chat với context từ memory
- Xử lý hình ảnh với OCR

## 📋 Bước 1: Import Functions

### 1.1 Truy cập Admin Panel
1. Mở Open WebUI (http://localhost:8080)
2. Đăng nhập với tài khoản admin
3. Vào **Settings** → **Admin Panel**

### 1.2 Import Memory Manager Function
1. Trong Admin Panel, chọn **Functions**
2. Click **Import Function**
3. Upload file: `openwebui_functions/memory_manager.py`
4. Verify function được import thành công

### 1.3 Import Image Processor Function
1. Click **Import Function** lần nữa
2. Upload file: `openwebui_functions/image_processor.py`
3. Verify function được import thành công

**Lưu ý**: Functions phải là file Python (.py), không phải JSON

## 🚀 Bước 2: Sử dụng Memory Functions

### 2.1 Thêm Memory
```python
memory_manager(action='add', text='Tôi làm việc tại MobiFone và chuyên về AI/ML')
```

### 2.2 Tìm Kiếm Memory
```python
memory_manager(action='search', query='MobiFone')
```

### 2.3 Xem Tất Cả Memories
```python
memory_manager(action='get_all')
```

### 2.4 Chat với Memory Context
```python
memory_manager(action='chat', text='Hãy tóm tắt thông tin về công việc của tôi')
```

## 🖼️ Bước 3: Xử Lý Hình Ảnh

### 3.1 Upload và Xử Lý Ảnh
1. Upload hình ảnh vào chat
2. Sử dụng function:
```python
image_processor(image_base64='[base64_data_from_upload]', filename='document.png')
```

## ⚙️ Cấu Hình Nâng Cao

### User ID Management
Mỗi user có thể có memory riêng:
```python
memory_manager(action='add', text='Personal note', user_id='user123')
```

### Memory Actions
- `add`: Thêm memory mới
- `search`: Tìm kiếm trong memory
- `get_all`: Lấy tất cả memories
- `chat`: Chat với memory context

## 🔧 Troubleshooting

### ✅ Function Validation Status
Tất cả functions đã được validate và có cấu trúc đúng:
- ✅ `test_simple.py` - Function test cơ bản
- ✅ `memory_manager.py` - Quản lý Mem0 memory
- ✅ `image_processor.py` - Xử lý hình ảnh với Docling

### Lỗi Function Import "No Function class found"
**Đã fix**: Tất cả functions hiện sử dụng `class Tools` structure đúng chuẩn Open WebUI

**Nếu vẫn gặp lỗi**:
1. Thử upload `test_simple.py` trước để test cơ bản
2. Kiểm tra Open WebUI version (cần version mới nhất)
3. Restart Open WebUI service
4. Kiểm tra logs của Open WebUI để xem lỗi chi tiết

### Lỗi Memory Connection
- Kiểm tra Qdrant server (port 6333)
- Verify Gemini API key trong environment
- Check mem0_config.json files
- Chạy: `python3 check_system_status.py`

### Lỗi Image Processing
- Kiểm tra Docling server (port 5001): `curl http://localhost:5001/health`
- Verify image format được support (PNG, JPG, PDF)
- Check base64 encoding đúng format
- **Lưu ý**: Một số lỗi Docling internal về channel dimensions là bình thường với test images

## 📊 Kiểm Tra System Status

Chạy script kiểm tra:
```bash
python3 check_system_status.py
```

## 🎯 Ví Dụ Sử Dụng Thực Tế

### Scenario 1: Personal Assistant
```python
# Lưu thông tin cá nhân
memory_manager(action='add', text='Tôi thích uống cà phê vào buổi sáng')

# Sau đó chat
memory_manager(action='chat', text='Gợi ý đồ uống cho tôi')
```

### Scenario 2: Work Notes
```python
# Lưu ghi chú công việc
memory_manager(action='add', text='Meeting với team AI ngày 7/7/2025 về Mem0 integration')

# Tìm kiếm
memory_manager(action='search', query='meeting AI team')
```

### Scenario 3: Document Processing
```python
# Xử lý document scan
image_processor(image_base64='[document_base64]', filename='contract.png')

# Lưu kết quả vào memory
memory_manager(action='add', text='Đã xử lý contract.png - chứa thông tin về...')
```

## 🔄 Workflow Tích Hợp

1. **Upload document** → `image_processor()` → **Extract text**
2. **Save to memory** → `memory_manager(action='add')` → **Store information**
3. **Query later** → `memory_manager(action='search')` → **Retrieve context**
4. **Enhanced chat** → `memory_manager(action='chat')` → **Contextual responses**

## 📈 Performance Tips

- Sử dụng specific queries cho search hiệu quả
- Organize memories với clear, descriptive text
- Regular cleanup với get_all và manual review
- Use user_id để phân tách contexts

---

✅ **System Ready**: Mem0 + Gemini API + Docling + Open WebUI
🎯 **Next**: Start using functions trong Open WebUI chat interface!