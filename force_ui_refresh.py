#!/usr/bin/env python3
"""
🔄 FORCE UI REFRESH
Force refresh Open WebUI to see new valves
"""

import requests
import json
import time

def force_ui_refresh():
    """Force refresh Open WebUI UI cache"""
    print("🔄 FORCE REFRESHING OPEN WEBUI UI CACHE")
    print("=" * 60)
    
    base_url = "http://localhost:3000"
    
    # Wait for Open WebUI to be ready
    print("⏳ Waiting for Open WebUI to be ready...")
    for i in range(30):
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ Open WebUI is ready!")
                break
        except:
            pass
        time.sleep(2)
        print(f"   Waiting... ({i+1}/30)")
    else:
        print("❌ Open WebUI not ready after 60 seconds")
        return False
    
    # Try to trigger pipeline reload via API
    try:
        print("\n🔄 Attempting to reload pipelines...")
        
        # Try different API endpoints that might trigger reload
        endpoints = [
            "/api/v1/pipelines",
            "/api/pipelines", 
            "/api/v1/admin/pipelines",
            "/admin/api/pipelines"
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=10)
                print(f"   {endpoint}: {response.status_code}")
                if response.status_code == 200:
                    break
            except Exception as e:
                print(f"   {endpoint}: Error - {e}")
        
        # Try POST to reload
        try:
            response = requests.post(f"{base_url}/api/v1/pipelines/reload", timeout=10)
            print(f"   Reload endpoint: {response.status_code}")
        except:
            print("   Reload endpoint: Not available")
            
    except Exception as e:
        print(f"❌ Error accessing API: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 MANUAL UI REFRESH STEPS:")
    print("1. Open http://localhost:3000 in browser")
    print("2. Press Ctrl+F5 (hard refresh) to clear cache")
    print("3. Go to Admin Panel → Pipelines")
    print("4. Look for Oracle Advanced Memory pipeline")
    print("5. If still showing old valves:")
    print("   a. Click 'Delete' on Oracle Advanced Memory")
    print("   b. Click 'Add Pipeline' button")
    print("   c. Upload oracle-advanced-memory.py file")
    print("   d. New valves should appear")
    
    print("\n🔧 EXPECTED NEW VALVES:")
    print("   ✅ override_llm_model (checkbox)")
    print("   ✅ target_llm_model (text input)")
    print("   ✅ target_llm_provider (text input)")
    print("   ✅ llm_temperature (number 0.0-1.0)")
    print("   ✅ llm_max_tokens (number)")
    print("   ✅ llm_top_p (number 0.0-1.0)")
    
    print("\n💡 TROUBLESHOOTING:")
    print("   - Clear browser cache completely")
    print("   - Try incognito/private browsing mode")
    print("   - Try different browser")
    print("   - Delete and re-add pipeline manually")
    
    return True

def check_container_valves():
    """Check valves in container"""
    print("\n" + "=" * 60)
    print("🔍 CONTAINER VALVES VERIFICATION:")
    
    import subprocess
    
    try:
        # Check total valve count
        result = subprocess.run([
            "docker", "exec", "open-webui-mcpo",
            "cat", "/app/backend/data/pipelines/oracle-advanced-memory/valves.json"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            valves = json.loads(result.stdout)
            print(f"✅ Total valves in container: {len(valves)}")
            
            # Check LLM override valves
            llm_valves = [
                "override_llm_model", "target_llm_model", "target_llm_provider",
                "llm_temperature", "llm_max_tokens", "llm_top_p"
            ]
            
            print("🔧 LLM Override Valves:")
            for valve in llm_valves:
                if valve in valves:
                    print(f"   ✅ {valve}: {valves[valve]}")
                else:
                    print(f"   ❌ {valve}: MISSING")
            
            # Check for empty valves
            empty_valves = []
            for key, value in valves.items():
                if value == "" or value is None:
                    empty_valves.append(key)
            
            if empty_valves:
                print(f"\n⚠️ Empty valves found: {empty_valves}")
            else:
                print("\n✅ No empty valves found!")
                
        else:
            print("❌ Failed to read valves from container")
            
    except Exception as e:
        print(f"❌ Error checking container valves: {e}")

if __name__ == "__main__":
    force_ui_refresh()
    check_container_valves()
    
    print(f"\n🎉 UI refresh steps completed!")
    print("💡 If valves still appear old in UI, try manual steps above")
