# AccA Mem0 Pipeline Deployment Guide

## 🎉 Success! Pipeline Fixed and Optimized

Your RAG memory pipeline using `mem0` has been successfully fixed and optimized. The key issue was the **memory relevance threshold** being too high (0.7), which filtered out relevant memories. The optimized version uses a threshold of 0.2 with fallback search capabilities.

## 📁 Deployed Files

### Primary Pipeline (Optimized)
- **File**: `webui-data/pipelines/acca_mem0_optimized.py`
- **Title**: "AccA Mem0 Memory Pipeline (Optimized)"
- **Key Features**:
  - Lowered relevance threshold from 0.7 to 0.2
  - Fallback search with dynamic threshold adjustment
  - Enhanced error handling and retry logic
  - Better debug logging
  - Optimized memory search (searches 10 memories, filters to top 3)

### Backup Pipeline
- **File**: `webui-data/pipelines/acca_mem0_pipeline.py`
- **Title**: "AccA Mem0 Memory Pipeline"
- **Status**: Original version (threshold 0.7)

## 🚀 Deployment Steps

### 1. Restart Open WebUI
```bash
# If running with Docker Compose
docker-compose restart

# If running with Docker
docker restart <openwebui-container-name>

# If running directly
# Stop and restart your Open WebUI process
```

### 2. Enable Pipeline in Admin Panel
1. Open Open WebUI in your browser
2. Go to **Admin Panel** → **Settings** → **Admin Settings** → **Pipelines**
3. You should see: **"AccA Mem0 Memory Pipeline (Optimized)"**
4. **Enable** the pipeline by toggling it ON

### 3. Configure Pipeline Valves

#### Essential Settings:
```
llm_api_key: [Your Gemini API Key]
embedder_api_key: [Your Gemini API Key] (same as above)
qdrant_host: **********
qdrant_port: 6333
collection_name: mem0_gemini_768
```

#### Optimized Memory Settings:
```
memory_relevance_threshold: 0.2 (OPTIMIZED - was 0.7)
max_memories_to_inject: 3
memory_search_limit: 10
enable_fallback_search: true
enable_debug_logging: true
auto_store_messages: true
```

#### Model Settings:
```
llm_model: gemini-2.5-flash
embedder_model: text-embedding-004
embedder_dims: 768
```

## 🧪 Test Results

The optimized pipeline has been tested and shows:

✅ **Memory Client Initialization**: Success  
✅ **Memory Search**: Found and filtered memories correctly  
✅ **Relevance Filtering**: 1 memory found, 1 relevant (threshold: 0.2)  
✅ **Memory Injection**: Successfully injected into system context  
✅ **Follow-up Processing**: Memory context maintained across messages  
✅ **Debug Logging**: Comprehensive logging for troubleshooting  

## 🔧 Key Optimizations Made

### 1. Lowered Relevance Threshold
- **Before**: 0.7 (too restrictive)
- **After**: 0.2 (better recall)
- **Impact**: More relevant memories are now included

### 2. Fallback Search Logic
- If no memories meet the threshold, automatically try with a lower threshold
- Prevents complete memory loss in edge cases
- Configurable via `enable_fallback_search` valve

### 3. Enhanced Search Parameters
- Increased search limit from default to 10 memories
- Better filtering and ranking of results
- Maintains top 3 most relevant for injection

### 4. Improved Error Handling
- Retry logic for memory client initialization
- Graceful degradation if memory system fails
- Better error messages and logging

### 5. Debug Capabilities
- Comprehensive logging throughout the pipeline
- Memory search and filtering details
- User and message tracking information

## 🐛 Troubleshooting

### Pipeline Not Detected
- Ensure files are in `webui-data/pipelines/` directory
- Restart Open WebUI completely
- Check file permissions (should be readable)

### Memory Not Working
1. **Check Debug Logs**: Enable `enable_debug_logging: true`
2. **Verify Qdrant Connection**: Ensure Qdrant is running on `**********:6333`
3. **Check API Keys**: Verify Gemini API key is valid
4. **Test Threshold**: Try lowering `memory_relevance_threshold` to 0.1

### No Memories Found
- Check if Qdrant collection `mem0_gemini_768` exists
- Verify embedding model `text-embedding-004` is working
- Enable fallback search if not already enabled

## 📊 Performance Monitoring

### Debug Log Indicators
Look for these log messages:
```
[AccA-Mem0-OPTIMIZED] Memory client initialized successfully
[AccA-Mem0-OPTIMIZED] Primary search found X memories
[AccA-Mem0-OPTIMIZED] Filtered to X relevant memories
[AccA-Mem0-OPTIMIZED] Successfully injected X memories into context
```

### Memory Injection Success
- System messages should contain: `🧠 Relevant memories from previous conversations:`
- Each memory shows relevance score: `(relevance: 0.XX)`
- Debug logs confirm injection: `Successfully injected X memories into context`

## 🎯 Next Steps

1. **Test Chat Functionality**: Start a conversation and verify memory persistence
2. **Monitor Performance**: Watch debug logs for any issues
3. **Adjust Threshold**: Fine-tune `memory_relevance_threshold` based on results
4. **Scale Testing**: Test with multiple users and longer conversations

## 📝 Configuration Backup

Save your working configuration:
```json
{
  "pipeline": "AccA Mem0 Memory Pipeline (Optimized)",
  "memory_relevance_threshold": 0.2,
  "max_memories_to_inject": 3,
  "enable_fallback_search": true,
  "enable_debug_logging": true,
  "qdrant_host": "**********",
  "qdrant_port": "6333",
  "collection_name": "mem0_gemini_768"
}
```

## 🎉 Success Indicators

Your pipeline is working correctly when you see:
- ✅ Pipeline appears in Admin Panel
- ✅ Debug logs show memory search and injection
- ✅ Chat responses reference previous conversation context
- ✅ System messages contain memory context
- ✅ No error messages in logs

---

**Status**: ✅ **FIXED AND OPTIMIZED**  
**Issue**: Memory relevance threshold too high (0.7 → 0.2)  
**Solution**: Deployed optimized pipeline with fallback search  
**Result**: Memory system now working with improved recall