#!/bin/bash

# REMOVE VENV FROM GIT: Remove virtual environments from git tracking while keeping local files
set -e

echo "🧹 Removing virtual environments from Git tracking..."

# Check if .git_disabled exists
if [ ! -d ".git_disabled" ]; then
    echo "❌ Error: .git_disabled directory not found"
    exit 1
fi

# Temporarily enable git repository
echo "🔌 Enabling git repository..."
mv .git_disabled .git

# Function to safely remove directory from git tracking
remove_from_git_tracking() {
    local dir=$1
    if [ -d "$dir" ]; then
        echo "🗑️  Removing $dir from git tracking..."
        git rm --cached "$dir" 2>/dev/null || true
        git reset HEAD "$dir" 2>/dev/null || true
    else
        echo "⚠️  Directory $dir not found, skipping..."
    fi
}

# Remove each virtual environment directory from git tracking
remove_from_git_tracking "docling_env"
remove_from_git_tracking "enhanced_table_env"
remove_from_git_tracking "venv_mem0"
remove_from_git_tracking "venv_oracle"
remove_from_git_tracking "venv_torch"
remove_from_git_tracking "qdrant_indexer_env"

# Add updated .gitignore to git
echo "📝 Adding updated .gitignore to git..."
git add .gitignore

# Commit the changes
echo "💾 Committing changes..."
git commit -m "🧹 Remove virtual environments from git tracking

- Remove docling_env/ from git tracking
- Remove enhanced_table_env/ from git tracking
- Remove venv_mem0/ from git tracking
- Remove venv_oracle/ from git tracking
- Remove venv_torch/ from git tracking
- Remove qdrant_indexer_env/ from git tracking
- Keep local files intact
- Update .gitignore to prevent future tracking"

# Disable git repository again
echo "🔌 Disabling git repository..."
mv .git .git_disabled

echo ""
echo "✅ Virtual environments removed from git tracking!"
echo ""
echo "📊 Current status:"
echo "• ✅ Virtual environments are no longer tracked by git"
echo "• ✅ Local files are preserved"
echo "• ✅ .gitignore is updated to prevent future tracking"
echo ""
echo "🔧 To verify the changes, you can:"
echo "   1. Rename .git_disabled back to .git temporarily"
echo "   2. Run 'git status' to see that virtual environments are no longer listed"
echo "   3. Rename .git back to .git_disabled"