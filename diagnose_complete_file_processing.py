#!/usr/bin/env python3
"""
Chẩn đoán toàn diện vấn đề xử lý file
Kiểm tra tại sao ngay cả PDF cũng không xử lý được
"""

import os
import sys
import requests
import json
import tempfile
import subprocess
from pathlib import Path
import time

def check_docling_server_detailed():
    """Kiểm tra chi tiết Docling server"""
    print("🔍 KIỂM TRA CHI TIẾT DOCLING SERVER")
    print("=" * 50)
    
    # 1. Kiểm tra process
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        docling_processes = [line for line in result.stdout.split('\n') if 'docling' in line.lower()]
        
        if docling_processes:
            print("✅ Docling processes đang chạy:")
            for proc in docling_processes:
                print(f"   {proc}")
        else:
            print("❌ Không tìm thấy Docling process")
            return False
    except Exception as e:
        print(f"❌ Lỗi kiểm tra process: {e}")
    
    # 2. Ki<PERSON>m tra port
    try:
        result = subprocess.run(['netstat', '-tlnp'], capture_output=True, text=True)
        port_5001 = [line for line in result.stdout.split('\n') if ':5001' in line]
        
        if port_5001:
            print("✅ Port 5001 đang listening:")
            for port in port_5001:
                print(f"   {port}")
        else:
            print("❌ Port 5001 không listening")
            return False
    except Exception as e:
        print(f"❌ Lỗi kiểm tra port: {e}")
    
    # 3. Test health endpoint
    try:
        response = requests.get("http://localhost:5001/health", timeout=5)
        print(f"✅ Health endpoint: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Service: {data.get('service')}")
            print(f"   Version: {data.get('version')}")
            print(f"   Endpoints: {data.get('endpoints')}")
        else:
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Health endpoint failed: {e}")
        return False
    
    return True

def check_openwebui_integration():
    """Kiểm tra tích hợp Open WebUI"""
    print("\n🌐 KIỂM TRA OPEN WEBUI INTEGRATION")
    print("=" * 50)
    
    # 1. Kiểm tra Open WebUI process
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        webui_processes = [line for line in result.stdout.split('\n') 
                          if any(term in line.lower() for term in ['open-webui', 'webui', 'uvicorn'])]
        
        if webui_processes:
            print("✅ Open WebUI processes:")
            for proc in webui_processes:
                print(f"   {proc}")
        else:
            print("❌ Không tìm thấy Open WebUI process")
    except Exception as e:
        print(f"❌ Lỗi kiểm tra Open WebUI process: {e}")
    
    # 2. Kiểm tra port 3000
    try:
        result = subprocess.run(['netstat', '-tlnp'], capture_output=True, text=True)
        port_3000 = [line for line in result.stdout.split('\n') if ':3000' in line]
        
        if port_3000:
            print("✅ Port 3000 đang listening:")
            for port in port_3000:
                print(f"   {port}")
        else:
            print("❌ Port 3000 không listening")
    except Exception as e:
        print(f"❌ Lỗi kiểm tra port 3000: {e}")
    
    # 3. Kiểm tra environment variables
    print("\n📋 Environment Variables:")
    important_vars = [
        'RAG_CONTENT_EXTRACTION_ENGINE',
        'DOCLING_SERVER_URL',
        'RAG_CONTENT_EXTRACTION_TIMEOUT'
    ]
    
    for var in important_vars:
        value = os.environ.get(var, 'NOT SET')
        status = "✅" if value != 'NOT SET' else "❌"
        print(f"   {status} {var}: {value}")

def test_direct_docling_upload():
    """Test upload trực tiếp lên Docling server"""
    print("\n🧪 TEST DIRECT DOCLING UPLOAD")
    print("=" * 50)
    
    # Tạo test PDF nhỏ
    test_content = """Test PDF Content
    
Đây là nội dung test để kiểm tra Docling server.

Bảng test:
| Cột 1 | Cột 2 |
|-------|-------|
| A     | B     |
| 1     | 2     |

Kết thúc test content.
"""
    
    # Tạo file text trước (vì tạo PDF phức tạp)
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        test_file_path = f.name
    
    try:
        print(f"📄 Test file: {test_file_path}")
        print(f"📏 File size: {os.path.getsize(test_file_path)} bytes")
        
        # Test với các endpoints
        endpoints = ['/extract_content', '/v1alpha/convert/file']
        
        for endpoint in endpoints:
            print(f"\n🔄 Testing {endpoint}...")
            
            try:
                with open(test_file_path, 'rb') as f:
                    files = {'files': ('test.txt', f, 'text/plain')}
                    
                    response = requests.post(
                        f"http://localhost:5001{endpoint}",
                        files=files,
                        timeout=30
                    )
                    
                    print(f"   Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            print(f"   ✅ Success: {data.get('success')}")
                            print(f"   📝 Text length: {len(data.get('text', ''))}")
                            print(f"   📊 Tables: {len(data.get('tables', []))}")
                            
                            if data.get('error'):
                                print(f"   ⚠️  Error in response: {data['error']}")
                            
                            # Hiển thị một phần text
                            text = data.get('text', '')
                            if text:
                                preview = text[:200] + "..." if len(text) > 200 else text
                                print(f"   📄 Text preview: {repr(preview)}")
                            else:
                                print(f"   ❌ No text extracted!")
                                
                        except json.JSONDecodeError:
                            print(f"   ❌ Invalid JSON response")
                            print(f"   📄 Raw response: {response.text[:500]}")
                    else:
                        print(f"   ❌ HTTP Error: {response.status_code}")
                        print(f"   📄 Response: {response.text}")
                        
            except Exception as e:
                print(f"   ❌ Request failed: {e}")
    
    finally:
        # Cleanup
        try:
            os.unlink(test_file_path)
        except:
            pass

def check_docling_logs():
    """Kiểm tra logs của Docling"""
    print("\n📋 KIỂM TRA DOCLING LOGS")
    print("=" * 50)
    
    log_files = [
        './docling.log',
        '/tmp/docling.log',
        './backend/app/rag/docling.log',
        'nohup.out'
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"\n📄 Log file: {log_file}")
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # Lấy 20 dòng cuối
                recent_lines = lines[-20:] if len(lines) > 20 else lines
                
                print("   Recent entries:")
                for line in recent_lines:
                    if line.strip():
                        print(f"   {line.strip()}")
                        
            except Exception as e:
                print(f"   ❌ Error reading log: {e}")
        else:
            print(f"❌ Log file not found: {log_file}")

def check_system_resources():
    """Kiểm tra tài nguyên hệ thống"""
    print("\n💻 KIỂM TRA TÀI NGUYÊN HỆ THỐNG")
    print("=" * 50)
    
    # Memory
    try:
        with open('/proc/meminfo', 'r') as f:
            meminfo = f.read()
        
        for line in meminfo.split('\n'):
            if 'MemTotal:' in line:
                total_kb = int(line.split()[1])
                print(f"📊 Total Memory: {total_kb // 1024} MB")
            elif 'MemAvailable:' in line:
                available_kb = int(line.split()[1])
                print(f"📊 Available Memory: {available_kb // 1024} MB")
    except:
        pass
    
    # Disk space
    try:
        statvfs = os.statvfs('.')
        free_space = statvfs.f_frsize * statvfs.f_bavail
        total_space = statvfs.f_frsize * statvfs.f_blocks
        print(f"💾 Free Disk Space: {free_space // (1024**3)} GB")
        print(f"💾 Total Disk Space: {total_space // (1024**3)} GB")
    except:
        pass
    
    # Load average
    try:
        with open('/proc/loadavg', 'r') as f:
            load = f.read().strip()
        print(f"⚡ Load Average: {load}")
    except:
        pass

def test_large_file_handling():
    """Test xử lý file lớn"""
    print("\n📦 TEST XỬ LÝ FILE LỚN")
    print("=" * 50)
    
    # Tạo file test lớn (1MB)
    large_content = "Test content for large file handling.\n" * 10000
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(large_content)
        large_file_path = f.name
    
    try:
        file_size = os.path.getsize(large_file_path)
        print(f"📄 Large test file: {file_size} bytes ({file_size // 1024} KB)")
        
        # Test với timeout dài hơn
        with open(large_file_path, 'rb') as f:
            files = {'files': ('large_test.txt', f, 'text/plain')}
            
            print("🔄 Testing large file processing...")
            start_time = time.time()
            
            try:
                response = requests.post(
                    "http://localhost:5001/extract_content",
                    files=files,
                    timeout=60  # 60 seconds timeout
                )
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                print(f"   ⏱️  Processing time: {processing_time:.2f} seconds")
                print(f"   📊 Status: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✅ Success: {data.get('success')}")
                    print(f"   📝 Text length: {len(data.get('text', ''))}")
                else:
                    print(f"   ❌ Failed: {response.text}")
                    
            except requests.exceptions.Timeout:
                print(f"   ⏰ Request timeout (>60s)")
            except Exception as e:
                print(f"   ❌ Request failed: {e}")
    
    finally:
        try:
            os.unlink(large_file_path)
        except:
            pass

def main():
    """Main diagnostic function"""
    print("🔍 CHẨN ĐOÁN TOÀN DIỆN VẤN ĐỀ XỬ LÝ FILE")
    print("=" * 60)
    print("Kiểm tra tại sao ngay cả PDF cũng không xử lý được")
    print("=" * 60)
    
    # 1. Kiểm tra Docling server
    docling_ok = check_docling_server_detailed()
    
    # 2. Kiểm tra Open WebUI integration
    check_openwebui_integration()
    
    # 3. Test direct upload
    if docling_ok:
        test_direct_docling_upload()
    
    # 4. Kiểm tra logs
    check_docling_logs()
    
    # 5. Kiểm tra tài nguyên hệ thống
    check_system_resources()
    
    # 6. Test file lớn
    if docling_ok:
        test_large_file_handling()
    
    print("\n" + "=" * 60)
    print("📋 TÓM TẮT VÀ KHUYẾN NGHỊ")
    print("=" * 60)
    
    if not docling_ok:
        print("🚨 CRITICAL: Docling server không hoạt động đúng!")
        print("   Khuyến nghị:")
        print("   1. Restart Docling server: python3 docling_server_updated.py")
        print("   2. Kiểm tra logs để tìm lỗi cụ thể")
        print("   3. Kiểm tra dependencies: pip install docling")
    else:
        print("✅ Docling server hoạt động")
        print("   Nếu vẫn không xử lý được file:")
        print("   1. Kiểm tra Open WebUI có kết nối đúng Docling không")
        print("   2. Set environment variables:")
        print("      export RAG_CONTENT_EXTRACTION_ENGINE=docling")
        print("      export DOCLING_SERVER_URL=http://localhost:5001")
        print("   3. Restart Open WebUI")

if __name__ == "__main__":
    main()