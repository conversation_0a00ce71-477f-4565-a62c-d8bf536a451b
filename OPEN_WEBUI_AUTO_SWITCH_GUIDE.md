# 🤖 Open WebUI Auto-Switch Setup Guide

## ✨ Tự động chuyển đổi model khi chat - KHÔNG cần lệnh manual!

**Date:** $(date)  
**Status:** ✅ HOẠT ĐỘNG HOÀN HẢO  
**Feature:** Tự động phát hiện và chuyển model khi bạn chọn trong Open WebUI  

---

## 🚀 Khởi động Auto-Switch Proxy

```bash
# Khởi động proxy (chỉ cần chạy 1 lần)
./start-auto-switch.sh
```

**Proxy sẽ chạy trên:** `http://localhost:8090`

---

## 🌐 Cấu hình Open WebUI

### Bước 1: Mở Open WebUI
- Truy cập: **http://localhost:3000**
- Đăng nhập vào tài khoản của bạn

### Bước 2: Thêm API Connection
1. Click **Settings** (⚙️) ở góc trên
2. Chọn **Connections** 
3. Trong phần **OpenAI API**, click **+** để thêm mới
4. Điền thông tin:

```
📋 Thông tin cấu hình:
┌─────────────────────────────────────────────┐
│ Name: Auto-Switch Models                    │
│ API Base URL: http://localhost:8090/v1      │
│ API Key: any-key-works                      │
│ ✅ Active: Enabled                          │
└─────────────────────────────────────────────┘
```

### Bước 3: Test Connection
- Click **Test Connection**
- Phải hiển thị: ✅ **Connected**

### Bước 4: Save & Refresh
- Click **Save**
- Refresh trang (F5)

---

## 🎯 Các Models có sẵn

Sau khi cấu hình, bạn sẽ thấy 5 models trong dropdown:

| Model trong WebUI | Thực tế chạy | RAM | Đặc điểm |
|-------------------|--------------|-----|----------|
| **GPT-3.5 Turbo** | Gemma 4B | 2.4GB | ⚡ Nhanh, cân bằng |
| **GPT-4** | Qwen 7B Coder | 4.4GB | 💻 Lập trình tốt |
| **GPT-4 Turbo** | Qwen 14B Coder | 8.4GB | 🏆 Chất lượng cao |
| **Claude 3 Sonnet** | Gemma 12B | 6.8GB | 🧠 Suy luận tốt |
| **GPT-3.5 Turbo 16K** | Jan Nano | 4.0GB | 🚀 Siêu nhanh |

---

## 💡 Cách sử dụng

### Tự động chuyển đổi:
1. **Chọn model** từ dropdown trong Open WebUI
2. **Gửi tin nhắn** bình thường
3. **Proxy tự động** phát hiện và chuyển model
4. **Đợi 5-10 giây** cho model khởi động (chỉ lần đầu)
5. **Chat bình thường** - các tin nhắn tiếp theo sẽ nhanh!

### Ví dụ workflow:
```
👤 Bạn: Chọn "GPT-4" và gửi "Write Python code"
🤖 Hệ thống: Tự động chuyển sang Qwen 7B Coder
⚡ Phản hồi: Code Python chất lượng cao

👤 Bạn: Chọn "GPT-3.5 Turbo" và gửi "Quick question"  
🤖 Hệ thống: Tự động chuyển sang Gemma 4B
⚡ Phản hồi: Trả lời nhanh và chính xác
```

---

## 📊 Monitoring & Status

### Kiểm tra status:
```bash
curl http://localhost:8090/status
```

### Xem logs realtime:
```bash
# Trong terminal khác
tail -f /tmp/auto-switch-proxy.log
```

### Xem models đang chạy:
```bash
ps aux | grep llama-server
```

---

## 🔧 Troubleshooting

### Model không chuyển được:
```bash
# Restart proxy
pkill -f auto-switch-proxy.py
./start-auto-switch.sh
```

### Open WebUI không thấy models:
1. Kiểm tra API Base URL: `http://localhost:8090/v1`
2. Test connection trong Settings
3. Refresh trang (F5)
4. Clear browser cache

### Model chậm lần đầu:
- **Bình thường!** Model cần 5-10 giây để khởi động
- Các request tiếp theo sẽ nhanh
- Chỉ 1 model chạy tại 1 thời điểm (tiết kiệm RAM)

### Port conflict:
```bash
# Kiểm tra port đang sử dụng
netstat -tulpn | grep 8090

# Nếu bị conflict, đổi port trong auto-switch-proxy.py
```

---

## 🎉 Tính năng đặc biệt

### ✅ Ưu điểm:
- **Tự động 100%** - không cần lệnh manual
- **Tiết kiệm RAM** - chỉ 1 model chạy
- **Dễ sử dụng** - như OpenAI API
- **5 models mạnh** - đáp ứng mọi nhu cầu
- **Switching nhanh** - 5-10 giây

### 🔄 Auto-switching logic:
```
GPT-3.5 Turbo → Gemma 4B (fast, balanced)
GPT-4 → Qwen 7B (programming)  
GPT-4 Turbo → Qwen 14B (best quality)
Claude 3 Sonnet → Gemma 12B (reasoning)
GPT-3.5 Turbo 16K → Jan Nano (ultra fast)
```

### 💾 RAM Usage:
- **Trước:** 5 models = ~36GB RAM
- **Sau:** 1 model = 2.4-8.4GB RAM  
- **Tiết kiệm:** 80-90% RAM!

---

## 🚦 Commands hữu ích

```bash
# Khởi động proxy
./start-auto-switch.sh

# Dừng proxy  
pkill -f auto-switch-proxy.py

# Kiểm tra status
curl http://localhost:8090/status

# Test API
curl -X POST http://localhost:8090/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"model": "gpt-4", "messages": [{"role": "user", "content": "Hello"}]}'

# Xem models available
curl http://localhost:8090/v1/models
```

---

## 🎯 Kết luận

**Giờ đây bạn có thể:**
- ✅ Chat với 5 models mạnh nhất
- ✅ Tự động chuyển đổi không cần lệnh
- ✅ Tiết kiệm 80-90% RAM
- ✅ Sử dụng như OpenAI API
- ✅ Workflow mượt mà trong Open WebUI

**Không cần nhớ lệnh nào nữa! Chỉ cần chọn model và chat! 🎉** 