#!/usr/bin/env python3
"""
Add reader mode fallback for blocked websites like zhihu.com
"""

import subprocess
import re

def add_reader_fallback():
    """Add reader mode fallback to jini_crawler.py"""
    
    print("🔧 Adding reader mode fallback for blocked websites...")
    
    # Read current jini_crawler.py
    result = subprocess.run([
        'docker', 'exec', 'jina-crawler-mcp', 'cat', '/app/jini_crawler.py'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ Error reading jini_crawler.py: {result.stderr}")
        return False
    
    content = result.stdout
    
    # Find the TLS bypass section and add reader mode fallback after it
    old_tls_section = '''            # Try TLS bypass if regular request failed or was blocked
            if not html_content and self.use_tls_client and self.tls_session:
                logger.info(f"🔒 Using TLS bypass for Cloudflare resistance: {url}")
                try:
                    # Run TLS bypass in executor to avoid blocking
                    loop = asyncio.get_event_loop()
                    tls_result = await loop.run_in_executor(
                        None, self._fetch_with_tls_bypass, url
                    )

                    if tls_result:
                        html_content = tls_result
                        status = 200
                        logger.info(f"✅ TLS bypass successful for {url}")
                    else:
                        logger.warning(f"⚠️ TLS bypass failed for {url}")

                except Exception as e:
                    logger.error(f"❌ TLS bypass error: {e}")'''
    
    new_tls_section = '''            # Try TLS bypass if regular request failed or was blocked
            if not html_content and self.use_tls_client and self.tls_session:
                logger.info(f"🔒 Using TLS bypass for Cloudflare resistance: {url}")
                try:
                    # Run TLS bypass in executor to avoid blocking
                    loop = asyncio.get_event_loop()
                    tls_result = await loop.run_in_executor(
                        None, self._fetch_with_tls_bypass, url
                    )

                    if tls_result:
                        html_content = tls_result
                        status = 200
                        logger.info(f"✅ TLS bypass successful for {url}")
                    else:
                        logger.warning(f"⚠️ TLS bypass failed for {url}")

                except Exception as e:
                    logger.error(f"❌ TLS bypass error: {e}")
            
            # 🚀 NEW: Reader Mode Fallback for blocked websites
            if not html_content or status in [403, 429, 503]:
                logger.info(f"📖 Trying Reader Mode fallback for blocked URL: {url}")
                try:
                    reader_content = await self._fetch_with_reader_mode(url)
                    if reader_content:
                        html_content = reader_content
                        status = 200
                        logger.info(f"✅ Reader Mode successful for {url}")
                    else:
                        logger.warning(f"⚠️ Reader Mode failed for {url}")
                except Exception as e:
                    logger.error(f"❌ Reader Mode error: {e}")'''
    
    # Replace the TLS section
    content = content.replace(old_tls_section, new_tls_section)
    
    # Add the reader mode method before the class ends
    reader_method = '''
    async def _fetch_with_reader_mode(self, url: str) -> Optional[str]:
        """
        🚀 READER MODE FALLBACK - Use Jina Reader API for blocked websites
        
        Args:
            url: URL to fetch with reader mode
            
        Returns:
            Cleaned HTML content or None if failed
        """
        try:
            # Use Jina Reader API (free service)
            reader_url = f"https://r.jina.ai/{url}"
            
            headers = {
                'User-Agent': self.user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
            
            async with self.session.get(reader_url, headers=headers, timeout=30) as response:
                if response.status == 200:
                    reader_content = await response.text()
                    
                    # Jina Reader returns clean markdown, convert to HTML for consistency
                    html_content = f"""
                    <html>
                    <head><title>Reader Mode Content</title></head>
                    <body>
                    <div class="reader-content">
                    {reader_content.replace(chr(10), '<br>')}
                    </div>
                    </body>
                    </html>
                    """
                    
                    logger.info(f"📖 Reader Mode extracted {len(reader_content)} chars from {url}")
                    return html_content
                else:
                    logger.warning(f"⚠️ Reader Mode API returned status {response.status} for {url}")
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Reader Mode API error for {url}: {e}")
            return None
'''
    
    # Find a good place to insert the method (before the last method)
    # Look for the last method definition
    last_method_pattern = r'(\n    async def cleanup\(self\):.*?)(\n\n# .*?$|\n$)'
    
    if re.search(last_method_pattern, content, re.DOTALL):
        content = re.sub(
            last_method_pattern, 
            reader_method + r'\1\2', 
            content, 
            flags=re.DOTALL
        )
    else:
        # Fallback: add before the end of the class
        content = content.replace(
            '\n# Setup logger first',
            reader_method + '\n\n# Setup logger first'
        )
    
    # Write fixed content
    with open('/tmp/jini_crawler_reader_fallback.py', 'w') as f:
        f.write(content)
    
    # Copy back to container
    copy_result = subprocess.run([
        'docker', 'cp', '/tmp/jini_crawler_reader_fallback.py', 'jina-crawler-mcp:/app/jini_crawler.py'
    ], capture_output=True, text=True)
    
    if copy_result.returncode != 0:
        print(f"❌ Error copying fixed file: {copy_result.stderr}")
        return False
    
    print("✅ Reader mode fallback added to jini_crawler.py")
    return True

def main():
    """Main function"""
    print("🚀 Adding Reader Mode fallback for blocked websites...")
    print("🎯 Target: Sites like zhihu.com, blocked by 403/429/503")
    print("📖 Solution: Jina Reader API fallback")
    print()
    
    if add_reader_fallback():
        print("\n🎉 Reader Mode fallback added successfully!")
        print("🔄 Restarting container...")
        
        # Restart container
        subprocess.run(['docker', 'restart', 'jina-crawler-mcp'])
        print("✅ Container restarted")
        
        print("\n📊 New fallback chain:")
        print("1. Regular request")
        print("2. TLS bypass (for Cloudflare)")
        print("3. 📖 Reader Mode (for blocked sites)")
        print("🎯 Should handle zhihu.com and similar blocked sites")
    else:
        print("\n❌ Failed to add reader mode fallback")

if __name__ == "__main__":
    main()
