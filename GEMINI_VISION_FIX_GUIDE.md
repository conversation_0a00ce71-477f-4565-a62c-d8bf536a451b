# Fix Gemini Vision - Hướng Dẫn Khắc Phục

## 🚨 Vấn Đề Hiện Tại
**Gemini models hỗ trợ vision nhưng khi upload ảnh thì báo "không thấy hình"**

## 🔍 Nguyên Nhân
1. **Pipeline chung không tối ưu cho Gemini**
2. **Gemini cần format ảnh đặc biệt** (inline_data format)
3. **Gemini prefer JPEG over PNG** cho hiệu suất
4. **Priority pipeline chưa đúng**

## ✅ Giải Pháp - Pipeline Riêng Cho Gemini

### 1. Đã Tạo Pipeline Chuyên Biệt
**File:** [`webui-data/pipelines/gemini_vision_pipeline.py`](webui-data/pipelines/gemini_vision_pipeline.py)

**Tính năng đặc biệt:**
- ✅ **Detect Gemini models** tự động
- ✅ **Format ảnh theo chuẩn Gemini** (inline_data)
- ✅ **Convert sang JPEG** (<PERSON> prefer)
- ✅ **Priority cao hơn** pipeline chung
- ✅ **Dual format support** (image_url + inline_data)

### 2. Models Được Hỗ Trợ
```python
gemini_models = [
    "gemini-2.5-flash",
    "gemini-1.5-pro", 
    "gemini-1.5-flash"
]
```

### 3. Khác Biệt Với Pipeline Chung

| Tính Năng | Pipeline Chung | Gemini Pipeline |
|-----------|----------------|-----------------|
| **Format** | PNG base64 | JPEG + inline_data |
| **Priority** | 0 | 1 (cao hơn) |
| **Target** | Tất cả models | Chỉ Gemini |
| **Optimization** | Generic | Gemini-specific |

## 🚀 Cách Deploy Pipeline Mới

### Bước 1: Restart WebUI
```bash
./restart_webui_with_gemini_pipeline.sh
```

### Bước 2: Kiểm Tra Pipeline
1. Vào **Settings** ⚙️
2. **Admin Panel** → **Pipelines**
3. Tìm **"Gemini Vision Pipeline"**
4. Đảm bảo **Enabled** (toggle xanh)
5. **Priority = 1** (cao hơn pipeline chung)

### Bước 3: Test Gemini Vision
1. **Chọn model:** `gemini-2.5-flash`
2. **Upload ảnh:** Click 📎
3. **Hỏi:** "Describe this image"
4. **Kết quả:** Gemini sẽ thấy và mô tả ảnh!

## 🔧 Debug Nếu Vẫn Lỗi

### Check Pipeline Logs:
```bash
docker logs catomanton-webui | grep -i "gemini.*pipeline" | tail -10
```

### Expected Logs:
```
Starting Gemini Vision Pipeline
Detected Gemini model: gemini-2.5-flash, applying Gemini-specific processing
Images detected, processing for Gemini
Successfully processed image for Gemini: {'width': 800, 'height': 600, ...}
```

### Nếu Không Thấy Logs:
```bash
# Restart lại
docker restart catomanton-webui
sleep 15

# Check pipeline files
ls -la webui-data/pipelines/
```

## 🎯 Test Case Cụ Thể

### 1. Chuẩn Bị:
- Model: **gemini-2.5-flash**
- Ảnh: JPEG/PNG < 10MB
- Google API key đã setup

### 2. Test Steps:
```
1. New chat
2. Select "gemini-2.5-flash"
3. Upload image (📎)
4. Type: "What do you see in this image?"
5. Send
```

### 3. Expected Result:
```
✅ Gemini: "I can see [detailed description of the image]..."
```

### 4. If Still Fails:
```
❌ "I cannot see the image"
→ Check pipeline enabled
→ Check API key valid
→ Check logs for errors
```

## 🔄 Rollback Plan

### Nếu Pipeline Mới Gây Lỗi:
```bash
# Disable Gemini pipeline
# Keep only generic pipeline
# Or rename gemini_vision_pipeline.py to .bak
```

## 📊 So Sánh Trước/Sau

### Trước (Generic Pipeline):
```
❌ Gemini: "I cannot see the image"
❌ Format: PNG base64 (không tối ưu cho Gemini)
❌ Priority: 0 (thấp)
```

### Sau (Gemini Pipeline):
```
✅ Gemini: "I can see a cat sitting on..."
✅ Format: JPEG + inline_data (tối ưu cho Gemini)
✅ Priority: 1 (cao)
```

## 🎉 Kết Luận

**Pipeline chuyên biệt cho Gemini đã được tạo!**

**Lý do tại sao sẽ work:**
1. ✅ **Gemini-specific format** (inline_data)
2. ✅ **JPEG optimization** (Gemini prefer)
3. ✅ **Higher priority** (chạy trước pipeline chung)
4. ✅ **Auto-detect Gemini models**
5. ✅ **Dual format support** (backup compatibility)

**Chỉ cần restart WebUI và test ngay!**