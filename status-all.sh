#!/bin/bash

echo "📊 Local AI Stack Status Report"
echo "==============================="
echo "Generated: $(date '+%Y-%m-%d %H:%M:%S')"

echo ""
echo "🔍 Service Status Check:"
echo "========================"

# Check Tika Engine
echo "1. Apache Tika Engine (Document Processing)"
if curl -s http://localhost:9998 >/dev/null 2>&1; then
    echo "   ✅ STATUS: Running on port 9998"
    echo "   📋 Container: $(docker ps --filter name=apache-tika --format 'table {{.Names}}\t{{.Status}}' | tail -1)"
    # Try to get version but don't fail if not available
    tika_test=$(echo "test" | curl -s -X PUT -H "Content-Type: text/plain" --data-binary @- "http://localhost:9998/tika/text" 2>/dev/null)
    if [ $? -eq 0 ]; then
        echo "   ⚡ PERFORMANCE: Responsive"
    else
        echo "   ⚠️  PERFORMANCE: May be slow or starting up"
    fi
else
    echo "   ❌ STATUS: Not running"
    echo "   💡 FIX: Run ./setup-tika-engine.sh"
fi

echo ""

# Check Qwen Model
echo "2. Qwen2.5-Coder-7B Model"
if curl -s -f http://localhost:11435/v1/models >/dev/null 2>&1; then
    echo "   ✅ STATUS: Running on port 11435"
    model_info=$(curl -s http://localhost:11435/v1/models | jq -r '.data[0].id' 2>/dev/null)
    echo "   🤖 MODEL: $model_info"
    echo "   🔗 API: OpenAI-compatible at http://localhost:11435/v1"
else
    echo "   ❌ STATUS: Not running"
    echo "   💡 FIX: Run ./restart-all-with-tika.sh"
fi

echo ""

# Check Gemma Model
echo "3. Gemma 3 4B Model"
if curl -s -f http://localhost:11434/v1/models >/dev/null 2>&1; then
    echo "   ✅ STATUS: Running on port 11434"
    model_info=$(curl -s http://localhost:11434/v1/models | jq -r '.data[0].id' 2>/dev/null)
    echo "   🤖 MODEL: $model_info"
    echo "   🔗 API: OpenAI-compatible at http://localhost:11434/v1"
else
    echo "   ❌ STATUS: Not running"
    echo "   💡 FIX: Run ./restart-all-with-tika.sh"
fi

echo ""

# Check Open WebUI
echo "4. Open WebUI (Frontend)"
if curl -s -f http://localhost:3000 >/dev/null 2>&1; then
    echo "   ✅ STATUS: Running on port 3000"
    echo "   🌐 ACCESS: http://localhost:3000"
    
    # Check if Tika integration is working by looking at logs
    if grep -q "TIKA_SERVER_URL" /tmp/open-webui.log 2>/dev/null; then
        echo "   📄 TIKA: Integrated (check logs for details)"
    else
        echo "   📄 TIKA: Integration unknown (check manually)"
    fi
    
    # Check for recent errors
    recent_errors=$(tail -20 /tmp/open-webui.log 2>/dev/null | grep -i error | wc -l)
    if [ "$recent_errors" -gt 0 ]; then
        echo "   ⚠️  ISSUES: $recent_errors recent errors in log"
    else
        echo "   ✅ HEALTH: No recent errors"
    fi
else
    echo "   ❌ STATUS: Not running"
    echo "   💡 FIX: Run ./restart-all-with-tika.sh"
fi

echo ""
echo "🚀 Performance Summary:"
echo "======================"

# Memory usage
echo "💾 Memory Usage:"
ps aux | grep -E "(llama-server|open-webui|python)" | grep -v grep | while read line; do
    mem=$(echo $line | awk '{print $6}')
    process=$(echo $line | awk '{print $11}')
    if [ ! -z "$mem" ] && [ "$mem" -gt 100000 ]; then
        mem_mb=$((mem / 1024))
        echo "   • $process: ${mem_mb}MB"
    fi
done

echo ""

# CPU usage snapshot
echo "🔧 CPU Usage (current snapshot):"
ps aux | grep -E "(llama-server|open-webui|python)" | grep -v grep | while read line; do
    cpu=$(echo $line | awk '{print $3}')
    process=$(echo $line | awk '{print $11}')
    if [ ! -z "$cpu" ]; then
        echo "   • $process: ${cpu}%"
    fi
done

echo ""

# Disk space for logs
echo "💽 Log Files:"
for log in /tmp/qwen-7b.log /tmp/gemma-4b.log /tmp/open-webui.log; do
    if [ -f "$log" ]; then
        size=$(du -h "$log" 2>/dev/null | cut -f1)
        echo "   • $(basename $log): $size"
    fi
done

echo ""
echo "🔗 Quick Access:"
echo "================"
echo "• Open WebUI:    http://localhost:3000"
echo "• Qwen API:      http://localhost:11435/v1"  
echo "• Gemma API:     http://localhost:11434/v1"
echo "• Tika Engine:   http://localhost:9998"

echo ""
echo "📋 Management Commands:"
echo "======================"
echo "• Start all:     ./restart-all-with-tika.sh"
echo "• Setup Tika:    ./setup-tika-engine.sh"
echo "• Test Tika:     ./test-tika-engine.sh"
echo "• Fix WebUI:     ./fix-webui-connections.sh"
echo "• View this:     ./status-all.sh"

echo ""
echo "📝 View Logs:"
echo "============="
echo "• tail -f /tmp/qwen-7b.log      (Qwen model)"
echo "• tail -f /tmp/gemma-4b.log     (Gemma model)"
echo "• tail -f /tmp/open-webui.log   (WebUI frontend)"
echo "• docker logs apache-tika       (Tika engine)"

echo ""
echo "🧪 Quick Tests:"
echo "==============="

# Quick API tests
echo "Testing APIs..."

# Test Tika
if curl -s http://localhost:9998 >/dev/null 2>&1; then
    echo "✅ Tika API: Responsive"
else
    echo "❌ Tika API: Not responding"
fi

# Test Qwen
if curl -s -f http://localhost:11435/v1/models >/dev/null 2>&1; then
    echo "✅ Qwen API: Responsive"
else
    echo "❌ Qwen API: Not responding"
fi

# Test Gemma
if curl -s -f http://localhost:11434/v1/models >/dev/null 2>&1; then
    echo "✅ Gemma API: Responsive"
else
    echo "❌ Gemma API: Not responding"
fi

# Test WebUI
if curl -s -f http://localhost:3000 >/dev/null 2>&1; then
    echo "✅ WebUI: Accessible"
else
    echo "❌ WebUI: Not accessible"
fi

echo ""
echo "✨ Status check complete!"
echo ""
echo "🎯 Next Steps:"
echo "• If any service is down: Run ./restart-all-with-tika.sh"
echo "• If WebUI models missing: Run ./fix-webui-connections.sh"
echo "• To test document processing: Upload a PDF in WebUI Knowledge section" 