#!/bin/bash

echo "🚀 Pushing all changes to repository..."

# Push the existing commit first
echo "📤 Pushing current commit..."
git push origin main

# Check submodules status
echo "🔍 Checking submodules..."
git submodule status

# Add all changes including submodules
echo "➕ Adding all changes..."
git add .

# Check if there are changes to commit
if git diff --staged --quiet; then
    echo "✅ No new changes to commit"
else
    echo "💾 Committing new changes..."
    git commit -m "Update: Add all pending changes including submodules"
    
    echo "📤 Pushing new commit..."
    git push origin main
fi

echo "✅ All changes pushed successfully!" 