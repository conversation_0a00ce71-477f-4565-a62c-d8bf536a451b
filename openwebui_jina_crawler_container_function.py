def jina_crawler_tool(url: str, method: str = "crawl_url", max_content_length: int = 10000) -> str:
    """
    Smart web crawler with AI-powered content processing using Jin<PERSON> and Gemini.
    Uses the containerized Jina Crawler on port 8009.
    
    Args:
        url: URL to crawl
        method: Tool method (crawl_url, crawl_full_article, crawl_batch, ai_search, crawl_bypass_paywall)
        max_content_length: Maximum content length
    
    Returns:
        Processed content from the URL
    """
    import requests
    import json
    
    try:
        # Use container hostname for internal Docker network communication
        base_url = "http://jina-crawler-8009:8009"
        api_key = "jina-crawler-secret-key-2025"
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # Prepare payload based on method
        if method == "crawl_url":
            payload = {
                "url": url,
                "max_content_length": max_content_length
            }
        elif method == "crawl_full_article":
            payload = {
                "url": url,
                "max_content_length": max_content_length
            }
        elif method == "crawl_bypass_paywall":
            payload = {
                "url": url,
                "max_content_length": max_content_length
            }
        elif method == "ai_search":
            payload = {
                "query": url,  # For AI search, url parameter is treated as query
                "max_sources": 10,
                "enable_query_refinement": True
            }
        elif method == "crawl_batch":
            # For batch, url should be a list of URLs (passed as JSON string)
            try:
                urls = json.loads(url) if isinstance(url, str) and url.startswith('[') else [url]
                payload = {
                    "urls": urls,
                    "max_content_length": max_content_length
                }
            except:
                payload = {
                    "urls": [url],
                    "max_content_length": max_content_length
                }
        else:
            payload = {
                "url": url,
                "max_content_length": max_content_length
            }
        
        # Make request to container
        response = requests.post(
            f"{base_url}/tools/{method}",
            json=payload,
            headers=headers,
            timeout=120
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                # Format response based on method
                if method == "ai_search":
                    results = data.get("results", [])
                    formatted_results = []
                    for result in results[:5]:  # Limit to top 5 results
                        formatted_results.append(f"**{result.get('title', 'N/A')}**\n{result.get('content', result.get('snippet', ''))}\nSource: {result.get('url', 'N/A')}")
                    return f"AI Search Results for: {payload.get('query', url)}\n\n" + "\n\n---\n\n".join(formatted_results)
                
                elif method == "crawl_batch":
                    results = data.get("results", [])
                    formatted_results = []
                    for result in results:
                        if result.get("success"):
                            formatted_results.append(f"**{result.get('title', 'N/A')}**\n{result.get('processed_content', result.get('content', ''))}\nURL: {result.get('url', 'N/A')}")
                        else:
                            formatted_results.append(f"Error for {result.get('url', 'N/A')}: {result.get('error', 'Unknown error')}")
                    return "Batch Crawl Results:\n\n" + "\n\n---\n\n".join(formatted_results)
                
                else:
                    # Single URL crawl methods
                    title = data.get('title', 'N/A')
                    content = data.get('processed_content', data.get('content', ''))
                    processing_time = data.get('processing_time', 0)
                    
                    return f"**{title}**\n\n{content}\n\n*Processing time: {processing_time:.2f}s*"
            else:
                return f"Error: {data.get('error', 'Unknown error')}"
        else:
            return f"HTTP Error: {response.status_code} - {response.text}"
            
    except Exception as e:
        return f"Error calling Jina Crawler container: {str(e)}"


def jina_crawler_search(query: str, max_sources: int = 10) -> str:
    """
    AI-powered web search using Jina Crawler.
    
    Args:
        query: Search query
        max_sources: Maximum number of sources to return
    
    Returns:
        Search results with AI processing
    """
    return jina_crawler_tool(query, method="ai_search", max_content_length=max_sources * 1000)


def jina_crawler_bypass_paywall(url: str) -> str:
    """
    Bypass paywall and extract full content.
    
    Args:
        url: Paywall-protected URL
    
    Returns:
        Full article content
    """
    return jina_crawler_tool(url, method="crawl_bypass_paywall", max_content_length=50000)


def jina_crawler_full_article(url: str) -> str:
    """
    Extract complete article content without summarization.
    
    Args:
        url: Article URL
    
    Returns:
        Complete article content
    """
    return jina_crawler_tool(url, method="crawl_full_article", max_content_length=50000)


def jina_crawler_batch(urls_json: str) -> str:
    """
    Crawl multiple URLs in batch.
    
    Args:
        urls_json: JSON string containing list of URLs, e.g., '["https://url1.com", "https://url2.com"]'
    
    Returns:
        Batch crawl results
    """
    return jina_crawler_tool(urls_json, method="crawl_batch", max_content_length=10000)
