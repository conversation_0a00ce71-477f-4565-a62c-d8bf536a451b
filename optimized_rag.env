# Optimized RAG Settings for Gemini API
# Source this file: source optimized_rag.env

# Chunk Settings - Optimized for Gemini embedding
export RAG_CHUNK_SIZE=512
export RAG_CHUNK_OVERLAP=64
export RAG_TOP_K=3
export RAG_SIMILARITY_THRESHOLD=0.65

# OpenWebUI Optimizations
export OPENW<PERSON>BUI_RAG_CHUNK_SIZE=512
export OPENWEBUI_RAG_CHUNK_OVERLAP=64
export OPENWEBUI_RAG_TOP_K=3
export OPENWEBUI_RAG_SIMILARITY_THRESHOLD=0.65
export OPENWEBUI_RAG_USE_RERANKING=false

# Performance Settings
export RAG_REQUEST_TIMEOUT=15
export RAG_MAX_RETRIES=2

# Cache Settings (for future implementation)
export ENABLE_RAG_EMBEDDING_CACHE=true
export RAG_CACHE_DIR=".gemini_embedding_cache"
export RAG_CACHE_TTL_HOURS=24
