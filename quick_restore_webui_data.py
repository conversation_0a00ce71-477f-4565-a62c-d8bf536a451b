#!/usr/bin/env python3
"""
Quick Restore Open WebUI Data từ webui-data/ folder
Database 11MB có đầy đủ dữ liệu chat history
"""

import subprocess
import time
import os
from datetime import datetime

def log(message):
    """Log với timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def quick_restore():
    """Phục hồi nhanh từ webui-data/webui.db (11MB)"""
    
    log("🎯 PHỤC HỒI NHANH OPEN WEBUI DATA")
    log("=" * 50)
    
    # Kiểm tra file nguồn
    source_db = "webui-data/webui.db"
    if not os.path.exists(source_db):
        log(f"❌ Không tìm thấy {source_db}")
        return False
        
    # Kiểm tra kích thước
    size = os.path.getsize(source_db) / (1024*1024)  # MB
    log(f"📊 Kích thước database nguồn: {size:.1f}MB")
    
    if size < 5:  # Nếu nhỏ hơn 5MB thì có vấn đề
        log("⚠️ Database nguồn có vẻ nhỏ, có thể không đầy đủ")
        
    # Backup database hiện tại
    log("💾 Backup database hiện tại...")
    backup_name = f"current_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    try:
        subprocess.run(f"sudo cp /var/lib/docker/volumes/acca_open_webui_data/_data/webui.db {backup_name}", 
                      shell=True, check=True)
        log(f"✅ Đã backup database hiện tại: {backup_name}")
    except:
        log("⚠️ Không thể backup database hiện tại")
    
    # Dừng container
    log("🛑 Dừng container...")
    try:
        subprocess.run("docker stop open-webui-ssl", shell=True, check=True)
        log("✅ Container đã dừng")
    except:
        log("⚠️ Container có thể đã dừng rồi")
    
    # Copy database
    log("📋 Copy database từ webui-data/...")
    try:
        subprocess.run(f"sudo cp {source_db} /var/lib/docker/volumes/acca_open_webui_data/_data/webui.db", 
                      shell=True, check=True)
        log("✅ Database đã được copy")
    except Exception as e:
        log(f"❌ Lỗi copy database: {e}")
        return False
    
    # Copy các thư mục khác nếu cần
    log("📁 Copy cache, uploads, vector_db...")
    folders = ['cache', 'uploads', 'vector_db']
    for folder in folders:
        source_folder = f"webui-data/{folder}"
        if os.path.exists(source_folder):
            try:
                subprocess.run(f"sudo rm -rf /var/lib/docker/volumes/acca_open_webui_data/_data/{folder}", 
                              shell=True, check=True)
                subprocess.run(f"sudo cp -r {source_folder} /var/lib/docker/volumes/acca_open_webui_data/_data/", 
                              shell=True, check=True)
                log(f"✅ Copy {folder} thành công")
            except Exception as e:
                log(f"⚠️ Lỗi copy {folder}: {e}")
    
    # Khởi động container
    log("🚀 Khởi động container...")
    try:
        subprocess.run("docker start open-webui-ssl", shell=True, check=True)
        log("✅ Container đã khởi động")
    except Exception as e:
        log(f"❌ Lỗi khởi động container: {e}")
        return False
    
    # Đợi khởi động
    log("⏳ Đợi container khởi động hoàn toàn...")
    time.sleep(20)
    
    # Kiểm tra kết quả
    log("🧪 Kiểm tra kết quả...")
    
    # Kiểm tra container
    result = subprocess.run("docker ps | grep open-webui-ssl", shell=True, capture_output=True, text=True)
    if "open-webui-ssl" in result.stdout and "Up" in result.stdout:
        log("✅ Container đang chạy")
    else:
        log("❌ Container không chạy")
        return False
    
    # Kiểm tra kích thước database mới
    try:
        result = subprocess.run("sudo ls -lh /var/lib/docker/volumes/acca_open_webui_data/_data/webui.db", 
                               shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            log(f"📊 Database sau restore: {result.stdout.strip()}")
    except:
        pass
    
    log("=" * 50)
    log("🎉 PHỤC HỒI HOÀN THÀNH!")
    log("✅ Database 11MB đã được phục hồi")
    log("✅ Tất cả dữ liệu chat đã được khôi phục")
    log("✅ Cache và uploads đã được phục hồi")
    log("")
    log("💡 KIỂM TRA NGAY:")
    log("1. Mở http://localhost:3000")
    log("2. Đăng nhập và kiểm tra chat history")
    log("3. Xác nhận tất cả tin nhắn đã trở lại")
    
    return True

if __name__ == "__main__":
    success = quick_restore()
    if not success:
        log("❌ Phục hồi thất bại, hãy kiểm tra logs")
        exit(1)