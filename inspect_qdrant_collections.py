#!/usr/bin/env python3
"""
Inspect actual Qdrant collections and their dimensions
"""

from qdrant_client import QdrantClient
import json

def inspect_collections():
    """Inspect all Qdrant collections and their actual dimensions"""
    
    try:
        client = QdrantClient(host='localhost', port=6333)
        
        # Get all collections
        collections = client.get_collections()
        print("🗂️  Available collections:")
        
        for collection in collections.collections:
            collection_name = collection.name
            print(f"\n📁 Collection: {collection_name}")
            
            try:
                # Get collection info
                info = client.get_collection(collection_name)
                vector_size = info.config.params.vectors.size
                distance = info.config.params.vectors.distance
                points_count = info.points_count
                
                print(f"   📏 Vector dimensions: {vector_size}")
                print(f"   📐 Distance metric: {distance}")
                print(f"   📊 Points count: {points_count}")
                
                # Get a sample point to verify actual dimensions
                if points_count > 0:
                    try:
                        sample = client.scroll(
                            collection_name=collection_name,
                            limit=1,
                            with_vectors=True,
                            with_payload=True
                        )
                        
                        if sample[0]:
                            point = sample[0][0]
                            actual_vector_size = len(point.vector)
                            print(f"   🔍 Actual vector size in data: {actual_vector_size}")
                            
                            # Show payload structure
                            if point.payload:
                                print(f"   📝 Sample payload keys: {list(point.payload.keys())}")
                                
                            # Check if dimensions match
                            if actual_vector_size != vector_size:
                                print(f"   ⚠️  MISMATCH: Config says {vector_size}, actual data has {actual_vector_size}")
                            else:
                                print(f"   ✅ Dimensions match: {vector_size}")
                                
                    except Exception as e:
                        print(f"   ❌ Error getting sample: {e}")
                        
            except Exception as e:
                print(f"   ❌ Error getting collection info: {e}")
                
    except Exception as e:
        print(f"❌ Error connecting to Qdrant: {e}")
        print("💡 Make sure Qdrant is running on localhost:6333")

if __name__ == "__main__":
    inspect_collections()