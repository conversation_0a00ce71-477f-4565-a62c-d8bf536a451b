"""
Mem0 Flexible Pipeline with Customizable Model and Dimensions
Optimized for AccA with configurable embedding dimensions and models
"""

import os
import asyncio
import json
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

try:
    from mem0 import AsyncMemory
    MEM0_AVAILABLE = True
except ImportError:
    print("⚠️  mem0 not available - pipeline will run in mock mode")
    MEM0_AVAILABLE = False


class Pipeline:
    class Valves(BaseModel):
        # Pipeline configuration
        pipelines: List[str] = ["*"]
        priority: int = 0
        user_id: str = Field(
            default="default_user", 
            description="Default user ID for memory operations"
        )

        # Qdrant configuration
        qdrant_host: str = Field(
            default="qdrant",
            description="Qdrant host (use 'qdrant' for Docker)"
        )
        qdrant_port: str = Field(
            default="6333",
            description="Qdrant port"
        )
        
        # Dynamic collection naming based on dimensions
        base_collection_name: str = Field(
            default="mem0_gemini", 
            description="Base collection name (dimensions will be appended)"
        )

        # Embedding provider and model configuration
        embedder_provider: str = Field(
            default="gemini", 
            description="Embedding provider (gemini, openai, jina, etc.)"
        )
        embedder_model: str = Field(
            default="text-embedding-004", 
            description="Embedding model name"
        )
        embedder_api_key: str = Field(
            default=os.getenv("GEMINI_API_KEY", ""), 
            description="API key for embedding provider"
        )
        embedder_dims: int = Field(
            default=768, 
            description="Embedding dimensions (768, 1024, 1536, 3072, etc.)"
        )

        # LLM configuration
        llm_provider: str = Field(
            default="gemini", 
            description="LLM provider"
        )
        llm_model: str = Field(
            default="gemini-2.5-flash", 
            description="LLM model name"
        )
        llm_api_key: str = Field(
            default=os.getenv("GEMINI_API_KEY", ""), 
            description="LLM API key"
        )
        llm_temperature: float = Field(
            default=0.1, 
            description="LLM temperature"
        )
        llm_max_tokens: int = Field(
            default=1000, 
            description="LLM max tokens"
        )

        # Memory behavior configuration
        max_memories_to_inject: int = Field(
            default=3, 
            description="Max memories to inject into context"
        )
        memory_relevance_threshold: float = Field(
            default=0.2, 
            description="Minimum relevance score for memory retrieval"
        )
        memory_search_limit: int = Field(
            default=10, 
            description="Maximum memories to search through"
        )
        
        # Advanced options
        auto_store_messages: bool = Field(
            default=True, 
            description="Automatically store user messages as memories"
        )
        enable_fallback_search: bool = Field(
            default=True, 
            description="Enable fallback search with lower threshold"
        )
        enable_debug_logging: bool = Field(
            default=False, 
            description="Enable detailed debug logging"
        )
        
        # Collection management
        auto_create_collection: bool = Field(
            default=True, 
            description="Automatically create Qdrant collection if not exists"
        )

    def __init__(self):
        self.type = "filter"
        self.valves = self.Valves()
        self.memory_client = None
        self.last_config_hash = None

    def get_collection_name(self) -> str:
        """Generate collection name based on provider, model and dimensions"""
        provider_short = self.valves.embedder_provider[:4]  # e.g., "gemi" for gemini
        return f"{self.valves.base_collection_name}_{provider_short}_{self.valves.embedder_dims}"

    def get_config_hash(self) -> str:
        """Generate hash of current configuration to detect changes"""
        config_str = f"{self.valves.embedder_provider}_{self.valves.embedder_model}_{self.valves.embedder_dims}_{self.valves.qdrant_host}_{self.valves.qdrant_port}"
        return str(hash(config_str))

    async def get_memory_client(self):
        """Get or create memory client with current configuration"""
        current_hash = self.get_config_hash()
        
        # Recreate client if configuration changed
        if self.memory_client is None or self.last_config_hash != current_hash:
            if self.valves.enable_debug_logging:
                print(f"🔄 Creating new memory client with config hash: {current_hash}")
            
            collection_name = self.get_collection_name()
            
            config = {
                "vector_store": {
                    "provider": "qdrant",
                    "config": {
                        "host": self.valves.qdrant_host,
                        "port": int(self.valves.qdrant_port),
                        "collection_name": collection_name,
                    },
                },
                "llm": {
                    "provider": self.valves.llm_provider,
                    "config": {
                        "api_key": self.valves.llm_api_key,
                        "model": self.valves.llm_model,
                        "temperature": self.valves.llm_temperature,
                        "max_tokens": self.valves.llm_max_tokens,
                    },
                },
                "embedder": {
                    "provider": self.valves.embedder_provider,
                    "config": {
                        "api_key": self.valves.embedder_api_key,
                        "model": self.valves.embedder_model,
                        "embedding_dims": self.valves.embedder_dims,
                    },
                },
            }
            
            if self.valves.enable_debug_logging:
                print(f"📊 Memory config: {json.dumps(config, indent=2)}")
            
            try:
                if MEM0_AVAILABLE:
                    self.memory_client = AsyncMemory.from_config(config)
                    self.last_config_hash = current_hash
                    print(f"✅ Memory client initialized with collection: {collection_name}")
                else:
                    print("⚠️  Running in mock mode - mem0 not available")
                    self.memory_client = None
            except Exception as e:
                print(f"❌ Failed to initialize memory client: {e}")
                self.memory_client = None
        
        return self.memory_client

    async def on_startup(self):
        print(f"🚀 Starting Mem0 Flexible Pipeline")
        print(f"📊 Configuration:")
        print(f"   - Embedder: {self.valves.embedder_provider}/{self.valves.embedder_model}")
        print(f"   - Dimensions: {self.valves.embedder_dims}")
        print(f"   - Collection: {self.get_collection_name()}")
        print(f"   - LLM: {self.valves.llm_provider}/{self.valves.llm_model}")
        
        # Initialize memory client
        await self.get_memory_client()

    async def on_shutdown(self):
        print(f"🛑 Shutting down Mem0 Flexible Pipeline")

    async def inlet(self, body: dict, user: Optional[dict] = None) -> dict:
        """Process incoming requests and inject relevant memories"""
        if not MEM0_AVAILABLE:
            return body
            
        try:
            memory_client = await self.get_memory_client()
            if not memory_client:
                return body

            # Get user ID
            user_id = user.get("id", self.valves.user_id) if user else self.valves.user_id
            
            # Get the last user message
            messages = body.get("messages", [])
            if not messages:
                return body
                
            last_message = messages[-1]
            if last_message.get("role") != "user":
                return body
                
            user_message = last_message.get("content", "")
            if not user_message:
                return body

            # Search for relevant memories
            if self.valves.enable_debug_logging:
                print(f"🔍 Searching memories for: {user_message[:100]}...")
            
            try:
                memories = await memory_client.search(
                    query=user_message,
                    user_id=user_id,
                    limit=self.valves.memory_search_limit
                )
                
                # Filter memories by relevance threshold
                relevant_memories = []
                for memory in memories:
                    score = memory.get("score", 0)
                    if score >= self.valves.memory_relevance_threshold:
                        relevant_memories.append(memory)
                
                # Fallback search with lower threshold if enabled
                if not relevant_memories and self.valves.enable_fallback_search:
                    fallback_threshold = max(0.1, self.valves.memory_relevance_threshold - 0.1)
                    for memory in memories:
                        score = memory.get("score", 0)
                        if score >= fallback_threshold:
                            relevant_memories.append(memory)
                
                # Limit to max memories to inject
                relevant_memories = relevant_memories[:self.valves.max_memories_to_inject]
                
                if relevant_memories:
                    memory_context = "\n".join([
                        f"- {memory['memory']}" for memory in relevant_memories
                    ])
                    
                    # Inject memories into system message
                    system_message = {
                        "role": "system",
                        "content": f"Relevant memories from previous conversations:\n{memory_context}\n\nUse these memories to provide more personalized and contextual responses."
                    }
                    
                    # Insert system message at the beginning
                    body["messages"].insert(0, system_message)
                    
                    if self.valves.enable_debug_logging:
                        print(f"💾 Injected {len(relevant_memories)} memories")
                
            except Exception as e:
                print(f"❌ Error searching memories: {e}")
                
        except Exception as e:
            print(f"❌ Error in inlet: {e}")
            
        return body

    async def outlet(self, body: dict, user: Optional[dict] = None) -> dict:
        """Process outgoing responses and store memories"""
        if not MEM0_AVAILABLE or not self.valves.auto_store_messages:
            return body
            
        try:
            memory_client = await self.get_memory_client()
            if not memory_client:
                return body

            # Get user ID
            user_id = user.get("id", self.valves.user_id) if user else self.valves.user_id
            
            # Get the last user message and assistant response
            messages = body.get("messages", [])
            if len(messages) < 2:
                return body
                
            # Find the last user message and assistant response
            user_message = None
            assistant_message = None
            
            for i in range(len(messages) - 1, -1, -1):
                msg = messages[i]
                if msg.get("role") == "assistant" and not assistant_message:
                    assistant_message = msg.get("content", "")
                elif msg.get("role") == "user" and not user_message:
                    user_message = msg.get("content", "")
                    
                if user_message and assistant_message:
                    break
            
            # Store the conversation as memory
            if user_message and assistant_message:
                conversation_text = f"User asked: {user_message}\nAssistant responded: {assistant_message}"
                
                try:
                    await memory_client.add(
                        messages=conversation_text,
                        user_id=user_id
                    )
                    
                    if self.valves.enable_debug_logging:
                        print(f"💾 Stored conversation memory for user: {user_id}")
                        
                except Exception as e:
                    print(f"❌ Error storing memory: {e}")
                    
        except Exception as e:
            print(f"❌ Error in outlet: {e}")
            
        return body
