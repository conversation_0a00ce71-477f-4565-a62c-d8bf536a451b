#!/usr/bin/env python3
"""
Docling Log Checker - <PERSON><PERSON><PERSON> tra và chẩn đoán vấn đề <PERSON>ling
Tìm hiểu tại sao LLM không đọc được file nào qua Open WebUI
"""

import os
import sys
import json
import logging
import subprocess
import requests
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Any
import time
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DoclingDiagnostic:
    """Chẩn đoán toàn diện hệ thống Docling"""
    
    def __init__(self):
        self.docling_url = "http://localhost:5001"
        self.openwebui_url = "http://localhost:8888"
        self.log_files = [
            "/var/log/docling.log",
            "./docling.log",
            "./backend/app/rag/docling.log",
            "/tmp/docling.log"
        ]
        
    def run_full_diagnostic(self):
        """Chạy chẩn đoán toàn diện"""
        logger.info("🔍 Bắt đầu chẩn đoán toàn diện Docling...")
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'docling_server_status': self.check_docling_server(),
            'openwebui_status': self.check_openwebui_status(),
            'docling_logs': self.analyze_docling_logs(),
            'file_processing_test': self.test_file_processing(),
            'network_connectivity': self.check_network_connectivity(),
            'environment_check': self.check_environment(),
            'recommendations': []
        }
        
        # Phân tích kết quả và đưa ra khuyến nghị
        results['recommendations'] = self.generate_recommendations(results)
        
        # Lưu kết quả
        self.save_diagnostic_report(results)
        
        return results
    
    def check_docling_server(self) -> Dict[str, Any]:
        """Kiểm tra trạng thái Docling server"""
        logger.info("🔄 Kiểm tra Docling server...")
        
        result = {
            'running': False,
            'health_check': False,
            'endpoints_available': [],
            'error': None,
            'process_info': None
        }
        
        try:
            # Kiểm tra process
            try:
                cmd = "ps aux | grep -i docling | grep -v grep"
                process_output = subprocess.check_output(cmd, shell=True, text=True)
                result['process_info'] = process_output.strip()
                result['running'] = bool(process_output.strip())
                logger.info(f"✅ Docling process: {result['running']}")
            except subprocess.CalledProcessError:
                result['running'] = False
                logger.warning("❌ Không tìm thấy Docling process")
            
            # Kiểm tra health endpoint
            try:
                response = requests.get(f"{self.docling_url}/health", timeout=5)
                if response.status_code == 200:
                    result['health_check'] = True
                    health_data = response.json()
                    result['health_data'] = health_data
                    logger.info("✅ Docling health check OK")
                else:
                    logger.warning(f"⚠️  Docling health check failed: {response.status_code}")
            except Exception as e:
                result['error'] = str(e)
                logger.error(f"❌ Không thể kết nối Docling health: {e}")
            
            # Kiểm tra các endpoints
            endpoints = ['/extract_tables', '/extract_text', '/extract_content']
            for endpoint in endpoints:
                try:
                    response = requests.options(f"{self.docling_url}{endpoint}", timeout=3)
                    if response.status_code in [200, 405]:  # 405 = Method Not Allowed is OK for OPTIONS
                        result['endpoints_available'].append(endpoint)
                except:
                    pass
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"❌ Lỗi kiểm tra Docling server: {e}")
        
        return result
    
    def check_openwebui_status(self) -> Dict[str, Any]:
        """Kiểm tra trạng thái Open WebUI"""
        logger.info("🔄 Kiểm tra Open WebUI...")
        
        result = {
            'running': False,
            'accessible': False,
            'docling_integration': False,
            'error': None
        }
        
        try:
            # Kiểm tra Open WebUI
            response = requests.get(f"{self.openwebui_url}/health", timeout=5)
            if response.status_code == 200:
                result['accessible'] = True
                logger.info("✅ Open WebUI accessible")
            
            # Kiểm tra process
            try:
                cmd = "ps aux | grep -i 'open-webui\\|webui' | grep -v grep"
                process_output = subprocess.check_output(cmd, shell=True, text=True)
                result['running'] = bool(process_output.strip())
                result['process_info'] = process_output.strip()
            except subprocess.CalledProcessError:
                result['running'] = False
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"❌ Lỗi kiểm tra Open WebUI: {e}")
        
        return result
    
    def analyze_docling_logs(self) -> Dict[str, Any]:
        """Phân tích log files của Docling"""
        logger.info("📋 Phân tích Docling logs...")
        
        result = {
            'log_files_found': [],
            'recent_errors': [],
            'processing_attempts': [],
            'common_issues': [],
            'log_analysis': {}
        }
        
        # Tìm log files
        for log_path in self.log_files:
            if os.path.exists(log_path):
                result['log_files_found'].append(log_path)
                logger.info(f"✅ Tìm thấy log: {log_path}")
        
        # Kiểm tra system logs
        try:
            # Journalctl logs
            cmd = "journalctl -u docling --no-pager -n 50"
            journal_output = subprocess.check_output(cmd, shell=True, text=True)
            if journal_output.strip():
                result['system_logs'] = journal_output
                logger.info("✅ Tìm thấy system logs")
        except:
            pass
        
        # Phân tích log files
        for log_file in result['log_files_found']:
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                # Lấy 100 dòng cuối
                recent_lines = lines[-100:] if len(lines) > 100 else lines
                
                # Tìm errors
                errors = [line.strip() for line in recent_lines if 'ERROR' in line.upper() or 'FAILED' in line.upper()]
                result['recent_errors'].extend(errors[-10:])  # 10 errors gần nhất
                
                # Tìm processing attempts
                processing = [line.strip() for line in recent_lines if 'processing' in line.lower() or 'extract' in line.lower()]
                result['processing_attempts'].extend(processing[-10:])
                
                result['log_analysis'][log_file] = {
                    'total_lines': len(lines),
                    'recent_lines': len(recent_lines),
                    'error_count': len(errors),
                    'last_modified': os.path.getmtime(log_file)
                }
                
            except Exception as e:
                logger.error(f"❌ Lỗi đọc log {log_file}: {e}")
        
        # Phân tích common issues
        all_text = ' '.join(result['recent_errors'] + result['processing_attempts'])
        
        common_patterns = {
            'connection_refused': 'connection refused',
            'timeout': 'timeout',
            'file_not_found': 'file not found',
            'permission_denied': 'permission denied',
            'import_error': 'importerror',
            'docling_error': 'docling',
            'ocr_error': 'ocr',
            'memory_error': 'memory'
        }
        
        for issue, pattern in common_patterns.items():
            if pattern in all_text.lower():
                result['common_issues'].append(issue)
        
        return result
    
    def test_file_processing(self) -> Dict[str, Any]:
        """Test xử lý file thực tế"""
        logger.info("🧪 Test xử lý file...")
        
        result = {
            'test_files': [],
            'docling_direct_test': {},
            'openwebui_integration_test': {},
            'errors': []
        }
        
        # Tạo test files
        test_files = self.create_test_files()
        result['test_files'] = test_files
        
        # Test trực tiếp với Docling
        for test_file in test_files:
            try:
                logger.info(f"🔄 Testing {test_file['name']} với Docling...")
                
                with open(test_file['path'], 'rb') as f:
                    files = {'files': (test_file['name'], f, test_file['mime_type'])}
                    
                    # Test extract_content endpoint
                    response = requests.post(
                        f"{self.docling_url}/extract_content",
                        files=files,
                        timeout=30
                    )
                    
                    test_result = {
                        'file': test_file['name'],
                        'status_code': response.status_code,
                        'success': response.status_code == 200,
                        'response_size': len(response.content),
                        'error': None
                    }
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            test_result['content_extracted'] = bool(data.get('content'))
                            test_result['tables_found'] = len(data.get('tables', []))
                            test_result['text_length'] = len(data.get('text', ''))
                            logger.info(f"✅ {test_file['name']}: {test_result['text_length']} chars, {test_result['tables_found']} tables")
                        except:
                            test_result['error'] = "Invalid JSON response"
                    else:
                        test_result['error'] = response.text
                        logger.error(f"❌ {test_file['name']}: {response.status_code} - {response.text}")
                    
                    result['docling_direct_test'][test_file['name']] = test_result
                    
            except Exception as e:
                error_msg = f"Error testing {test_file['name']}: {str(e)}"
                result['errors'].append(error_msg)
                logger.error(f"❌ {error_msg}")
        
        # Cleanup test files
        for test_file in test_files:
            try:
                os.unlink(test_file['path'])
            except:
                pass
        
        return result
    
    def create_test_files(self) -> List[Dict[str, str]]:
        """Tạo test files"""
        test_files = []
        
        # Test TXT file
        txt_content = """Đây là file test tiếng Việt.
        
Nội dung này để kiểm tra khả năng xử lý văn bản của Docling server.

Bảng test:
| Cột 1 | Cột 2 | Cột 3 |
|-------|-------|-------|
| A     | B     | C     |
| 1     | 2     | 3     |
"""
        
        txt_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8')
        txt_file.write(txt_content)
        txt_file.close()
        
        test_files.append({
            'name': 'test_vietnamese.txt',
            'path': txt_file.name,
            'mime_type': 'text/plain',
            'type': 'text'
        })
        
        # Test JSON file
        json_content = {
            "test": "Đây là test JSON",
            "data": {
                "name": "Test Document",
                "content": "Nội dung tiếng Việt",
                "numbers": [1, 2, 3, 4, 5]
            }
        }
        
        json_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8')
        json.dump(json_content, json_file, ensure_ascii=False, indent=2)
        json_file.close()
        
        test_files.append({
            'name': 'test_data.json',
            'path': json_file.name,
            'mime_type': 'application/json',
            'type': 'json'
        })
        
        return test_files
    
    def check_network_connectivity(self) -> Dict[str, Any]:
        """Kiểm tra kết nối mạng"""
        logger.info("🌐 Kiểm tra network connectivity...")
        
        result = {
            'localhost_5001': False,
            'localhost_8888': False,
            'port_listening': {},
            'firewall_status': None
        }
        
        # Kiểm tra ports
        ports_to_check = [5001, 8888, 3000]
        
        for port in ports_to_check:
            try:
                cmd = f"netstat -tlnp | grep :{port}"
                output = subprocess.check_output(cmd, shell=True, text=True)
                result['port_listening'][port] = bool(output.strip())
                logger.info(f"✅ Port {port}: {'listening' if result['port_listening'][port] else 'not listening'}")
            except subprocess.CalledProcessError:
                result['port_listening'][port] = False
                logger.warning(f"❌ Port {port}: not listening")
        
        # Test connectivity
        try:
            response = requests.get(f"{self.docling_url}/health", timeout=3)
            result['localhost_5001'] = response.status_code == 200
        except:
            result['localhost_5001'] = False
        
        try:
            response = requests.get(f"{self.openwebui_url}/health", timeout=3)
            result['localhost_8888'] = response.status_code == 200
        except:
            result['localhost_8888'] = False
        
        return result
    
    def check_environment(self) -> Dict[str, Any]:
        """Kiểm tra environment"""
        logger.info("🔧 Kiểm tra environment...")
        
        result = {
            'python_version': sys.version,
            'working_directory': os.getcwd(),
            'environment_variables': {},
            'disk_space': {},
            'memory_usage': {}
        }
        
        # Environment variables quan trọng
        important_vars = [
            'DOCLING_SERVER_URL',
            'RAG_CONTENT_EXTRACTION_ENGINE',
            'DISABLE_OCR',
            'TESSDATA_PREFIX',
            'OPENAI_API_KEY',
            'GEMINI_API_KEY'
        ]
        
        for var in important_vars:
            result['environment_variables'][var] = os.environ.get(var, 'Not set')
        
        # Disk space
        try:
            statvfs = os.statvfs('.')
            free_space = statvfs.f_frsize * statvfs.f_bavail
            total_space = statvfs.f_frsize * statvfs.f_blocks
            result['disk_space'] = {
                'free_gb': round(free_space / (1024**3), 2),
                'total_gb': round(total_space / (1024**3), 2),
                'usage_percent': round((1 - free_space/total_space) * 100, 2)
            }
        except:
            pass
        
        # Memory usage
        try:
            with open('/proc/meminfo', 'r') as f:
                meminfo = f.read()
                for line in meminfo.split('\n'):
                    if 'MemTotal:' in line:
                        total_kb = int(line.split()[1])
                        result['memory_usage']['total_gb'] = round(total_kb / (1024**2), 2)
                    elif 'MemAvailable:' in line:
                        available_kb = int(line.split()[1])
                        result['memory_usage']['available_gb'] = round(available_kb / (1024**2), 2)
        except:
            pass
        
        return result
    
    def generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Tạo khuyến nghị dựa trên kết quả chẩn đoán"""
        recommendations = []
        
        # Kiểm tra Docling server
        if not results['docling_server_status']['running']:
            recommendations.append("🚨 CRITICAL: Docling server không chạy. Khởi động bằng: python docling_server_updated.py")
        
        if not results['docling_server_status']['health_check']:
            recommendations.append("🚨 CRITICAL: Docling server không phản hồi health check. Kiểm tra port 5001")
        
        # Kiểm tra logs
        if results['docling_logs']['recent_errors']:
            recommendations.append("⚠️  WARNING: Có errors trong Docling logs. Xem chi tiết trong báo cáo")
        
        # Kiểm tra network
        if not results['network_connectivity']['port_listening'].get(5001, False):
            recommendations.append("🚨 CRITICAL: Port 5001 không listening. Docling server chưa khởi động đúng")
        
        # Kiểm tra file processing
        docling_tests = results['file_processing_test']['docling_direct_test']
        if docling_tests:
            failed_tests = [name for name, test in docling_tests.items() if not test['success']]
            if failed_tests:
                recommendations.append(f"⚠️  WARNING: File processing failed cho: {', '.join(failed_tests)}")
        
        # Kiểm tra environment
        env_vars = results['environment_check']['environment_variables']
        if env_vars.get('RAG_CONTENT_EXTRACTION_ENGINE') != 'docling':
            recommendations.append("⚠️  WARNING: RAG_CONTENT_EXTRACTION_ENGINE không được set thành 'docling'")
        
        if env_vars.get('DOCLING_SERVER_URL') == 'Not set':
            recommendations.append("⚠️  WARNING: DOCLING_SERVER_URL không được set. Nên set thành 'http://localhost:5001'")
        
        # Disk space
        disk_info = results['environment_check'].get('disk_space', {})
        if disk_info.get('free_gb', 0) < 1:
            recommendations.append("⚠️  WARNING: Disk space thấp (< 1GB). Có thể ảnh hưởng xử lý file")
        
        if not recommendations:
            recommendations.append("✅ Không phát hiện vấn đề nghiêm trọng. Hệ thống có vẻ hoạt động bình thường")
        
        return recommendations
    
    def save_diagnostic_report(self, results: Dict[str, Any]):
        """Lưu báo cáo chẩn đoán"""
        report_file = f"docling_diagnostic_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"📄 Báo cáo chẩn đoán đã lưu: {report_file}")
            
            # Tạo summary report
            self.create_summary_report(results, report_file.replace('.json', '_summary.txt'))
            
        except Exception as e:
            logger.error(f"❌ Lỗi lưu báo cáo: {e}")
    
    def create_summary_report(self, results: Dict[str, Any], summary_file: str):
        """Tạo báo cáo tóm tắt dễ đọc"""
        try:
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("=" * 60 + "\n")
                f.write("DOCLING DIAGNOSTIC SUMMARY REPORT\n")
                f.write("=" * 60 + "\n")
                f.write(f"Thời gian: {results['timestamp']}\n\n")
                
                # Status overview
                f.write("📊 TỔNG QUAN TRẠNG THÁI:\n")
                f.write("-" * 30 + "\n")
                f.write(f"Docling Server Running: {'✅' if results['docling_server_status']['running'] else '❌'}\n")
                f.write(f"Docling Health Check: {'✅' if results['docling_server_status']['health_check'] else '❌'}\n")
                f.write(f"Open WebUI Running: {'✅' if results['openwebui_status']['running'] else '❌'}\n")
                f.write(f"Port 5001 Listening: {'✅' if results['network_connectivity']['port_listening'].get(5001) else '❌'}\n")
                f.write("\n")
                
                # Recommendations
                f.write("🔧 KHUYẾN NGHỊ:\n")
                f.write("-" * 30 + "\n")
                for i, rec in enumerate(results['recommendations'], 1):
                    f.write(f"{i}. {rec}\n")
                f.write("\n")
                
                # Recent errors
                if results['docling_logs']['recent_errors']:
                    f.write("❌ ERRORS GẦN ĐÂY:\n")
                    f.write("-" * 30 + "\n")
                    for error in results['docling_logs']['recent_errors'][-5:]:
                        f.write(f"• {error}\n")
                    f.write("\n")
                
                # File processing results
                if results['file_processing_test']['docling_direct_test']:
                    f.write("🧪 KẾT QUẢ TEST FILE:\n")
                    f.write("-" * 30 + "\n")
                    for filename, test in results['file_processing_test']['docling_direct_test'].items():
                        status = "✅" if test['success'] else "❌"
                        f.write(f"{status} {filename}: {test.get('text_length', 0)} chars\n")
                    f.write("\n")
                
                f.write("=" * 60 + "\n")
                f.write("Chi tiết đầy đủ trong file JSON tương ứng\n")
                f.write("=" * 60 + "\n")
            
            logger.info(f"📄 Summary report đã lưu: {summary_file}")
            
        except Exception as e:
            logger.error(f"❌ Lỗi tạo summary report: {e}")

def main():
    """Main function"""
    print("🔍 DOCLING DIAGNOSTIC TOOL")
    print("=" * 50)
    
    diagnostic = DoclingDiagnostic()
    results = diagnostic.run_full_diagnostic()
    
    print("\n" + "=" * 50)
    print("📋 TỔNG KẾT:")
    print("=" * 50)
    
    for i, recommendation in enumerate(results['recommendations'], 1):
        print(f"{i}. {recommendation}")
    
    print(f"\n📄 Chi tiết báo cáo đã được lưu")
    print("🔧 Chạy lại script này sau khi sửa lỗi để kiểm tra lại")

if __name__ == "__main__":
    main()