#!/usr/bin/env python3
"""
Auto Deploy RAG to Container Script
Tự động deploy RAG system vào container pipelines mà không cần copy tay
"""

import subprocess
import sys
import os
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AutoRAGDeployer:
    """Tự động deploy RAG system vào container"""
    
    def __init__(self):
        self.container_name = "pipelines"
        self.container_path = "/app/pipelines"
        
    def deploy_rag_system(self):
        """Deploy toàn bộ RAG system vào container"""
        logger.info("🚀 Bắt đầu auto deploy RAG system vào container...")
        
        # Danh sách files cần deploy
        files_to_deploy = [
            "simple_rag_optimization_system.py",
            "simple_rag_pipeline_integration.py", 
            "optimized_rag_pipeline_simple.py",
            "rag_deployment_config.json"
        ]
        
        try:
            # 1. Kiểm tra container đang chạy
            if not self._check_container_running():
                logger.error("❌ Container pipelines không chạy!")
                return False
            
            # 2. Deploy từng file
            for file_name in files_to_deploy:
                if Path(file_name).exists():
                    success = self._copy_file_to_container(file_name)
                    if success:
                        logger.info(f"✅ Deployed: {file_name}")
                    else:
                        logger.error(f"❌ Failed to deploy: {file_name}")
                        return False
                else:
                    logger.warning(f"⚠️ File not found: {file_name}")
            
            # 3. Thay thế optimized_rag_pipeline.py
            self._replace_main_pipeline()
            
            # 4. Restart container để load lại
            self._restart_container()
            
            # 5. Verify deployment
            if self._verify_deployment():
                logger.info("🎉 Auto deploy RAG system thành công!")
                return True
            else:
                logger.error("❌ Deployment verification failed!")
                return False
                
        except Exception as e:
            logger.error(f"❌ Auto deploy failed: {str(e)}")
            return False
    
    def _check_container_running(self):
        """Kiểm tra container có đang chạy không"""
        try:
            result = subprocess.run([
                "docker", "ps", "--filter", f"name={self.container_name}", "--format", "{{.Names}}"
            ], capture_output=True, text=True, check=True)
            
            return self.container_name in result.stdout
        except:
            return False
    
    def _copy_file_to_container(self, file_name):
        """Copy file vào container"""
        try:
            subprocess.run([
                "docker", "cp", file_name, f"{self.container_name}:{self.container_path}/"
            ], check=True, capture_output=True)
            return True
        except:
            return False
    
    def _replace_main_pipeline(self):
        """Thay thế file pipeline chính"""
        try:
            # Copy optimized_rag_pipeline_simple.py thành optimized_rag_pipeline.py
            subprocess.run([
                "docker", "cp", "optimized_rag_pipeline_simple.py", 
                f"{self.container_name}:{self.container_path}/optimized_rag_pipeline.py"
            ], check=True, capture_output=True)
            logger.info("✅ Replaced main pipeline file")
            return True
        except:
            logger.error("❌ Failed to replace main pipeline file")
            return False
    
    def _restart_container(self):
        """Restart container"""
        try:
            logger.info("🔄 Restarting container...")
            subprocess.run(["docker", "restart", self.container_name], check=True, capture_output=True)
            
            # Đợi container khởi động
            import time
            time.sleep(10)
            
            logger.info("✅ Container restarted")
            return True
        except:
            logger.error("❌ Failed to restart container")
            return False
    
    def _verify_deployment(self):
        """Verify deployment thành công"""
        try:
            import requests
            import time
            
            # Đợi server khởi động
            time.sleep(5)
            
            # Test server response
            response = requests.get('http://localhost:9099/', timeout=10)
            if response.status_code == 200:
                data = response.json()
                return data.get('status') == True
            
            return False
        except:
            return False
    
    def create_volume_mount_solution(self):
        """Tạo giải pháp volume mount để tránh copy tay"""
        logger.info("📁 Tạo giải pháp volume mount...")
        
        # Tạo thư mục pipelines local
        pipelines_dir = Path("./pipelines_local")
        pipelines_dir.mkdir(exist_ok=True)
        
        # Copy files vào thư mục local
        files_to_copy = [
            "simple_rag_optimization_system.py",
            "simple_rag_pipeline_integration.py", 
            "optimized_rag_pipeline_simple.py",
            "rag_deployment_config.json"
        ]
        
        for file_name in files_to_copy:
            if Path(file_name).exists():
                import shutil
                shutil.copy2(file_name, pipelines_dir / file_name)
                logger.info(f"✅ Copied to local: {file_name}")
        
        # Copy optimized_rag_pipeline_simple.py as optimized_rag_pipeline.py
        if Path("optimized_rag_pipeline_simple.py").exists():
            import shutil
            shutil.copy2("optimized_rag_pipeline_simple.py", pipelines_dir / "optimized_rag_pipeline.py")
            logger.info("✅ Created optimized_rag_pipeline.py")
        
        # Tạo docker-compose với volume mount
        docker_compose_content = '''
version: '3.8'
services:
  pipelines:
    image: ghcr.io/open-webui/pipelines:main
    container_name: pipelines
    ports:
      - "9099:9099"
    volumes:
      - ./pipelines_local:/app/pipelines
    environment:
      - PIPELINES_URLS=
    restart: unless-stopped
'''
        
        with open("docker-compose-pipelines.yml", "w") as f:
            f.write(docker_compose_content)
        
        logger.info("✅ Created docker-compose-pipelines.yml with volume mount")
        logger.info("💡 Để sử dụng volume mount:")
        logger.info("   1. docker-compose -f docker-compose-pipelines.yml down")
        logger.info("   2. docker-compose -f docker-compose-pipelines.yml up -d")
        logger.info("   3. Files sẽ tự động sync từ ./pipelines_local/")

def main():
    """Main function"""
    deployer = AutoRAGDeployer()
    
    print("🤖 Auto RAG Deployment Tool")
    print("=" * 50)
    print("1. Auto deploy (copy files to existing container)")
    print("2. Create volume mount solution (recommended)")
    print("3. Both")
    
    choice = input("\nChọn option (1/2/3): ").strip()
    
    if choice in ["1", "3"]:
        print("\n🚀 Running auto deploy...")
        success = deployer.deploy_rag_system()
        if success:
            print("✅ Auto deploy completed successfully!")
        else:
            print("❌ Auto deploy failed!")
    
    if choice in ["2", "3"]:
        print("\n📁 Creating volume mount solution...")
        deployer.create_volume_mount_solution()
        print("✅ Volume mount solution created!")
    
    print("\n" + "=" * 50)
    print("📋 HƯỚNG DẪN SỬ DỤNG:")
    print("1. KHÔNG CẦN upload file nào lên Open WebUI")
    print("2. RAG system đã tự động hoạt động trong background")
    print("3. Chỉ cần chat bình thường, RAG sẽ tự động enhance context")
    print("4. Để update code sau này, chỉ cần chạy script này lại")

if __name__ == "__main__":
    main()