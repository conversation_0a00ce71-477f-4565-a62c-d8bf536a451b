# Oracle Database Integration Requirements
# ======================================

# Oracle Database Driver
oracledb>=1.4.0

# OCI (Oracle Cloud Infrastructure) SDK
oci>=2.100.0

# Additional Oracle utilities
cx_Oracle>=8.3.0

# Cryptography for OCI authentication
cryptography>=3.4.8

# JSON handling
jsonschema>=4.0.0

# Async support
asyncio-mqtt>=0.11.0

# Existing requirements for AI/ML (already in backend/requirements.txt)
# sentence-transformers>=2.2.0
# tiktoken>=0.5.0
# numpy>=1.24.0
# fastapi>=0.110.0
# pydantic>=2.0.0

# Optional: For Oracle Instant Client (if not using wallet)
# Note: You may need to install Oracle Instant Client separately 