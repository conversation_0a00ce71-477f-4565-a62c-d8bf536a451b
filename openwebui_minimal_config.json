{"mcpServers": {"mcpo_complete_proxy_8000": {"transport": {"type": "http", "url": "http://mcpo-complete-proxy-8000:8000"}, "description": "MCPO Complete Proxy - 12 MCP tools with namespace separation"}, "jina_crawler_8002": {"transport": {"type": "http", "url": "http://jina-crawler-mcp-proxy-8002:8002"}, "description": "<PERSON><PERSON> - Advanced web crawling with AI processing"}, "pandas_unified_8004": {"transport": {"type": "http", "url": "http://pandas-unified-server:8004"}, "description": "Pandas Server - Data analysis tools via HTTP"}}}