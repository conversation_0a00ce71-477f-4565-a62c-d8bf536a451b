#!/usr/bin/env python3
"""
Script to migrate memory embeddings from 768 to 1536 dimensions
Re-embeds all memories using new Gemini embedding model
"""

import subprocess
import time
import json

def run_docker_exec(cmd, description):
    """Run command inside docker container"""
    full_cmd = f"docker exec catomanton-webui {cmd}"
    print(f"\n🔧 {description}")
    print(f"Command: {full_cmd}")
    
    try:
        result = subprocess.run(full_cmd, shell=True, capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print(f"✅ Success: {description}")
            if result.stdout.strip():
                print(f"Output: {result.stdout.strip()}")
            return result.stdout.strip()
        else:
            print(f"❌ Failed: {description}")
            print(f"Error: {result.stderr.strip()}")
            return None
    except subprocess.TimeoutExpired:
        print(f"⏰ Timeout: {description}")
        return None
    except Exception as e:
        print(f"💥 Exception: {e}")
        return None

def main():
    print("🔄 Memory Embedding Migration: 768 → 1536 dimensions")
    print("📋 This will:")
    print("   1. List existing Qdrant collections")
    print("   2. Export memories from old collections (768 dim)")
    print("   3. Delete old collections")
    print("   4. Re-embed and import to new collection (1536 dim)")
    
    # Wait for container to be ready
    print("\n⏳ Waiting for container to be ready...")
    time.sleep(3)
    
    # Create migration script inside container
    migration_script = '''
import os
import sys
import json
from datetime import datetime

# Set environment
gemini_key = os.getenv("GEMINI_API_KEY")
print(f"🔑 Using API key: {gemini_key[:20] if gemini_key else 'None'}...")

try:
    from qdrant_client import QdrantClient
    import mem0
    from mem0 import Memory
    
    print("✅ Required libraries imported")
    
    # Connect to Qdrant
    print("🔗 Connecting to Qdrant...")
    qdrant_client = QdrantClient(host="qdrant", port=6333)
    
    # List existing collections
    print("📋 Existing collections:")
    collections = qdrant_client.get_collections()
    old_collections = []
    
    for collection in collections.collections:
        collection_name = collection.name
        print(f"  - {collection_name}")
        
        # Check if it's an old 768-dim collection
        if "768" in collection_name or collection_name in ["mem0_gemini_768", "mem0_openai_768", "mem0_default"]:
            old_collections.append(collection_name)
            print(f"    ⚠️  Old collection detected: {collection_name}")
    
    if not old_collections:
        print("ℹ️  No old collections found to migrate")
        sys.exit(0)
    
    # Export memories from old collections
    exported_memories = []
    
    for old_collection in old_collections:
        print(f"\\n📤 Exporting memories from: {old_collection}")
        
        try:
            # Get all points from collection
            scroll_result = qdrant_client.scroll(
                collection_name=old_collection,
                limit=1000,  # Adjust if you have more memories
                with_payload=True,
                with_vectors=False  # We don't need old vectors
            )
            
            points = scroll_result[0]
            print(f"   Found {len(points)} memories")
            
            for point in points:
                memory_data = {
                    "id": point.id,
                    "payload": point.payload,
                    "collection": old_collection
                }
                exported_memories.append(memory_data)
                
        except Exception as e:
            print(f"   ❌ Failed to export from {old_collection}: {e}")
    
    print(f"\\n📊 Total memories exported: {len(exported_memories)}")
    
    if not exported_memories:
        print("ℹ️  No memories to migrate")
        sys.exit(0)
    
    # Initialize new Memory client with 1536 dimensions
    print("\\n🧠 Initializing new Memory client (1536 dim)...")
    
    config = {
        "llm": {
            "provider": "gemini",
            "config": {
                "model": "gemini-2.5-flash",
                "api_key": gemini_key,
                "temperature": 0.1,
                "max_tokens": 1000
            }
        },
        "embedder": {
            "provider": "gemini",
            "config": {
                "model": "gemini-embedding-001",
                "api_key": gemini_key,
                "embedding_dims": 1536
            }
        },
        "vector_store": {
            "provider": "qdrant",
            "config": {
                "host": "qdrant",
                "port": 6333,
                "collection_name": "mem0_gemini_1536"
            }
        }
    }
    
    memory = Memory.from_config(config)
    print("✅ New memory client initialized")
    
    # Re-embed and store memories
    print("\\n🔄 Re-embedding memories with new dimensions...")
    
    migrated_count = 0
    failed_count = 0
    
    for i, mem_data in enumerate(exported_memories, 1):
        try:
            payload = mem_data["payload"]
            
            # Extract memory text and metadata
            memory_text = payload.get("memory", payload.get("text", ""))
            user_id = payload.get("user_id", "migrated_user")
            metadata = payload.get("metadata", {})
            
            # Add migration info to metadata
            metadata.update({
                "migrated_from": mem_data["collection"],
                "migrated_at": datetime.now().isoformat(),
                "migration_batch": "768_to_1536",
                "original_id": str(mem_data["id"])
            })
            
            if memory_text:
                print(f"   {i}/{len(exported_memories)}: Re-embedding '{memory_text[:50]}...'")
                
                # Add memory with new embeddings
                result = memory.add(
                    messages=[{"role": "user", "content": memory_text}],
                    user_id=user_id,
                    metadata=metadata
                )
                
                migrated_count += 1
                print(f"   ✅ Migrated: {result}")
                
            else:
                print(f"   ⚠️  Skipping empty memory: {mem_data['id']}")
                
        except Exception as e:
            failed_count += 1
            print(f"   ❌ Failed to migrate memory {mem_data['id']}: {e}")
    
    print(f"\\n📊 Migration Summary:")
    print(f"   ✅ Successfully migrated: {migrated_count}")
    print(f"   ❌ Failed: {failed_count}")
    print(f"   📁 Total processed: {len(exported_memories)}")
    
    # Ask user before deleting old collections
    print(f"\\n🗑️  Old collections to delete: {old_collections}")
    print("⚠️  WARNING: This will permanently delete old collections!")
    
    # For automation, we'll skip deletion and let user decide manually
    print("ℹ️  Skipping automatic deletion. Please manually delete old collections if migration successful.")
    
    print("\\n🎉 Migration completed successfully!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Make sure mem0ai and qdrant-client are installed")
except Exception as e:
    print(f"❌ Migration failed: {e}")
    import traceback
    traceback.print_exc()
'''
    
    # Write migration script to container
    run_docker_exec(f"cat > /tmp/migrate_embeddings.py << 'EOF'\n{migration_script}\nEOF", "Creating migration script")
    
    # Run migration script
    print("\n🚀 Running memory migration...")
    run_docker_exec("cd /app && python /tmp/migrate_embeddings.py", "Executing migration")
    
    print("\n📋 Migration completed!")
    print("\n🔍 Next steps:")
    print("1. Verify new memories in mem0_gemini_1536 collection")
    print("2. Test memory search functionality")
    print("3. If successful, manually delete old collections:")
    print("   docker exec catomanton-webui python -c \"from qdrant_client import QdrantClient; c=QdrantClient(host='qdrant', port=6333); c.delete_collection('OLD_COLLECTION_NAME')\"")

if __name__ == "__main__":
    main()