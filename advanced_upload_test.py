#!/usr/bin/env python3
"""
🔧 Advanced Upload Test - Find the 'list' object has no attribute 'strip' error
Test actual Open WebUI endpoints with proper authentication
"""

import requests
import json
import io
from PIL import Image
import tempfile
import time
import traceback

class AdvancedUploadTest:
    def __init__(self):
        self.base_url = "http://localhost:3000"
        self.session = requests.Session()
        self.real_endpoints = [
            # Chat/Knowledge Base uploads
            "/api/v1/files/",
            "/api/v1/knowledge/upload",
            "/api/v1/rag/upload_document", 
            
            # AI Edge provider uploads
            "/api/v1/ai-edge/upload_image",
            
            # Gemma3n uploads
            "/api/v1/gemma3n/upload-image",
            
            # TensorFlow Lite uploads
            "/api/v1/tflite/classify-image",
            "/api/v1/tflite/detect-objects",
            "/api/v1/tflite/analyze-image-for-rag",
            
            # Generic file uploads
            "/api/files/upload",
            "/upload"
        ]
    
    def create_test_image(self):
        """Create a test image"""
        img = Image.new('RGB', (100, 100), color='red')
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        return img_buffer.getvalue()
    
    def create_test_document(self):
        """Create a test text document"""
        return "This is a test document for upload testing.".encode('utf-8')
    
    def test_upload_scenarios(self):
        """Test different upload scenarios"""
        print("🔍 Testing Open WebUI upload endpoints...")
        
        # Test files
        image_data = self.create_test_image()
        doc_data = self.create_test_document()
        
        test_files = [
            {
                "name": "test_image.png",
                "data": image_data,
                "content_type": "image/png",
                "type": "image"
            },
            {
                "name": "test_doc.txt",
                "data": doc_data,
                "content_type": "text/plain",
                "type": "document"
            }
        ]
        
        # Test different upload methods
        upload_methods = [
            self.test_multipart_upload,
            self.test_form_data_upload,
            self.test_files_list_upload,
            self.test_content_type_list_upload  # This might trigger the error!
        ]
        
        for endpoint in self.real_endpoints:
            print(f"\n📍 Testing endpoint: {endpoint}")
            
            for test_file in test_files:
                if "image" in endpoint and test_file["type"] != "image":
                    continue
                if "rag" in endpoint or "document" in endpoint and test_file["type"] != "document":
                    continue
                
                print(f"  📄 File: {test_file['name']}")
                
                for method in upload_methods:
                    try:
                        method(endpoint, test_file)
                        time.sleep(0.2)  # Brief pause
                    except Exception as e:
                        print(f"    ⚠️  Method error: {e}")
    
    def test_multipart_upload(self, endpoint, test_file):
        """Standard multipart upload"""
        try:
            url = f"{self.base_url}{endpoint}"
            files = {'file': (test_file['name'], test_file['data'], test_file['content_type'])}
            
            response = self.session.post(url, files=files, timeout=10)
            
            self.analyze_response("Multipart Upload", response, endpoint, test_file['name'])
            
        except Exception as e:
            print(f"    ❌ Multipart Upload Error: {e}")
    
    def test_form_data_upload(self, endpoint, test_file):
        """Form data upload with different field names"""
        try:
            url = f"{self.base_url}{endpoint}"
            
            # Try different field names that might cause issues
            field_variants = ['file', 'upload', 'document', 'image', 'files']
            
            for field_name in field_variants:
                files = {field_name: (test_file['name'], test_file['data'], test_file['content_type'])}
                
                response = self.session.post(url, files=files, timeout=5)
                
                if response.status_code != 405:  # Skip method not allowed
                    self.analyze_response(f"Form Data ({field_name})", response, endpoint, test_file['name'])
                    break
                    
        except Exception as e:
            print(f"    ❌ Form Data Error: {e}")
    
    def test_files_list_upload(self, endpoint, test_file):
        """Upload with files as a list - this might trigger the error!"""
        try:
            url = f"{self.base_url}{endpoint}"
            
            # Create files as list instead of single file
            files = [
                ('file', (test_file['name'], test_file['data'], test_file['content_type'])),
                ('files', (test_file['name'], test_file['data'], test_file['content_type']))
            ]
            
            response = self.session.post(url, files=files, timeout=10)
            
            self.analyze_response("Files List Upload", response, endpoint, test_file['name'])
            
        except Exception as e:
            print(f"    ❌ Files List Error: {e}")
    
    def test_content_type_list_upload(self, endpoint, test_file):
        """Upload with content_type as list - likely cause of the error!"""
        try:
            url = f"{self.base_url}{endpoint}"
            
            # Force content_type to be a list 
            content_types = [test_file['content_type'], 'application/octet-stream']
            
            files = {'file': (test_file['name'], test_file['data'], content_types)}
            
            response = self.session.post(url, files=files, timeout=10)
            
            self.analyze_response("Content-Type List", response, endpoint, test_file['name'])
            
        except Exception as e:
            print(f"    ❌ Content-Type List Error: {e}")
    
    def analyze_response(self, method_name, response, endpoint, filename):
        """Analyze response for the specific error"""
        status_ok = response.status_code in [200, 201]
        
        if status_ok:
            print(f"    ✅ {method_name}: Success ({response.status_code})")
        else:
            error_text = response.text
            print(f"    ❌ {method_name}: {response.status_code}")
            
            # Check for our target error
            if "'list' object has no attribute 'strip'" in error_text:
                print("    🎯 FOUND THE ERROR!")
                self.detailed_error_analysis(method_name, response, endpoint, filename)
            elif "strip" in error_text.lower():
                print(f"    🔍 Strip-related error: {error_text[:200]}...")
            else:
                print(f"    📄 Response: {error_text[:100]}...")
    
    def detailed_error_analysis(self, method_name, response, endpoint, filename):
        """Detailed analysis when we find the target error"""
        print("\n" + "="*70)
        print("🎯 TARGET ERROR FOUND!")
        print("="*70)
        print(f"Method: {method_name}")
        print(f"Endpoint: {endpoint}")
        print(f"File: {filename}")
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print("\nFull Error Response:")
        print("-" * 50)
        print(response.text)
        print("-" * 50)
        print("="*70)
    
    def test_raw_multipart_with_list_content_type(self):
        """Direct test with malformed content-type that causes the error"""
        print("\n🎯 Testing raw multipart with list content-type...")
        
        try:
            import requests_toolbelt
            from requests_toolbelt.multipart.encoder import MultipartEncoder
            
            # Create multipart data with list content-type
            test_data = self.create_test_image()
            
            # This should trigger the error - content type as list
            fields = {
                'file': ('test.png', test_data, ['image/png', 'application/octet-stream'])
            }
            
            m = MultipartEncoder(fields=fields)
            
            response = self.session.post(
                f"{self.base_url}/api/v1/files/",
                data=m,
                headers={'Content-Type': m.content_type},
                timeout=10
            )
            
            self.analyze_response("Raw Multipart List CT", response, "/api/v1/files/", "test.png")
            
        except ImportError:
            print("    ℹ️  requests-toolbelt not available for advanced testing")
        except Exception as e:
            print(f"    ❌ Raw test error: {e}")

def main():
    print("🔧 Advanced Upload Test - Finding the 'list' object error...")
    
    tester = AdvancedUploadTest()
    
    # Test all scenarios
    tester.test_upload_scenarios()
    
    # Test specific edge case
    tester.test_raw_multipart_with_list_content_type()
    
    print("\n✅ Advanced upload testing completed!")
    print("If no errors found, the issue might be in frontend form handling.")

if __name__ == "__main__":
    main() 