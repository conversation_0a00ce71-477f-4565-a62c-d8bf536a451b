# 🧠 Oracle Advanced Memory System for AccA AI Assistant

## 📋 Tổng quan

Oracle Advanced Memory System là một hệ thống memory thông minh được thiết kế cho AccA AI Assistant Platform, sử dụng Oracle Autonomous Database với kiến trúc hybrid thin mode + wallet connection để cung cấp:

- **🏛️ Long-term Memory**: <PERSON><PERSON><PERSON> trữ bền vững conversations và patterns
- **👤 User Personalization**: <PERSON><PERSON><PERSON> và thích ứng theo từng user
- **🔄 Mem0 Integration**: <PERSON><PERSON><PERSON> hợp với hệ thống Mem0 hiện tại
- **⚡ High Performance**: Tối ưu hóa với thin mode và wallet connections
- **🧠 Context Awareness**: Tự động inject memory context vào LLM

## 🏗️ Kiến trúc hệ thống

```mermaid
graph TB
    subgraph "Open WebUI"
        UI[Chat Interface]
        LLM[Language Model]
    end
    
    subgraph "Oracle Advanced Memory Pipeline"
        INLET[Inlet: Memory Search & Context Injection]
        OUTLET[Outlet: Conversation Storage & Learning]
    end
    
    subgraph "Memory Layer"
        OTM[Oracle Thin Mode Pool<br/>Analytics & Patterns]
        OWB[Oracle Wallet Pool<br/>Real-time Operations]
        MEM0[Mem0 Coordinator<br/>Recent Context]
    end
    
    subgraph "Oracle Autonomous DB"
        LTM[Long-term Memory]
        UP[User Patterns]
        DOC[Document Intelligence]
        ANA[Analytics]
    end
    
    subgraph "Existing Systems"
        QD[Qdrant Vector DB]
        GM[Gemini Embeddings]
    end
    
    UI --> INLET
    INLET --> LLM
    LLM --> OUTLET
    OUTLET --> UI
    
    INLET --> OTM
    INLET --> MEM0
    OUTLET --> OWB
    
    OTM --> LTM
    OTM --> UP
    OWB --> LTM
    OWB --> ANA
    
    MEM0 --> QD
    QD --> GM
```

## 🚀 Cài đặt và Triển khai

### Bước 1: Chuẩn bị môi trường

```bash
# Clone repository (nếu chưa có)
git clone <repository-url>
cd AccA

# Kiểm tra Oracle configuration
ls -la .env.oracle
ls -la oracle_wallet/
```

### Bước 2: Chạy deployment script

```bash
# Chạy deployment script tự động
python deploy_oracle_advanced_memory.py
```

Script sẽ tự động:
- ✅ Kiểm tra Oracle configuration
- ✅ Cài đặt dependencies (oracledb, pydantic, etc.)
- ✅ Tạo database schema deployment script
- ✅ Deploy pipeline vào Open WebUI
- ✅ Tạo test script
- ✅ Generate deployment report

### Bước 3: Deploy database schema

```bash
# Deploy Oracle schema
sqlplus /nolog @deploy_schema.sql
```

### Bước 4: Test hệ thống

```bash
# Chạy test để verify functionality
python test_oracle_advanced_memory.py
```

### Bước 5: Enable pipeline trong Open WebUI

1. Mở Open WebUI Admin Panel
2. Vào **Pipelines** section
3. Tìm **oracle-advanced-memory** pipeline
4. **Enable** pipeline
5. Configure **valves** nếu cần thiết

## ⚙️ Cấu hình

### Oracle Database Settings

```json
{
  "oracle_user": "ADMIN",
  "oracle_password": "your_password",
  "oracle_dsn": "your_dsn",
  "oracle_wallet_location": "./oracle_wallet/Wallet_SWIV8HV5Y96IWO2T"
}
```

### Memory Behavior Settings

```json
{
  "enable_oracle_memory": true,
  "enable_mem0_coordination": true,
  "enable_pattern_learning": true,
  "max_oracle_memories": 3,
  "max_mem0_memories": 2,
  "memory_relevance_threshold": 0.4,
  "auto_store_conversations": true
}
```

### Performance Settings

```json
{
  "memory_search_timeout": 2,
  "enable_debug_logging": true,
  "default_user_id": "default_user"
}
```

## 🔧 Cách hoạt động

### 1. Memory Flow trong Conversation

```
User Message 
    ↓
Pipeline Inlet (Memory Search & Context Injection)
    ↓
LLM Processing (với memory context)
    ↓
AI Response
    ↓
Pipeline Outlet (Store conversation & Learn patterns)
    ↓
Response to User
```

### 2. Memory Context Example

Khi user gửi message, LLM sẽ nhận được context như này:

```
System: You are an AI assistant with access to user memory and behavioral patterns.

🏛️ Long-term Memory (Oracle):
  1. [conversation] User discussed Python FastAPI project last week... (confidence: 0.85)
  2. [preference] User prefers detailed technical explanations... (confidence: 0.90)
  3. [pattern] User usually asks follow-up questions about implementation... (confidence: 0.75)

🔄 Recent Context (Mem0):
  1. Asked about database optimization yesterday... 
  2. Discussed Oracle integration this morning...

👤 User Patterns:
  • Communication: formal, prefers detailed responses
  • Interests: Python, Database, AI/ML
  • Prefers: detailed responses with code examples

💡 Use this context to provide personalized, contextually-aware responses.

User: How do I optimize my Oracle queries in Python?
```

### 3. Hybrid Memory Architecture

#### **Immediate Memory (Mem0)**
- **Purpose**: Recent conversation context
- **Storage**: Qdrant vector database
- **Speed**: < 100ms
- **Scope**: Current session + recent history

#### **Long-term Memory (Oracle)**
- **Purpose**: Persistent learning, user patterns
- **Storage**: Oracle Autonomous Database
- **Speed**: < 200ms with thin mode
- **Scope**: Cross-session, long-term insights

#### **Connection Strategy**
- **Thin Mode Pool**: Analytics, pattern extraction, complex queries
- **Wallet Pool**: Real-time storage, immediate operations

## 📊 Database Schema

### Core Tables

#### USER_MEMORY
```sql
CREATE TABLE USER_MEMORY (
    memory_id VARCHAR2(100) PRIMARY KEY,
    user_id VARCHAR2(100) NOT NULL,
    memory_type VARCHAR2(50), -- 'conversation', 'fact', 'preference'
    memory_content CLOB NOT NULL,
    confidence_score NUMBER(3,2),
    session_id VARCHAR2(100),
    tags JSON,
    metadata JSON,
    created_date DATE DEFAULT SYSDATE
);
```

#### USER_PATTERNS
```sql
CREATE TABLE USER_PATTERNS (
    pattern_id VARCHAR2(100) PRIMARY KEY,
    user_id VARCHAR2(100) NOT NULL,
    pattern_type VARCHAR2(50), -- 'communication_style', 'topic_interest'
    pattern_data JSON NOT NULL,
    strength NUMBER(3,2), -- 0.00 to 1.00
    confidence NUMBER(3,2),
    sample_count NUMBER DEFAULT 1
);
```

#### MEMORY_CONTEXT
```sql
CREATE TABLE MEMORY_CONTEXT (
    context_id VARCHAR2(100) PRIMARY KEY,
    memory_id VARCHAR2(100) NOT NULL,
    related_memory_id VARCHAR2(100) NOT NULL,
    relationship_type VARCHAR2(50), -- 'similar', 'causal', 'temporal'
    relationship_strength NUMBER(3,2)
);
```

### Advanced Features

- **Oracle Text Indexes**: Full-text search trên memory content
- **JSON Support**: Flexible metadata và pattern storage
- **Partitioning**: Tự động partition theo thời gian
- **Triggers**: Automatic updates cho access tracking

## 🧪 Testing và Monitoring

### Test Script

```bash
# Chạy comprehensive test
python test_oracle_advanced_memory.py
```

Test sẽ verify:
- ✅ Oracle connection (thin mode + wallet)
- ✅ Memory storage và retrieval
- ✅ Pattern extraction
- ✅ Performance metrics
- ✅ Integration với Mem0

### Monitoring Metrics

Pipeline cung cấp real-time metrics:

```python
{
    'total_requests': 150,
    'oracle_searches': 120,
    'mem0_searches': 130,
    'memories_injected': 89,
    'patterns_applied': 45,
    'avg_response_time': 0.15,
    'thin_mode_operations': 67,
    'wallet_operations': 83
}
```

### Log Monitoring

```bash
# Check pipeline logs trong Open WebUI
# Tìm các log entries:
# ✅ "Memory context injected into LLM"
# 🔍 "Found X Oracle memories"
# 💾 "Stored memory for user"
# 🧠 "Extracted patterns for user"
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Oracle Connection Failed
```bash
# Check Oracle configuration
cat .env.oracle

# Test Oracle connectivity
sqlplus ADMIN/password@dsn

# Verify wallet location
ls -la oracle_wallet/Wallet_*/
```

#### 2. Pipeline Not Loading
```bash
# Check pipeline file
ls -la webui-data/pipelines/oracle-advanced-memory/

# Check dependencies
python -c "import oracledb; print('OK')"

# Check Open WebUI logs
docker logs open-webui-container
```

#### 3. Memory Not Working
```bash
# Run test script
python test_oracle_advanced_memory.py

# Check database tables
sqlplus ADMIN/password@dsn
SQL> SELECT COUNT(*) FROM USER_MEMORY;

# Check pipeline valves configuration
cat webui-data/pipelines/oracle-advanced-memory/valves.json
```

### Performance Tuning

#### Oracle Connection Pools
```python
# Thin mode pool (for analytics)
thin_pool_config = {
    'min': 3,      # Minimum connections
    'max': 15,     # Maximum connections  
    'increment': 2, # Scale increment
    'timeout': 300  # 5 minute timeout
}

# Wallet pool (for real-time)
wallet_pool_config = {
    'min': 2,      # Minimum connections
    'max': 8,      # Maximum connections
    'increment': 1, # Conservative scaling
    'timeout': 60   # 1 minute timeout
}
```

#### Memory Search Optimization
```python
# Adjust search parameters
valves_config = {
    'memory_relevance_threshold': 0.4,  # Lower = more memories
    'max_oracle_memories': 3,           # Limit context size
    'memory_search_timeout': 2,         # Timeout in seconds
}
```

## 🔐 Security và Compliance

### Data Privacy
- **PII Detection**: Tự động mask sensitive information
- **Encryption**: Sensitive data được encrypt trước khi store
- **Access Control**: User isolation trong database
- **Audit Trail**: Complete logging của memory access

### GDPR Compliance
```python
# Delete user data (GDPR right to be forgotten)
await oracle_service.delete_all_user_data(user_id)
```

### Security Best Practices
- Oracle wallet authentication
- Connection pooling với timeout
- Input validation và sanitization
- Regular security updates

## 📈 Performance Benchmarks

### Typical Performance
- **Memory Search**: 150-200ms (Oracle) + 50-100ms (Mem0)
- **Memory Storage**: 100-150ms (wallet connection)
- **Pattern Extraction**: 200-300ms (background)
- **Context Injection**: < 50ms

### Scalability
- **Concurrent Users**: 50+ users với current pool settings
- **Memory Storage**: Unlimited (Oracle Autonomous DB)
- **Search Performance**: Scales với Oracle Text indexes

## 🛠️ Development và Customization

### Extending Memory Types
```python
# Add new memory type
await oracle_service.store_memory(
    user_id="user123",
    content="Custom content",
    memory_type="custom_type",  # New type
    tags=["custom", "feature"],
    metadata={"custom_field": "value"}
)
```

### Custom Pattern Extractors
```python
class CustomPatternExtractor:
    def analyze_custom_pattern(self, content: str) -> Dict:
        # Custom pattern analysis logic
        return {"custom_pattern": "value"}
```

### Integration với External Systems
```python
# Add external system integration
class ExternalSystemIntegrator:
    async def sync_with_external_system(self, user_id: str, memories: List[Dict]):
        # Sync logic với external systems
        pass
```

## 📚 API Reference

### OracleAdvancedMemoryService

#### Methods

```python
# Initialize service
await service.initialize()

# Store memory
memory_id = await service.store_memory(
    user_id="user123",
    content="Memory content",
    memory_type="conversation",
    confidence=0.8,
    tags=["tag1", "tag2"],
    metadata={"key": "value"}
)

# Search memories
results = await service.search_memories(
    user_id="user123",
    query="search query",
    limit=5,
    min_confidence=0.3
)

# Get user patterns
patterns = await service.get_user_patterns("user123")

# Get metrics
metrics = await service.get_metrics()
```

### Pipeline Configuration

#### Valves Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `enable_oracle_memory` | bool | true | Enable Oracle memory system |
| `enable_mem0_coordination` | bool | true | Enable Mem0 integration |
| `enable_pattern_learning` | bool | true | Enable pattern learning |
| `max_oracle_memories` | int | 3 | Max Oracle memories in context |
| `max_mem0_memories` | int | 2 | Max Mem0 memories in context |
| `memory_relevance_threshold` | float | 0.4 | Minimum relevance score |
| `memory_search_timeout` | int | 2 | Search timeout (seconds) |

## 🤝 Contributing

### Development Setup
```bash
# Setup development environment
python -m venv venv_oracle_dev
source venv_oracle_dev/bin/activate
pip install -r requirements.txt

# Run tests
python test_oracle_advanced_memory.py

# Run deployment
python deploy_oracle_advanced_memory.py
```

### Code Style
- Follow PEP 8
- Use type hints
- Add comprehensive docstrings
- Include error handling
- Write tests for new features

## 📞 Support

### Getting Help
1. **Check logs**: Pipeline logs trong Open WebUI
2. **Run tests**: `python test_oracle_advanced_memory.py`
3. **Verify config**: Check `.env.oracle` và `valves.json`
4. **Database check**: Verify Oracle connectivity

### Common Solutions
- **Connection issues**: Check Oracle wallet và credentials
- **Performance issues**: Adjust connection pool settings
- **Memory not working**: Verify pipeline is enabled
- **Pattern issues**: Check pattern extraction logs

---

## 🎉 Kết luận

Oracle Advanced Memory System biến AccA AI Assistant thành một AI thực sự **thông minh** với khả năng:

- **🧠 Nhớ lâu dài**: Cross-session memory persistence
- **👤 Cá nhân hóa**: Học và thích ứng theo từng user
- **⚡ Hiệu suất cao**: Hybrid thin mode + wallet architecture
- **🔄 Tích hợp mượt mà**: Phối hợp với Mem0 hiện tại
- **📊 Phân tích sâu**: User patterns và behavior analytics

Hệ thống đã sẵn sàng để deploy và sử dụng ngay lập tức!

**Happy Coding! 🚀**