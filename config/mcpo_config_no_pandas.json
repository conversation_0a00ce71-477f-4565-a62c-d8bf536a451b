{"mcpServers": {"document_processing": {"command": "python", "args": ["/app/servers/document_processing/server.py"]}, "vietnamese_language": {"command": "python", "args": ["/app/servers/vietnamese_language/server.py"]}, "web_automation": {"command": "python", "args": ["/app/servers/web_automation/server_playwright.py"]}, "time_utilities": {"command": "python", "args": ["/app/servers/time_utilities/server.py"]}, "weather_service": {"command": "python", "args": ["/app/servers/weather_service/server.py"]}, "filesystem": {"command": "python", "args": ["/app/servers/filesystem/server.py"]}, "wikipedia": {"command": "python", "args": ["/app/servers/wikipedia/server.py"]}, "sqlite": {"command": "python", "args": ["/app/servers/sqlite/server.py"]}, "github": {"command": "python", "args": ["/app/servers/github/server.py"]}, "brave_search": {"command": "python", "args": ["/app/servers/brave_search/server.py"]}, "gemini_search_engine": {"command": "python", "args": ["/app/servers/gemini_search_engine/server.py"], "env": {"GEMINI_API_KEY": "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM", "REDIS_URL": "redis://localhost:6379", "REDIS_DB": "0", "CACHE_TTL": "7200"}}, "mem0_system": {"command": "python", "args": ["/app/servers/mem0_system/server.py"]}, "jina_crawler": {"command": "python", "args": ["/app/servers/jina_crawler/real_smoldocling_integration.py"]}}}