# Hướng Dẫn Setup Gemini 2.5 Flash cho Vision

## Tại Sao Chọn Gemini 2.5 Flash?

### ✅ Ưu Điểm:
- **Nhẹ:** Không cần download model (API-based)
- **Nhanh:** Tốc độ xử lý vision rất nhanh
- **Rẻ:** Chi phí thấp hơn GPT-4o
- **Vision tốt:** Chất lượng nhận diện ảnh cao
- **Không tốn RAM:** Chạy trên cloud

### ❌ So Sánh với Local Models:
| Tiêu Chí | Gemini 2.5 Flash | LLaVA Local |
|----------|------------------|-------------|
| **Size** | 0MB | ~4.7GB |
| **RAM** | 0MB | 8GB+ |
| **Tốc độ** | ⚡ Rất nhanh | 🐌 Chậm |
| **Chi phí** | ~$0.001/image | Miễn phí |
| **Setup** | 2 phút | 30+ phút |

## Bước 1: <PERSON><PERSON>y Google API Key

### 1.1 T<PERSON>y Cập Google AI Studio:
```
https://aistudio.google.com/app/apikey
```

### 1.2 Tạo API Key:
1. Click **"Create API Key"**
2. Chọn project hoặc tạo mới
3. Copy API key (dạng: `AIza...`)

### 1.3 Test API Key:
```bash
curl -H "Content-Type: application/json" \
     -d '{"contents":[{"parts":[{"text":"Hello"}]}]}' \
     -X POST "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=YOUR_API_KEY"
```

## Bước 2: Cấu Hình Open WebUI

### 2.1 Thêm API Key:
```bash
echo "GOOGLE_API_KEY=AIza-your-api-key-here" >> .env.catomanton
```

### 2.2 Restart WebUI:
```bash
docker restart catomanton-webui
sleep 15
```

### 2.3 Kiểm Tra:
```bash
docker logs catomanton-webui | grep -i "google\|gemini" | tail -5
```

## Bước 3: Sử Dụng Gemini Vision

### 3.1 Truy Cập WebUI:
- URL: http://localhost:3000
- Login admin account

### 3.2 Chọn Model:
1. Click dropdown model
2. Tìm **"gemini-2.5-flash"**
3. Select model

### 3.3 Test Vision:
1. **Upload ảnh:** Click 📎
2. **Hỏi:** "Describe this image in detail"
3. **Kết quả:** Gemini sẽ mô tả ảnh chi tiết

## Bước 4: Troubleshooting

### 4.1 Lỗi "API Key Invalid":
```bash
# Kiểm tra API key trong env
grep GOOGLE_API_KEY .env.catomanton

# Test API key trực tiếp
curl "https://generativelanguage.googleapis.com/v1beta/models?key=YOUR_API_KEY"
```

### 4.2 Lỗi "Model Not Found":
1. Đảm bảo chọn đúng **gemini-2.5-flash**
2. Không chọn **gemini-pro** (không có vision)
3. Restart WebUI nếu cần

### 4.3 Lỗi "Image Not Processed":
1. Kiểm tra pipeline hoạt động:
```bash
docker logs catomanton-webui | grep -i "image.*pipeline" | tail -3
```

2. Restart pipeline:
```bash
docker restart catomanton-webui
```

## Bước 5: Tối Ưu Sử Dụng

### 5.1 Best Practices:
- **Format ảnh:** JPEG, PNG, WebP
- **Size:** < 10MB
- **Resolution:** < 4096x4096
- **Prompt:** Càng cụ thể càng tốt

### 5.2 Sample Prompts:
```
"Describe this image in detail"
"What objects do you see in this image?"
"Analyze the colors and composition"
"What text is visible in this image?"
"Identify any people or faces"
```

### 5.3 Cost Optimization:
- Gemini 2.5 Flash: ~$0.001 per image
- Batch multiple questions về cùng 1 ảnh
- Sử dụng specific prompts để giảm response length

## Kết Luận

**Gemini 2.5 Flash là lựa chọn tối ưu cho vision:**
- ✅ Setup nhanh (< 5 phút)
- ✅ Không tốn storage/RAM
- ✅ Tốc độ nhanh
- ✅ Chi phí hợp lý
- ✅ Vision quality cao

**So với LLaVA local:**
- Không cần download 4.7GB
- Không cần 8GB+ RAM
- Nhanh hơn 10x
- Setup đơn giản hơn

**Chỉ cần API key và 2 phút setup!**