#!/bin/bash
# final_monitor.sh - Complete integration monitoring

echo "🚀 <PERSON><PERSON> Crawler + MCPO Integration Status"
echo "=" * 50

echo -e "\n=== Jina Crawler Service ==="
sudo systemctl status jina-crawler --no-pager | head -5
curl -s http://localhost:8001/health | jq . 2>/dev/null || echo "❌ Jina Crawler not responding"

echo -e "\n=== MCPO Service ==="
docker ps | grep mcpo || echo "❌ MCPO container not running"
curl -s http://localhost:5000/health 2>/dev/null || echo "❌ MCPO not responding"

echo -e "\n=== MCP Integration Test ==="
curl -s -X POST http://localhost:5000/jina_crawler/health_check \
  -H "Content-Type: application/json" \
  -d '{"detailed": true}' 2>/dev/null | head -3 || echo "❌ MCP integration failed"

echo -e "\n=== Crawl Test ==="
curl -s -X POST http://localhost:5000/jina_crawler/crawl_url \
  -H "Content-Type: application/json" \
  -d '{"url": "https://httpbin.org/get", "timeout": 5}' 2>/dev/null | head -3 || echo "❌ Crawl test failed"

echo -e "\n=== System Resources ==="
echo "Memory: $(free -h | grep Mem | awk '{print $3"/"$2}')"
echo "Jina Memory: $(sudo systemctl show jina-crawler --property=MemoryCurrent | cut -d= -f2 | numfmt --to=iec)"

echo -e "\n=== Available Tools in Open WebUI ==="
echo "🌐 Access Open WebUI at: http://localhost:3000"
echo "🔧 Available jina_crawler tools:"
echo "   - crawl_url: Fast single URL crawling"
echo "   - crawl_batch: Concurrent batch crawling"
echo "   - search_site: Site-specific search"
echo "   - benchmark_performance: Performance testing"
echo "   - get_crawler_stats: Real-time statistics"
echo "   - health_check: Service health monitoring"
echo "   - extract_content_advanced: Advanced content extraction"
echo "   - crawl_with_tls_bypass: TLS bypass crawling"
echo "   - optimize_arm64_performance: ARM64 optimizations"
echo "   - get_model_info: SmolDocling model information"
