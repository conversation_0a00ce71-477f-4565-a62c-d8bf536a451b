#!/usr/bin/env python3
"""
Optimize Gemini batch processing to use 2.5 Flash directly
Remove redundant Flash Lite layer
"""

import subprocess
import re

def optimize_gemini_processor():
    """Update GeminiProcessor to use Gemini 2.5 Flash instead of Flash Lite"""
    
    print("🔧 Optimizing GeminiProcessor to use Gemini 2.5 Flash...")
    
    # Read current gemini_processor.py
    result = subprocess.run([
        'docker', 'exec', 'jina-crawler-mcp', 'cat', '/app/gemini_processor.py'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ Error reading gemini_processor.py: {result.stderr}")
        return False
    
    content = result.stdout
    
    # Replace Flash Lite with Flash
    content = content.replace(
        'model_name: str = "gemini-2.5-flash-lite"',
        'model_name: str = "gemini-2.5-flash"'
    )
    
    content = content.replace(
        'model_name=os.getenv("GEMINI_MODEL_NAME", "gemini-2.5-flash-lite")',
        'model_name=os.getenv("GEMINI_MODEL_NAME", "gemini-2.5-flash")'
    )
    
    # Update batch processing prompt to be more synthesis-focused
    old_prompt_pattern = r'Process the following web sources and extract key information relevant to the query\.\s*Return structured data for each source in JSON format\.'
    
    new_prompt = '''Process and synthesize the following web sources to provide comprehensive analysis.
Focus on cross-source synthesis, insights, and actionable information relevant to the query.
Return structured data for each source in JSON format with enhanced analysis.'''
    
    content = re.sub(old_prompt_pattern, new_prompt, content, flags=re.MULTILINE)
    
    # Update comments to reflect the change
    content = content.replace(
        'Fast content processor using Google\'s Gemini 2.5 Flash API.',
        'Advanced content processor using Google\'s Gemini 2.5 Flash API for synthesis.'
    )
    
    content = content.replace(
        'Ultra-fast processing (sub-second response times)',
        'High-quality synthesis with comprehensive analysis'
    )
    
    # Write optimized content
    with open('/tmp/gemini_processor_optimized.py', 'w') as f:
        f.write(content)
    
    # Copy back to container
    copy_result = subprocess.run([
        'docker', 'cp', '/tmp/gemini_processor_optimized.py', 'jina-crawler-mcp:/app/gemini_processor.py'
    ], capture_output=True, text=True)
    
    if copy_result.returncode != 0:
        print(f"❌ Error copying optimized file: {copy_result.stderr}")
        return False
    
    print("✅ GeminiProcessor optimized to use Gemini 2.5 Flash")
    return True

def update_batch_processing_flow():
    """Update batch processing to skip redundant Flash Lite processing"""
    
    print("🔧 Updating batch processing flow...")
    
    # Read current batch_crawler_service.py
    result = subprocess.run([
        'docker', 'exec', 'jina-crawler-mcp', 'cat', '/app/ai_search/batch_crawler_service.py'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ Error reading batch_crawler_service.py: {result.stderr}")
        return False
    
    content = result.stdout
    
    # Update comments to reflect the optimization
    content = content.replace(
        '# Step 2: Process all raw content with single Gemini batch call',
        '# Step 2: Process cleaned content with single Gemini 2.5 Flash batch call (synthesis-focused)'
    )
    
    content = content.replace(
        'logger.info(f"✅ BATCH PROCESSING completed: {len(results)} URLs processed with 1 Gemini call instead of {len(urls)}!")',
        'logger.info(f"✅ OPTIMIZED BATCH PROCESSING: {len(results)} URLs synthesized with 1 Gemini 2.5 Flash call!")'
    )
    
    content = content.replace(
        'logger.info(f"💰 API efficiency gain: {len(urls)}x reduction in Gemini requests!")',
        'logger.info(f"💰 Efficiency: Direct synthesis without redundant processing layers!")'
    )
    
    # Write updated content
    with open('/tmp/batch_crawler_service_optimized.py', 'w') as f:
        f.write(content)
    
    # Copy back to container
    copy_result = subprocess.run([
        'docker', 'cp', '/tmp/batch_crawler_service_optimized.py', 'jina-crawler-mcp:/app/ai_search/batch_crawler_service.py'
    ], capture_output=True, text=True)
    
    if copy_result.returncode != 0:
        print(f"❌ Error copying optimized file: {copy_result.stderr}")
        return False
    
    print("✅ Batch processing flow optimized")
    return True

def main():
    """Main optimization function"""
    print("🚀 Starting Gemini batch processing optimization...")
    print("📋 Goal: BeautifulSoup → Gemini 2.5 Flash (direct synthesis)")
    print("❌ Removing: Redundant Flash Lite processing layer")
    print()
    
    success = True
    
    # Step 1: Optimize GeminiProcessor
    if not optimize_gemini_processor():
        success = False
    
    # Step 2: Update batch processing flow
    if not update_batch_processing_flow():
        success = False
    
    if success:
        print("\n🎉 Optimization completed successfully!")
        print("🔄 Restarting container to apply changes...")
        
        # Restart container
        subprocess.run(['docker', 'restart', 'jina-crawler-mcp'])
        print("✅ Container restarted")
        
        print("\n📊 New optimized flow:")
        print("Raw HTML → BeautifulSoup (clean) → Gemini 2.5 Flash (batch synthesis) → Final Answer")
        print("🚀 Benefits: Faster, cheaper, less redundant, better quality")
    else:
        print("\n❌ Optimization failed")

if __name__ == "__main__":
    main()
