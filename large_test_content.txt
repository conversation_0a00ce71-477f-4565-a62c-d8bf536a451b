
=== TRANG 1 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 1 ---


=== TRANG 2 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 2 ---


=== TRANG 3 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 3 ---


=== TRANG 4 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 4 ---


=== TRANG 5 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 5 ---


=== TRANG 6 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 6 ---


=== TRANG 7 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 7 ---


=== TRANG 8 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 8 ---


=== TRANG 9 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 9 ---


=== TRANG 10 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 10 ---


=== TRANG 11 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 11 ---


=== TRANG 12 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 12 ---


=== TRANG 13 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 13 ---


=== TRANG 14 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 14 ---


=== TRANG 15 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 15 ---


=== TRANG 16 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 16 ---


=== TRANG 17 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 17 ---


=== TRANG 18 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 18 ---


=== TRANG 19 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 19 ---


=== TRANG 20 ===

# TÀI LIỆU TEST PERFORMANCE PDF PROCESSING

## Thông tin Model AI

### Gemma 3N Model
- **Context Window**: 8192 tokens
- **Memory Usage**: 6GB VRAM  
- **Performance**: 89.5% accuracy
- **Language Support**: Vietnamese, English, Chinese

### Llama 3.1 Model
- **Context Window**: 131072 tokens (128K)
- **Memory Usage**: 8GB VRAM
- **Performance**: 92.1% accuracy
- **Specialization**: Code generation, reasoning

## Bảng So Sánh Models

| Model | Context | Memory | Accuracy | Speed |
|-------|---------|--------|----------|-------|
| Gemma 3N | 8,192 | 6GB | 89.5% | Fast |
| Llama 3.1 | 131,072 | 8GB | 92.1% | Medium |
| Claude | 200,000 | 12GB | 94.2% | Slow |

## Hướng Dẫn Cài Đặt RAG

### Bước 1: Cài Đặt Ollama
```bash
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve
```

### Bước 2: Download Models
```bash
ollama pull llama3.1:8b
ollama pull gemma2:9b
```

### Bước 3: Tạo Enhanced Models
```bash
# Tạo model với context window lớn hơn
ollama create llama3.1-rag:8b -f Modelfile
```

### Bước 4: Config Open WebUI
- URL: http://localhost:3001
- Backend: Ollama (port 11434)
- RAG: Activate với # symbol

## Tối Ưu Performance

### PDF Processing Optimization
1. **Caching**: Cache processed PDFs
2. **Parallel Processing**: Process multiple pages simultaneously  
3. **Smart Chunking**: Intelligent text segmentation
4. **Vietnamese Support**: Proper Unicode handling

### Memory Management
- **Chunk Size**: 1000 characters optimal
- **Overlap**: 25% between chunks
- **Cache Limit**: 512MB max
- **Workers**: 4 parallel threads

## Test Cases

### Case 1: Simple Query
**Query**: Context window của Gemma 3N?
**Expected**: 8192 tokens

### Case 2: Table Data
**Query**: So sánh memory usage các models?
**Expected**: Gemma 3N: 6GB, Llama 3.1: 8GB, Claude: 12GB

### Case 3: Vietnamese Content
**Query**: Hướng dẫn cài đặt Ollama?
**Expected**: curl -fsSL https://ollama.ai/install.sh | sh

### Case 4: Complex Technical
**Query**: Tối ưu PDF processing như thế nào?
**Expected**: Caching, parallel processing, smart chunking

## Kết Luận

RAG system với PDF optimization có thể cải thiện:
- **Tốc độ**: 3-5x nhanh hơn
- **Accuracy**: Better chunking = better retrieval
- **Memory**: Efficient caching
- **User Experience**: Faster response times

Việc tích hợp cache và parallel processing là key factors để đạt được performance tối ưu trong production environment.

## Vietnamese Text Test

Đây là đoạn văn bản tiếng Việt để test khả năng xử lý Unicode và dấu thanh của hệ thống RAG. 

Các dấu thanh: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ
Các chữ cái đặc biệt: đ ê ế ề ể ễ ệ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

Câu hoàn chỉnh: "Hệ thống RAG cần xử lý được tiếng Việt một cách chính xác và hiệu quả."

--- END PAGE 20 ---

