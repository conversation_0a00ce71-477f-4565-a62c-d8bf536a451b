#!/bin/bash
"""
🔄 RECREATE ORACLE PIPELINE
Completely recreate Oracle Advanced Memory pipeline
"""

echo "🔄 RECREATING ORACLE ADVANCED MEMORY PIPELINE"
echo "============================================================"

# Stop Open WebUI
echo "🛑 Stopping Open WebUI container..."
docker stop open-webui-mcpo

# Remove old pipeline directory
echo "🗑️ Removing old pipeline directory..."
docker run --rm -v $(pwd)/mem0-owui/webui-data:/data alpine rm -rf /data/pipelines/oracle-advanced-memory

# Recreate pipeline directory
echo "📁 Creating fresh pipeline directory..."
mkdir -p mem0-owui/webui-data/pipelines/oracle-advanced-memory

# Copy fresh files
echo "📋 Copying fresh pipeline files..."
cp mem0-owui/webui-data/pipelines/oracle-advanced-memory/oracle-advanced-memory.py mem0-owui/webui-data/pipelines/oracle-advanced-memory/
cp mem0-owui/webui-data/pipelines/oracle-advanced-memory/valves.json mem0-owui/webui-data/pipelines/oracle-advanced-memory/

# Set proper permissions
echo "🔧 Setting permissions..."
chmod 644 mem0-owui/webui-data/pipelines/oracle-advanced-memory/*

# Start Open WebUI
echo "🚀 Starting Open WebUI container..."
docker start open-webui-mcpo

# Wait for startup
echo "⏳ Waiting for Open WebUI to start..."
sleep 20

# Check status
echo "✅ Checking container status..."
docker ps | grep open-webui-mcpo

echo ""
echo "🎉 PIPELINE RECREATION COMPLETE!"
echo "📝 Next steps:"
echo "1. Open http://localhost:3000"
echo "2. Go to Admin → Pipelines"
echo "3. Oracle Advanced Memory should have fresh valves"
echo "4. Look for new LLM override valves:"
echo "   - override_llm_model"
echo "   - target_llm_model"
echo "   - target_llm_provider"
echo "   - llm_temperature"
echo "   - llm_max_tokens"
echo "   - llm_top_p"
