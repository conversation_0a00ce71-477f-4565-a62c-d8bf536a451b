#!/bin/bash

# Oracle Advanced Memory System - Quick Start Script
# AccA AI Assistant Platform
# Author: AccA System Team

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Print banner
print_banner() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║    🧠 Oracle Advanced Memory System - Quick Start 🚀        ║"
    echo "║                                                              ║"
    echo "║    AccA AI Assistant Platform                                ║"
    echo "║    Advanced Memory with Oracle Autonomous DB                 ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Print step header
print_step() {
    echo -e "\n${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
}

# Print success message
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Print warning message
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Print error message
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Print info message
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if file exists
check_file() {
    if [ -f "$1" ]; then
        print_success "Found: $1"
        return 0
    else
        print_error "Missing: $1"
        return 1
    fi
}

# Check if directory exists
check_directory() {
    if [ -d "$1" ]; then
        print_success "Found directory: $1"
        return 0
    else
        print_error "Missing directory: $1"
        return 1
    fi
}

# Check prerequisites
check_prerequisites() {
    print_step "🔍 STEP 1: Checking Prerequisites"
    
    local all_good=true
    
    # Check Python
    if command -v python3 &> /dev/null; then
        python_version=$(python3 --version)
        print_success "Python: $python_version"
    else
        print_error "Python 3 is not installed"
        all_good=false
    fi
    
    # Check pip
    if command -v pip3 &> /dev/null; then
        print_success "pip3 is available"
    else
        print_error "pip3 is not installed"
        all_good=false
    fi
    
    # Check Oracle configuration
    if check_file ".env.oracle"; then
        print_info "Oracle configuration found"
    else
        print_error "Oracle configuration missing - please create .env.oracle"
        all_good=false
    fi
    
    # Check Oracle wallet
    if check_directory "oracle_wallet"; then
        wallet_files=$(find oracle_wallet -name "*.sso" -o -name "*.p12" | wc -l)
        if [ "$wallet_files" -gt 0 ]; then
            print_success "Oracle wallet files found ($wallet_files files)"
        else
            print_warning "Oracle wallet directory exists but no wallet files found"
        fi
    else
        print_error "Oracle wallet directory missing"
        all_good=false
    fi
    
    # Check if we're in the right directory
    if [ -f "oracle_advanced_memory_schema.sql" ] && [ -f "deploy_oracle_advanced_memory.py" ]; then
        print_success "Oracle Advanced Memory System files found"
    else
        print_error "Oracle Advanced Memory System files not found - are you in the right directory?"
        all_good=false
    fi
    
    if [ "$all_good" = false ]; then
        print_error "Prerequisites check failed. Please fix the issues above."
        exit 1
    fi
    
    print_success "All prerequisites satisfied!"
}

# Install dependencies
install_dependencies() {
    print_step "📦 STEP 2: Installing Dependencies"
    
    print_info "Installing Python dependencies..."
    
    # Create requirements file if it doesn't exist
    if [ ! -f "requirements_oracle_memory.txt" ]; then
        cat > requirements_oracle_memory.txt << EOF
oracledb>=1.4.0
pydantic>=2.0.0
requests>=2.28.0
asyncio-mqtt>=0.11.0
python-dotenv>=1.0.0
EOF
        print_info "Created requirements_oracle_memory.txt"
    fi
    
    # Install dependencies
    if pip3 install -r requirements_oracle_memory.txt; then
        print_success "Dependencies installed successfully"
    else
        print_error "Failed to install dependencies"
        exit 1
    fi
    
    # Verify oracledb installation
    if python3 -c "import oracledb; print(f'oracledb version: {oracledb.__version__}')" 2>/dev/null; then
        print_success "oracledb library verified"
    else
        print_error "oracledb library installation failed"
        exit 1
    fi
}

# Deploy system
deploy_system() {
    print_step "🚀 STEP 3: Deploying Oracle Advanced Memory System"
    
    print_info "Running deployment script..."
    
    if python3 deploy_oracle_advanced_memory.py; then
        print_success "Deployment script completed successfully"
    else
        print_error "Deployment script failed"
        exit 1
    fi
}

# Deploy database schema
deploy_database() {
    print_step "🗄️ STEP 4: Database Schema Deployment"
    
    print_info "Database schema deployment script has been created: deploy_schema.sql"
    print_warning "You need to manually deploy the database schema using Oracle SQL*Plus"
    
    echo -e "\n${YELLOW}To deploy the database schema, run:${NC}"
    echo -e "${CYAN}sqlplus /nolog @deploy_schema.sql${NC}"
    
    echo -e "\n${YELLOW}Or connect to your Oracle database and run:${NC}"
    echo -e "${CYAN}@oracle_advanced_memory_schema.sql${NC}"
    
    read -p "$(echo -e ${YELLOW}Have you deployed the database schema? [y/N]: ${NC})" -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "Please deploy the database schema before continuing"
        print_info "You can run this script again after deploying the schema"
        exit 0
    fi
    
    print_success "Database schema deployment confirmed"
}

# Test system
test_system() {
    print_step "🧪 STEP 5: Testing System"
    
    print_info "Running system tests..."
    
    if [ -f "test_oracle_advanced_memory.py" ]; then
        if python3 test_oracle_advanced_memory.py; then
            print_success "All tests passed!"
        else
            print_warning "Some tests failed - check the output above"
            print_info "The system may still work, but there might be configuration issues"
        fi
    else
        print_warning "Test script not found - skipping tests"
    fi
}

# Setup Open WebUI integration
setup_openwebui() {
    print_step "🌐 STEP 6: Open WebUI Integration"
    
    # Check if pipeline directory exists
    pipeline_dir="webui-data/pipelines/oracle-advanced-memory"
    if check_directory "$pipeline_dir"; then
        print_success "Pipeline deployed to: $pipeline_dir"
        
        # Check pipeline files
        if check_file "$pipeline_dir/oracle-advanced-memory.py" && check_file "$pipeline_dir/valves.json"; then
            print_success "Pipeline files are ready"
        else
            print_error "Pipeline files are missing"
            exit 1
        fi
    else
        print_error "Pipeline directory not found"
        exit 1
    fi
    
    print_info "To enable the pipeline in Open WebUI:"
    echo -e "${CYAN}1. Open Open WebUI Admin Panel${NC}"
    echo -e "${CYAN}2. Go to Pipelines section${NC}"
    echo -e "${CYAN}3. Find 'oracle-advanced-memory' pipeline${NC}"
    echo -e "${CYAN}4. Enable the pipeline${NC}"
    echo -e "${CYAN}5. Configure valves if needed${NC}"
    
    print_warning "Make sure to update the Oracle credentials in the pipeline valves!"
}

# Show completion summary
show_completion() {
    print_step "🎉 DEPLOYMENT COMPLETED!"
    
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║  🎊 Oracle Advanced Memory System Successfully Deployed! 🎊  ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    print_info "What was deployed:"
    echo -e "${CYAN}✅ Database Schema: oracle_advanced_memory_schema.sql${NC}"
    echo -e "${CYAN}✅ Memory Service: oracle_advanced_memory_service.py${NC}"
    echo -e "${CYAN}✅ Pipeline: webui-data/pipelines/oracle-advanced-memory/${NC}"
    echo -e "${CYAN}✅ Configuration: valves.json${NC}"
    echo -e "${CYAN}✅ Test Script: test_oracle_advanced_memory.py${NC}"
    echo -e "${CYAN}✅ Documentation: ORACLE_ADVANCED_MEMORY_SYSTEM.md${NC}"
    
    print_info "Next steps:"
    echo -e "${YELLOW}1. Enable pipeline in Open WebUI Admin Panel${NC}"
    echo -e "${YELLOW}2. Configure Oracle credentials in pipeline valves${NC}"
    echo -e "${YELLOW}3. Start chatting with enhanced memory!${NC}"
    
    print_info "Monitoring:"
    echo -e "${CYAN}• Check pipeline logs in Open WebUI${NC}"
    echo -e "${CYAN}• Run: python3 test_oracle_advanced_memory.py${NC}"
    echo -e "${CYAN}• Monitor Oracle database performance${NC}"
    
    print_info "Documentation:"
    echo -e "${CYAN}• Read: ORACLE_ADVANCED_MEMORY_SYSTEM.md${NC}"
    echo -e "${CYAN}• Check: ORACLE_MEMORY_DEPLOYMENT_REPORT.md${NC}"
    
    echo -e "\n${PURPLE}🧠 Your AccA AI Assistant now has advanced memory capabilities!${NC}"
    echo -e "${PURPLE}🚀 Enjoy intelligent, personalized conversations!${NC}"
}

# Main execution
main() {
    print_banner
    
    print_info "Starting Oracle Advanced Memory System deployment..."
    print_info "This script will guide you through the complete setup process."
    
    # Ask for confirmation
    read -p "$(echo -e ${YELLOW}Do you want to proceed with the deployment? [Y/n]: ${NC})" -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        print_info "Deployment cancelled by user"
        exit 0
    fi
    
    # Execute deployment steps
    check_prerequisites
    install_dependencies
    deploy_system
    deploy_database
    test_system
    setup_openwebui
    show_completion
    
    print_success "Quick start completed successfully!"
}

# Handle script interruption
trap 'echo -e "\n${RED}❌ Deployment interrupted by user${NC}"; exit 1' INT

# Run main function
main "$@"