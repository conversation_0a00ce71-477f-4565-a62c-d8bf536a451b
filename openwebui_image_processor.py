#!/usr/bin/env python3
"""
Open WebUI Image Processing Tool
Kết nối Docling server với Open WebUI để xử lý hình ảnh
"""

import requests
import base64
import json
from typing import Dict, Any, Optional
import tempfile
import os

class OpenWebUIImageProcessor:
    def __init__(self, docling_url: str = "http://localhost:5001"):
        self.docling_url = docling_url
        
    def process_image_with_docling(self, image_data: bytes, filename: str) -> Dict[str, Any]:
        """Xử lý hình ảnh qua Docling server"""
        try:
            # Tạo file tạm thời
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as tmp_file:
                tmp_file.write(image_data)
                tmp_file_path = tmp_file.name
            
            # Gửi đến Docling server
            with open(tmp_file_path, 'rb') as f:
                files = {'files': (filename, f, 'application/octet-stream')}
                data = {
                    'ocr_engine': 'tesseract',
                    'ocr_lang': ['vie', 'eng']  # Hỗ trợ tiếng Việt và tiếng Anh
                }
                
                response = requests.post(
                    f"{self.docling_url}/extract_tables",
                    files=files,
                    data=data,
                    timeout=30
                )
            
            # Xóa file tạm
            os.unlink(tmp_file_path)
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "content": result.get("content", []),
                    "tables": result.get("tables", []),
                    "total_tables": result.get("total_tables", 0),
                    "method": "docling_via_openwebui"
                }
            else:
                return {
                    "success": False,
                    "error": f"Docling server error: {response.status_code}",
                    "content": [],
                    "tables": []
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"Image processing error: {str(e)}",
                "content": [],
                "tables": []
            }

# Open WebUI Function để tích hợp
def process_image_for_chat(image_base64: str, filename: str = "image.png") -> str:
    """
    Function cho Open WebUI để xử lý hình ảnh
    """
    try:
        # Decode base64 image
        image_data = base64.b64decode(image_base64)
        
        # Xử lý qua Docling
        processor = OpenWebUIImageProcessor()
        result = processor.process_image_with_docling(image_data, filename)
        
        if result["success"]:
            response_text = "🖼️ **Đã phân tích hình ảnh thành công:**\n\n"
            
            # Thêm nội dung text
            if result["content"]:
                response_text += "📝 **Nội dung text:**\n"
                for content in result["content"]:
                    response_text += f"{content}\n\n"
            
            # Thêm bảng
            if result["tables"]:
                response_text += f"📊 **Tìm thấy {result['total_tables']} bảng:**\n\n"
                for i, table in enumerate(result["tables"]):
                    response_text += f"**Bảng {i+1}:**\n"
                    response_text += f"{table['markdown']}\n\n"
            
            if not result["content"] and not result["tables"]:
                response_text += "ℹ️ Không tìm thấy text hoặc bảng trong hình ảnh này."
                
            return response_text
        else:
            return f"❌ **Lỗi xử lý hình ảnh:** {result['error']}"
            
    except Exception as e:
        return f"❌ **Lỗi:** {str(e)}"

if __name__ == "__main__":
    # Test function
    print("🧪 Testing image processor...")
    processor = OpenWebUIImageProcessor()
    
    # Test connection to Docling
    try:
        response = requests.get("http://localhost:5001/health")
        if response.status_code == 200:
            print("✅ Docling server connection successful")
        else:
            print("❌ Docling server connection failed")
    except:
        print("❌ Cannot connect to Docling server")