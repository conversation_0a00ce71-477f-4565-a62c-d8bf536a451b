#!/bin/bash

# REMOVE LARGE FILES FROM GIT: Remove large files and directories from git tracking
set -e

echo "🧹 Removing large files and directories from Git tracking..."

# Check if .git_disabled exists
if [ ! -d ".git_disabled" ]; then
    echo "❌ Error: .git_disabled directory not found"
    exit 1
fi

# Temporarily enable git repository
echo "🔌 Enabling git repository..."
mv .git_disabled .git

# Find and remove all large directories and files from git tracking
echo "🗑️  Removing large directories and files from git tracking..."
git rm -r --cached mem0-owui webui-data backend venv_oracle docling_env openwebui_backup_*.tar.gz 2>/dev/null || true
git reset HEAD mem0-owui webui-data backend venv_oracle docling_env openwebui_backup_*.tar.gz 2>/dev/null || true

# Add updated .gitignore to git
echo "📝 Adding updated .gitignore to git..."
cat >> .gitignore << 'EOF'

# Ignore large directories and files
mem0-owui/
webui-data/
backend/
venv_oracle/
docling_env/
openwebui_backup_*.tar.gz
EOF
git add .gitignore

# Commit the changes
echo "💾 Committing changes..."
git commit -m "🧹 Remove large files and directories from git tracking

- Remove mem0-owui/, webui-data/, backend/, venv_oracle/, docling_env/ from git tracking
- Remove openwebui_backup_*.tar.gz from git tracking
- Keep local files intact
- Update .gitignore to prevent future tracking"

# Disable git repository again
echo "🔌 Disabling git repository..."
mv .git .git_disabled

echo ""
echo "✅ Large files and directories removed from git tracking!"
echo ""
echo "📊 Current status:"
echo "• ✅ Large files and directories are no longer tracked by git"
echo "• ✅ Local files are preserved"
echo "• ✅ .gitignore is updated to prevent future tracking"
echo ""
echo "🔧 To verify the changes, you can:"
echo "   1. Rename .git_disabled back to .git temporarily"
echo "   2. Run 'git status' to see that large files and directories are no longer listed"
echo "   3. Rename .git back to .git_disabled"