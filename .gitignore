# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# poetry
#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control
#poetry.lock

# pdm
#   Similar to Pipfile.lock, it is generally recommended to include pdm.lock in version control.
#pdm.lock
#   pdm stores project-wide configurations in .pdm.toml, but it is recommended to not include it
#   in version control.
#   https://pdm.fming.dev/#use-with-ide
.pdm.toml

# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
/venv/

# Specific virtual environments
docling_env/
enhanced_table_env/
venv_mem0/
venv_oracle/
venv_torch/
qdrant_indexer_env/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be added to the global gitignore or merged into this project gitignore.  For a PyCharm
#  project, it is also recommended to add the content_root and source_roots to the gitignore.
#  Reference: https://intellij-support.jetbrains.com/hc/en/articles/206544839

.idea/
*.swp
*.swo

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Flutter/Dart
ai_assistant_mobile/build/
ai_assistant_mobile/.dart_tool/
ai_assistant_mobile/.flutter-plugins
ai_assistant_mobile/.flutter-plugins-dependencies
ai_assistant_mobile/.packages
ai_assistant_mobile/.pub-cache/
ai_assistant_mobile/.pub/
ai_assistant_mobile/pubspec.lock

ai_assistant_mobile_new/build/
ai_assistant_mobile_new/.dart_tool/
ai_assistant_mobile_new/.flutter-plugins
ai_assistant_mobile_new/.flutter-plugins-dependencies
ai_assistant_mobile_new/.packages
ai_assistant_mobile_new/.pub-cache/
ai_assistant_mobile_new/.pub/
ai_assistant_mobile_new/pubspec.lock

# Android
ai_assistant_mobile/android/.gradle/
ai_assistant_mobile/android/captures/
ai_assistant_mobile/android/gradlew
ai_assistant_mobile/android/gradlew.bat
ai_assistant_mobile/android/local.properties
ai_assistant_mobile/android/key.properties

ai_assistant_mobile_new/android/.gradle/
ai_assistant_mobile_new/android/captures/
ai_assistant_mobile_new/android/gradlew
ai_assistant_mobile_new/android/gradlew.bat
ai_assistant_mobile_new/android/local.properties
ai_assistant_mobile_new/android/key.properties

# iOS
ai_assistant_mobile/ios/Flutter/App.framework
ai_assistant_mobile/ios/Flutter/Flutter.framework
ai_assistant_mobile/ios/Flutter/Flutter.podspec
ai_assistant_mobile/ios/Flutter/Generated.xcconfig
ai_assistant_mobile/ios/Flutter/app.flx
ai_assistant_mobile/ios/Flutter/app.zip
ai_assistant_mobile/ios/Flutter/flutter_assets/
ai_assistant_mobile/ios/ServiceDefinitions.json
ai_assistant_mobile/ios/Runner/GeneratedPluginRegistrant.*

ai_assistant_mobile_new/ios/Flutter/App.framework
ai_assistant_mobile_new/ios/Flutter/Flutter.framework
ai_assistant_mobile_new/ios/Flutter/Flutter.podspec
ai_assistant_mobile_new/ios/Flutter/Generated.xcconfig
ai_assistant_mobile_new/ios/Flutter/app.flx
ai_assistant_mobile_new/ios/Flutter/app.zip
ai_assistant_mobile_new/ios/Flutter/flutter_assets/
ai_assistant_mobile_new/ios/ServiceDefinitions.json
ai_assistant_mobile_new/ios/Runner/GeneratedPluginRegistrant.*

# Local data
/data/
rag_data/
vector_store/
uploaded_files/
*.db
*.sqlite

# Models
*.gguf
*.bin
*.model
*.tflite
models/

# Logs
*.log

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Sensitive files
config.json
.env
secrets.txt
api_keys.txt

# Temporary files
temp/
tmp/
*.tmp

# Memory bank (project intelligence and context - now included)
# memory-bank/ - REMOVED: Memory bank is essential project documentation

# Docker
Dockerfile.bak
docker-compose.override.yml llama-cpp-migration/llama.cpp/

# === RAG & PROCESSING FILES ===
# RAG chunks and processed content
optimized_rag_chunks/
rag_chunks/
extracted_tables/
webui_ready_files/
ocr_corrected_files/
enhanced_files/

# Upload directories
/tmp/webui_uploads/
/tmp/webui_uploads_auto_corrected/

# Processing logs
auto_pipeline.log
auto_pipeline_daemon.log
table_processing.log
ocr_processing.log
*.log

# Process IDs
*.pid
auto_pipeline.pid

# Temporary processing files
temp_*.txt
temp_*.pdf
temp_*.docx
processing_temp/

# Knowledge files (actual documents)
knowledge_files/*.pdf
knowledge_files/*.docx
knowledge_files/*.doc
knowledge_files/*.txt
# Keep example files
!knowledge_files/README.md
!knowledge_files/.gitkeep

# Enhanced table processor outputs
enhanced_*.txt
table_*.txt
chunk_*.txt

# OCR correction outputs
ocr_corrected_*.txt
corrections_log.json
ocr_corrections_log.json

# Database files
*.db
*.sqlite
*.sqlite3

# Environment files
.env
.env.local
.env.production

# Node modules (if any)
node_modules/

# Backup files
*.bak
*.backup
*_backup.*

# Llama.cpp build and Git artifacts
llama-cpp-migration/llama.cpp/build/
llama-cpp-migration/llama.cpp/.git/
llama-cpp-migration/llama.cpp/.gitmodules
llama-cpp-migration/llama.cpp/CMakeCache.txt
llama-cpp-migration/llama.cpp/compile_commands.json

# Model files (too large for Git)
models/*.gguf
models/*.bin
models/*.safetensors

# Temporary files
*.tmp
.DS_Store

# Completely ignore llama-cpp-migration
llama-cpp-migration/
**/llama-cpp-migration/

# Models are downloaded at runtime
models/*.gguf
models/*.bin
models/*.safetensors

# Coolify-specific ignores
.coolify/

# Nested git repositories
*/.git
!.git/

# Specific nested repositories to ignore
docling-serve/
mcp-integration/servers/pandas_mcp/
mem0-owui/

# Ignore all virtual environments, node_modules, __pycache__, and nested git repos
**/venv/
**/*_env/
**/node_modules/
**/__pycache__/
**/.git/

# Ignore all virtual environments, node_modules, __pycache__, and nested git repos
**/venv/
**/*_env/
**/node_modules/
**/__pycache__/
**/.git/

# Ignore large directories and files
mem0-owui/
webui-data/
backend/
venv_oracle/
docling_env/
openwebui_backup_*.tar.gz

# Ignore large directories and files
mem0-owui/
webui-data/
backend/
venv_oracle/
docling_env/
openwebui_backup_*.tar.gz

# Ignore all virtual environments, node_modules, __pycache__, and nested git repos
**/venv/
**/*_env/
**/node_modules/
**/__pycache__/
**/.git/

# Ignore large directories and files
mem0-owui/
webui-data/
backend/
venv_oracle/
docling_env/
openwebui_backup_*.tar.gz
