import re

# Đọc file gốc
with open('/app/pipelines/oracle-advanced-memory.py', 'r') as f:
    content = f.read()

# Loại bỏ phần init_oracle_client (thick mode)
content = re.sub(r'\s*# Initialize wallet pool\s*\n\s*if self\.config\.get\(\'oracle_wallet_location\'\):\s*\n\s*oracledb\.init_oracle_client\(config_dir=self\.config\[\'oracle_wallet_location\'\]\)\s*\n', '', content)

# Loại bỏ wallet_pool và chỉ giữ thin_pool
content = re.sub(r'\s*self\.wallet_pool = oracledb\.create_pool\(\s*\n\s*user=self\.config\[\'oracle_user\'\],\s*\n\s*password=self\.config\[\'oracle_password\'\],\s*\n\s*dsn=self\.config\[\'oracle_dsn\'\],\s*\n\s*min=1, max=5, increment=1,\s*\n\s*ping_interval=30, timeout=60\s*\n\s*\)', '', content)

# Sửa comment
content = content.replace('# Initialize thin mode pool', '# Initialize Oracle connection pool (thin mode only)')

# Ghi lại file
with open('/app/pipelines/oracle-advanced-memory.py', 'w') as f:
    f.write(content)

print("✅ Oracle Advanced Memory đã được sửa để chỉ sử dụng thin mode")
