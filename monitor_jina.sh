#!/bin/bash
# monitor_jina.sh

echo "=== Jina Crawler Service Status ==="
sudo systemctl status jina-crawler --no-pager | head -10

echo -e "\n=== Jina Crawler Health Check ==="
curl -s http://localhost:8001/health | jq . 2>/dev/null || echo "Service not responding"

echo -e "\n=== MCP Integration Test ==="
curl -s -X POST http://localhost:5000/jina_crawler/health_check \
  -H "Content-Type: application/json" \
  -d '{"detailed": true}' 2>/dev/null || echo "MCP integration not responding"

echo -e "\n=== System Resources ==="
echo "Memory: $(free -h | grep Mem | awk '{print $3"/"$2}')"
echo "Disk: $(df -h / | tail -1 | awk '{print $3"/"$2" ("$5")"}')"

echo -e "\n=== Service Logs (last 5 lines) ==="
sudo journalctl -u jina-crawler --no-pager -n 5
