"""
title: AccA Mem0 Memory Filter Pipeline
author: AccA Team
date: 2025-01-15
version: 1.0
license: MIT
description: A filter pipeline that integrates mem0 for conversation memory management
requirements: mem0ai, qdrant-client, google-generativeai
"""

from typing import List, Optional, Dict, Any
import asyncio
import logging
from pydantic import BaseModel, Field
import json

try:
    from mem0 import Memory
    import google.generativeai as genai
    from qdrant_client import QdrantClient
    MEM0_AVAILABLE = True
except ImportError as e:
    MEM0_AVAILABLE = False
    IMPORT_ERROR = str(e)

class Pipeline:
    class Valves(BaseModel):
        # Mem0 Configuration
        mem0_enabled: bool = Field(
            default=True,
            description="Enable/disable mem0 memory integration"
        )
        
        # Gemini API Configuration
        gemini_api_key: str = Field(
            default="",
            description="Google Gemini API key for embeddings and LLM"
        )
        
        # Qdrant Configuration
        qdrant_host: str = Field(
            default="localhost",
            description="Qdrant server host"
        )
        qdrant_port: int = Field(
            default=6333,
            description="Qdrant server port"
        )
        
        # Memory Configuration
        memory_relevance_threshold: float = Field(
            default=0.2,
            description="Minimum relevance score for memory retrieval (0.0-1.0)"
        )
        max_memories_to_inject: int = Field(
            default=3,
            description="Maximum number of memories to inject into context"
        )
        memory_search_limit: int = Field(
            default=10,
            description="Number of memories to search before filtering"
        )
        
        # Debug Configuration
        debug_logging: bool = Field(
            default=False,
            description="Enable detailed debug logging"
        )

    def __init__(self):
        self.type = "filter"
        self.name = "AccA Mem0 Memory Filter"
        self.valves = self.Valves()
        self.memory_client = None
        self.logger = self._setup_logging()
        
    def _setup_logging(self):
        """Setup logging configuration"""
        logger = logging.getLogger(f"acca_mem0_filter")
        logger.setLevel(logging.DEBUG if self.valves.debug_logging else logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger

    async def on_startup(self):
        """Initialize mem0 client on startup"""
        if not MEM0_AVAILABLE:
            self.logger.error(f"❌ Mem0 dependencies not available: {IMPORT_ERROR}")
            return
            
        if not self.valves.mem0_enabled:
            self.logger.info("🔄 Mem0 integration disabled via valves")
            return
            
        if not self.valves.gemini_api_key:
            self.logger.warning("⚠️ Gemini API key not configured")
            return
            
        try:
            # Configure Gemini API
            genai.configure(api_key=self.valves.gemini_api_key)
            
            # Initialize mem0 with Qdrant and Gemini
            config = {
                "vector_store": {
                    "provider": "qdrant",
                    "config": {
                        "host": self.valves.qdrant_host,
                        "port": self.valves.qdrant_port,
                        "collection_name": "acca_memory"
                    }
                },
                "embedder": {
                    "provider": "google_ai",
                    "config": {
                        "model": "models/text-embedding-004"
                    }
                },
                "llm": {
                    "provider": "google_ai", 
                    "config": {
                        "model": "models/gemini-2.0-flash-exp"
                    }
                }
            }
            
            self.memory_client = Memory(config=config)
            self.logger.info("✅ Mem0 client initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize mem0 client: {str(e)}")
            self.memory_client = None

    async def on_shutdown(self):
        """Cleanup on shutdown"""
        if self.memory_client:
            try:
                # Mem0 doesn't have explicit close method, just clear reference
                self.memory_client = None
                self.logger.info("🔄 Mem0 client cleaned up")
            except Exception as e:
                self.logger.error(f"❌ Error during cleanup: {str(e)}")

    def _extract_user_id(self, __user__: Dict[str, Any]) -> str:
        """Extract user ID from user context"""
        if isinstance(__user__, dict):
            return __user__.get("id", "default_user")
        return "default_user"

    async def _search_memories(self, query: str, user_id: str) -> List[Dict[str, Any]]:
        """Search for relevant memories"""
        if not self.memory_client:
            return []
            
        try:
            # Search memories with higher limit for better filtering
            memories = self.memory_client.search(
                query=query,
                user_id=user_id,
                limit=self.valves.memory_search_limit
            )
            
            if self.valves.debug_logging:
                self.logger.debug(f"🔍 Found {len(memories)} memories for query: {query[:100]}...")
            
            # Filter by relevance threshold
            relevant_memories = []
            for memory in memories:
                score = memory.get('score', 0.0)
                if score >= self.valves.memory_relevance_threshold:
                    relevant_memories.append(memory)
                    
            if self.valves.debug_logging:
                self.logger.debug(f"✅ Filtered to {len(relevant_memories)} relevant memories (threshold: {self.valves.memory_relevance_threshold})")
                
            # Limit to max memories to inject
            return relevant_memories[:self.valves.max_memories_to_inject]
            
        except Exception as e:
            self.logger.error(f"❌ Error searching memories: {str(e)}")
            return []

    async def _add_memory(self, message: str, user_id: str) -> None:
        """Add message to memory"""
        if not self.memory_client:
            return
            
        try:
            self.memory_client.add(
                messages=[{"role": "user", "content": message}],
                user_id=user_id
            )
            
            if self.valves.debug_logging:
                self.logger.debug(f"💾 Added memory for user {user_id}: {message[:100]}...")
                
        except Exception as e:
            self.logger.error(f"❌ Error adding memory: {str(e)}")

    def _inject_memory_context(self, messages: List[Dict[str, Any]], memories: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Inject memory context into messages"""
        if not memories:
            return messages
            
        # Create memory context
        memory_context = "Previous conversation context:\n"
        for i, memory in enumerate(memories, 1):
            memory_text = memory.get('memory', memory.get('text', ''))
            score = memory.get('score', 0.0)
            memory_context += f"{i}. {memory_text} (relevance: {score:.2f})\n"
            
        # Find system message or create one
        system_message_found = False
        for message in messages:
            if message.get('role') == 'system':
                # Append to existing system message
                message['content'] += f"\n\n{memory_context}"
                system_message_found = True
                break
                
        if not system_message_found:
            # Insert new system message at the beginning
            system_message = {
                "role": "system",
                "content": f"You are a helpful AI assistant with access to conversation history.\n\n{memory_context}"
            }
            messages.insert(0, system_message)
            
        if self.valves.debug_logging:
            self.logger.debug(f"💉 Injected {len(memories)} memories into context")
            
        return messages

    async def inlet(self, body: Dict[str, Any], __user__: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Process incoming messages - inject memory context"""
        if not self.valves.mem0_enabled or not self.memory_client:
            return body
            
        try:
            messages = body.get("messages", [])
            if not messages:
                return body
                
            user_id = self._extract_user_id(__user__ or {})
            
            # Get the latest user message for memory search
            latest_message = None
            for message in reversed(messages):
                if message.get('role') == 'user':
                    latest_message = message.get('content', '')
                    break
                    
            if not latest_message:
                return body
                
            # Search for relevant memories
            memories = await self._search_memories(latest_message, user_id)
            
            # Inject memory context into messages
            if memories:
                body["messages"] = self._inject_memory_context(messages, memories)
                
            return body
            
        except Exception as e:
            self.logger.error(f"❌ Error in inlet: {str(e)}")
            return body

    async def outlet(self, body: Dict[str, Any], __user__: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Process outgoing messages - store in memory"""
        if not self.valves.mem0_enabled or not self.memory_client:
            return body
            
        try:
            # Extract the conversation for memory storage
            messages = body.get("messages", [])
            if not messages:
                return body
                
            user_id = self._extract_user_id(__user__ or {})
            
            # Find the latest user message and assistant response
            user_message = None
            assistant_message = None
            
            for message in reversed(messages):
                if message.get('role') == 'user' and not user_message:
                    user_message = message.get('content', '')
                elif message.get('role') == 'assistant' and not assistant_message:
                    assistant_message = message.get('content', '')
                    
            # Store the conversation in memory
            if user_message:
                conversation = f"User: {user_message}"
                if assistant_message:
                    conversation += f"\nAssistant: {assistant_message}"
                    
                await self._add_memory(conversation, user_id)
                
            return body
            
        except Exception as e:
            self.logger.error(f"❌ Error in outlet: {str(e)}")
            return body