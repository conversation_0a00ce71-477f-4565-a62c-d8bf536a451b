# Hướng Dẫn Sử Dụng Valves cho Open WebUI Filters

## Tổng Quan

**Valves** là tính năng cấu hình của Open WebUI cho phép bạn tùy chỉnh các thiết lập cho từng Filter function một cách trực quan thông qua giao diện web. Thay vì phải chỉnh sửa code, bạn có thể điều chỉnh các tham số hoạt động của filters ngay trong Open WebUI.

## Cài Đặt và Kích Hoạt

### 1. Upload Filters với Valves Support

```bash
# Kiểm tra filters có hỗ trợ Valves
python3 validate_openwebui_filters_with_valves.py
```

Upload các file sau vào Open WebUI:
- `openwebui_filters/memory_manager_filter.py` - Memory Manager với Valves
- `openwebui_filters/image_processor_filter.py` - Image Processor với Valves

### 2. <PERSON><PERSON><PERSON> Ho<PERSON>t Filters trong Open WebUI

1. Vào **Settings** → **Functions**
2. Tì<PERSON> và enable:
   - **Memory Manager** (ID: `memory_manager`)
   - **Image Processor** (ID: `image_processor`)

### 3. Truy Cập Valves Settings

1. Sau khi enable filters, click vào **⚙️ Settings** bên cạnh tên filter
2. Giao diện Valves sẽ hiển thị các tùy chọn cấu hình

## Memory Manager Valves

### Cấu Hình Cơ Bản

| Thiết Lập | Mặc Định | Mô Tả |
|-----------|----------|--------|
| **Max Search Results** | 10 | Số kết quả tối đa khi tìm kiếm memory (1-50) |
| **Memory Search Limit Chat** | 3 | Số memories hiển thị trong chat context (1-10) |
| **Enable Auto Memory** | True | Tự động lưu tin nhắn khi dùng `/memory chat` |
| **Show Relevance Scores** | True | Hiển thị điểm relevance trong kết quả tìm kiếm |
| **Memory Command Prefix** | `/memory` | Prefix cho các lệnh memory |

### Cấu Hình Nâng Cao

| Thiết Lập | Mặc Định | Mô Tả |
|-----------|----------|--------|
| **Gemini API Key** | (trống) | API key riêng (để trống dùng biến môi trường) |
| **Qdrant Host** | localhost | Địa chỉ Qdrant server |
| **Qdrant Port** | 6333 | Port Qdrant server (1-65535) |

### Ví Dụ Cấu Hình

**Cấu hình cho team nhỏ:**
```
Max Search Results: 5
Memory Search Limit Chat: 2
Enable Auto Memory: True
Show Relevance Scores: False
Memory Command Prefix: /mem
```

**Cấu hình cho nghiên cứu:**
```
Max Search Results: 20
Memory Search Limit Chat: 5
Enable Auto Memory: True
Show Relevance Scores: True
Memory Command Prefix: /memory
```

## Image Processor Valves

### Cấu Hình Xử Lý

| Thiết Lập | Mặc Định | Mô Tả |
|-----------|----------|--------|
| **Auto Process Images** | True | Tự động xử lý ảnh upload |
| **Max Image Size MB** | 10.0 | Kích thước ảnh tối đa (0.1-50.0 MB) |
| **Include Table Extraction** | True | Trích xuất bảng từ ảnh |
| **Preserve Formatting** | True | Giữ nguyên định dạng tài liệu |
| **OCR Languages** | vie+eng | Ngôn ngữ OCR (vie=Tiếng Việt, eng=English) |

### Cấu Hình Hiển Thị

| Thiết Lập | Mặc Định | Mô Tả |
|-----------|----------|--------|
| **Show Processing Status** | True | Hiển thị trạng thái xử lý |
| **Wrap Results in Code Blocks** | True | Bọc kết quả OCR trong code blocks |
| **OCR Command Prefix** | `/ocr` | Prefix cho lệnh OCR help |

### Cấu Hình Server

| Thiết Lập | Mặc Định | Mô Tả |
|-----------|----------|--------|
| **Docling Server URL** | http://localhost:5001 | URL Docling server |
| **Connection Timeout** | 30 | Timeout kết nối (5-120 giây) |

### Ví Dụ Cấu Hình

**Cấu hình tiết kiệm tài nguyên:**
```
Auto Process Images: False
Max Image Size MB: 5.0
Include Table Extraction: False
Show Processing Status: False
Wrap Results in Code Blocks: False
```

**Cấu hình xử lý chuyên sâu:**
```
Auto Process Images: True
Max Image Size MB: 20.0
Include Table Extraction: True
Preserve Formatting: True
OCR Languages: vie+eng+fra+deu
Show Processing Status: True
Connection Timeout: 60
```

## Sử Dụng Thực Tế

### 1. Thay Đổi Command Prefix

Nếu bạn muốn dùng `/mem` thay vì `/memory`:

1. Vào Memory Manager Valves
2. Đổi **Memory Command Prefix** thành `/mem`
3. Save settings
4. Bây giờ dùng: `/mem add`, `/mem search`, `/mem chat`, `/mem list`

### 2. Tối Ưu Hiệu Suất

Cho hệ thống có tài nguyên hạn chế:

**Memory Manager:**
```
Max Search Results: 5
Memory Search Limit Chat: 2
Show Relevance Scores: False
```

**Image Processor:**
```
Auto Process Images: False
Max Image Size MB: 3.0
Include Table Extraction: False
Show Processing Status: False
```

### 3. Cấu Hình Đa Ngôn Ngữ

Cho OCR nhiều ngôn ngữ:
```
OCR Languages: vie+eng+jpn+kor+chi_sim
```

Các mã ngôn ngữ phổ biến:
- `vie`: Tiếng Việt
- `eng`: English
- `jpn`: Japanese
- `kor`: Korean
- `chi_sim`: Chinese Simplified
- `fra`: French
- `deu`: German

## Troubleshooting

### 1. Valves Không Hiển Thị

**Nguyên nhân:** Filter chưa có Valves support

**Giải pháp:**
```bash
# Kiểm tra filter có Valves
python3 validate_openwebui_filters_with_valves.py

# Nếu không có, update filter files
```

### 2. Cài Đặt Không Lưu

**Nguyên nhân:** Lỗi validation hoặc quyền truy cập

**Giải pháp:**
1. Kiểm tra giá trị trong phạm vi cho phép
2. Restart Open WebUI
3. Re-upload filter nếu cần

### 3. Filter Không Hoạt Động Sau Thay Đổi

**Nguyên nhân:** Cần restart hoặc reload

**Giải pháp:**
1. Disable và enable lại filter
2. Refresh trang Open WebUI
3. Kiểm tra console logs

## Best Practices

### 1. Backup Cấu Hình

Trước khi thay đổi lớn, note lại cấu hình hiện tại:

```
Memory Manager:
- Max Search Results: 10
- Memory Search Limit Chat: 3
- Enable Auto Memory: True
...

Image Processor:
- Auto Process Images: True
- Max Image Size MB: 10.0
...
```

### 2. Test Từng Bước

1. Thay đổi 1-2 settings mỗi lần
2. Test functionality
3. Nếu OK, tiếp tục thay đổi khác

### 3. Monitor Performance

Theo dõi:
- Thời gian response
- Memory usage
- Error rates

Điều chỉnh settings nếu cần thiết.

## Kết Luận

Valves giúp bạn:
- ✅ Tùy chỉnh filters không cần code
- ✅ Thay đổi real-time
- ✅ Tối ưu hiệu suất theo nhu cầu
- ✅ Dễ dàng backup/restore cấu hình

Với Valves, việc quản lý và tùy chỉnh Open WebUI filters trở nên đơn giản và linh hoạt hơn nhiều!