#!/bin/bash
# Reinstall Open WebUI với cài đặt gốc nhưng giữ lịch sử trò chuyện

echo "🔄 REINSTALL OPEN WEBUI - KEEP CHAT HISTORY"
echo "==========================================="

WEBUI_DB="/home/<USER>/.local/lib/python3.12/site-packages/open_webui/data/webui.db"
BACKUP_DIR="/home/<USER>/AccA/webui_backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Create backup directory
mkdir -p "$BACKUP_DIR"

echo ""
echo "📋 Step 1: Backup chat history và user data"
echo "-------------------------------------------"

if [ -f "$WEBUI_DB" ]; then
    # Full backup
    cp "$WEBUI_DB" "$BACKUP_DIR/webui_full_backup_$TIMESTAMP.db"
    echo "✅ Full database backed up: webui_full_backup_$TIMESTAMP.db"
    
    # Export only chat history and user data (keep important tables)
    echo "🔍 Extracting chat history and user data..."
    sqlite3 "$WEBUI_DB" << 'EOF' > "$BACKUP_DIR/chat_history_backup_$TIMESTAMP.sql"
-- Export chat/conversation data
.dump chat
.dump user
.dump auth
.dump model
.dump document
.dump memory
EOF
    
    echo "✅ Chat history exported: chat_history_backup_$TIMESTAMP.sql"
else
    echo "⚠️  No existing database found"
fi

echo ""
echo "📋 Step 2: Uninstall current Open WebUI"
echo "---------------------------------------"

# Stop Open WebUI
pkill -f "open-webui" 2>/dev/null
sleep 3

# Uninstall Open WebUI
echo "🗑️  Uninstalling current Open WebUI..."
pip3 uninstall open-webui -y

# Clear custom environment variables
unset ENABLE_RAG_WEB_SEARCH
unset RAG_WEB_SEARCH_ENGINE
unset SEARXNG_QUERY_URL
unset BRAVE_SEARCH_API_KEY
unset ENABLE_RAG_LOCAL_WEB_FETCH
unset PLAYWRIGHT_BROWSERS_PATH
unset PLAYWRIGHT_HEADLESS

echo "✅ Open WebUI uninstalled"

echo ""
echo "📋 Step 3: Install fresh Open WebUI"
echo "-----------------------------------"

# Install latest Open WebUI with default settings
echo "📦 Installing fresh Open WebUI..."
pip3 install open-webui --user --upgrade

echo "✅ Fresh Open WebUI installed"

echo ""
echo "📋 Step 4: Restore chat history"
echo "-------------------------------"

# Start Open WebUI once to create default database structure
echo "🚀 Starting Open WebUI to create default database..."
nohup /home/<USER>/.local/bin/open-webui serve --host 0.0.0.0 --port 3000 > /tmp/webui-temp.log 2>&1 &

# Wait for database to be created
sleep 15

# Stop to restore data
pkill -f "open-webui serve"
sleep 3

# Find new database location
NEW_WEBUI_DB=$(find /home/<USER>"webui.db" 2>/dev/null | head -1)

if [ -f "$NEW_WEBUI_DB" ] && [ -f "$BACKUP_DIR/chat_history_backup_$TIMESTAMP.sql" ]; then
    echo "🔄 Restoring chat history to new database..."
    
    # Import chat history back
    sqlite3 "$NEW_WEBUI_DB" < "$BACKUP_DIR/chat_history_backup_$TIMESTAMP.sql"
    
    echo "✅ Chat history restored successfully"
else
    echo "⚠️  Could not restore chat history automatically"
    echo "💡 Manual restoration may be needed from: $BACKUP_DIR/"
fi

echo ""
echo "📋 Step 5: Start clean Open WebUI"
echo "---------------------------------"

# Start Open WebUI with default settings (no custom environment)
echo "🚀 Starting clean Open WebUI..."
nohup /home/<USER>/.local/bin/open-webui serve --host 0.0.0.0 --port 3000 > /tmp/open-webui-clean.log 2>&1 &

sleep 10

# Check if running
if pgrep -f "open-webui serve" >/dev/null; then
    echo "✅ Open WebUI started successfully (PID: $(pgrep -f 'open-webui serve'))"
    
    # Test web interface
    if curl -s "http://localhost:3000/" >/dev/null; then
        echo "✅ Web interface accessible"
    else
        echo "⚠️  Web interface may need a moment to load"
    fi
else
    echo "❌ Open WebUI failed to start"
    echo "📋 Check logs: tail -f /tmp/open-webui-clean.log"
fi

echo ""
echo "✅ OPEN WEBUI REINSTALL COMPLETED!"
echo "=================================="
echo "🌐 URL: http://**************:3000"
echo "📊 Logs: tail -f /tmp/open-webui-clean.log"
echo "💾 Backups saved in: $BACKUP_DIR/"
echo ""
echo "📋 What was done:"
echo "• ✅ Uninstalled old Open WebUI with custom configs"
echo "• ✅ Installed fresh Open WebUI with default settings"
echo "• ✅ Backed up and restored chat history"
echo "• ✅ Removed all custom web search configurations"
echo "• ✅ Clean default Open WebUI now running"
echo ""
echo "🎯 Your chat history should be preserved!"
echo "💡 If you need web search later, we can add it back properly"

# Show backup information
echo ""
echo "📋 Backup files created:"
ls -la "$BACKUP_DIR/" | grep "$TIMESTAMP" || echo "No backup files found" 