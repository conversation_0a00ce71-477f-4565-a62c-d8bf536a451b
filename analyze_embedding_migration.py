#!/usr/bin/env python3
"""
Analyze Embedding Migration - Local to Gemini
Phân tích và đề xuất gi<PERSON>i pháp migration embedding
"""

import sqlite3
import json
import os
import requests
import numpy as np
from datetime import datetime

def analyze_existing_vectors():
    """Phân tích vectors hiện có"""
    print("🔍 ANALYZING EXISTING VECTORS")
    print("=" * 60)
    
    chroma_db = os.path.expanduser("~/.local/lib/python3.12/site-packages/open_webui/data/vector_db/chroma.sqlite3")
    
    if not os.path.exists(chroma_db):
        print("❌ ChromaDB not found")
        return None
    
    try:
        conn = sqlite3.connect(chroma_db)
        cursor = conn.cursor()
        
        # Lấy thông tin về embeddings
        cursor.execute("SELECT COUNT(*) FROM embeddings")
        total_embeddings = cursor.fetchone()[0]
        
        # Lấy sample embedding để check dimensions
        cursor.execute("SELECT embedding FROM embeddings LIMIT 1")
        sample = cursor.fetchone()
        
        if sample:
            # Parse embedding (thường là JSON array hoặc binary)
            try:
                embedding_data = json.loads(sample[0]) if isinstance(sample[0], str) else sample[0]
                if isinstance(embedding_data, list):
                    dimensions = len(embedding_data)
                else:
                    # Binary data, estimate dimensions
                    dimensions = len(embedding_data) // 4  # Assuming float32
                    
                print(f"📊 Current vectors analysis:")
                print(f"   Total vectors: {total_embeddings:,}")
                print(f"   Dimensions: {dimensions}")
                
                # Gemini text-embedding-004 có 768 dimensions
                gemini_dimensions = 768
                print(f"   Gemini dimensions: {gemini_dimensions}")
                
                if dimensions == gemini_dimensions:
                    print("✅ Dimensions match! Vectors might be compatible")
                    compatibility = "COMPATIBLE"
                else:
                    print("❌ Dimensions mismatch! Need reindexing")
                    compatibility = "INCOMPATIBLE"
                
                conn.close()
                return {
                    "total_vectors": total_embeddings,
                    "current_dimensions": dimensions,
                    "gemini_dimensions": gemini_dimensions,
                    "compatibility": compatibility
                }
                
            except Exception as e:
                print(f"❌ Error parsing embedding: {e}")
                conn.close()
                return None
        else:
            print("❌ No embeddings found")
            conn.close()
            return None
            
    except Exception as e:
        print(f"❌ Error analyzing vectors: {e}")
        return None

def analyze_collections_and_documents():
    """Phân tích collections và documents"""
    print("\n📚 ANALYZING COLLECTIONS AND DOCUMENTS")
    print("=" * 60)
    
    chroma_db = os.path.expanduser("~/.local/lib/python3.12/site-packages/open_webui/data/vector_db/chroma.sqlite3")
    webui_db = os.path.expanduser("~/.local/lib/python3.12/site-packages/open_webui/data/webui.db")
    
    collections_info = []
    
    try:
        # Analyze ChromaDB collections
        conn = sqlite3.connect(chroma_db)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT c.name, c.id, COUNT(e.id) as vector_count
            FROM collections c
            LEFT JOIN segments s ON c.id = s.collection
            LEFT JOIN embeddings e ON s.id = e.segment_id
            GROUP BY c.id, c.name
        """)
        
        for name, coll_id, vector_count in cursor.fetchall():
            collections_info.append({
                "name": name,
                "id": coll_id,
                "vector_count": vector_count,
                "type": "knowledge" if name.startswith("file-") else "other"
            })
        
        conn.close()
        
        # Analyze WebUI documents
        conn = sqlite3.connect(webui_db)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM document")
        document_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM file")
        file_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"📄 Documents analysis:")
        print(f"   Total documents: {document_count}")
        print(f"   Total files: {file_count}")
        print(f"   Total collections: {len(collections_info)}")
        
        print(f"\n📋 Collections breakdown:")
        knowledge_collections = 0
        other_collections = 0
        total_vectors = 0
        
        for coll in collections_info:
            if coll["type"] == "knowledge":
                knowledge_collections += 1
                print(f"   📄 {coll['name']}: {coll['vector_count']} vectors")
            else:
                other_collections += 1
                print(f"   🔍 {coll['name']}: {coll['vector_count']} vectors")
            
            total_vectors += coll["vector_count"]
        
        print(f"\n📊 Summary:")
        print(f"   Knowledge collections: {knowledge_collections}")
        print(f"   Other collections: {other_collections}")
        print(f"   Total vectors: {total_vectors:,}")
        
        return collections_info
        
    except Exception as e:
        print(f"❌ Error analyzing collections: {e}")
        return []

def estimate_reindex_time_and_cost():
    """Ước tính thời gian và chi phí reindex"""
    print("\n⏱️ REINDEX TIME & COST ESTIMATION")
    print("=" * 60)
    
    # Lấy thông tin về documents
    webui_db = os.path.expanduser("~/.local/lib/python3.12/site-packages/open_webui/data/webui.db")
    
    try:
        conn = sqlite3.connect(webui_db)
        cursor = conn.cursor()
        
        # Estimate document content size
        cursor.execute("SELECT COUNT(*) FROM document")
        doc_count = cursor.fetchone()[0]
        
        # Estimate chunks (assuming 1000 chars per chunk, 200 overlap)
        estimated_chunks = doc_count * 5  # Rough estimate
        
        conn.close()
        
        # Gemini API limits and costs
        gemini_rpm = 1500  # requests per minute for free tier
        gemini_cost_per_1k = 0.0  # Free for text-embedding-004
        
        # Time estimation
        minutes_needed = estimated_chunks / gemini_rpm
        hours_needed = minutes_needed / 60
        
        print(f"📊 Reindex estimation:")
        print(f"   Documents: {doc_count}")
        print(f"   Estimated chunks: {estimated_chunks:,}")
        print(f"   Gemini API limit: {gemini_rpm} requests/minute")
        print(f"   Estimated time: {hours_needed:.1f} hours")
        print(f"   Cost: FREE (text-embedding-004)")
        
        return {
            "documents": doc_count,
            "estimated_chunks": estimated_chunks,
            "hours_needed": hours_needed,
            "cost": 0
        }
        
    except Exception as e:
        print(f"❌ Error estimating reindex: {e}")
        return None

def create_migration_strategies():
    """Tạo các chiến lược migration"""
    print("\n🎯 MIGRATION STRATEGIES")
    print("=" * 60)
    
    strategies = {
        "1": {
            "name": "FULL REINDEX (Recommended)",
            "description": "Xóa tất cả vectors cũ, reindex với Gemini",
            "pros": [
                "✅ Consistency hoàn toàn",
                "✅ Sử dụng 100% Gemini API",
                "✅ Không có vector cũ lẫn lộn"
            ],
            "cons": [
                "❌ Mất thời gian reindex",
                "❌ Tạm thời mất RAG functionality"
            ],
            "steps": [
                "1. Backup current database",
                "2. Clear all vector collections", 
                "3. Re-upload documents",
                "4. Auto-generate new Gemini vectors"
            ]
        },
        
        "2": {
            "name": "SELECTIVE REINDEX",
            "description": "Chỉ reindex documents quan trọng",
            "pros": [
                "✅ Nhanh hơn full reindex",
                "✅ Giữ lại một số vectors cũ"
            ],
            "cons": [
                "❌ Mixed vector quality",
                "❌ Phức tạp quản lý"
            ],
            "steps": [
                "1. Identify important collections",
                "2. Clear selected collections",
                "3. Re-upload selected documents",
                "4. Keep other vectors as-is"
            ]
        },
        
        "3": {
            "name": "GRADUAL MIGRATION",
            "description": "Từ từ thay thế vectors khi có queries",
            "pros": [
                "✅ Không downtime",
                "✅ Tự động cập nhật theo usage"
            ],
            "cons": [
                "❌ Inconsistent quality",
                "❌ Lâu để hoàn thành"
            ],
            "steps": [
                "1. Keep existing vectors",
                "2. New uploads use Gemini",
                "3. Re-embed on query if needed",
                "4. Gradually replace old vectors"
            ]
        }
    }
    
    for key, strategy in strategies.items():
        print(f"\n📋 Strategy {key}: {strategy['name']}")
        print(f"   {strategy['description']}")
        print(f"   Pros:")
        for pro in strategy['pros']:
            print(f"     {pro}")
        print(f"   Cons:")
        for con in strategy['cons']:
            print(f"     {con}")
    
    return strategies

def create_reindex_script():
    """Tạo script reindex"""
    print("\n📝 CREATING REINDEX SCRIPT")
    print("=" * 60)
    
    script_content = '''#!/usr/bin/env python3
"""
Full Reindex Script - Local to Gemini Migration
CẢNH BÁO: Script này sẽ xóa tất cả vectors cũ!
"""

import sqlite3
import os
import shutil
from datetime import datetime

def backup_databases():
    """Backup databases trước khi reindex"""
    print("💾 Creating backup...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"embedding_backup_{timestamp}"
    os.makedirs(backup_dir, exist_ok=True)
    
    # Backup files
    files_to_backup = [
        "~/.local/lib/python3.12/site-packages/open_webui/data/webui.db",
        "~/.local/lib/python3.12/site-packages/open_webui/data/vector_db/chroma.sqlite3"
    ]
    
    for file_path in files_to_backup:
        expanded_path = os.path.expanduser(file_path)
        if os.path.exists(expanded_path):
            filename = os.path.basename(expanded_path)
            shutil.copy2(expanded_path, f"{backup_dir}/{filename}")
            print(f"   ✅ Backed up: {filename}")
    
    print(f"✅ Backup completed: {backup_dir}")
    return backup_dir

def clear_vector_database():
    """Xóa tất cả vectors trong ChromaDB"""
    print("🗑️  Clearing vector database...")
    
    chroma_db = os.path.expanduser("~/.local/lib/python3.12/site-packages/open_webui/data/vector_db/chroma.sqlite3")
    
    if os.path.exists(chroma_db):
        try:
            conn = sqlite3.connect(chroma_db)
            cursor = conn.cursor()
            
            # Clear all embedding tables
            tables_to_clear = [
                'embeddings',
                'embedding_metadata', 
                'embeddings_queue',
                'collections',
                'segments'
            ]
            
            for table in tables_to_clear:
                cursor.execute(f"DELETE FROM {table}")
                print(f"   ✅ Cleared: {table}")
            
            conn.commit()
            conn.close()
            print("✅ Vector database cleared")
            
        except Exception as e:
            print(f"❌ Error clearing database: {e}")
    else:
        print("❌ ChromaDB not found")

def main():
    """Main reindex process"""
    print("🚨 FULL REINDEX - LOCAL TO GEMINI MIGRATION")
    print("=" * 60)
    print("⚠️  WARNING: This will delete all existing vectors!")
    print("⚠️  Make sure Open WebUI is stopped!")
    
    confirm = input("\\nType 'CONFIRM' to proceed: ")
    if confirm != "CONFIRM":
        print("❌ Cancelled")
        return
    
    # Step 1: Backup
    backup_dir = backup_databases()
    
    # Step 2: Clear vectors
    clear_vector_database()
    
    print("\\n🎯 REINDEX COMPLETED!")
    print("=" * 60)
    print("✅ Old vectors cleared")
    print("✅ Database backed up")
    print("\\n📝 Next steps:")
    print("1. Start Open WebUI")
    print("2. Re-upload your documents")
    print("3. New vectors will use Gemini API")
    print(f"\\n💾 Backup location: {backup_dir}")

if __name__ == "__main__":
    main()
'''
    
    with open('full_reindex_migration.py', 'w') as f:
        f.write(script_content)
    
    os.chmod('full_reindex_migration.py', 0o755)
    print("✅ Created: full_reindex_migration.py")

def main():
    """Main analysis"""
    print("🔄 EMBEDDING MIGRATION ANALYSIS")
    print("=" * 70)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. Analyze existing vectors
    vector_analysis = analyze_existing_vectors()
    
    # 2. Analyze collections
    collections = analyze_collections_and_documents()
    
    # 3. Estimate reindex time/cost
    estimation = estimate_reindex_time_and_cost()
    
    # 4. Create migration strategies
    strategies = create_migration_strategies()
    
    # 5. Create reindex script
    create_reindex_script()
    
    # Final recommendation
    print(f"\n🎯 RECOMMENDATION")
    print("=" * 70)
    
    if vector_analysis and vector_analysis["compatibility"] == "COMPATIBLE":
        print("✅ Your vectors might be compatible with Gemini!")
        print("💡 You can try keeping existing vectors and only reindex new uploads")
        print("🔧 Recommended: Strategy 3 (Gradual Migration)")
    else:
        print("❌ Your vectors are incompatible with Gemini dimensions")
        print("💡 Full reindex is recommended for best results")
        print("🔧 Recommended: Strategy 1 (Full Reindex)")
    
    if estimation:
        print(f"\n⏱️  Estimated reindex time: {estimation['hours_needed']:.1f} hours")
        print(f"💰 Cost: FREE (Gemini text-embedding-004)")
    
    print(f"\n📋 Available tools:")
    print(f"   - full_reindex_migration.py: Complete reindex script")
    print(f"   - Current system: Already configured for Gemini API")

if __name__ == "__main__":
    main() 