#!/usr/bin/env python3
"""
Script để phục hồi dữ liệu Open WebUI từ thư mục webui-data/ vào Docker volume
Restore Open WebUI data from webui-data/ directory to Docker volume
"""

import os
import subprocess
import sys
from datetime import datetime

class WebUIVolumeRestore:
    def __init__(self):
        self.local_data_dir = "webui-data"
        self.volume_name = "acca_open_webui_data"
        self.volume_path = f"/var/lib/docker/volumes/{self.volume_name}/_data"
        
    def log(self, message):
        """Log với timestamp"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def check_prerequisites(self):
        """Kiểm tra điều kiện trước khi restore"""
        self.log("🔍 Kiểm tra điều kiện...")
        
        # Kiểm tra thư mục local data
        if not os.path.exists(self.local_data_dir):
            self.log(f"❌ <PERSON>hông tìm thấy thư mục {self.local_data_dir}")
            return False
            
        # Kiểm tra kích thước
        result = subprocess.run(f"du -sh {self.local_data_dir}", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            size = result.stdout.strip().split()[0]
            self.log(f"📊 Kích thước dữ liệu local: {size}")
        
        # Kiểm tra Docker volume
        result = subprocess.run(f"docker volume inspect {self.volume_name}", shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            self.log(f"❌ Docker volume {self.volume_name} không tồn tại")
            return False
            
        self.log("✅ Tất cả điều kiện đã sẵn sàng")
        return True
        
    def backup_current_volume(self):
        """Backup dữ liệu hiện tại trong volume"""
        self.log("💾 Backup dữ liệu hiện tại...")
        
        backup_name = f"webui_volume_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            # Tạo backup directory
            subprocess.run(f"mkdir -p {backup_name}", shell=True, check=True)
            
            # Copy dữ liệu hiện tại
            subprocess.run(f"sudo cp -r {self.volume_path}/* {backup_name}/", shell=True, check=True)
            
            self.log(f"✅ Backup hoàn tất: {backup_name}")
            return backup_name
        except subprocess.CalledProcessError as e:
            self.log(f"⚠️ Lỗi backup: {e}")
            return None
            
    def restore_data(self):
        """Restore dữ liệu từ webui-data vào volume"""
        self.log("🚀 Bắt đầu restore dữ liệu...")
        
        try:
            # Dừng container nếu đang chạy
            self.log("🛑 Dừng Open WebUI container...")
            subprocess.run("docker stop $(docker ps -q --filter ancestor=ghcr.io/open-webui/open-webui:main) 2>/dev/null || true", shell=True)
            
            # Xóa dữ liệu cũ trong volume
            self.log("🗑️ Xóa dữ liệu cũ...")
            subprocess.run(f"sudo rm -rf {self.volume_path}/*", shell=True, check=True)
            
            # Copy dữ liệu mới
            self.log("📋 Copy dữ liệu mới...")
            
            # Copy database chính
            if os.path.exists(f"{self.local_data_dir}/webui.db"):
                subprocess.run(f"sudo cp {self.local_data_dir}/webui.db {self.volume_path}/", shell=True, check=True)
                self.log("✅ Database chính đã được copy")
            
            # Copy các thư mục
            folders = ['cache', 'uploads', 'vector_db', 'pipelines']
            for folder in folders:
                source = f"{self.local_data_dir}/{folder}"
                if os.path.exists(source):
                    subprocess.run(f"sudo cp -r {source} {self.volume_path}/", shell=True, check=True)
                    self.log(f"✅ Thư mục {folder} đã được copy")
                    
            # Copy backup databases
            for file in os.listdir(self.local_data_dir):
                if file.endswith('.db') and file != 'webui.db':
                    subprocess.run(f"sudo cp {self.local_data_dir}/{file} {self.volume_path}/", shell=True, check=True)
                    self.log(f"✅ Backup database {file} đã được copy")
                    
            # Set permissions
            subprocess.run(f"sudo chown -R root:root {self.volume_path}", shell=True, check=True)
            subprocess.run(f"sudo chmod -R 755 {self.volume_path}", shell=True, check=True)
            
            self.log("✅ Restore hoàn tất!")
            
        except subprocess.CalledProcessError as e:
            self.log(f"❌ Lỗi restore: {e}")
            raise
            
    def verify_restore(self):
        """Kiểm tra kết quả restore"""
        self.log("🔍 Kiểm tra kết quả restore...")
        
        try:
            # Kiểm tra kích thước
            result = subprocess.run(f"sudo du -sh {self.volume_path}", shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                size = result.stdout.strip().split()[0]
                self.log(f"📊 Kích thước dữ liệu sau restore: {size}")
                
            # Kiểm tra database
            if os.path.exists(f"{self.volume_path}/webui.db"):
                result = subprocess.run(f"sudo ls -lh {self.volume_path}/webui.db", shell=True, capture_output=True, text=True)
                if result.returncode == 0:
                    self.log(f"📊 Database: {result.stdout.strip()}")
                    
            # Kiểm tra các thư mục
            result = subprocess.run(f"sudo ls -la {self.volume_path}", shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                self.log("📁 Nội dung volume sau restore:")
                for line in result.stdout.strip().split('\n')[2:]:  # Skip . and ..
                    self.log(f"   {line}")
                    
            self.log("✅ Kiểm tra hoàn tất!")
            
        except Exception as e:
            self.log(f"⚠️ Lỗi kiểm tra: {e}")
            
    def run(self):
        """Chạy toàn bộ quá trình restore"""
        self.log("🎯 BẮT ĐẦU RESTORE OPEN WEBUI DATA")
        self.log("=" * 50)
        
        try:
            # Kiểm tra điều kiện
            if not self.check_prerequisites():
                return False
                
            # Backup dữ liệu hiện tại
            backup_name = self.backup_current_volume()
            
            # Restore dữ liệu
            self.restore_data()
            
            # Kiểm tra kết quả
            self.verify_restore()
            
            self.log("=" * 50)
            self.log("🎉 RESTORE HOÀN TẤT!")
            self.log("📋 TỔNG KẾT:")
            self.log(f"   ✅ Dữ liệu đã được restore từ {self.local_data_dir}")
            self.log(f"   ✅ Volume: {self.volume_name}")
            if backup_name:
                self.log(f"   ✅ Backup cũ: {backup_name}")
            self.log("")
            self.log("🚀 BƯỚC TIẾP THEO:")
            self.log("   1. Khởi động lại Open WebUI container")
            self.log("   2. Kiểm tra dữ liệu chat history")
            self.log("   3. Xác nhận tất cả hoạt động bình thường")
            
            return True
            
        except Exception as e:
            self.log(f"❌ LỖI: {e}")
            return False

def main():
    """Main function"""
    if os.geteuid() != 0:
        print("⚠️ Script này cần quyền sudo để truy cập Docker volume")
        print("Chạy lại với: sudo python3 restore_webui_volume.py")
        sys.exit(1)
        
    restorer = WebUIVolumeRestore()
    success = restorer.run()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()