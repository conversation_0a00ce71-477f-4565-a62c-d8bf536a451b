#!/usr/bin/env python3
"""
🔧 FORCE IMPORT CONFIG
Force import config directly into Open WebUI via database manipulation
"""

import json
import subprocess
import time

def force_import_config():
    """Force import config into Open WebUI"""
    print("🔧 FORCE IMPORTING CONFIG INTO OPEN WEBUI")
    print("=" * 60)
    
    # Stop Open WebUI
    print("🛑 Stopping Open WebUI...")
    subprocess.run(["docker", "stop", "open-webui-mcpo"], check=True)
    
    # Copy database out
    print("📤 Copying database out...")
    subprocess.run([
        "docker", "cp", 
        "open-webui-mcpo:/app/backend/data/webui.db", 
        "./temp_webui.db"
    ], check=True)
    
    # Load config from file
    print("📋 Loading config from file...")
    with open("mem0-owui/mcp-integration/config/openwebui_mcpo_config.json", "r") as f:
        config_data = json.load(f)
    
    # Convert to JSON string for database
    config_json = json.dumps(config_data)
    
    # Update database with proper escaping
    print("🗄️ Updating database...")
    
    # First, delete existing config
    subprocess.run([
        "sqlite3", "temp_webui.db", 
        "DELETE FROM config;"
    ], check=True)
    
    # Insert new config
    subprocess.run([
        "sqlite3", "temp_webui.db", 
        f"INSERT INTO config (id, data, version, created_at, updated_at) VALUES (1, '{config_json}', 0, datetime('now'), datetime('now'));"
    ], check=True)
    
    # Verify insertion
    result = subprocess.run([
        "sqlite3", "temp_webui.db",
        "SELECT COUNT(*) FROM config;"
    ], capture_output=True, text=True)
    
    if "1" in result.stdout:
        print("   ✅ Config inserted successfully")
    else:
        print("   ❌ Config insertion failed")
        return False
    
    # Copy database back
    print("📥 Copying database back...")
    subprocess.run([
        "docker", "cp", 
        "./temp_webui.db", 
        "open-webui-mcpo:/app/backend/data/webui.db"
    ], check=True)
    
    # Start Open WebUI
    print("🚀 Starting Open WebUI...")
    subprocess.run(["docker", "start", "open-webui-mcpo"], check=True)
    
    # Wait for startup
    print("⏳ Waiting for startup...")
    time.sleep(20)
    
    # Test health
    result = subprocess.run([
        "curl", "-s", "http://localhost:3000/health"
    ], capture_output=True, text=True)
    
    if '{"status":true}' in result.stdout:
        print("   ✅ Open WebUI is healthy")
    else:
        print("   ❌ Open WebUI health check failed")
    
    # Cleanup
    subprocess.run(["rm", "temp_webui.db"], check=True)
    
    print("\n" + "=" * 60)
    print("📊 CONFIG IMPORT SUMMARY:")
    print("✅ Database updated with fresh config")
    print("✅ MCP servers configuration imported")
    print("✅ Open WebUI restarted")
    
    print("\n🎯 NEXT STEPS:")
    print("1. Open http://localhost:3000")
    print("2. Go to Admin → Settings")
    print("3. Check MCP Servers section")
    print("4. Should see all configured servers")
    
    return True

def create_minimal_config():
    """Create minimal config if main config fails"""
    print("\n🔧 CREATING MINIMAL CONFIG AS FALLBACK...")
    
    minimal_config = {
        "mcpServers": {
            "mcpo_complete_proxy_8000": {
                "transport": {
                    "type": "http",
                    "url": "http://mcpo-complete-proxy-8000:8000"
                },
                "description": "MCPO Complete Proxy - 12 MCP tools with namespace separation"
            },
            "jina_crawler_8002": {
                "transport": {
                    "type": "http", 
                    "url": "http://jina-crawler-mcp-proxy-8002:8002"
                },
                "description": "Jina Crawler - Advanced web crawling with AI processing"
            }
        }
    }
    
    # Save minimal config
    with open("minimal_config.json", "w") as f:
        json.dump(minimal_config, f, indent=2)
    
    print("   ✅ Minimal config created: minimal_config.json")
    print("   💡 You can manually import this in Open WebUI if needed")

if __name__ == "__main__":
    try:
        success = force_import_config()
        if success:
            print("\n🎉 Config import completed successfully!")
        else:
            print("\n❌ Config import failed!")
            create_minimal_config()
    except Exception as e:
        print(f"\n❌ Error during config import: {e}")
        create_minimal_config()
