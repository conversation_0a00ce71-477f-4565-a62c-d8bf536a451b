#!/usr/bin/env python3
"""
🔄 FORCE REFRESH ORACLE PIPELINE
Force refresh Oracle Advanced Memory pipeline in Open WebUI
"""

import requests
import json
import time

def force_refresh_pipeline():
    """Force refresh Oracle pipeline in Open WebUI"""
    print("🔄 FORCE REFRESHING ORACLE ADVANCED MEMORY PIPELINE")
    print("=" * 60)
    
    base_url = "http://localhost:3000"
    
    # Wait for Open WebUI to be ready
    print("⏳ Waiting for Open WebUI to be ready...")
    for i in range(30):
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ Open WebUI is ready!")
                break
        except:
            pass
        time.sleep(2)
        print(f"   Waiting... ({i+1}/30)")
    else:
        print("❌ Open WebUI not ready after 60 seconds")
        return False
    
    # Try to access pipelines API
    try:
        print("\n🔍 Checking pipelines API...")
        response = requests.get(f"{base_url}/api/v1/pipelines", timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            pipelines = response.json()
            print(f"   Found {len(pipelines)} pipelines")
            
            # Look for Oracle pipeline
            oracle_pipeline = None
            for pipeline in pipelines:
                if "oracle-advanced-memory" in pipeline.get("id", "").lower():
                    oracle_pipeline = pipeline
                    break
            
            if oracle_pipeline:
                print(f"✅ Found Oracle pipeline: {oracle_pipeline.get('id')}")
                print(f"   Name: {oracle_pipeline.get('name', 'N/A')}")
                print(f"   Type: {oracle_pipeline.get('type', 'N/A')}")
                
                # Check valves
                valves = oracle_pipeline.get("valves", {})
                print(f"\n🔧 Current valves count: {len(valves)}")
                
                # Check for new LLM override valves
                llm_valves = [
                    "override_llm_model", "target_llm_model", "target_llm_provider",
                    "llm_temperature", "llm_max_tokens", "llm_top_p"
                ]
                
                missing_valves = []
                for valve in llm_valves:
                    if valve not in valves:
                        missing_valves.append(valve)
                
                if missing_valves:
                    print(f"❌ Missing LLM override valves: {missing_valves}")
                else:
                    print("✅ All LLM override valves present!")
                    for valve in llm_valves:
                        print(f"   {valve}: {valves.get(valve)}")
                
            else:
                print("❌ Oracle Advanced Memory pipeline not found")
                print("Available pipelines:")
                for pipeline in pipelines:
                    print(f"   - {pipeline.get('id', 'Unknown')}")
        
        else:
            print(f"❌ Failed to get pipelines: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error accessing pipelines API: {e}")
        return False
    
    return True

def manual_pipeline_reload():
    """Manual steps to reload pipeline"""
    print("\n" + "=" * 60)
    print("🔧 MANUAL PIPELINE RELOAD STEPS:")
    print("1. Open http://localhost:3000 in browser")
    print("2. Go to Admin Panel (top right)")
    print("3. Click 'Pipelines' in sidebar")
    print("4. Find 'Oracle Advanced Memory' pipeline")
    print("5. Click 'Reload' or 'Refresh' button")
    print("6. Check if new valves appear:")
    print("   - override_llm_model")
    print("   - target_llm_model") 
    print("   - target_llm_provider")
    print("   - llm_temperature")
    print("   - llm_max_tokens")
    print("   - llm_top_p")
    
    print("\n🔄 ALTERNATIVE: Delete and Re-add Pipeline:")
    print("1. In Pipelines admin, delete Oracle Advanced Memory")
    print("2. Click 'Add Pipeline'")
    print("3. Upload the oracle-advanced-memory.py file")
    print("4. Configure valves with new LLM override options")

def check_file_timestamps():
    """Check if files are properly updated"""
    print("\n" + "=" * 60)
    print("📅 CHECKING FILE TIMESTAMPS:")
    
    import subprocess
    import os
    
    # Check local file
    local_file = "mem0-owui/webui-data/pipelines/oracle-advanced-memory/oracle-advanced-memory.py"
    if os.path.exists(local_file):
        stat = os.stat(local_file)
        print(f"📁 Local file: {time.ctime(stat.st_mtime)}")
    
    # Check container file
    try:
        result = subprocess.run([
            "docker", "exec", "open-webui-mcpo", 
            "stat", "/app/backend/data/pipelines/oracle-advanced-memory/oracle-advanced-memory.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("🐳 Container file stat:")
            print(result.stdout)
        else:
            print("❌ Failed to check container file")
    except Exception as e:
        print(f"❌ Error checking container file: {e}")

if __name__ == "__main__":
    success = force_refresh_pipeline()
    check_file_timestamps()
    manual_pipeline_reload()
    
    if success:
        print(f"\n🎉 Pipeline refresh completed!")
        print("💡 If valves still appear old, try manual reload steps above")
    else:
        print(f"\n❌ Pipeline refresh failed!")
        print("🔧 Try manual steps or container restart")
