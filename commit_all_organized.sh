#!/bin/bash
set -e

echo "🚀 Organizing and committing all changes..."

# Push existing commit first if needed
echo "1. Checking current status..."
git status --porcelain

echo "2. Pushing any existing commits..."
git push origin main || echo "Nothing to push or push failed"

# Group and commit files by type
echo "3. Adding and committing files by groups..."

# RAG and AI files
echo "📊 Committing RAG and AI files..."
git add external_rag*.py simple_rag*.py openwebui_external_rag*.py
git add EXTERNAL_RAG*.md GEMINI_EXTERNAL_RAG_GUIDE.md OPEN_WEBUI_INTEGRATION.md
git add *rag*.py *embedding*.py docling_server.py
git add google_drive_json_search.py json_*.py integrated_json_rag_system.py
git add document_to_json_converter.py demo_document_to_json.py
git commit -m "🤖 Add comprehensive RAG and AI integration system

- External RAG servers with multiple backends
- Gemini API integration
- Document processing and JSON conversion
- Google Drive integration
- Performance optimization scripts
- Comprehensive documentation" || echo "No RAG files to commit"

# Upload and test files
echo "📤 Committing upload and test files..."
git add upload_interface.html simple_upload_test.py
git add test_*.py *test*.py monitor_*.py
git add test_upload.txt fresh_test.txt test_table_document.txt
git add simple_fast_upload.py ultra_fast_upload.py speed_up_uploads.py
git commit -m "📤 Add upload interface and comprehensive testing suite

- Upload interfaces and test servers
- Performance testing and monitoring
- Fast upload implementations
- Test documents and validation" || echo "No upload files to commit"

# Configuration and setup files
echo "⚙️ Committing configuration files..."
git add *.sh setup_*.sh start_*.sh restart_*.sh monitor_*.sh
git add docker-compose*.yml Dockerfile* *.env* init-db.sql
git add *config*.py *settings*.py verify_*.py check_*.py
git add external_rag_requirements.txt
git commit -m "⚙️ Add configuration and deployment setup

- Shell scripts for automation
- Docker configurations
- Environment setup files
- Configuration validation scripts
- Requirements and dependencies" || echo "No config files to commit"

# HTTPS and security
echo "🔒 Committing HTTPS and security files..."
git add HTTPS_*.md https-*.py start-webui-https*.sh monitor-https*.sh
git commit -m "🔒 Add HTTPS security implementation

- HTTPS setup guides
- SSL/TLS configuration
- Security monitoring scripts
- HTTPS redirect server" || echo "No HTTPS files to commit"

# Performance and optimization
echo "⚡ Committing performance files..."
git add *optimizer*.py *performance*.py *comparison*.py
git add apply_gemini_rag_optimizations.py hybrid_table_chunk_optimizer.py
git add embedding_cache_guide.md rag_performance_comparison.json
git commit -m "⚡ Add performance optimization and analysis

- RAG optimization algorithms
- Performance comparison tools
- Caching mechanisms
- Benchmarking utilities" || echo "No performance files to commit"

# System maintenance and fixes
echo "🔧 Committing system maintenance files..."
git add fix_*.py clear_*.py force_*.py restart_*.py
git add system_status.py final_system_check.py
git commit -m "🔧 Add system maintenance and fix utilities

- System diagnostic tools
- Database maintenance scripts
- Configuration fix utilities
- Status monitoring tools" || echo "No maintenance files to commit"

# Documentation and guides
echo "📚 Committing documentation..."
git add *.md open_webui_*.md embedding_cache_guide.md
git commit -m "📚 Add comprehensive documentation

- Integration guides
- Setup instructions
- Performance guides
- Configuration documentation" || echo "No docs to commit"

# Logs and cache (add to gitignore if needed)
echo "📝 Handling logs and cache..."
git add *.log webui.log llama-gemma3-server.log
git commit -m "📝 Add system logs for debugging" || echo "No logs to commit"

# Any remaining files
echo "🔄 Adding any remaining files..."
git add .
git commit -m "🔄 Add remaining files and updates" || echo "No remaining files"

# Final push
echo "4. Final push to repository..."
git push origin main

echo "✅ All files committed and pushed successfully!"
echo "📊 Final status:"
git status 