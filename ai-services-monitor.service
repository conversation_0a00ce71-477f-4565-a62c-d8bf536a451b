[Unit]
Description=AI Services Monitor - Auto-restart crashed services
After=network.target
Wants=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/AccA
ExecStart=/home/<USER>/AccA/monitor-services.sh start
ExecStop=/home/<USER>/AccA/monitor-services.sh stop
Restart=always
RestartSec=30
StandardOutput=journal
StandardError=journal

# Environment variables
Environment=PATH=/usr/local/bin:/usr/bin:/bin:/home/<USER>/.local/bin
Environment=HOME=/home/<USER>

# Resource limits
LimitNOFILE=65536
LimitNPROC=32768

[Install]
WantedBy=multi-user.target 