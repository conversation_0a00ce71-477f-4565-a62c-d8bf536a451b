#!/usr/bin/env python3
"""
Oracle Hybrid Thin Mode Implementation
Uses thin mode with wallet SSL certificates for Autonomous Database
"""

import os
import asyncio
import json
import hashlib
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from concurrent.futures import Thread<PERSON>oolExecutor
import oracledb
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class OracleConfig:
    """Oracle hybrid thin mode configuration"""
    oracle_user: str
    oracle_password: str
    oracle_dsn: str
    oracle_wallet_location: str
    pool_min: int = 2
    pool_max: int = 10
    pool_increment: int = 1
    ping_interval: int = 60
    timeout: int = 300

class OracleHybridThinModeClient:
    """Hybrid Oracle thin mode client - uses thin mode with wallet SSL"""
    
    def __init__(self, config: OracleConfig):
        self.config = config
        self.pool = None
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.initialized = False
        
        # Performance metrics
        self.metrics = {
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'avg_response_time': 0.0,
            'pool_status': 'inactive',
            'connection_mode': 'hybrid_thin'
        }
    
    async def initialize(self):
        """Initialize Oracle hybrid thin mode connection"""
        if self.initialized:
            return True
            
        try:
            logger.info("🚀 Initializing Oracle Hybrid Thin Mode Client...")
            
            # Initialize Oracle client with wallet for SSL certificates
            # This is needed for Autonomous Database SSL connections
            if self.config.oracle_wallet_location and os.path.exists(self.config.oracle_wallet_location):
                logger.info(f"🔐 Initializing Oracle client with wallet: {self.config.oracle_wallet_location}")
                oracledb.init_oracle_client(config_dir=self.config.oracle_wallet_location)
                logger.info("✅ Oracle client initialized with wallet SSL certificates")
            else:
                logger.warning("⚠️ Wallet location not found, trying pure thin mode")
            
            # Create connection pool
            self.pool = await asyncio.get_event_loop().run_in_executor(
                self.executor,
                self._create_pool
            )
            
            # Test the connection
            await self._test_connection()
            
            self.initialized = True
            self.metrics['pool_status'] = 'active'
            
            logger.info("✅ Oracle Hybrid Thin Mode Client initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Oracle client: {e}")
            self.metrics['pool_status'] = 'failed'
            raise
    
    def _create_pool(self):
        """Create Oracle connection pool (synchronous)"""
        return oracledb.create_pool(
            user=self.config.oracle_user,
            password=self.config.oracle_password,
            dsn=self.config.oracle_dsn,
            min=self.config.pool_min,
            max=self.config.pool_max,
            increment=self.config.pool_increment,
            ping_interval=self.config.ping_interval,
            timeout=self.config.timeout
        )
    
    async def _test_connection(self):
        """Test Oracle connection"""
        try:
            conn = await asyncio.get_event_loop().run_in_executor(
                self.executor,
                self.pool.acquire
            )
            
            try:
                cursor = conn.cursor()
                cursor.execute("SELECT 'Oracle Autonomous DB Hybrid Thin Mode OK' as status, SYSDATE as current_time FROM DUAL")
                result = cursor.fetchone()
                
                if result:
                    logger.info(f"✅ Connection test successful: {result[0]} at {result[1]}")
                else:
                    raise Exception("Connection test failed")
                
                # Test database version
                cursor.execute("SELECT banner FROM v$version WHERE rownum = 1")
                version = cursor.fetchone()
                logger.info(f"📋 Database: {version[0]}")
                
                # Test user privileges
                cursor.execute("SELECT username, account_status FROM user_users")
                user_info = cursor.fetchone()
                logger.info(f"👤 User: {user_info[0]}, Status: {user_info[1]}")
                    
                cursor.close()
                
            finally:
                conn.close()
                
        except Exception as e:
            logger.error(f"❌ Oracle connection test failed: {e}")
            raise
    
    async def execute_query(self, sql: str, params: Dict = None) -> List[Dict]:
        """Execute SELECT query and return results"""
        if not self.initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            conn = await asyncio.get_event_loop().run_in_executor(
                self.executor,
                self.pool.acquire
            )
            
            try:
                cursor = conn.cursor()
                
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)
                
                # Get column names
                columns = [desc[0] for desc in cursor.description] if cursor.description else []
                
                # Fetch results
                rows = cursor.fetchall()
                
                # Convert to list of dictionaries
                results = []
                for row in rows:
                    result_dict = {}
                    for i, value in enumerate(row):
                        if i < len(columns):
                            # Handle datetime objects
                            if isinstance(value, datetime):
                                result_dict[columns[i]] = value.isoformat()
                            else:
                                result_dict[columns[i]] = value
                    results.append(result_dict)
                
                cursor.close()
                
                # Update metrics
                self.metrics['total_operations'] += 1
                self.metrics['successful_operations'] += 1
                
                response_time = time.time() - start_time
                self.metrics['avg_response_time'] = (
                    (self.metrics['avg_response_time'] * (self.metrics['total_operations'] - 1) + response_time) /
                    self.metrics['total_operations']
                )
                
                logger.debug(f"✅ Query executed successfully ({response_time:.3f}s)")
                return results
                
            finally:
                conn.close()
                
        except Exception as e:
            self.metrics['total_operations'] += 1
            self.metrics['failed_operations'] += 1
            logger.error(f"❌ Query execution failed: {e}")
            raise
    
    async def execute_dml(self, sql: str, params: Dict = None) -> int:
        """Execute INSERT/UPDATE/DELETE and return affected rows"""
        if not self.initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            conn = await asyncio.get_event_loop().run_in_executor(
                self.executor,
                self.pool.acquire
            )
            
            try:
                cursor = conn.cursor()
                
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)
                
                affected_rows = cursor.rowcount
                conn.commit()
                cursor.close()
                
                # Update metrics
                self.metrics['total_operations'] += 1
                self.metrics['successful_operations'] += 1
                
                response_time = time.time() - start_time
                self.metrics['avg_response_time'] = (
                    (self.metrics['avg_response_time'] * (self.metrics['total_operations'] - 1) + response_time) /
                    self.metrics['total_operations']
                )
                
                logger.debug(f"✅ DML executed successfully, {affected_rows} rows affected ({response_time:.3f}s)")
                return affected_rows
                
            finally:
                conn.close()
                
        except Exception as e:
            self.metrics['total_operations'] += 1
            self.metrics['failed_operations'] += 1
            logger.error(f"❌ DML execution failed: {e}")
            raise
    
    async def create_tables(self):
        """Create required tables if they don't exist"""
        try:
            logger.info("🔧 Creating Oracle tables...")
            
            # Create USER_MEMORY table
            memory_table_sql = """
            CREATE TABLE USER_MEMORY (
                memory_id VARCHAR2(100) PRIMARY KEY,
                user_id VARCHAR2(100) NOT NULL,
                memory_type VARCHAR2(50) DEFAULT 'conversation',
                memory_content CLOB NOT NULL,
                confidence_score NUMBER(3,2) DEFAULT 0.8,
                session_id VARCHAR2(100),
                tags CLOB,
                metadata CLOB,
                created_date DATE DEFAULT SYSDATE,
                status VARCHAR2(20) DEFAULT 'ACTIVE'
            )
            """
            
            # Create USER_PATTERNS table
            patterns_table_sql = """
            CREATE TABLE USER_PATTERNS (
                pattern_id VARCHAR2(100) PRIMARY KEY,
                user_id VARCHAR2(100) NOT NULL,
                pattern_type VARCHAR2(50) NOT NULL,
                pattern_data CLOB,
                strength NUMBER(3,2) DEFAULT 0.5,
                confidence NUMBER(3,2) DEFAULT 0.5,
                sample_count NUMBER DEFAULT 1,
                created_date DATE DEFAULT SYSDATE,
                updated_date DATE DEFAULT SYSDATE
            )
            """
            
            try:
                await self.execute_dml(memory_table_sql)
                logger.info("✅ USER_MEMORY table created")
            except Exception as e:
                if "name is already used" in str(e).lower():
                    logger.info("ℹ️ USER_MEMORY table already exists")
                else:
                    raise
            
            try:
                await self.execute_dml(patterns_table_sql)
                logger.info("✅ USER_PATTERNS table created")
            except Exception as e:
                if "name is already used" in str(e).lower():
                    logger.info("ℹ️ USER_PATTERNS table already exists")
                else:
                    raise
            
            logger.info("✅ All tables ready")
            
        except Exception as e:
            logger.error(f"❌ Error creating tables: {e}")
            raise
    
    async def search_memories(self, user_id: str, query: str, limit: int = 5) -> List[Dict]:
        """Search user memories"""
        try:
            sql = """
            SELECT memory_id, memory_content, confidence_score, created_date, memory_type, session_id
            FROM USER_MEMORY
            WHERE user_id = :user_id
            AND status = 'ACTIVE'
            AND (
                UPPER(memory_content) LIKE UPPER(:query1)
                OR UPPER(memory_content) LIKE UPPER(:query2)
            )
            ORDER BY confidence_score DESC, created_date DESC
            FETCH FIRST :limit ROWS ONLY
            """
            
            query_pattern1 = f"%{query}%"
            query_pattern2 = f"%{' '.join(query.split()[:3])}%"
            
            results = await self.execute_query(sql, {
                'user_id': user_id,
                'query1': query_pattern1,
                'query2': query_pattern2,
                'limit': limit
            })
            
            # Format results
            memories = []
            for row in results:
                memories.append({
                    'memory_id': row['MEMORY_ID'],
                    'content': row['MEMORY_CONTENT'],
                    'confidence': float(row['CONFIDENCE_SCORE']),
                    'created_date': row['CREATED_DATE'],
                    'memory_type': row['MEMORY_TYPE'],
                    'session_id': row['SESSION_ID'],
                    'source': 'oracle'
                })
            
            logger.info(f"🔍 Found {len(memories)} memories for user {user_id}")
            return memories
            
        except Exception as e:
            logger.error(f"❌ Error searching memories: {e}")
            return []
    
    async def store_memory(self, user_id: str, content: str, memory_type: str = "conversation",
                          session_id: str = None, confidence: float = 0.8) -> str:
        """Store memory in Oracle"""
        try:
            memory_id = hashlib.md5(
                f"{user_id}_{content}_{datetime.now().isoformat()}".encode()
            ).hexdigest()
            
            sql = """
            INSERT INTO USER_MEMORY (
                memory_id, user_id, memory_type, memory_content,
                confidence_score, session_id, created_date, status
            ) VALUES (
                :memory_id, :user_id, :memory_type, :content,
                :confidence, :session_id, SYSDATE, 'ACTIVE'
            )
            """
            
            await self.execute_dml(sql, {
                'memory_id': memory_id,
                'user_id': user_id,
                'memory_type': memory_type,
                'content': content,
                'confidence': confidence,
                'session_id': session_id
            })
            
            logger.info(f"💾 Stored memory {memory_id[:8]}... for user {user_id}")
            return memory_id
            
        except Exception as e:
            logger.error(f"❌ Error storing memory: {e}")
            return ""
    
    def get_metrics(self) -> Dict:
        """Get client performance metrics"""
        return {
            **self.metrics,
            'initialized': self.initialized,
            'pool_active': self.pool is not None
        }
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.pool:
                self.pool.close()
            self.executor.shutdown(wait=True)
            logger.info("🧹 Oracle Hybrid Thin Mode Client cleaned up")
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")

def load_oracle_config() -> OracleConfig:
    """Load Oracle configuration from environment"""
    return OracleConfig(
        oracle_user=os.getenv("ORACLE_USER", "ADMIN"),
        oracle_password=os.getenv("ORACLE_PASSWORD", ""),
        oracle_dsn=os.getenv("ORACLE_DSN", ""),
        oracle_wallet_location=os.getenv("ORACLE_WALLET_LOCATION", "./oracle_wallet/Wallet_SWIV8HV5Y96IWO2T"),
        pool_min=int(os.getenv("ORACLE_POOL_MIN", "2")),
        pool_max=int(os.getenv("ORACLE_POOL_MAX", "10")),
        pool_increment=int(os.getenv("ORACLE_POOL_INCREMENT", "1"))
    )

async def test_oracle_integration():
    """Test Oracle hybrid thin mode integration"""
    try:
        logger.info("🧪 Testing Oracle Hybrid Thin Mode Integration...")
        
        # Load configuration
        config = load_oracle_config()
        
        if not config.oracle_password or not config.oracle_dsn:
            logger.error("❌ Oracle credentials not configured")
            return False
        
        logger.info(f"📋 Configuration:")
        logger.info(f"  User: {config.oracle_user}")
        logger.info(f"  DSN: {config.oracle_dsn}")
        logger.info(f"  Wallet: {config.oracle_wallet_location}")
        logger.info(f"  Wallet exists: {os.path.exists(config.oracle_wallet_location)}")
        
        # Initialize client
        client = OracleHybridThinModeClient(config)
        await client.initialize()
        
        # Create tables
        await client.create_tables()
        
        # Test storing memory
        memory_id = await client.store_memory(
            user_id="test_user",
            content="Testing Oracle Autonomous Database hybrid thin mode integration",
            memory_type="test"
        )
        
        if memory_id:
            logger.info(f"✅ Memory stored: {memory_id[:8]}...")
        
        # Test searching memories
        memories = await client.search_memories("test_user", "testing", limit=5)
        logger.info(f"✅ Found {len(memories)} memories")
        
        # Get metrics
        metrics = client.get_metrics()
        logger.info(f"📊 Client metrics: {metrics}")
        
        # Cleanup
        await client.cleanup()
        
        logger.info("✅ Oracle Autonomous Database Hybrid Thin Mode Integration test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Oracle integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Load environment variables
    load_dotenv('.env.oracle')
    
    # Run test
    success = asyncio.run(test_oracle_integration())
    exit(0 if success else 1)