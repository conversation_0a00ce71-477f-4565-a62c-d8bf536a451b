<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MobiFone RAG - Knowledge Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --mobifone-blue: #0066cc;
            --mobifone-orange: #ff6600;
            --mobifone-light: #f8f9fa;
        }
        
        body {
            background-color: var(--mobifone-light);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--mobifone-blue), var(--mobifone-orange));
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s ease-in-out;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .drop-zone {
            border: 3px dashed var(--mobifone-blue);
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: linear-gradient(45deg, #ffffff, #f8f9fa);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .drop-zone.dragover {
            border-color: var(--mobifone-orange);
            background: linear-gradient(45deg, #fff3e6, #ffe6d9);
            transform: scale(1.02);
        }
        
        .drop-zone i {
            font-size: 3rem;
            color: var(--mobifone-blue);
            margin-bottom: 20px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--mobifone-blue), var(--mobifone-orange));
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
            font-weight: 600;
        }
        
        .btn-outline-primary {
            color: var(--mobifone-blue);
            border-color: var(--mobifone-blue);
            border-radius: 25px;
            padding: 8px 25px;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .upload-progress {
            display: none;
        }
        
        .file-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .file-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .format-badge {
            background: var(--mobifone-orange);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .search-results {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .source-doc {
            background: #e8f4f8;
            border-left: 4px solid var(--mobifone-blue);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        
        .answer-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand text-white" href="#">
                <i class="fas fa-brain me-2"></i>
                MobiFone RAG System
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="fas fa-database me-1"></i>
                    {{ total_docs }} tài liệu
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3><i class="fas fa-file-alt"></i> {{ total_docs }}</h3>
                        <p class="mb-0">Tổng tài liệu</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3><i class="fas fa-building"></i> {{ departments|length }}</h3>
                        <p class="mb-0">Phòng ban</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3><i class="fas fa-file-upload"></i> {{ supported_formats|length }}</h3>
                        <p class="mb-0">Định dạng hỗ trợ</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3><i class="fas fa-robot"></i> AI</h3>
                        <p class="mb-0">Gemma 3 Ready</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- File Upload Section -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-cloud-upload-alt me-2"></i>Upload Tài liệu</h5>
                    </div>
                    <div class="card-body">
                        <form id="uploadForm" enctype="multipart/form-data">
                            <!-- Drag & Drop Zone -->
                            <div class="drop-zone" id="dropZone">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <h4>Kéo thả files vào đây</h4>
                                <p class="text-muted">hoặc click để chọn files</p>
                                <input type="file" id="fileInput" multiple style="display: none;">
                                
                                <!-- Supported Formats -->
                                <div class="mt-3">
                                    <small class="text-muted">Hỗ trợ: </small>
                                    {% for format in supported_formats[:8] %}
                                        <span class="format-badge">{{ format }}</span>
                                    {% endfor %}
                                    {% if supported_formats|length > 8 %}
                                        <span class="format-badge">+{{ supported_formats|length - 8 }} more</span>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- File Options -->
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label class="form-label">Phòng ban</label>
                                    <div class="input-group">
                                        <select class="form-select" name="department" id="department">
                                            <option value="UNKNOWN">Chọn phòng ban</option>
                                            <option value="TTNS">TTNS - Trung tâm Nhân sự</option>
                                            <option value="TTTC">TTTC - Trung tâm Tài chính</option>
                                            <option value="TTPL">TTPL - Trung tâm Pháp lý</option>
                                            <option value="TTBV">TTBV - Trung tâm Bảo vệ</option>
                                            <option value="TTKTCN">TTKTCN - Trung tâm Kỹ thuật</option>
                                            <option value="TTKT">TTKT - Trung tâm Kế toán</option>
                                            <option value="TTKDDN">TTKDDN - Kinh doanh DN</option>
                                            <option value="custom">🔧 Tuỳ chỉnh...</option>
                                        </select>
                                        <input type="text" class="form-control" id="customDepartment" 
                                               placeholder="Nhập mã phòng ban" style="display: none;">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Loại tài liệu</label>
                                    <select class="form-select" name="doc_type" id="docType">
                                        <option value="document">Document</option>
                                        <option value="regulation">Quy định</option>
                                        <option value="procedure">Quy trình</option>
                                        <option value="policy">Chính sách</option>
                                        <option value="guide">Hướng dẫn</option>
                                        <option value="form">Biểu mẫu</option>
                                        <option value="report">Báo cáo</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Selected Files List -->
                            <div id="fileList" class="file-list mt-3" style="display: none;">
                                <h6>Files đã chọn:</h6>
                                <div id="selectedFiles"></div>
                            </div>

                            <!-- Upload Progress -->
                            <div class="upload-progress mt-3" id="uploadProgress">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                         style="width: 0%"></div>
                                </div>
                            </div>

                            <!-- Upload Button -->
                            <button type="submit" class="btn btn-primary mt-3" id="uploadBtn">
                                <i class="fas fa-upload me-2"></i>Upload & Import
                            </button>
                        </form>

                        <!-- Upload Results -->
                        <div id="uploadResults" class="mt-3" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Query Section -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-question-circle me-2"></i>Đặt câu hỏi</h5>
                    </div>
                    <div class="card-body">
                        <form id="queryForm">
                            <div class="mb-3">
                                <label class="form-label">Câu hỏi của bạn</label>
                                <textarea class="form-control" name="question" rows="3" 
                                         placeholder="Ví dụ: Quy định công tác phí có gì?"></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Lọc theo phòng ban</label>
                                    <select class="form-select" name="department">
                                        <option value="">Tất cả</option>
                                        {% for dept in departments %}
                                            <option value="{{ dept }}">{{ dept }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Số tài liệu tham khảo</label>
                                    <select class="form-select" name="max_docs">
                                        <option value="3">3 tài liệu</option>
                                        <option value="5">5 tài liệu</option>
                                        <option value="7">7 tài liệu</option>
                                    </select>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-success mt-3">
                                <i class="fas fa-search me-2"></i>Tìm câu trả lời
                            </button>
                        </form>

                        <!-- Query Results -->
                        <div id="queryResults" class="mt-3" style="display: none;">
                            <div class="answer-section" id="answerSection">
                                <h6><i class="fas fa-robot me-2"></i>Câu trả lời:</h6>
                                <div id="answerText"></div>
                            </div>
                            
                            <h6><i class="fas fa-book me-2"></i>Tài liệu tham khảo:</h6>
                            <div id="sourceDocs" class="search-results"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Documents Management -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-folder me-2"></i>Quản lý tài liệu</h5>
                        <button class="btn btn-outline-primary" onclick="loadDocuments()">
                            <i class="fas fa-sync me-2"></i>Làm mới
                        </button>
                    </div>
                    <div class="card-body">
                        <!-- Filters -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <select class="form-select" id="filterDepartment">
                                    <option value="">Tất cả phòng ban</option>
                                    {% for dept in departments %}
                                        <option value="{{ dept }}">{{ dept }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <select class="form-select" id="filterType">
                                    <option value="">Tất cả loại</option>
                                    <option value="regulation">Quy định</option>
                                    <option value="procedure">Quy trình</option>
                                    <option value="policy">Chính sách</option>
                                    <option value="guide">Hướng dẫn</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-primary" onclick="filterDocuments()">
                                    <i class="fas fa-filter me-2"></i>Lọc
                                </button>
                            </div>
                        </div>

                        <!-- Documents Table -->
                        <div id="documentsTable">
                            <div class="text-center">
                                <button class="btn btn-outline-primary" onclick="loadDocuments()">
                                    <i class="fas fa-eye me-2"></i>Xem danh sách tài liệu
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Drag & Drop functionality
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        
        dropZone.addEventListener('click', () => fileInput.click());
        
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });
        
        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });
        
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            fileInput.files = e.dataTransfer.files;
            displaySelectedFiles();
        });
        
        fileInput.addEventListener('change', displaySelectedFiles);
        
        // Handle department selection changes
        document.getElementById('department').addEventListener('change', function() {
            const customDeptInput = document.getElementById('customDepartment');
            if (this.value === 'custom') {
                customDeptInput.style.display = 'block';
                customDeptInput.required = true;
                this.style.display = 'none';
            } else {
                customDeptInput.style.display = 'none';
                customDeptInput.required = false;
            }
        });
        
        // Reset custom department when clicking back
        document.getElementById('customDepartment').addEventListener('blur', function() {
            if (!this.value.trim()) {
                document.getElementById('department').style.display = 'block';
                this.style.display = 'none';
                document.getElementById('department').value = 'UNKNOWN';
            }
        });
        
        function displaySelectedFiles() {
            const fileList = document.getElementById('fileList');
            const selectedFiles = document.getElementById('selectedFiles');
            
            if (fileInput.files.length > 0) {
                fileList.style.display = 'block';
                selectedFiles.innerHTML = '';
                
                Array.from(fileInput.files).forEach((file, index) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    fileItem.innerHTML = `
                        <span>
                            <i class="fas fa-file me-2"></i>
                            ${file.name} 
                            <small class="text-muted">(${(file.size / 1024 / 1024).toFixed(2)} MB)</small>
                        </span>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    selectedFiles.appendChild(fileItem);
                });
            } else {
                fileList.style.display = 'none';
            }
        }
        
        function removeFile(index) {
            const dt = new DataTransfer();
            Array.from(fileInput.files).forEach((file, i) => {
                if (i !== index) dt.items.add(file);
            });
            fileInput.files = dt.files;
            displaySelectedFiles();
        }
        
        // Upload form submission
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            if (fileInput.files.length === 0) {
                alert('Vui lòng chọn files để upload');
                return;
            }
            
            const formData = new FormData();
            Array.from(fileInput.files).forEach(file => {
                formData.append('files', file);
            });
            
            // Get department value (either dropdown or custom input)
            const deptSelect = document.getElementById('department');
            const customDept = document.getElementById('customDepartment');
            const departmentValue = deptSelect.value === 'custom' ? customDept.value : deptSelect.value;
            
            formData.append('department', departmentValue);
            formData.append('doc_type', document.getElementById('docType').value);
            
            // Show progress
            document.getElementById('uploadProgress').style.display = 'block';
            document.getElementById('uploadBtn').disabled = true;
            
            try {
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                displayUploadResults(result);
                
                // Reset form
                fileInput.value = '';
                displaySelectedFiles();
                
            } catch (error) {
                console.error('Upload error:', error);
                alert('Lỗi upload: ' + error.message);
            } finally {
                document.getElementById('uploadProgress').style.display = 'none';
                document.getElementById('uploadBtn').disabled = false;
            }
        });
        
        function displayUploadResults(result) {
            const resultsDiv = document.getElementById('uploadResults');
            resultsDiv.style.display = 'block';
            
            let html = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>Kết quả upload:</h6>
                    <p>✅ Thành công: ${result.successful}/${result.total_files} files</p>
                    ${result.failed > 0 ? `<p>❌ Thất bại: ${result.failed} files</p>` : ''}
                </div>
            `;
            
            if (result.results.length > 0) {
                html += '<div class="mt-2">';
                result.results.forEach(item => {
                    const alertClass = item.status === 'success' ? 'alert-success' : 'alert-danger';
                    const icon = item.status === 'success' ? 'check' : 'times';
                    html += `
                        <div class="alert ${alertClass} py-2">
                            <i class="fas fa-${icon} me-2"></i>
                            ${item.filename}: ${item.message || 'Thành công'}
                        </div>
                    `;
                });
                html += '</div>';
            }
            
            resultsDiv.innerHTML = html;
        }
        
        // Query form submission
        document.getElementById('queryForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            
            if (!formData.get('question').trim()) {
                alert('Vui lòng nhập câu hỏi');
                return;
            }
            
            try {
                const response = await fetch('/query', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                displayQueryResults(result);
                
            } catch (error) {
                console.error('Query error:', error);
                alert('Lỗi truy vấn: ' + error.message);
            }
        });
        
        function displayQueryResults(result) {
            const resultsDiv = document.getElementById('queryResults');
            const answerText = document.getElementById('answerText');
            const sourceDocs = document.getElementById('sourceDocs');
            
            resultsDiv.style.display = 'block';
            
            // Display answer
            answerText.innerHTML = result.answer.replace(/\n/g, '<br>');
            
            // Display source documents
            sourceDocs.innerHTML = '';
            result.sources.forEach((doc, index) => {
                const sourceDiv = document.createElement('div');
                sourceDiv.className = 'source-doc';
                sourceDiv.innerHTML = `
                    <h6>${index + 1}. ${doc.title}</h6>
                    <p><small class="text-muted">
                        ${doc.department} | ${doc.document_type} | Similarity: ${(doc.similarity * 100).toFixed(1)}%
                    </small></p>
                    <p>${doc.content.substring(0, 200)}...</p>
                `;
                sourceDocs.appendChild(sourceDiv);
            });
        }
        

        
        // Filter documents
        async function filterDocuments() {
            const department = document.getElementById('filterDepartment').value;
            const docType = document.getElementById('filterType').value;
            
            let url = '/documents?limit=50';
            if (department) url += `&department=${department}`;
            if (docType) url += `&doc_type=${docType}`;
            
            try {
                const response = await fetch(url);
                const result = await response.json();
                
                // Update table with filtered results
                updateDocumentsTable(result);
                
            } catch (error) {
                console.error('Filter error:', error);
                alert('Lỗi lọc tài liệu: ' + error.message);
            }
        }
        
        // Helper function to update documents table
        function updateDocumentsTable(result) {
            const tableDiv = document.getElementById('documentsTable');
            
            if (!result.documents || result.documents.length === 0) {
                tableDiv.innerHTML = '<p class="text-center text-muted">Không tìm thấy tài liệu nào</p>';
                return;
            }
            
            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-primary">
                            <tr>
                                <th>Tiêu đề</th>
                                <th>Phòng ban</th>
                                <th>Loại</th>
                                <th>Ngày cập nhật</th>
                                <th>Độ dài</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            result.documents.forEach(doc => {
                html += `
                    <tr>
                        <td><strong>${doc.title}</strong></td>
                        <td><span class="badge bg-primary">${doc.department || 'UNKNOWN'}</span></td>
                        <td><span class="badge bg-secondary">${doc.document_type || 'document'}</span></td>
                        <td>${doc.last_updated || 'N/A'}</td>
                        <td>${doc.content ? doc.content.length : 0} chars</td>
                        <td>
                            <button class="btn btn-sm btn-outline-info" onclick="previewDocument('${doc.doc_id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            html += `
                        </tbody>
                    </table>
                </div>
                <p class="text-muted">Hiển thị ${result.documents.length} tài liệu</p>
            `;
            
            tableDiv.innerHTML = html;
        }
        
        // Load documents function updated
        async function loadDocuments() {
            try {
                const response = await fetch('/documents?limit=20');
                const result = await response.json();
                updateDocumentsTable(result);
                
            } catch (error) {
                console.error('Load documents error:', error);
                alert('Lỗi tải danh sách tài liệu');
            }
        }
        
        // Preview document function
        function previewDocument(docId) {
            alert(`Preview for document: ${docId}\n(Feature coming soon)`);
        }
        
        // Auto-load documents when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadDocuments();
        });
    </script>
</body>
</html> 