#!/bin/bash

echo "🤖 Auto-Adding Model Connections to Open WebUI"
echo "=============================================="

# Check if Open WebUI is running
if ! curl -s -f http://localhost:3000 >/dev/null; then
    echo "❌ Open WebUI is not running on port 3000"
    echo "Please run ./restart-webui-with-tika.sh first"
    exit 1
fi

echo "✅ Open WebUI is running"

echo ""
echo "🧪 Testing model APIs first..."

# Test Qwen API
if curl -s -f http://localhost:11435/v1/models >/dev/null; then
    echo "✅ Qwen API working on port 11435"
    QWEN_WORKING=true
else
    echo "❌ Qwen API not working on port 11435"
    QWEN_WORKING=false
fi

# Test Gemma API  
if curl -s -f http://localhost:11434/v1/models >/dev/null; then
    echo "✅ Gemma API working on port 11434"
    GEMMA_WORKING=true
else
    echo "❌ Gemma API not working on port 11434"
    GEMMA_WORKING=false
fi

echo ""
echo "🔧 Attempting to add model connections..."

# Function to add OpenAI connection
add_openai_connection() {
    local name="$1"
    local base_url="$2"
    
    echo "🔗 Adding connection: $name"
    
    # Try different API endpoints that might work
    endpoints=(
        "/api/v1/auths/openai"
        "/api/v1/configs/openai" 
        "/api/openai/config"
        "/api/connections"
        "/api/models/openai"
    )
    
    for endpoint in "${endpoints[@]}"; do
        echo "   Trying endpoint: $endpoint"
        
        # Create the JSON payload
        payload=$(cat <<EOF
{
  "name": "$name",
  "url": "$base_url", 
  "key": "sk-dummy-key",
  "active": true
}
EOF
)
        
        # Try to add the connection
        response=$(curl -s -X POST "http://localhost:3000$endpoint" \
            -H "Content-Type: application/json" \
            -H "Accept: application/json" \
            -d "$payload" 2>/dev/null)
        
        if [ $? -eq 0 ] && [ ! -z "$response" ]; then
            echo "   ✅ Response received from $endpoint"
            echo "   Response: $response"
            break
        else
            echo "   ⚠️  No response from $endpoint"
        fi
    done
}

# Add connections if APIs are working
if [ "$QWEN_WORKING" = true ]; then
    add_openai_connection "Qwen2.5-Coder-7B" "http://localhost:11435/v1"
fi

if [ "$GEMMA_WORKING" = true ]; then
    add_openai_connection "Gemma-3-4B" "http://localhost:11434/v1"
fi

echo ""
echo "📋 Manual Configuration Required"
echo "================================"
echo ""
echo "The automatic API approach may not work due to authentication."
echo "Please follow these manual steps:"
echo ""

echo "🌐 1. Open browser: http://localhost:3000"
echo ""

echo "👤 2. Sign in to your account"
echo ""

echo "⚙️  3. Go to Settings:"
echo "   • Click your profile picture (top right)"
echo "   • Select 'Settings' or 'Admin Panel'"
echo ""

echo "🔗 4. Find 'Connections' section:"
echo "   • Look for 'Connections', 'OpenAI', 'External APIs', or 'Models'"
echo "   • This might be under 'Admin' → 'Settings' → 'Connections'"
echo ""

echo "➕ 5. Add Model Connections:"
echo ""

if [ "$QWEN_WORKING" = true ]; then
echo "   🤖 Qwen2.5-Coder-7B Connection:"
echo "   • Name: Qwen2.5-Coder-7B"
echo "   • Type: OpenAI Compatible" 
echo "   • API Base URL: http://localhost:11435/v1"
echo "   • API Key: sk-dummy (or leave empty)"
echo "   • Click 'Test Connection' then 'Save'"
echo ""
fi

if [ "$GEMMA_WORKING" = true ]; then
echo "   🤖 Gemma-3-4B Connection:"
echo "   • Name: Gemma-3-4B"
echo "   • Type: OpenAI Compatible"
echo "   • API Base URL: http://localhost:11434/v1" 
echo "   • API Key: sk-dummy (or leave empty)"
echo "   • Click 'Test Connection' then 'Save'"
echo ""
fi

echo "🎯 6. Verify in Chat:"
echo "   • Go back to Chat interface"
echo "   • Look for model selector dropdown"
echo "   • Both models should now be available"

echo ""
echo "🔍 Alternative locations to look for settings:"
echo "=============================================="
echo "• Settings → Connections"
echo "• Settings → External Services"  
echo "• Settings → OpenAI API"
echo "• Admin Panel → Models"
echo "• Admin Panel → Connections"
echo "• Profile Menu → Admin Settings"

echo ""
echo "🧪 Quick Test Commands:"
echo "======================"
echo "# Test Qwen directly:"
echo "curl -X POST http://localhost:11435/v1/chat/completions \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"model\":\"qwen\",\"messages\":[{\"role\":\"user\",\"content\":\"Hello\"}],\"max_tokens\":50}'"
echo ""
echo "# Test Gemma directly:"  
echo "curl -X POST http://localhost:11434/v1/chat/completions \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"model\":\"gemma\",\"messages\":[{\"role\":\"user\",\"content\":\"Hello\"}],\"max_tokens\":50}'"

echo ""
echo "📊 Current Status:"
echo "================="
echo "• Open WebUI: ✅ http://localhost:3000"
if [ "$QWEN_WORKING" = true ]; then
    echo "• Qwen API: ✅ http://localhost:11435/v1"
else
    echo "• Qwen API: ❌ Not responding"
fi

if [ "$GEMMA_WORKING" = true ]; then
    echo "• Gemma API: ✅ http://localhost:11434/v1"
else
    echo "• Gemma API: ❌ Not responding"
fi

echo "• Tika Engine: ✅ http://localhost:9998"

echo ""
echo "💡 Tips:"
echo "========"
echo "• If models don't appear: Refresh browser (Ctrl+F5)"
echo "• Check API URLs use 'http' not 'https'"
echo "• Make sure to include '/v1' at the end of URLs"
echo "• Some versions use 'External APIs' instead of 'Connections'"

echo ""
echo "✨ After adding connections, your local models will be available!" 