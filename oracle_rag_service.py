#!/usr/bin/env python3
"""
Oracle RAG Service for AI Assistant
D<PERSON><PERSON> vụ RAG sử dụng Oracle 19C Autonomous Database với hybrid approach
"""

import asyncio
import logging
import hashlib
import uuid
import json
import numpy as np
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

# FastAPI
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

# Oracle Database
import oracledb
from concurrent.futures import ThreadPoolExecutor

# AI/ML  
from sentence_transformers import SentenceTransformer
import tiktoken

# Local imports
from oracle_oci_config import OracleOCIConfig, OracleAIConfig

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Pydantic Models
class OracleDocument(BaseModel):
    doc_id: str
    title: str
    content: str
    file_type: str
    metadata: Dict[str, Any] = {}

class OracleSearchRequest(BaseModel):
    query: str
    top_k: int = 5
    search_type: str = "hybrid"  # "text", "semantic", "hybrid"
    filters: Dict[str, Any] = {}

class OracleSearchResult(BaseModel):
    doc_id: str
    title: str
    content: str
    score: float
    metadata: Dict[str, Any]

class OracleRAGService:
    """Oracle RAG Service với hybrid approach cho Oracle 19C"""
    
    def __init__(self, oracle_config: OracleOCIConfig = None, ai_config: OracleAIConfig = None):
        self.oracle_config = oracle_config or OracleOCIConfig()
        self.ai_config = ai_config or OracleAIConfig()
        
        # Connection pool
        self.pool = None
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Embedding model (for hybrid approach)
        self.embedding_model = None
        
        # Tokenizer for text chunking
        self.tokenizer = tiktoken.get_encoding("cl100k_base")
        
        # Performance metrics
        self.metrics = {
            'total_documents': 0,
            'total_searches': 0,
            'avg_search_time': 0.0,
            'oracle_connection_status': False
        }
    
    async def initialize(self) -> bool:
        """Initialize Oracle RAG service"""
        logger.info("🚀 Initializing Oracle RAG Service...")
        
        try:
            # Initialize Oracle connection pool
            await self._setup_oracle_pool()
            
            # Setup database schema
            await self._setup_database_schema()
            
            # Initialize embedding model (for hybrid approach)
            await self._initialize_embedding_model()
            
            logger.info("✅ Oracle RAG Service initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Oracle RAG Service: {e}")
            return False
    
    async def _setup_oracle_pool(self):
        """Setup Oracle connection pool"""
        try:
            # Set wallet location if provided
            if self.oracle_config.oracle_wallet_location:
                oracledb.init_oracle_client(config_dir=self.oracle_config.oracle_wallet_location)
            
            # Create connection pool (synchronous operation in thread)
            loop = asyncio.get_event_loop()
            self.pool = await loop.run_in_executor(
                self.executor,
                self._create_oracle_pool
            )
            
            self.metrics['oracle_connection_status'] = True
            logger.info("✅ Oracle connection pool created")
            
        except Exception as e:
            logger.error(f"❌ Failed to create Oracle pool: {e}")
            raise
    
    def _create_oracle_pool(self):
        """Create Oracle connection pool (synchronous)"""
        return oracledb.create_pool(
            user=self.oracle_config.oracle_user,
            password=self.oracle_config.oracle_password,
            dsn=self.oracle_config.oracle_dsn,
            min=self.oracle_config.pool_min,
            max=self.oracle_config.pool_max,
            increment=self.oracle_config.pool_increment
        )

# FastAPI app for Oracle RAG service
app = FastAPI(title="Oracle RAG Service", version="1.0.0")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8030)
