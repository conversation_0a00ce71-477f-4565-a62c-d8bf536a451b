
# Open WebUI Brave Search Rate Limit Fix

## Manual Configuration Steps:

1. Access Open WebUI Admin Settings:
   http://**************:3000/admin/settings

2. In Web Search section:
   ✅ Enable Web Search
   ✅ Search Engine: brave
   ✅ API Key: BSAXYx3BRnMxPu_xUghx8Vw3GgG3dlW
   ✅ Result Count: 2 (reduced to minimize API calls)
   ✅ Concurrent Requests: 1

3. Usage Tips:
   - Wait 2-3 seconds between searches
   - Don't spam search requests
   - Use web search only when necessary
   - Monitor your quota: 1984 requests remaining

4. If still getting 429 errors:
   - Wait 60 seconds before trying again
   - Clear browser cache
   - Restart Open WebUI if needed

## Current Status:
- API Key: ✅ Valid
- Rate Limit: ⚠️ 1 request/second (strict)
- Monthly Quota: ✅ 1984/2000 remaining
