#!/usr/bin/env python3

def main():
    print("🔧 Final DoclingLoader fix with table extraction...")
    
    # Read the current file
    with open('/app/backend/open_webui/retrieval/loaders/main.py', 'r') as f:
        content = f.read()
    
    # Find and replace the old text extraction logic
    old_pattern = 'text = document_data.get("md_content", "<No text content found>")'
    
    if old_pattern in content:
        new_logic = '''# FIXED: Handle both old and new Docling response formats
            text = "<No text content found>"
            
            # Try new format first (content as list)
            if "content" in result:
                content_data = result["content"]
                if isinstance(content_data, list):
                    # Join list items with newlines
                    text = "\\n\\n".join(str(item) for item in content_data if item)
                elif isinstance(content_data, str):
                    text = content_data
                    
            # Fallback to old format (document.md_content)
            elif "document" in result:
                document_data = result.get("document", {})
                text = document_data.get("md_content", text)
            
            # ENHANCED: Extract tables if available
            text_parts = [text] if text != "<No text content found>" else []
            
            if "tables" in result and result["tables"]:
                import re
                for table in result["tables"]:
                    if isinstance(table, dict) and "markdown" in table:
                        markdown_str = table["markdown"]
                        # Extract text values using regex
                        text_matches = re.findall(r"text='([^']*)'", markdown_str)
                        
                        if text_matches:
                            # Remove duplicates while preserving order
                            seen = set()
                            unique_texts = []
                            for text_item in text_matches:
                                if text_item not in seen and text_item.strip():
                                    seen.add(text_item)
                                    unique_texts.append(text_item)
                            
                            if unique_texts:
                                # Format as readable table
                                table_text = "TABLE DATA:\\n" + "\\n".join(f"- {text_item}" for text_item in unique_texts)
                                text_parts.append(table_text)
            
            # Handle images (placeholder for now)
            if "images" in result and result["images"]:
                image_text = f"[DOCUMENT CONTAINS {len(result['images'])} IMAGES]"
                text_parts.append(image_text)
            
            # Combine all parts
            if text_parts:
                text = "\\n\\n".join(text_parts)
            else:
                text = "<No content found>"'''
        
        new_content = content.replace(old_pattern, new_logic)
        
        # Write back to file
        with open('/app/backend/open_webui/retrieval/loaders/main.py', 'w') as f:
            f.write(new_content)
        
        print("✅ DoclingLoader fixed with table extraction support")
    else:
        print("❌ Pattern not found, DoclingLoader may already be updated")

if __name__ == "__main__":
    main()