# 🛡️ Cloudflare Resistant <PERSON><PERSON> Crawler Features

## 🚀 **T<PERSON>h năng chính**

### **1. Cloudflare Resistance**
- **TLS Client Bypass**: Sử dụng `tls-client` để vượt qua Cloudflare
- **User-Agent Rotation**: Tự động thay đổi User-Agent đ<PERSON> tránh phát hiện
- **Stealth Headers**: Headers gi<PERSON> lập trình duyệt thực tế
- **Fallback Mechanism**: Quay lại aiohttp nếu TLS bypass thất bại

### **2. Cải tiến xử lý nội dung**
- **Improved News Extraction**: Prompt tùy chỉnh để trích xuất tin tức tốt hơn
- **Increased Content Limits**: Xử lý đến 20,000 ký tự (từ 10,000)
- **Better Output Tokens**: 8,192 tokens đầu ra (từ 2,048)
- **Multiple Task Types**: Hỗ trợ nhiều loại xử lý khác nhau

### **3. <PERSON><PERSON><PERSON> hợp Open WebUI**
- **Full OpenAPI 3.0**: Chuẩn OpenAPI hoàn chỉnh
- **Pydantic Validation**: Xác thực yêu cầu đầu vào
- **7 Tools Available**: 7 công cụ mạnh mẽ
- **No Authentication**: Không cần xác thực

## 🛠️ **Các công cụ có sẵn**

### **1. crawl_url**
```json
{
  "url": "https://example.com",
  "max_content_length": 20000,
  "task_type": "news_extraction"
}
```
**Tính năng**: Crawl và xử lý 1 URL với Cloudflare resistance

### **2. crawl_batch**
```json
{
  "urls": ["https://site1.com", "https://site2.com"],
  "max_content_length": 20000,
  "task_type": "news_extraction"
}
```
**Tính năng**: Crawl nhiều URL đồng thời với Cloudflare resistance

### **3. extract_news**
```json
{
  "url": "https://news-site.com"
}
```
**Tính năng**: Trích xuất tin tức với prompt tùy chỉnh

### **4. crawl_with_fallback**
```json
{
  "url": "https://difficult-site.com"
}
```
**Tính năng**: Crawl với nhiều phương pháp fallback để tỷ lệ thành công tối đa

### **5. health_check**
```json
{}
```
**Tính năng**: Kiểm tra sức khỏe hệ thống

### **6. get_stats**
```json
{}
```
**Tính năng**: Thống kê crawler và tính năng

### **7. test_crawl**
```json
{}
```
**Tính năng**: Test crawl với URL mẫu

## 🔒 **Chi tiết Cloudflare Resistance**

### **TLS Client Bypass**
```python
# Sử dụng tls-client để giả lập trình duyệt thực tế
session = tls_client.Session(
    random_tls_extension_order=True,
    client_identifier='chrome_120'
)
```

### **User-Agent Rotation**
```python
# Tự động thay đổi User-Agent
user_agents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    # ... nhiều User-Agent khác
]
```

### **Stealth Headers**
```python
headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'accept-language': 'en-US,en;q=0.9',
    'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'none',
    'user-agent': selected_user_agent
}
```

### **Fallback Mechanism**
1. **TLS Bypass First**: Thử vượt qua Cloudflare bằng TLS client
2. **aiohttp Fallback**: Nếu TLS thất bại, quay lại aiohttp thông thường
3. **Multiple Methods**: Thử nhiều phương pháp xử lý khác nhau

## 📊 **Thông số kỹ thuật**

### **Model**: `gemini-2.5-flash-lite`
### **Generation Config**:
```json
{
  "temperature": 0.0,
  "maxOutputTokens": 8192,
  "topK": 1,
  "topP": 1.0
}
```

### **Content Limits**:
- **Max Input**: 20,000 characters
- **Max Output**: 8,192 tokens
- **Processing Time**: ~3-5 seconds

### **Retry Logic**:
```python
for attempt in range(max_retries + 1):
    try:
        # API call
    except Exception:
        wait_time = 2 ** attempt  # Exponential backoff
        await asyncio.sleep(wait_time)
```

## 🎯 **Use Cases**

### **1. News Aggregation**
- Tổng hợp tin tức từ các trang bị Cloudflare bảo vệ
- Trích xuất tin tức có cấu trúc
- Giữ nguyên tất cả tin tức

### **2. Content Research**
- Nghiên cứu nội dung từ các trang web khó crawl
- Xử lý nội dung bị chặn bởi bot detection
- Trích xuất thông tin chi tiết

### **3. Web Scraping**
- Crawl dữ liệu từ các trang web hiện đại
- Vượt qua các hệ thống bảo vệ bot
- Xử lý nội dung động

## 🚀 **Ưu điểm**

1. **Vượt Cloudflare**: TLS client bypass hiệu quả
2. **Tỷ lệ thành công cao**: Cơ chế fallback đa tầng
3. **Tốc độ xử lý**: Vẫn nhanh nhờ Gemini Flash Lite
4. **Tối ưu tiếng Việt**: Bảo tồn chính xác văn bản
5. **Tương thích Open WebUI**: Hoàn toàn tích hợp
6. **Dễ sử dụng**: API đơn giản, tài liệu đầy đủ

## 📈 **So sánh các phiên bản**

| Phiên bản | Cloudflare Resistance | Tools | Content Limit | Output Tokens |
|-----------|----------------------|-------|---------------|---------------|
| Simple Jina Crawler | ❌ Không | 5 | 10,000 | 2,048 |
| Standard Jina Crawler | ❌ Không | 5 | 10,000 | 2,048 |
| Final Jina Crawler | ❌ Không | 5 | 10,000 | 2,048 |
| **Cloudflare Resistant** | ✅ **Có** | **7** | **20,000** | **8,192** |

## 📋 **Kết nối với Open WebUI**

### **URL**: `http://cloudflare-resistant-jina-crawler:8009/openapi.json`

### **Container**: `cloudflare-resistant-jina-crawler`
### **Port**: `8009`
### **Network**: `acca-network`

---

**📝 Note**: Cloudflare Resistant Jina Crawler là phiên bản mạnh mẽ nhất, có khả năng vượt qua Cloudflare và các hệ thống bot detection khác, đồng thời cung cấp chất lượng xử lý nội dung cao nhất.