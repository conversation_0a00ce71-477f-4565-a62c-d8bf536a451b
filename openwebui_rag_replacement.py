"""
Open WebUI RAG Knowledge System Replacement
===========================================

This module provides a complete drop-in replacement for Open WebUI's built-in
Knowledge RAG system, using our optimized RAG pipeline while maintaining
full API compatibility.

Key Features:
- Full API compatibility with existing /knowledge and /retrieval endpoints
- Enhanced Vietnamese text processing
- Context-aware chunking and intelligent retrieval
- Rich metadata and citation support
- Seamless integration with existing Qdrant vector database
"""

import json
import logging
import asyncio
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from pathlib import Path

from fastapi import APIRouter, Depends, HTTPException, Request, status
from pydantic import BaseModel
import tiktoken

# Import our optimized RAG components
from rag_optimization_system import (
    RAGOptimizationSystem,
    VietnameseTextProcessor,
    ContextAwareChunker,
    IntelligentRetriever,
    ConversationMetadata,
    ChunkData
)

# Import existing Open WebUI components we need to maintain compatibility
from open_webui.models.files import FileModel, Files
from open_webui.models.knowledge import (
    Knowledges,
    KnowledgeForm,
    KnowledgeResponse,
    KnowledgeUserResponse,
    KnowledgeFilesResponse
)
from open_webui.retrieval.vector.factory import VECTOR_DB_CLIENT
from open_webui.utils.auth import get_verified_user
from open_webui.utils.access_control import has_access
from open_webui.constants import ERROR_MESSAGES

log = logging.getLogger(__name__)

class EnhancedRAGKnowledgeSystem:
    """
    Enhanced RAG Knowledge System that replaces Open WebUI's built-in RAG
    while maintaining full API compatibility.
    """
    
    def __init__(self):
        self.rag_system = RAGOptimizationSystem()
        self.text_processor = VietnameseTextProcessor()
        self.chunker = ContextAwareChunker()
        self.retriever = IntelligentRetriever()
        
        # Cache for performance optimization
        self._embedding_cache = {}
        self._collection_cache = {}
        
    async def process_document_enhanced(
        self, 
        file_model: FileModel, 
        collection_name: str,
        user_id: str
    ) -> Dict[str, Any]:
        """
        Enhanced document processing with our optimized pipeline.
        
        Args:
            file_model: The file to process
            collection_name: Target collection name (knowledge base ID)
            user_id: User ID for access control
            
        Returns:
            Processing result with metadata
        """
        try:
            # Extract text content
            text_content = file_model.data.get("content", "")
            if not text_content:
                raise ValueError("No content found in file")
            
            # Create conversation metadata for document context
            metadata = ConversationMetadata(
                conversation_id=collection_name,
                timestamp=datetime.now(),
                participants=[user_id],
                topic=f"Document: {file_model.filename}",
                context_type="document",
                source_file=file_model.filename,
                file_id=file_model.id
            )
            
            # Process with our enhanced pipeline
            processed_chunks = await self.rag_system.process_conversation(
                conversation_text=text_content,
                metadata=metadata
            )
            
            # Convert to Open WebUI compatible format and store in Qdrant
            documents = []
            for chunk in processed_chunks:
                doc_metadata = {
                    "file_id": file_model.id,
                    "source": file_model.filename,
                    "chunk_id": chunk.chunk_id,
                    "chunk_type": chunk.chunk_type,
                    "confidence_score": chunk.confidence_score,
                    "keywords": chunk.keywords,
                    "topic": chunk.topic,
                    "timestamp": chunk.timestamp.isoformat(),
                    "created_by": user_id,
                    "name": file_model.filename,
                    **file_model.meta
                }
                
                # Store in Qdrant using existing vector client
                VECTOR_DB_CLIENT.upsert(
                    collection_name=collection_name,
                    points=[{
                        "id": chunk.chunk_id,
                        "vector": chunk.embedding,
                        "payload": {
                            "content": chunk.content,
                            **doc_metadata
                        }
                    }]
                )
                
                documents.append({
                    "content": chunk.content,
                    "metadata": doc_metadata
                })
            
            return {
                "status": "success",
                "chunks_created": len(processed_chunks),
                "documents": documents,
                "collection_name": collection_name
            }
            
        except Exception as e:
            log.error(f"Enhanced document processing failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Document processing failed: {str(e)}"
            )
    
    async def query_knowledge_enhanced(
        self,
        collection_name: str,
        query: str,
        k: int = 5,
        user_id: str = None
    ) -> Dict[str, Any]:
        """
        Enhanced knowledge querying with intelligent retrieval.
        
        Args:
            collection_name: Knowledge base collection name
            query: User query
            k: Number of results to return
            user_id: User ID for access control
            
        Returns:
            Enhanced query results with citations
        """
        try:
            # Use our intelligent retriever
            results = await self.rag_system.query_memory(
                query=query,
                conversation_id=collection_name,
                top_k=k
            )
            
            # Convert to Open WebUI compatible format
            formatted_results = []
            for result in results:
                # Extract citation information
                metadata = result.get("metadata", {})
                citation = {
                    "source": metadata.get("source", "Unknown"),
                    "file_id": metadata.get("file_id"),
                    "chunk_id": metadata.get("chunk_id"),
                    "confidence": metadata.get("confidence_score", 0.0),
                    "page": metadata.get("page", 1),
                    "keywords": metadata.get("keywords", [])
                }
                
                formatted_results.append({
                    "content": result["content"],
                    "metadata": metadata,
                    "citation": citation,
                    "score": result.get("score", 0.0)
                })
            
            return {
                "status": "success",
                "results": formatted_results,
                "query": query,
                "total_results": len(formatted_results)
            }
            
        except Exception as e:
            log.error(f"Enhanced knowledge query failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Knowledge query failed: {str(e)}"
            )

# Global instance
enhanced_rag_system = EnhancedRAGKnowledgeSystem()

# API Models for compatibility
class ProcessFileForm(BaseModel):
    file_id: str
    collection_name: str

class QueryDocForm(BaseModel):
    collection_name: str
    query: str
    k: Optional[int] = 5
    k_reranker: Optional[int] = None
    r: Optional[float] = None
    hybrid: Optional[bool] = None

class KnowledgeFileIdForm(BaseModel):
    file_id: str

# Enhanced API endpoints that replace the original ones
router = APIRouter()

@router.post("/process/file/enhanced")
async def process_file_enhanced(
    request: Request,
    form_data: ProcessFileForm,
    user=Depends(get_verified_user)
):
    """
    Enhanced file processing endpoint that replaces the original process_file.
    Maintains API compatibility while using our optimized pipeline.
    """
    try:
        # Get file from database
        file = Files.get_file_by_id(form_data.file_id)
        if not file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.NOT_FOUND
            )
        
        # Check if file has content
        if not file.data or not file.data.get("content"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=ERROR_MESSAGES.FILE_NOT_PROCESSED
            )
        
        # Process with enhanced pipeline
        result = await enhanced_rag_system.process_document_enhanced(
            file_model=file,
            collection_name=form_data.collection_name,
            user_id=user.id
        )
        
        # Update file metadata
        Files.update_file_metadata_by_id(
            form_data.file_id,
            {"collection_name": form_data.collection_name, "enhanced_rag": True}
        )
        
        return {
            "status": True,
            "file_id": form_data.file_id,
            "collection_name": form_data.collection_name,
            "chunks_created": result["chunks_created"],
            "message": "File processed with enhanced RAG pipeline"
        }
        
    except Exception as e:
        log.error(f"Enhanced file processing error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post("/query/doc/enhanced")
async def query_doc_enhanced(
    request: Request,
    form_data: QueryDocForm,
    user=Depends(get_verified_user)
):
    """
    Enhanced document querying endpoint with intelligent retrieval and citations.
    """
    try:
        # Query with enhanced system
        results = await enhanced_rag_system.query_knowledge_enhanced(
            collection_name=form_data.collection_name,
            query=form_data.query,
            k=form_data.k or 5,
            user_id=user.id
        )
        
        return results
        
    except Exception as e:
        log.error(f"Enhanced query error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post("/knowledge/{id}/file/add/enhanced")
async def add_file_to_knowledge_enhanced(
    request: Request,
    id: str,
    form_data: KnowledgeFileIdForm,
    user=Depends(get_verified_user)
):
    """
    Enhanced endpoint for adding files to knowledge base with optimized processing.
    """
    try:
        # Check knowledge base exists and user has access
        knowledge = Knowledges.get_knowledge_by_id(id=id)
        if not knowledge:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.NOT_FOUND
            )
        
        if (
            knowledge.user_id != user.id
            and not has_access(user.id, "write", knowledge.access_control)
            and user.role != "admin"
        ):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=ERROR_MESSAGES.ACCESS_PROHIBITED
            )
        
        # Get file
        file = Files.get_file_by_id(form_data.file_id)
        if not file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ERROR_MESSAGES.NOT_FOUND
            )
        
        # Process with enhanced pipeline
        result = await enhanced_rag_system.process_document_enhanced(
            file_model=file,
            collection_name=id,
            user_id=user.id
        )
        
        # Update knowledge base data
        data = knowledge.data or {}
        file_ids = data.get("file_ids", [])
        
        if form_data.file_id not in file_ids:
            file_ids.append(form_data.file_id)
            data["file_ids"] = file_ids
            data["enhanced_rag"] = True  # Mark as using enhanced RAG
            
            knowledge = Knowledges.update_knowledge_data_by_id(id=id, data=data)
            
            if knowledge:
                files = Files.get_file_metadatas_by_ids(file_ids)
                return KnowledgeFilesResponse(
                    **knowledge.model_dump(),
                    files=files,
                    processing_info={
                        "enhanced_rag": True,
                        "chunks_created": result["chunks_created"]
                    }
                )
        
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File already exists in knowledge base"
        )
        
    except Exception as e:
        log.error(f"Enhanced add file error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

# Compatibility layer functions
async def migrate_existing_knowledge_base(knowledge_id: str, user_id: str):
    """
    Migrate an existing knowledge base to use the enhanced RAG system.
    """
    try:
        knowledge = Knowledges.get_knowledge_by_id(knowledge_id)
        if not knowledge:
            return False
        
        data = knowledge.data or {}
        file_ids = data.get("file_ids", [])
        
        migrated_count = 0
        for file_id in file_ids:
            file = Files.get_file_by_id(file_id)
            if file and file.data and file.data.get("content"):
                try:
                    await enhanced_rag_system.process_document_enhanced(
                        file_model=file,
                        collection_name=knowledge_id,
                        user_id=user_id
                    )
                    migrated_count += 1
                except Exception as e:
                    log.error(f"Failed to migrate file {file_id}: {str(e)}")
        
        # Mark knowledge base as migrated
        data["enhanced_rag"] = True
        data["migrated_files"] = migrated_count
        Knowledges.update_knowledge_data_by_id(id=knowledge_id, data=data)
        
        return True
        
    except Exception as e:
        log.error(f"Knowledge base migration failed: {str(e)}")
        return False

@router.post("/migrate/knowledge/{id}")
async def migrate_knowledge_base(
    id: str,
    user=Depends(get_verified_user)
):
    """
    Migrate an existing knowledge base to use enhanced RAG.
    """
    try:
        success = await migrate_existing_knowledge_base(id, user.id)
        if success:
            return {"status": "success", "message": "Knowledge base migrated to enhanced RAG"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Migration failed"
            )
    except Exception as e:
        log.error(f"Migration endpoint error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

# Health check endpoint
@router.get("/enhanced/health")
async def enhanced_rag_health():
    """
    Health check for enhanced RAG system.
    """
    try:
        # Test basic functionality
        test_result = await enhanced_rag_system.rag_system.query_memory(
            query="test",
            conversation_id="health_check",
            top_k=1
        )
        
        return {
            "status": "healthy",
            "enhanced_rag": True,
            "components": {
                "text_processor": True,
                "chunker": True,
                "retriever": True,
                "vector_db": True
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }