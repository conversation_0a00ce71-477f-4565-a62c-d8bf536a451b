<template>
  <div class="memory-search">
    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
      Search Memories
    </h2>

    <!-- Search Form -->
    <div class="mb-6 p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
      <form @submit.prevent="performSearch" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Search Query
          </label>
          <div class="flex space-x-3">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Enter your search query..."
              class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <button
              type="submit"
              :disabled="!searchQuery.trim() || loading"
              class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
            >
              <svg v-if="loading" class="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span v-else>Search</span>
            </button>
          </div>
        </div>

        <div class="flex items-center space-x-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Results Limit
            </label>
            <select
              v-model.number="searchLimit"
              class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option :value="3">3 results</option>
              <option :value="5">5 results</option>
              <option :value="10">10 results</option>
              <option :value="20">20 results</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              User ID
            </label>
            <input
              v-model="searchUserId"
              type="text"
              placeholder="default_user"
              class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>
        </div>
      </form>
    </div>

    <!-- Search Stats -->
    <div v-if="searchResults.length > 0 || hasSearched" class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          <span v-if="searchResults.length > 0">
            Found {{ searchResults.length }} result{{ searchResults.length !== 1 ? 's' : '' }}
            for "{{ lastSearchQuery }}"
          </span>
          <span v-else-if="hasSearched">
            No results found for "{{ lastSearchQuery }}"
          </span>
        </div>
        
        <div v-if="searchTime" class="text-xs text-gray-500 dark:text-gray-400">
          Search took {{ searchTime }}ms
        </div>
      </div>
    </div>

    <!-- Search Results -->
    <div v-if="searchResults.length > 0" class="space-y-4">
      <div
        v-for="(result, index) in searchResults"
        :key="result.id"
        class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700"
      >
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <!-- Relevance Score -->
            <div class="flex items-center mb-2">
              <span class="text-xs font-medium px-2 py-1 rounded-full" :class="[
                getRelevanceColor(result.score || 0)
              ]">
                Relevance: {{ ((result.score || 0) * 100).toFixed(1) }}%
              </span>
              <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">
                Rank #{{ index + 1 }}
              </span>
            </div>

            <!-- Memory Content -->
            <p class="text-gray-900 dark:text-white mb-2">{{ result.memory }}</p>
            
            <!-- Metadata -->
            <div class="text-sm text-gray-500 dark:text-gray-400">
              <span>ID: {{ result.id }}</span>
              <span class="mx-2">•</span>
              <span>Created: {{ formatDate(result.created_at) }}</span>
              <span class="mx-2">•</span>
              <span>User: {{ result.user_id }}</span>
            </div>

            <!-- Highlight matching terms if available -->
            <div v-if="result.highlights" class="mt-2">
              <div class="text-xs text-gray-600 dark:text-gray-400 mb-1">Highlights:</div>
              <div class="text-sm">
                <span 
                  v-for="(highlight, hIndex) in result.highlights" 
                  :key="hIndex"
                  class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded mr-1"
                >
                  {{ highlight }}
                </span>
              </div>
            </div>
          </div>
          
          <!-- Actions -->
          <div class="ml-4 flex space-x-2">
            <button
              @click="viewMemoryDetails(result)"
              class="p-2 text-blue-500 hover:text-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900 rounded"
              title="View Details"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
            </button>
            
            <button
              @click="deleteSearchResult(result)"
              class="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900 rounded"
              title="Delete Memory"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- No Results State -->
    <div v-else-if="hasSearched && !loading" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No memories found</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Try adjusting your search query or check if memories exist for this user.
      </p>
    </div>

    <!-- Loading State -->
    <div v-else-if="loading" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400 animate-spin" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Searching memories...</p>
    </div>

    <!-- Initial State -->
    <div v-else class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Search Memories</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Enter a search query to find relevant memories.
      </p>
    </div>

    <!-- Memory Details Modal -->
    <div v-if="selectedMemory" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[80vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Memory Details</h3>
          <button @click="selectedMemory = null" class="text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Memory Content
            </label>
            <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded">
              {{ selectedMemory.memory }}
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Memory ID
              </label>
              <div class="text-sm text-gray-900 dark:text-white font-mono">
                {{ selectedMemory.id }}
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                User ID
              </label>
              <div class="text-sm text-gray-900 dark:text-white">
                {{ selectedMemory.user_id }}
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Created At
              </label>
              <div class="text-sm text-gray-900 dark:text-white">
                {{ formatDate(selectedMemory.created_at) }}
              </div>
            </div>

            <div v-if="selectedMemory.score">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Relevance Score
              </label>
              <div class="text-sm text-gray-900 dark:text-white">
                {{ (selectedMemory.score * 100).toFixed(2) }}%
              </div>
            </div>
          </div>

          <div v-if="selectedMemory.metadata">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Metadata
            </label>
            <pre class="p-3 bg-gray-50 dark:bg-gray-700 rounded text-xs overflow-x-auto">{{ JSON.stringify(selectedMemory.metadata, null, 2) }}</pre>
          </div>
        </div>

        <div class="flex justify-end mt-6">
          <button
            @click="selectedMemory = null"
            class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useMemoryStore } from '@/stores/memoryStore'
import { storeToRefs } from 'pinia'
import type { MemoryItem } from '@/services/memoryService'

const memoryStore = useMemoryStore()
const { loading, currentUserId } = storeToRefs(memoryStore)

const searchQuery = ref('')
const searchLimit = ref(10)
const searchUserId = ref('')
const searchResults = ref<(MemoryItem & { score?: number, highlights?: string[] })[]>([])
const hasSearched = ref(false)
const lastSearchQuery = ref('')
const searchTime = ref<number | null>(null)
const selectedMemory = ref<MemoryItem | null>(null)

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleString()
}

function getRelevanceColor(score: number) {
  if (score >= 0.8) return 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
  if (score >= 0.6) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
  return 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
}

async function performSearch() {
  if (!searchQuery.value.trim()) return

  const startTime = Date.now()
  lastSearchQuery.value = searchQuery.value
  hasSearched.value = true

  try {
    const userId = searchUserId.value || currentUserId.value
    const results = await memoryStore.searchMemories(searchQuery.value, searchLimit.value)
    searchResults.value = results
    searchTime.value = Date.now() - startTime
  } catch (error) {
    console.error('Search failed:', error)
    searchResults.value = []
    searchTime.value = null
  }
}

function viewMemoryDetails(memory: MemoryItem) {
  selectedMemory.value = memory
}

async function deleteSearchResult(memory: MemoryItem) {
  if (!confirm(`Are you sure you want to delete this memory?\n\n"${memory.memory}"`)) {
    return
  }

  try {
    await memoryStore.deleteMemory(memory.id)
    // Remove from search results
    searchResults.value = searchResults.value.filter(r => r.id !== memory.id)
  } catch (error) {
    console.error('Failed to delete memory:', error)
  }
}

onMounted(() => {
  searchUserId.value = currentUserId.value
})
</script> 