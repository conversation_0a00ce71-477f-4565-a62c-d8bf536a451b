<template>
  <div class="memory-test">
    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
      Memory System Test
    </h2>

    <!-- Test Configuration -->
    <div class="mb-6 p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Test Configuration</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Test User ID
          </label>
          <input
            v-model="testUserId"
            type="text"
            placeholder="test_user"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Number of Test Memories
          </label>
          <select
            v-model.number="testMemoryCount"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            <option :value="3">3 memories</option>
            <option :value="5">5 memories</option>
            <option :value="10">10 memories</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Test Actions -->
    <div class="mb-6 flex flex-wrap gap-3">
      <button
        @click="runConfigTest"
        :disabled="loading"
        class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
      >
        Test Configuration
      </button>

      <button
        @click="runMemoryTest"
        :disabled="loading"
        class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50"
      >
        Test Memory Operations
      </button>

      <button
        @click="runSearchTest"
        :disabled="loading"
        class="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50"
      >
        Test Search
      </button>

      <button
        @click="runIsolationTest"
        :disabled="loading"
        class="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 disabled:opacity-50"
      >
        Test User Isolation
      </button>

      <button
        @click="runFullTest"
        :disabled="loading"
        class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50"
      >
        Run All Tests
      </button>

      <button
        @click="clearTestData"
        :disabled="loading"
        class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 disabled:opacity-50"
      >
        Clear Test Data
      </button>
    </div>

    <!-- Test Progress -->
    <div v-if="testProgress.total > 0" class="mb-6 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-blue-900 dark:text-blue-100">
          Test Progress
        </span>
        <span class="text-sm text-blue-700 dark:text-blue-300">
          {{ testProgress.current }} / {{ testProgress.total }}
        </span>
      </div>
      <div class="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2">
        <div 
          class="bg-blue-600 dark:bg-blue-400 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${(testProgress.current / testProgress.total) * 100}%` }"
        ></div>
      </div>
      <div v-if="testProgress.currentStep" class="text-sm text-blue-700 dark:text-blue-300 mt-1">
        {{ testProgress.currentStep }}
      </div>
    </div>

    <!-- Test Results -->
    <div v-if="testResults.length > 0" class="space-y-4">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Test Results</h3>
      
      <div
        v-for="(result, index) in testResults"
        :key="index"
        class="p-4 rounded-lg border" 
        :class="[
          result.success 
            ? 'bg-green-50 border-green-200 dark:bg-green-900 dark:border-green-700' 
            : 'bg-red-50 border-red-200 dark:bg-red-900 dark:border-red-700'
        ]"
      >
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <div class="flex items-center mb-2">
              <svg 
                v-if="result.success" 
                class="w-5 h-5 text-green-600 dark:text-green-400 mr-2" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <svg 
                v-else 
                class="w-5 h-5 text-red-600 dark:text-red-400 mr-2" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
              
              <h4 class="font-medium" :class="[
                result.success 
                  ? 'text-green-800 dark:text-green-200' 
                  : 'text-red-800 dark:text-red-200'
              ]">
                {{ result.testName }}
              </h4>
              
              <span class="ml-2 text-xs px-2 py-1 rounded-full" :class="[
                result.success 
                  ? 'bg-green-200 text-green-800 dark:bg-green-800 dark:text-green-200' 
                  : 'bg-red-200 text-red-800 dark:bg-red-800 dark:text-red-200'
              ]">
                {{ result.duration }}ms
              </span>
            </div>
            
            <p class="text-sm mb-2" :class="[
              result.success 
                ? 'text-green-700 dark:text-green-300' 
                : 'text-red-700 dark:text-red-300'
            ]">
              {{ result.message }}
            </p>

            <div v-if="result.details" class="text-xs space-y-1" :class="[
              result.success 
                ? 'text-green-600 dark:text-green-400' 
                : 'text-red-600 dark:text-red-400'
            ]">
              <div v-for="(detail, dIndex) in result.details" :key="dIndex">
                • {{ detail }}
              </div>
            </div>

            <div v-if="result.data" class="mt-2">
              <details class="text-xs">
                <summary class="cursor-pointer font-medium" :class="[
                  result.success 
                    ? 'text-green-700 dark:text-green-300' 
                    : 'text-red-700 dark:text-red-300'
                ]">
                  View Raw Data
                </summary>
                <pre class="mt-1 p-2 bg-gray-100 dark:bg-gray-800 rounded overflow-x-auto">{{ JSON.stringify(result.data, null, 2) }}</pre>
              </details>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-else-if="loading" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400 animate-spin" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Running tests...</p>
    </div>

    <!-- Initial State -->
    <div v-else class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Memory System Testing</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Run tests to verify memory functionality and performance.
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useMemoryStore } from '@/stores/memoryStore'
import { storeToRefs } from 'pinia'
import memoryService from '@/services/memoryService'

interface TestResult {
  testName: string
  success: boolean
  message: string
  duration: number
  details?: string[]
  data?: any
}

const memoryStore = useMemoryStore()
const { loading } = storeToRefs(memoryStore)

const testUserId = ref('test_user')
const testMemoryCount = ref(5)
const testResults = ref<TestResult[]>([])
const testProgress = ref({
  current: 0,
  total: 0,
  currentStep: ''
})

const testMemories = [
  "I love pizza with extra cheese and mushrooms",
  "My favorite color is blue and I enjoy painting",
  "I work as a software engineer at a tech company",
  "I have a pet cat named Whiskers who loves to play",
  "I enjoy reading science fiction novels in my spare time",
  "I prefer coffee over tea and drink it every morning",
  "I live in San Francisco and love the weather here",
  "I practice yoga every Tuesday and Thursday",
  "My birthday is in December and I love winter",
  "I speak three languages: English, Spanish, and French"
]

function updateProgress(current: number, total: number, step: string) {
  testProgress.value = { current, total, currentStep: step }
}

async function runTest(testName: string, testFn: () => Promise<any>): Promise<TestResult> {
  const startTime = Date.now()
  
  try {
    const result = await testFn()
    const duration = Date.now() - startTime
    
    return {
      testName,
      success: true,
      message: 'Test passed successfully',
      duration,
      data: result
    }
  } catch (error) {
    const duration = Date.now() - startTime
    
    return {
      testName,
      success: false,
      message: error instanceof Error ? error.message : 'Test failed',
      duration
    }
  }
}

async function runConfigTest() {
  testResults.value = []
  updateProgress(0, 1, 'Testing configuration...')
  
  const result = await runTest('Configuration Test', async () => {
    const testResult = await memoryStore.testConfig()
    
    if (testResult.status !== 'success') {
      throw new Error(testResult.message)
    }
    
    return testResult
  })
  
  testResults.value.push(result)
  updateProgress(1, 1, 'Configuration test completed')
  
  setTimeout(() => {
    testProgress.value = { current: 0, total: 0, currentStep: '' }
  }, 2000)
}

async function runMemoryTest() {
  testResults.value = []
  const totalSteps = 4
  let currentStep = 0
  
  // Test 1: Add Memory
  updateProgress(++currentStep, totalSteps, 'Testing add memory...')
  const addResult = await runTest('Add Memory Test', async () => {
    const messages = [{ role: 'user', content: 'This is a test memory for testing purposes' }]
    return await memoryService.addMemory(messages, testUserId.value)
  })
  testResults.value.push(addResult)
  
  // Test 2: Get All Memories
  updateProgress(++currentStep, totalSteps, 'Testing get all memories...')
  const getAllResult = await runTest('Get All Memories Test', async () => {
    return await memoryService.getAllMemories(testUserId.value)
  })
  testResults.value.push(getAllResult)
  
  // Test 3: Search Memories
  updateProgress(++currentStep, totalSteps, 'Testing search memories...')
  const searchResult = await runTest('Search Memories Test', async () => {
    return await memoryService.searchMemories('test memory', testUserId.value, 3)
  })
  testResults.value.push(searchResult)
  
  // Test 4: Memory Enhancement
  updateProgress(++currentStep, totalSteps, 'Testing memory enhancement...')
  const enhanceResult = await runTest('Memory Enhancement Test', async () => {
    const messages = [{ role: 'user', content: 'Tell me about testing' }]
    return await memoryService.enhancePrompt(messages, testUserId.value, 3)
  })
  testResults.value.push(enhanceResult)
  
  updateProgress(totalSteps, totalSteps, 'Memory tests completed')
  
  setTimeout(() => {
    testProgress.value = { current: 0, total: 0, currentStep: '' }
  }, 2000)
}

async function runSearchTest() {
  testResults.value = []
  const totalSteps = testMemoryCount.value + 2
  let currentStep = 0
  
  // Add test memories
  updateProgress(++currentStep, totalSteps, 'Adding test memories...')
  const addResults = await runTest('Add Test Memories', async () => {
    const results = []
    for (let i = 0; i < testMemoryCount.value; i++) {
      const messages = [{ role: 'user', content: testMemories[i] }]
      const result = await memoryService.addMemory(messages, testUserId.value)
      results.push(result)
      updateProgress(++currentStep, totalSteps, `Added memory ${i + 1}/${testMemoryCount.value}`)
    }
    return results
  })
  testResults.value.push(addResults)
  
  // Test search functionality
  updateProgress(++currentStep, totalSteps, 'Testing search functionality...')
  const searchTests = [
    { query: 'pizza', expected: 'food preferences' },
    { query: 'software engineer', expected: 'job information' },
    { query: 'cat pet', expected: 'pet information' },
    { query: 'blue color', expected: 'color preferences' }
  ]
  
  for (const test of searchTests) {
    const searchResult = await runTest(`Search: "${test.query}"`, async () => {
      const result = await memoryService.searchMemories(test.query, testUserId.value, 3)
      
      if (!result.results || result.results.length === 0) {
        throw new Error('No search results found')
      }
      
      return {
        query: test.query,
        resultsCount: result.results.length,
        results: result.results
      }
    })
    testResults.value.push(searchResult)
  }
  
  updateProgress(totalSteps, totalSteps, 'Search tests completed')
  
  setTimeout(() => {
    testProgress.value = { current: 0, total: 0, currentStep: '' }
  }, 2000)
}

async function runIsolationTest() {
  testResults.value = []
  const user1 = 'test_user_1'
  const user2 = 'test_user_2'
  const totalSteps = 6
  let currentStep = 0
  
  // Add memories for user 1
  updateProgress(++currentStep, totalSteps, 'Adding memories for user 1...')
  const user1AddResult = await runTest('User 1 Add Memory', async () => {
    const messages = [{ role: 'user', content: 'User 1 secret information' }]
    return await memoryService.addMemory(messages, user1)
  })
  testResults.value.push(user1AddResult)
  
  // Add memories for user 2
  updateProgress(++currentStep, totalSteps, 'Adding memories for user 2...')
  const user2AddResult = await runTest('User 2 Add Memory', async () => {
    const messages = [{ role: 'user', content: 'User 2 secret information' }]
    return await memoryService.addMemory(messages, user2)
  })
  testResults.value.push(user2AddResult)
  
  // Test user 1 can only see their memories
  updateProgress(++currentStep, totalSteps, 'Testing user 1 isolation...')
  const user1IsolationResult = await runTest('User 1 Isolation Test', async () => {
    const memories = await memoryService.getAllMemories(user1)
    const hasUser2Data = memories.memories.some(m => m.memory.includes('User 2'))
    
    if (hasUser2Data) {
      throw new Error('User 1 can see User 2 data - isolation failed!')
    }
    
    return {
      user: user1,
      memoriesCount: memories.memories.length,
      containsOtherUserData: hasUser2Data
    }
  })
  testResults.value.push(user1IsolationResult)
  
  // Test user 2 can only see their memories
  updateProgress(++currentStep, totalSteps, 'Testing user 2 isolation...')
  const user2IsolationResult = await runTest('User 2 Isolation Test', async () => {
    const memories = await memoryService.getAllMemories(user2)
    const hasUser1Data = memories.memories.some(m => m.memory.includes('User 1'))
    
    if (hasUser1Data) {
      throw new Error('User 2 can see User 1 data - isolation failed!')
    }
    
    return {
      user: user2,
      memoriesCount: memories.memories.length,
      containsOtherUserData: hasUser1Data
    }
  })
  testResults.value.push(user2IsolationResult)
  
  // Test search isolation
  updateProgress(++currentStep, totalSteps, 'Testing search isolation...')
  const searchIsolationResult = await runTest('Search Isolation Test', async () => {
    const user1Search = await memoryService.searchMemories('secret', user1, 10)
    const user2Search = await memoryService.searchMemories('secret', user2, 10)
    
    const user1HasUser2Results = user1Search.results.some(r => r.memory.includes('User 2'))
    const user2HasUser1Results = user2Search.results.some(r => r.memory.includes('User 1'))
    
    if (user1HasUser2Results || user2HasUser1Results) {
      throw new Error('Search isolation failed - users can see each other\'s results')
    }
    
    return {
      user1Results: user1Search.results.length,
      user2Results: user2Search.results.length,
      isolationPassed: !user1HasUser2Results && !user2HasUser1Results
    }
  })
  testResults.value.push(searchIsolationResult)
  
  // Cleanup
  updateProgress(++currentStep, totalSteps, 'Cleaning up test data...')
  const cleanupResult = await runTest('Cleanup Test Data', async () => {
    await memoryService.clearMemories(user1)
    await memoryService.clearMemories(user2)
    return { cleaned: [user1, user2] }
  })
  testResults.value.push(cleanupResult)
  
  updateProgress(totalSteps, totalSteps, 'Isolation tests completed')
  
  setTimeout(() => {
    testProgress.value = { current: 0, total: 0, currentStep: '' }
  }, 2000)
}

async function runFullTest() {
  testResults.value = []
  
  await runConfigTest()
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  await runMemoryTest()
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  await runSearchTest()
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  await runIsolationTest()
}

async function clearTestData() {
  testResults.value = []
  updateProgress(0, 1, 'Clearing test data...')
  
  const result = await runTest('Clear Test Data', async () => {
    await memoryService.clearMemories(testUserId.value)
    await memoryService.clearMemories('test_user_1')
    await memoryService.clearMemories('test_user_2')
    return { message: 'All test data cleared' }
  })
  
  testResults.value.push(result)
  updateProgress(1, 1, 'Test data cleared')
  
  setTimeout(() => {
    testProgress.value = { current: 0, total: 0, currentStep: '' }
  }, 2000)
}
</script> 