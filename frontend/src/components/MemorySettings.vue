<template>
  <div class="memory-settings">
    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
      Memory Settings
    </h2>

    <!-- Current Configuration -->
    <div class="mb-8 p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Current Configuration</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Memory System
          </label>
          <div class="flex items-center space-x-2">
            <div 
              :class="[
                'w-3 h-3 rounded-full',
                config.enabled ? 'bg-green-500' : 'bg-red-500'
              ]"
            ></div>
            <span class="text-sm text-gray-900 dark:text-white">
              {{ config.enabled ? 'Enabled' : 'Disabled' }}
            </span>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Embedding Provider
          </label>
          <div class="text-sm text-gray-900 dark:text-white">
            {{ currentProvider ? currentProvider.toUpperCase() : 'Not configured' }}
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Max Memories
          </label>
          <div class="text-sm text-gray-900 dark:text-white">
            {{ config.max_memories }}
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Memories per Query
          </label>
          <div class="text-sm text-gray-900 dark:text-white">
            {{ config.memories_per_query }}
          </div>
        </div>
      </div>
    </div>

    <!-- Configuration Form -->
    <form @submit.prevent="saveSettings" class="space-y-8">
      <!-- Basic Settings -->
      <div class="p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Basic Settings</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="flex items-center">
              <input
                v-model="formData.enabled"
                type="checkbox"
                class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              />
              <span class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Enable Memory System
              </span>
            </label>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Max Memories
            </label>
            <input
              v-model.number="formData.max_memories"
              type="number"
              min="100"
              max="10000"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Memories per Query
            </label>
            <input
              v-model.number="formData.memories_per_query"
              type="number"
              min="1"
              max="10"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Min Relevance Score
            </label>
            <input
              v-model.number="formData.min_relevance_score"
              type="number"
              min="0"
              max="1"
              step="0.1"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Memory TTL (days)
            </label>
            <input
              v-model.number="formData.memory_ttl_days"
              type="number"
              min="1"
              max="365"
              placeholder="Leave empty for no expiration"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>
        </div>
      </div>

      <!-- Embedding Provider Selection -->
      <div class="p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Embedding Provider</h3>
        
        <div class="space-y-4">
          <!-- Provider Selection -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Select Provider
            </label>
            <select
              v-model="selectedProvider"
              @change="onProviderChange"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="">Select a provider...</option>
              <option 
                v-for="(provider, key) in providers" 
                :key="key" 
                :value="key"
              >
                {{ provider.name }} ({{ provider.pricing }})
              </option>
            </select>
          </div>

          <!-- Provider Configuration -->
          <div v-if="selectedProvider" class="space-y-4">
            <!-- Model Selection -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Model
              </label>
              <select
                v-model="formData.embedding_provider.model"
                @change="onModelChange"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option value="">Select a model...</option>
                <option 
                  v-for="model in providers[selectedProvider]?.models" 
                  :key="model.name" 
                  :value="model.name"
                >
                  {{ model.name }} ({{ model.dimensions }}d)
                </option>
              </select>
            </div>

            <!-- API Key -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                API Key
              </label>
              <input
                v-model="formData.embedding_provider.api_key"
                type="password"
                placeholder="Enter your API key"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>

            <!-- Dimensions (read-only) -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Dimensions
              </label>
              <input
                v-model.number="formData.embedding_provider.dimensions"
                type="number"
                readonly
                class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 dark:bg-gray-600 text-gray-500 dark:text-gray-400"
              />
            </div>

            <!-- Task (for Jina v3) -->
            <div v-if="selectedProvider === 'jina' && formData.embedding_provider.model === 'jina-embeddings-v3'">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Task
              </label>
              <select
                v-model="formData.embedding_provider.task"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option value="text-matching">Text Matching</option>
                <option value="retrieval">Retrieval</option>
                <option value="classification">Classification</option>
                <option value="clustering">Clustering</option>
              </select>
            </div>
          </div>

          <!-- Provider Info -->
          <div v-if="selectedProvider" class="p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
            <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2">
              {{ providers[selectedProvider]?.name }} Information
            </h4>
            <ul class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>• Pricing: {{ providers[selectedProvider]?.pricing }}</li>
              <li>• Requires API Key: {{ providers[selectedProvider]?.requires_api_key ? 'Yes' : 'No' }}</li>
              <li>• Available Models: {{ providers[selectedProvider]?.models.length }}</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Legacy OpenAI Support -->
      <div class="p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Legacy OpenAI Support
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
          For backward compatibility. Use the embedding provider section above for new configurations.
        </p>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            OpenAI API Key (Legacy)
          </label>
          <input
            v-model="formData.openai_api_key"
            type="password"
            placeholder="sk-..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-between items-center">
        <div class="space-x-3">
          <button
            type="button"
            @click="testSettings"
            :disabled="loading || !canTest"
            class="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 disabled:opacity-50"
          >
            <svg v-if="loading" class="w-4 h-4 animate-spin inline mr-2" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Test Configuration
          </button>

          <button
            type="button"
            @click="syncService"
            :disabled="loading"
            class="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50"
          >
            Sync Service
          </button>
        </div>

        <div class="space-x-3">
          <button
            type="button"
            @click="resetForm"
            class="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            Reset
          </button>
          
          <button
            type="submit"
            :disabled="loading"
            class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
          >
            <svg v-if="loading" class="w-4 h-4 animate-spin inline mr-2" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Save Settings
          </button>
        </div>
      </div>
    </form>

    <!-- Test Results -->
    <div v-if="testResult" class="mt-6 p-4 rounded-lg" :class="[
      testResult.status === 'success' ? 'bg-green-100 border border-green-400 text-green-700' :
      testResult.status === 'warning' ? 'bg-yellow-100 border border-yellow-400 text-yellow-700' :
      'bg-red-100 border border-red-400 text-red-700'
    ]">
      <div class="flex items-center justify-between">
        <div>
          <h4 class="font-medium">Test Result</h4>
          <p class="text-sm">{{ testResult.message }}</p>
          <p v-if="testResult.provider" class="text-xs mt-1">
            Provider: {{ testResult.provider }} | Model: {{ testResult.model }}
          </p>
        </div>
        <button @click="testResult = null" class="text-current hover:opacity-70">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useMemoryStore } from '@/stores/memoryStore'
import { storeToRefs } from 'pinia'
import type { MemoryConfig, EmbeddingProvider, TestResult } from '@/services/memoryService'

const memoryStore = useMemoryStore()
const { 
  config, 
  providers, 
  loading, 
  currentProvider 
} = storeToRefs(memoryStore)

const selectedProvider = ref('')
const testResult = ref<TestResult | null>(null)

const formData = ref<MemoryConfig>({
  enabled: false,
  max_memories: 1000,
  memory_ttl_days: undefined,
  memories_per_query: 3,
  min_relevance_score: 0.7,
  openai_api_key: undefined,
  embedding_provider: {
    provider: 'openai',
    model: '',
    api_key: undefined,
    dimensions: undefined,
    task: undefined,
  }
})

const canTest = computed(() => {
  return formData.value.enabled && (
    formData.value.openai_api_key ||
    (formData.value.embedding_provider?.api_key && formData.value.embedding_provider?.model)
  )
})

function resetForm() {
  formData.value = JSON.parse(JSON.stringify(config.value))
  updateSelectedProvider()
}

function updateSelectedProvider() {
  if (formData.value.embedding_provider?.provider) {
    selectedProvider.value = formData.value.embedding_provider.provider
  } else {
    selectedProvider.value = ''
  }
}

function onProviderChange() {
  if (!selectedProvider.value) return

  formData.value.embedding_provider = {
    provider: selectedProvider.value as any,
    model: '',
    api_key: undefined,
    dimensions: undefined,
    task: undefined,
  }
}

function onModelChange() {
  if (!selectedProvider.value || !formData.value.embedding_provider?.model) return

  const provider = providers.value[selectedProvider.value]
  const model = provider?.models.find(m => m.name === formData.value.embedding_provider?.model)
  
  if (model && formData.value.embedding_provider) {
    formData.value.embedding_provider.dimensions = model.dimensions
    
    // Set default task for Jina v3
    if (selectedProvider.value === 'jina' && model.name === 'jina-embeddings-v3') {
      formData.value.embedding_provider.task = 'text-matching'
    }
  }
}

async function saveSettings() {
  try {
    await memoryStore.updateConfig(formData.value)
    testResult.value = {
      status: 'success',
      message: 'Settings saved successfully!'
    }
  } catch (error) {
    testResult.value = {
      status: 'error',
      message: error instanceof Error ? error.message : 'Failed to save settings'
    }
  }
}

async function testSettings() {
  try {
    testResult.value = null
    const result = await memoryStore.testConfig()
    testResult.value = result
  } catch (error) {
    testResult.value = {
      status: 'error',
      message: error instanceof Error ? error.message : 'Test failed'
    }
  }
}

async function syncService() {
  try {
    await memoryStore.syncService()
    testResult.value = {
      status: 'success',
      message: 'Service synced successfully!'
    }
  } catch (error) {
    testResult.value = {
      status: 'error',
      message: error instanceof Error ? error.message : 'Sync failed'
    }
  }
}

// Watch for config changes
watch(config, (newConfig) => {
  if (newConfig) {
    formData.value = JSON.parse(JSON.stringify(newConfig))
    updateSelectedProvider()
  }
}, { deep: true, immediate: true })

onMounted(async () => {
  if (Object.keys(providers.value).length === 0) {
    await memoryStore.loadProviders()
  }
  resetForm()
})
</script> 