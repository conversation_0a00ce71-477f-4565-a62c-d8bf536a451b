<template>
  <div class="memory-list">
    <!-- Header with actions -->
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
        Your Memories ({{ memoriesCount }})
      </h2>
      
      <div class="flex space-x-3">
        <!-- Add Memory Button -->
        <button
          @click="showAddDialog = true"
          class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 flex items-center space-x-2"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          <span>Add Memory</span>
        </button>

        <!-- Refresh Button -->
        <button
          @click="loadMemories"
          :disabled="loading"
          class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 flex items-center space-x-2"
        >
          <svg v-if="loading" class="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          <span>Refresh</span>
        </button>

        <!-- Clear All Button -->
        <button
          @click="showClearDialog = true"
          :disabled="memoriesCount === 0"
          class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 flex items-center space-x-2"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
          <span>Clear All</span>
        </button>
      </div>
    </div>

    <!-- User ID Input -->
    <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        User ID
      </label>
      <div class="flex space-x-3">
        <input
          v-model="newUserId"
          type="text"
          placeholder="Enter user ID"
          class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
        />
        <button
          @click="switchUser"
          :disabled="!newUserId || newUserId === currentUserId"
          class="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50"
        >
          Switch User
        </button>
      </div>
      <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
        Current user: <span class="font-medium">{{ currentUserId }}</span>
      </p>
    </div>

    <!-- Memory Cards -->
    <div v-if="memories.length > 0" class="space-y-4">
      <div
        v-for="memory in memories"
        :key="memory.id"
        class="p-4 bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700"
      >
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <p class="text-gray-900 dark:text-white mb-2">{{ memory.memory }}</p>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              <span>ID: {{ memory.id }}</span>
              <span class="mx-2">•</span>
              <span>Created: {{ formatDate(memory.created_at) }}</span>
              <span class="mx-2">•</span>
              <span>User: {{ memory.user_id }}</span>
            </div>
          </div>
          
          <button
            @click="confirmDelete(memory)"
            class="ml-4 p-2 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900 rounded"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!loading" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No memories</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Get started by adding your first memory.
      </p>
      <div class="mt-6">
        <button
          @click="showAddDialog = true"
          class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Add Memory
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-else class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400 animate-spin" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Loading memories...</p>
    </div>

    <!-- Add Memory Dialog -->
    <div v-if="showAddDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Add New Memory</h3>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Memory Content
            </label>
            <textarea
              v-model="newMemoryContent"
              rows="4"
              placeholder="Enter memory content..."
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            ></textarea>
          </div>
        </div>

        <div class="flex justify-end space-x-3 mt-6">
          <button
            @click="cancelAdd"
            class="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            @click="addMemory"
            :disabled="!newMemoryContent.trim() || loading"
            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
          >
            Add Memory
          </button>
        </div>
      </div>
    </div>

    <!-- Clear All Dialog -->
    <div v-if="showClearDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Clear All Memories</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          Are you sure you want to clear all memories for user "{{ currentUserId }}"? This action cannot be undone.
        </p>

        <div class="flex justify-end space-x-3">
          <button
            @click="showClearDialog = false"
            class="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            @click="clearAllMemories"
            :disabled="loading"
            class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50"
          >
            Clear All
          </button>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Dialog -->
    <div v-if="memoryToDelete" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Delete Memory</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          Are you sure you want to delete this memory?
        </p>
        <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded mb-6">
          <p class="text-sm text-gray-900 dark:text-white">{{ memoryToDelete.memory }}</p>
        </div>

        <div class="flex justify-end space-x-3">
          <button
            @click="memoryToDelete = null"
            class="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            @click="deleteMemory"
            :disabled="loading"
            class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useMemoryStore } from '@/stores/memoryStore'
import { storeToRefs } from 'pinia'
import type { MemoryItem } from '@/services/memoryService'

const memoryStore = useMemoryStore()
const { 
  memories, 
  loading, 
  currentUserId, 
  memoriesCount 
} = storeToRefs(memoryStore)

const showAddDialog = ref(false)
const showClearDialog = ref(false)
const newMemoryContent = ref('')
const newUserId = ref('')
const memoryToDelete = ref<MemoryItem | null>(null)

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleString()
}

async function loadMemories() {
  await memoryStore.loadMemories()
}

async function addMemory() {
  if (!newMemoryContent.value.trim()) return

  try {
    const messages = [
      { role: 'user', content: newMemoryContent.value.trim() }
    ]
    await memoryStore.addMemory(messages)
    cancelAdd()
  } catch (error) {
    console.error('Failed to add memory:', error)
  }
}

function cancelAdd() {
  showAddDialog.value = false
  newMemoryContent.value = ''
}

async function clearAllMemories() {
  try {
    await memoryStore.clearAllMemories()
    showClearDialog.value = false
  } catch (error) {
    console.error('Failed to clear memories:', error)
  }
}

function confirmDelete(memory: MemoryItem) {
  memoryToDelete.value = memory
}

async function deleteMemory() {
  if (!memoryToDelete.value) return

  try {
    await memoryStore.deleteMemory(memoryToDelete.value.id)
    memoryToDelete.value = null
  } catch (error) {
    console.error('Failed to delete memory:', error)
  }
}

async function switchUser() {
  if (!newUserId.value || newUserId.value === currentUserId.value) return

  memoryStore.setUserId(newUserId.value)
  await loadMemories()
  newUserId.value = ''
}

onMounted(async () => {
  if (memories.value.length === 0) {
    await loadMemories()
  }
})
</script> 