<template>
  <div class="memory-management p-6 max-w-6xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
        Memory Management
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        Manage AI memories with Jina.ai, Google AI Studio, and OpenAI embeddings
      </p>
    </div>

    <!-- Error <PERSON> -->
    <div v-if="error" class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
      <div class="flex items-center justify-between">
        <span>{{ error }}</span>
        <button @click="clearError" class="text-red-500 hover:text-red-700">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Status Card -->
    <div class="mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Service Status</h2>
        <button 
          @click="refreshStatus" 
          :disabled="loading"
          class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          <svg v-if="loading" class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span v-else>Refresh</span>
        </button>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
          <div class="text-sm text-gray-600 dark:text-gray-400">Service</div>
          <div class="font-semibold" :class="serviceStatus.service_available ? 'text-green-600' : 'text-red-600'">
            {{ serviceStatus.service_available ? 'Available' : 'Unavailable' }}
          </div>
        </div>
        
        <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
          <div class="text-sm text-gray-600 dark:text-gray-400">Provider</div>
          <div class="font-semibold text-blue-600">
            {{ currentProvider ? currentProvider.toUpperCase() : 'Not Set' }}
          </div>
        </div>
        
        <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
          <div class="text-sm text-gray-600 dark:text-gray-400">Memories</div>
          <div class="font-semibold text-purple-600">
            {{ memoriesCount }}
          </div>
        </div>
      </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
      <nav class="flex space-x-8 border-b border-gray-200 dark:border-gray-700">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="[
            'py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === tab.id
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          {{ tab.label }}
        </button>
      </nav>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      <!-- Memories Tab -->
      <div v-if="activeTab === 'memories'" class="space-y-6">
        <MemoryList />
      </div>

      <!-- Settings Tab -->
      <div v-if="activeTab === 'settings'" class="space-y-6">
        <MemorySettings />
      </div>

      <!-- Search Tab -->
      <div v-if="activeTab === 'search'" class="space-y-6">
        <MemorySearch />
      </div>

      <!-- Test Tab -->
      <div v-if="activeTab === 'test'" class="space-y-6">
        <MemoryTest />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useMemoryStore } from '@/stores/memoryStore'
import { storeToRefs } from 'pinia'
import MemoryList from './MemoryList.vue'
import MemorySettings from './MemorySettings.vue'
import MemorySearch from './MemorySearch.vue'
import MemoryTest from './MemoryTest.vue'

const memoryStore = useMemoryStore()
const { 
  error, 
  loading, 
  serviceStatus, 
  currentProvider, 
  memoriesCount 
} = storeToRefs(memoryStore)

const activeTab = ref('memories')

const tabs = [
  { id: 'memories', label: 'Memories' },
  { id: 'search', label: 'Search' },
  { id: 'settings', label: 'Settings' },
  { id: 'test', label: 'Test' },
]

async function refreshStatus() {
  await memoryStore.loadServiceStatus()
}

function clearError() {
  memoryStore.clearError()
}

onMounted(async () => {
  await memoryStore.initialize()
})
</script>

<style scoped>
.memory-management {
  min-height: 100vh;
}

.dark {
  background-color: #1f2937;
}
</style> 