<template>
  <div class="config-menu p-4 text-base">
    <div class="mb-6">
      <h2 class="text-2xl font-bold mb-4">Memory Tool Configuration</h2>
      
      <!-- Tabs -->
      <div class="mb-4 border-b border-gray-200">
        <nav class="flex space-x-4">
          <button
            @click="activeTab = 'memory'"
            :class="['px-4 py-2 text-sm font-medium', activeTab === 'memory' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700']"
          >
            Memory Settings
          </button>
          <button
            @click="activeTab = 'models'"
            :class="['px-4 py-2 text-sm font-medium', activeTab === 'models' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700']"
          >
            Model Settings
          </button>
          <button
            @click="activeTab = 'performance'"
            :class="['px-4 py-2 text-sm font-medium', activeTab === 'performance' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-gray-700']"
          >
            Performance
          </button>
        </nav>
      </div>

      <!-- Memory Settings Tab -->
      <div v-if="activeTab === 'memory'" class="space-y-6">
        <div class="p-4 border rounded-lg shadow-sm">
          <h3 class="text-lg font-medium mb-4">Memory Service</h3>
          <div class="mb-4">
            <label class="flex items-center space-x-2">
              <input
                type="checkbox"
                v-model="config.memory.enabled"
                class="rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              />
              <span>Enable Memory Service</span>
            </label>
          </div>

          <div class="mb-4">
            <label class="block text-sm font-medium mb-1">OpenAI API Key</label>
            <div class="flex space-x-2">
              <input
                type="password"
                v-model="config.memory.openai_api_key"
                placeholder="sk-..."
                class="flex-1 p-2 border rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                :disabled="!config.memory.enabled"
              />
              <button
                @click="testMemorySettings"
                class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                :disabled="!config.memory.enabled || !config.memory.openai_api_key"
              >
                Test
              </button>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="mb-4">
              <label class="block text-sm font-medium mb-1">Max Memories</label>
              <input
                type="number"
                v-model.number="config.memory.max_memories"
                class="w-full p-2 border rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                :disabled="!config.memory.enabled"
                min="1"
                max="10000"
              />
            </div>

            <div class="mb-4">
              <label class="block text-sm font-medium mb-1">Memories Per Query</label>
              <input
                type="number"
                v-model.number="config.memory.memories_per_query"
                class="w-full p-2 border rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                :disabled="!config.memory.enabled"
                min="1"
                max="10"
              />
            </div>

            <div class="mb-4">
              <label class="block text-sm font-medium mb-1">Memory TTL (days)</label>
              <input
                type="number"
                v-model.number="config.memory.memory_ttl"
                class="w-full p-2 border rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                :disabled="!config.memory.enabled"
                min="1"
              />
            </div>

            <div class="mb-4">
              <label class="block text-sm font-medium mb-1">Min Relevance Score</label>
              <input
                type="number"
                v-model.number="config.memory.min_relevance_score"
                class="w-full p-2 border rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                :disabled="!config.memory.enabled"
                min="0"
                max="1"
                step="0.1"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Model Settings Tab -->
      <div v-if="activeTab === 'models'" class="space-y-6">
        <div class="p-4 border rounded-lg shadow-sm">
          <h3 class="text-lg font-medium mb-4">API Configuration</h3>
          
          <div class="mb-4">
            <label class="block text-sm font-medium mb-2">API Provider</label>
            <select 
              v-model="selectedProvider"
              class="w-full p-2 border rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              @change="updateApiConfig"
            >
              <option value="">Select Provider</option>
              <option value="openai">OpenAI API</option>
              <option value="azure">Azure OpenAI</option>
              <option value="anthropic">Anthropic Claude</option>
              <option value="mistral">Mistral AI</option>
              <option value="openrouter">OpenRouter</option>
              <option value="custom">Custom Provider</option>
            </select>
          </div>

          <div v-if="selectedProvider && selectedProvider !== 'openrouter'" class="mb-4">
            <label class="block text-sm font-medium mb-2">Model</label>
            <select 
              v-model="config.models.model"
              class="w-full p-2 border rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            >
              <option v-for="model in availableModels" :key="model.id" :value="model.id">
                {{ model.name }} - {{ model.description }}
              </option>
            </select>
          </div>
          
          <div v-if="selectedProvider === 'openrouter'" class="mb-4">
             <label class="block text-sm font-medium mb-2">Model Name (e.g. google/gemini-pro)</label>
             <input
                type="text"
                v-model="config.models.model"
                placeholder="Model from OpenRouter"
                class="w-full p-2 border rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              />
          </div>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium mb-2">API Endpoint</label>
              <input
                type="text"
                v-model="config.models.endpoint"
                :placeholder="getApiPlaceholder"
                class="w-full p-2 border rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              />
            </div>

            <div v-if="selectedProvider === 'azure'" class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium mb-2">Resource Name</label>
                <input
                  type="text"
                  v-model="azureConfig.resourceName"
                  placeholder="your-resource-name"
                  class="w-full p-2 border rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  @input="updateAzureEndpoint"
                />
              </div>
              <div>
                <label class="block text-sm font-medium mb-2">Deployment Name</label>
                <input
                  type="text"
                  v-model="azureConfig.deploymentName"
                  placeholder="your-deployment-name"
                  class="w-full p-2 border rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  @input="updateAzureEndpoint"
                />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium mb-2">API Key</label>
              <div class="flex space-x-2">
                <input
                  type="password"
                  v-model="config.models.api_key"
                  :placeholder="getApiKeyPlaceholder"
                  class="flex-1 p-2 border rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
                <button
                  @click="testConnection"
                  class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
                  :disabled="!canTestConnection"
                >
                  Test
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Performance Tab -->
      <div v-if="activeTab === 'performance'" class="space-y-6">
        <div class="p-4 border rounded-lg shadow-sm">
          <h3 class="text-lg font-medium mb-4">External Models</h3>
          
          <div v-for="(model, index) in config.models.external_models" :key="index" class="mb-4 p-4 border rounded-lg">
            <div class="flex justify-between items-start mb-4">
              <div class="flex-1 pr-4">
                <input
                  type="text"
                  v-model="model.name"
                  placeholder="Model Name"
                  class="w-full p-2 border rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 mb-2"
                />
                <div class="flex items-center">
                  <input
                    type="checkbox"
                    v-model="model.enabled"
                    class="rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 mr-2"
                  />
                  <span class="text-sm">Enable this model</span>
                </div>
              </div>
              <button
                @click="removeExternalModel(index)"
                class="text-red-500 hover:text-red-700"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium mb-1">Endpoint URL</label>
                <input
                  type="text"
                  v-model="model.endpoint"
                  placeholder="http://example.com/v1"
                  class="w-full p-2 border rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
              </div>
              <div>
                <label class="block text-sm font-medium mb-1">API Key (optional)</label>
                <input
                  type="password"
                  v-model="model.apiKey"
                  placeholder="Optional API Key"
                  class="w-full p-2 border rounded border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
              </div>
            </div>
          </div>

          <button
            @click="addExternalModel"
            class="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Add External Model
          </button>
        </div>
      </div>
    </div>

    <div class="mt-8 flex justify-end space-x-3">
      <button 
        @click="resetConfig"
        class="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400"
      >
        Reset to Defaults
      </button>
      <button 
        @click="saveConfig"
        class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
      >
        Save Configuration
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue'

const activeTab = ref('memory')

const config = reactive({
  memory: {
    enabled: true,
    openai_api_key: '',
    max_memories: 1000,
    memories_per_query: 3,
    memory_ttl: 90,
    min_relevance_score: 0.8
  },
  models: {
    selected_provider: '',
    model: '',
    endpoint: '',
    api_key: '',
    azure_config: {
      resource_name: '',
      deployment_name: ''
    },
    external_models: [] as { name: string; enabled: boolean; endpoint: string; apiKey: string }[]
  },
  performance: {
    // Add performance settings here if any
  }
})

const selectedProvider = ref('')
const azureConfig = reactive({
  resourceName: '',
  deploymentName: '',
})

const availableModels = computed(() => {
  switch (selectedProvider.value) {
    case 'openai':
      return [
        { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: 'Latest GPT-4 model' },
        { id: 'gpt-4', name: 'GPT-4', description: 'Stable and powerful' },
        { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'Fast and cost-effective' },
      ]
    case 'azure':
      return [
        { id: 'azure-gpt-4', name: 'GPT-4 on Azure', description: 'Your deployment' },
      ]
    case 'anthropic':
       return [
        { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', description: 'Most powerful model' },
        { id: 'claude-3-sonnet-20240229', name: 'Claude 3 Sonnet', description: 'Balanced performance' },
        { id: 'claude-2.1', name: 'Claude 2.1', description: 'Legacy model' },
      ]
    case 'mistral':
      return [
        { id: 'mistral-large-latest', name: 'Mistral Large', description: 'Top-tier reasoning' },
        { id: 'mistral-medium-latest', name: 'Mistral Medium', description: 'High performance' },
      ]
    default:
      return []
  }
})

const getApiPlaceholder = computed(() => {
  switch (selectedProvider.value) {
    case 'openai': return 'https://api.openai.com/v1'
    case 'anthropic': return 'https://api.anthropic.com/v1'
    case 'mistral': return 'https://api.mistral.ai/v1'
    case 'openrouter': return 'https://openrouter.ai/api/v1'
    case 'azure': return `https://${azureConfig.resourceName}.openai.azure.com/openai/deployments/${azureConfig.deploymentName}`
    default: return 'Enter API endpoint'
  }
})

const getApiKeyPlaceholder = computed(() => {
  switch (selectedProvider.value) {
    case 'azure': return 'Azure OpenAI API Key'
    case 'openrouter': return 'OpenRouter API Key'
    default: return 'API Key'
  }
})

const canTestConnection = computed(() => {
  return config.models.endpoint && config.models.api_key;
})

function updateApiConfig() {
  config.models.selected_provider = selectedProvider.value
  config.models.endpoint = getApiPlaceholder.value
  config.models.model = ''
  if(selectedProvider.value === 'azure') {
     updateAzureEndpoint()
  }
}

function updateAzureEndpoint() {
  if (selectedProvider.value === 'azure') {
    config.models.endpoint = `https://${azureConfig.resourceName}.openai.azure.com/openai/deployments/${azureConfig.deploymentName}`
  }
}

watch(selectedProvider, updateApiConfig)
watch(azureConfig, updateAzureEndpoint, { deep: true })

const addExternalModel = () => {
  config.models.external_models.push({
    name: '',
    enabled: true,
    endpoint: '',
    apiKey: ''
  })
}

const removeExternalModel = (index: number) => {
  config.models.external_models.splice(index, 1)
}

const saveConfig = () => {
  console.log('Saving config:', JSON.stringify(config, null, 2))
  // Implement API call to save config
  alert('Configuration saved!')
}

const resetConfig = () => {
  // Implement reset logic
  alert('Configuration reset to defaults!')
}

const testMemorySettings = () => {
  // Implement test logic
  alert('Testing memory settings...')
}

const testConnection = () => {
  // Implement test logic
  alert(`Testing connection to ${config.models.endpoint}...`)
}

onMounted(() => {
  // Load config from API
  console.log('ConfigMenu component mounted. Loading configuration...')
})

</script>

<style scoped>
.form-input, .form-select, .form-checkbox {
  @apply rounded-md border-gray-300 shadow-sm;
}
.form-input:focus, .form-select:focus {
  @apply border-blue-300 ring ring-blue-200 ring-opacity-50;
}
</style> 