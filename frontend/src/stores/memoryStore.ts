import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import memoryService, { 
  type MemoryItem, 
  type MemoryConfig, 
  type EmbeddingProvider,
  type ProviderInfo,
  type MemoryServiceStatus 
} from '@/services/memoryService'

export const useMemoryStore = defineStore('memory', () => {
  // State
  const memories = ref<MemoryItem[]>([])
  const config = ref<MemoryConfig>({
    enabled: false,
    max_memories: 1000,
    memory_ttl_days: undefined,
    memories_per_query: 3,
    min_relevance_score: 0.7,
  })
  const providers = ref<Record<string, ProviderInfo>>({})
  const serviceStatus = ref<MemoryServiceStatus>({
    service_available: false
  })
  const loading = ref(false)
  const error = ref<string | null>(null)
  const currentUserId = ref('default_user')

  // Computed
  const isConfigured = computed(() => {
    return config.value.enabled && (
      config.value.openai_api_key || 
      (config.value.embedding_provider?.api_key)
    )
  })

  const currentProvider = computed(() => {
    if (config.value.embedding_provider) {
      return config.value.embedding_provider.provider
    }
    if (config.value.openai_api_key) {
      return 'openai'
    }
    return null
  })

  const memoriesCount = computed(() => memories.value.length)

  // Actions
  async function loadMemories() {
    try {
      loading.value = true
      error.value = null
      const response = await memoryService.getAllMemories(currentUserId.value)
      memories.value = response.memories || []
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load memories'
      console.error('Error loading memories:', err)
    } finally {
      loading.value = false
    }
  }

  async function searchMemories(query: string, limit = 10) {
    try {
      loading.value = true
      error.value = null
      const response = await memoryService.searchMemories(query, currentUserId.value, limit)
      return response.results || []
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to search memories'
      console.error('Error searching memories:', err)
      return []
    } finally {
      loading.value = false
    }
  }

  async function addMemory(messages: Array<{role: string, content: string}>) {
    try {
      loading.value = true
      error.value = null
      await memoryService.addMemory(messages, currentUserId.value)
      // Reload memories to get the updated list
      await loadMemories()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to add memory'
      console.error('Error adding memory:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function deleteMemory(memoryId: string) {
    try {
      loading.value = true
      error.value = null
      await memoryService.deleteMemory(memoryId, currentUserId.value)
      // Remove from local state
      memories.value = memories.value.filter(m => m.id !== memoryId)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to delete memory'
      console.error('Error deleting memory:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function clearAllMemories() {
    try {
      loading.value = true
      error.value = null
      await memoryService.clearMemories(currentUserId.value)
      memories.value = []
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to clear memories'
      console.error('Error clearing memories:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function loadConfig() {
    try {
      loading.value = true
      error.value = null
      config.value = await memoryService.getMemorySettings()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load config'
      console.error('Error loading config:', err)
    } finally {
      loading.value = false
    }
  }

  async function updateConfig(newConfig: MemoryConfig) {
    try {
      loading.value = true
      error.value = null
      config.value = await memoryService.updateMemorySettings(newConfig)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update config'
      console.error('Error updating config:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function testConfig() {
    try {
      loading.value = true
      error.value = null
      return await memoryService.testMemorySettings()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to test config'
      console.error('Error testing config:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function loadProviders() {
    try {
      loading.value = true
      error.value = null
      const response = await memoryService.getAvailableProviders()
      providers.value = response.providers
      // Update config if it was returned
      if (response.current_config) {
        config.value = response.current_config
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load providers'
      console.error('Error loading providers:', err)
    } finally {
      loading.value = false
    }
  }

  async function loadServiceStatus() {
    try {
      loading.value = true
      error.value = null
      serviceStatus.value = await memoryService.getMemoryServiceStatus()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load service status'
      console.error('Error loading service status:', err)
    } finally {
      loading.value = false
    }
  }

  async function syncService() {
    try {
      loading.value = true
      error.value = null
      const response = await memoryService.syncMemoryService()
      // Reload service status after sync
      await loadServiceStatus()
      return response
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to sync service'
      console.error('Error syncing service:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  function setUserId(userId: string) {
    currentUserId.value = userId
    // Clear memories when switching users
    memories.value = []
  }

  function clearError() {
    error.value = null
  }

  // Initialize store
  async function initialize() {
    await Promise.all([
      loadConfig(),
      loadProviders(),
      loadServiceStatus()
    ])
    
    if (isConfigured.value) {
      await loadMemories()
    }
  }

  return {
    // State
    memories,
    config,
    providers,
    serviceStatus,
    loading,
    error,
    currentUserId,
    
    // Computed
    isConfigured,
    currentProvider,
    memoriesCount,
    
    // Actions
    loadMemories,
    searchMemories,
    addMemory,
    deleteMemory,
    clearAllMemories,
    loadConfig,
    updateConfig,
    testConfig,
    loadProviders,
    loadServiceStatus,
    syncService,
    setUserId,
    clearError,
    initialize,
  }
}) 