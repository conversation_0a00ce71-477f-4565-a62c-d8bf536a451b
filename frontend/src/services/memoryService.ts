/**
 * Memory Service for interacting with Mem0 API endpoints
 */

export interface MemoryItem {
  id: string
  memory: string
  user_id: string
  created_at: string
  metadata?: any
}

export interface EmbeddingProvider {
  provider: 'openai' | 'jina' | 'google_ai'
  model: string
  api_key?: string
  dimensions?: number
  task?: string
}

export interface MemoryConfig {
  enabled: boolean
  max_memories: number
  memory_ttl_days?: number
  memories_per_query: number
  min_relevance_score: number
  openai_api_key?: string
  embedding_provider?: EmbeddingProvider
}

export interface SearchResult {
  results: MemoryItem[]
}

export interface TestResult {
  status: 'success' | 'error' | 'warning'
  message: string
  provider?: string
  model?: string
}

export interface ProviderInfo {
  name: string
  models: Array<{
    name: string
    dimensions: number
    tasks?: string[]
    multimodal?: boolean
  }>
  requires_api_key: boolean
  pricing: 'free' | 'paid' | 'freemium'
}

export interface ProvidersResponse {
  providers: Record<string, ProviderInfo>
  current_config: MemoryConfig
}

export interface MemoryServiceStatus {
  service_available: boolean
  mem0_status?: string
  settings?: any
  embedding_config?: any
  config_locations_checked?: string[]
  message?: string
}

class MemoryService {
  private baseUrl: string

  constructor() {
    // Use environment variable or default to localhost
    this.baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:8000'
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseUrl}/api/v1${endpoint}`
    
    const defaultHeaders = {
      'Content-Type': 'application/json',
    }

    const response = await fetch(url, {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorText}`)
    }

    return response.json()
  }

  // Memory Management API
  async addMemory(messages: Array<{role: string, content: string}>, userId = 'default_user'): Promise<{status: string, message: string}> {
    return this.request('/memory/memories/add', {
      method: 'POST',
      body: JSON.stringify(messages),
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }

  async searchMemories(query: string, userId = 'default_user', limit = 3): Promise<SearchResult> {
    const params = new URLSearchParams({
      query,
      user_id: userId,
      limit: limit.toString(),
    })
    
    return this.request(`/memory/memories/search?${params}`)
  }

  async getAllMemories(userId = 'default_user'): Promise<{memories: MemoryItem[]}> {
    const params = new URLSearchParams({
      user_id: userId,
    })
    
    return this.request(`/memory/memories/all?${params}`)
  }

  async deleteMemory(memoryId: string, userId = 'default_user'): Promise<{status: string, message: string}> {
    return this.request(`/memory/memories/${memoryId}?user_id=${userId}`, {
      method: 'DELETE',
    })
  }

  async clearMemories(userId = 'default_user'): Promise<{status: string, message: string}> {
    return this.request(`/memory/memories/clear/${userId}`, {
      method: 'DELETE',
    })
  }

  async enhancePrompt(
    messages: Array<{role: string, content: string}>, 
    userId = 'default_user', 
    limit = 3
  ): Promise<{enhanced_messages: Array<{role: string, content: string}>}> {
    return this.request('/memory/memories/enhance', {
      method: 'POST',
      body: JSON.stringify(messages),
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }

  // Memory Settings API
  async getMemorySettings(): Promise<MemoryConfig> {
    return this.request('/mem0/settings')
  }

  async updateMemorySettings(config: MemoryConfig): Promise<MemoryConfig> {
    return this.request('/mem0/settings', {
      method: 'POST',
      body: JSON.stringify(config),
    })
  }

  async testMemorySettings(): Promise<TestResult> {
    return this.request('/mem0/settings/test', {
      method: 'POST',
    })
  }

  async getAvailableProviders(): Promise<ProvidersResponse> {
    return this.request('/mem0/providers')
  }

  async syncMemoryService(): Promise<{status: string, message: string, memory_service_settings: any}> {
    return this.request('/mem0/sync', {
      method: 'POST',
    })
  }

  async getMemoryServiceStatus(): Promise<MemoryServiceStatus> {
    return this.request('/mem0/status')
  }
}

export const memoryService = new MemoryService()
export default memoryService 