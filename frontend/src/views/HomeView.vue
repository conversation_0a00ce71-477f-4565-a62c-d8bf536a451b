<script setup lang="ts">
import { RouterLink } from 'vue-router'
</script>

<template>
  <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
    <div class="text-center">
      <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-8">
        AccA Memory Server
      </h1>
      <p class="text-xl text-gray-600 dark:text-gray-400 mb-12">
        AI Memory Management with Jina.ai, Google AI Studio, and OpenAI Embeddings
      </p>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        <!-- Memory Management Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div class="text-blue-500 mb-4">
            <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Memory Management
          </h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            View, search, and manage AI memories with user isolation
          </p>
          <RouterLink
            to="/memory"
            class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Manage Memories
            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </RouterLink>
        </div>

        <!-- Embedding Providers Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div class="text-green-500 mb-4">
            <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Multiple Providers
          </h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            Support for Jina.ai, Google AI Studio, and OpenAI embeddings
          </p>
          <RouterLink
            to="/memory?tab=settings"
            class="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            Configure
            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </RouterLink>
        </div>

        <!-- Testing Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div class="text-purple-500 mb-4">
            <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Testing Suite
          </h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            Comprehensive testing for memory operations and user isolation
          </p>
          <RouterLink
            to="/memory?tab=test"
            class="inline-flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
          >
            Run Tests
            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </RouterLink>
        </div>
      </div>

      <!-- Features List -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          Key Features
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
          <div class="flex items-start space-x-3">
            <svg class="w-6 h-6 text-green-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <div>
              <h3 class="font-semibold text-gray-900 dark:text-white">User Isolation</h3>
              <p class="text-gray-600 dark:text-gray-400">Each user has separate memory space</p>
            </div>
          </div>
          
          <div class="flex items-start space-x-3">
            <svg class="w-6 h-6 text-green-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <div>
              <h3 class="font-semibold text-gray-900 dark:text-white">Multiple Embedding Providers</h3>
              <p class="text-gray-600 dark:text-gray-400">Jina.ai, Google AI Studio, OpenAI support</p>
            </div>
          </div>
          
          <div class="flex items-start space-x-3">
            <svg class="w-6 h-6 text-green-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <div>
              <h3 class="font-semibold text-gray-900 dark:text-white">Semantic Search</h3>
              <p class="text-gray-600 dark:text-gray-400">Find relevant memories using natural language</p>
            </div>
          </div>
          
          <div class="flex items-start space-x-3">
            <svg class="w-6 h-6 text-green-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <div>
              <h3 class="font-semibold text-gray-900 dark:text-white">Memory Enhancement</h3>
              <p class="text-gray-600 dark:text-gray-400">Automatically enhance prompts with relevant memories</p>
            </div>
          </div>
          
          <div class="flex items-start space-x-3">
            <svg class="w-6 h-6 text-green-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <div>
              <h3 class="font-semibold text-gray-900 dark:text-white">Real-time Testing</h3>
              <p class="text-gray-600 dark:text-gray-400">Comprehensive test suite for all operations</p>
            </div>
          </div>
          
          <div class="flex items-start space-x-3">
            <svg class="w-6 h-6 text-green-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <div>
              <h3 class="font-semibold text-gray-900 dark:text-white">RESTful API</h3>
              <p class="text-gray-600 dark:text-gray-400">Complete API for memory management integration</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
