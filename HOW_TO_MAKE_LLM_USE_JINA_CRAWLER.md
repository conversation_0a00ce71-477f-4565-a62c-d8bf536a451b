# 🤖 Hướng dẫn làm LLM sử dụng Jina Crawler Tool

## 🎯 Vấn đề: LLM không tự động sử dụng tool

Mặc dù tool đã kết nối thành công, LLM cần được hướng dẫn cụ thể về khi nào và cách sử dụng tool.

## ✅ Giải pháp: 3 bước thiết lập

### Bước 1: Cập nhật Tool Configuration trong Open WebUI

1. **Vào Admin Panel** → **Tools** hoặc **External APIs**
2. **Cập nhật tool với URL mới**:
   ```
   http://jina-crawler-http-8001:8001/jina_crawler/openapi.json
   ```
3. **Refresh/Reload** tool configuration

### Bước 2: Thêm System Prompt

1. **Vào Settings** → **System Prompt** hoặc **Model Settings**
2. **Thêm system prompt** từ file `openwebui_system_prompt_for_jina_crawler.md`
3. **Hoặc tạo Custom Model** với system prompt này

### Bước 3: Test với Prompt cụ thể

## 🧪 Test Cases - Thử ngay các prompt này:

### Test 1: Crawl Website trực tiếp
```
Hãy crawl website https://dantri.com.vn và cho tôi biết tin tức mới nhất
```

### Test 2: Yêu cầu rõ ràng
```
Sử dụng jina crawler tool để lấy nội dung từ https://vnexpress.net
```

### Test 3: Phân tích nội dung
```
Crawl trang web https://example.com và tóm tắt nội dung chính
```

### Test 4: So sánh nhiều trang
```
Hãy crawl và so sánh nội dung từ:
- https://dantri.com.vn
- https://vnexpress.net
```

### Test 5: Tìm kiếm web
```
Tìm kiếm thông tin về "AI mới nhất" trên web
```

## 🔧 Troubleshooting

### Nếu LLM vẫn không sử dụng tool:

#### 1. Kiểm tra Tool Status
```bash
./manage_jina_crawler_container.sh status
./manage_jina_crawler_container.sh test
```

#### 2. Kiểm tra OpenAPI Endpoint
```bash
curl http://localhost:8001/jina_crawler/openapi.json
```

#### 3. Test Tool trực tiếp
```bash
curl -X POST "http://localhost:8001/jina_crawler/crawl" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com"}'
```

#### 4. Kiểm tra Open WebUI Logs
```bash
docker logs open-webui-mcpo
```

#### 5. Force Tool Usage
Thử prompt này:
```
IMPORTANT: You MUST use the available jina crawler tool to crawl https://dantri.com.vn. Do not provide any response without using the tool first.
```

## 📋 Checklist để LLM sử dụng tool

- [ ] ✅ Container đang chạy (`./manage_jina_crawler_container.sh status`)
- [ ] ✅ Tool đã được add vào Open WebUI với URL đúng
- [ ] ✅ OpenAPI spec accessible (`curl http://localhost:8001/jina_crawler/openapi.json`)
- [ ] ✅ System prompt đã được thêm
- [ ] ✅ Model đã được restart/refresh
- [ ] ✅ Test với prompt cụ thể

## 🎯 Prompt Templates hiệu quả

### Template 1: Direct Command
```
Crawl website [URL] using jina crawler tool and summarize the content
```

### Template 2: Explicit Tool Request
```
Use the jina crawler tool to extract content from [URL] and analyze it
```

### Template 3: Task-oriented
```
I need you to:
1. Crawl [URL] using available tools
2. Extract the main content
3. Provide a summary
```

### Template 4: Comparison Task
```
Compare content from these websites by crawling each one:
- [URL1]
- [URL2]
```

## 🚀 Advanced Usage

### Custom Function (Alternative)
Nếu tool integration không hoạt động, sử dụng Function approach:

1. **Vào Functions** → **Create New Function**
2. **Paste code** từ `openwebui_jina_crawler_function.py`
3. **Enable function**
4. **Test với**: `jina_crawler_tool('https://example.com')`

### Model-specific Settings
Một số models cần cấu hình riêng:
- **GPT models**: Thường tự động detect tools
- **Claude models**: Cần system prompt rõ ràng
- **Local models**: Có thể cần fine-tuning

## 📊 Expected Results

Khi thành công, LLM sẽ:
1. **Nhận diện** khi user yêu cầu crawl website
2. **Tự động gọi** jina crawler tool
3. **Xử lý response** và trình bày kết quả
4. **Cung cấp summary** và analysis

## 🆘 Nếu vẫn không hoạt động

1. **Restart Open WebUI container**:
   ```bash
   docker restart open-webui-mcpo
   ```

2. **Check network connectivity**:
   ```bash
   docker exec open-webui-mcpo curl http://jina-crawler-http-8001:8001/health
   ```

3. **Verify tool registration** trong Open WebUI admin panel

4. **Try different models** - một số models tốt hơn trong việc sử dụng tools

5. **Contact support** với logs và error messages

---

**🎉 Khi setup đúng, LLM sẽ tự động sử dụng Jina Crawler tool mỗi khi user yêu cầu crawl websites!**
