#!/usr/bin/env python3
"""
Migrate memories from old collections (768, 1536 dims) to new 3072 dimension collection
"""

import subprocess
import json
import time

def run_docker_command(cmd, timeout=60):
    """Run a docker command and return the result"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"

def migrate_memories():
    print("🔄 Migrating memories to 3072 dimensions...")
    
    # Migration script to run inside container
    migration_cmd = '''docker exec catomanton-webui python -c "
import sys
import os
import asyncio
from qdrant_client import QdrantClient
sys.path.append('/app/backend/data/pipelines/mem0-owui-gemini')

# Load the pipeline
exec(open('/app/backend/data/pipelines/mem0-owui-gemini/mem0-owui-gemini.py').read())

async def migrate_collection(source_collection, target_user_prefix):
    client = QdrantClient(host='qdrant', port=6333)
    
    try:
        # Get all memories from source collection
        result = client.scroll(
            collection_name=source_collection,
            limit=1000,  # Get up to 1000 memories
            with_payload=True,
            with_vectors=False
        )
        
        memories = result[0]
        print(f'📊 Found {len(memories)} memories in {source_collection}')
        
        if not memories:
            print(f'⚠️  No memories to migrate from {source_collection}')
            return 0
        
        # Initialize pipeline for re-embedding
        p = Pipeline()
        await p._ensure_memory_client()
        
        migrated_count = 0
        skipped_count = 0
        
        for i, memory in enumerate(memories, 1):
            try:
                payload = memory.payload
                text = payload.get('text', '')
                
                if not text or text.strip() == '' or text == 'No text':
                    print(f'  ⏭️  Skipping empty memory {i}/{len(memories)}')
                    skipped_count += 1
                    continue
                
                # Extract user_id from metadata or use default
                user_id = payload.get('user_id', f'{target_user_prefix}_migrated')
                
                # Create message for re-embedding
                message_data = {
                    'role': 'user',
                    'content': text
                }
                
                # Prepare metadata
                metadata = {
                    'migrated_from': source_collection,
                    'original_id': str(memory.id),
                    'migration_timestamp': '2025-01-16T01:45:00Z'
                }
                
                # Add original metadata if available
                if 'metadata' in payload:
                    original_metadata = payload.get('metadata', {})
                    if isinstance(original_metadata, dict):
                        metadata.update(original_metadata)
                
                # Re-embed and store in new collection
                result = await p.m.add(
                    user_id=user_id,
                    messages=[message_data],
                    metadata=metadata
                )
                
                print(f'  ✅ Migrated memory {i}/{len(memories)}: {text[:50]}...')
                migrated_count += 1
                
                # Small delay to avoid overwhelming the API
                await asyncio.sleep(0.1)
                
            except Exception as e:
                print(f'  ❌ Failed to migrate memory {i}: {e}')
                skipped_count += 1
                continue
        
        print(f'✅ Migration complete for {source_collection}:')
        print(f'   - Migrated: {migrated_count}')
        print(f'   - Skipped: {skipped_count}')
        
        return migrated_count
        
    except Exception as e:
        print(f'❌ Migration failed for {source_collection}: {e}')
        return 0

async def main():
    print('🚀 Starting memory migration to 3072 dimensions...')
    
    total_migrated = 0
    
    # Migrate from 768 dimension collection
    print('\\n📦 Migrating from mem0_gemini_768...')
    migrated_768 = await migrate_collection('mem0_gemini_768', 'user_768')
    total_migrated += migrated_768
    
    # Migrate from 1536 dimension collection  
    print('\\n📦 Migrating from mem0_gemini_1536...')
    migrated_1536 = await migrate_collection('mem0_gemini_1536', 'user_1536')
    total_migrated += migrated_1536
    
    print(f'\\n🎉 Migration completed!')
    print(f'Total memories migrated: {total_migrated}')
    
    # Verify final collection
    client = QdrantClient(host='qdrant', port=6333)
    info = client.get_collection('mem0_gemini_3072')
    print(f'\\n📊 Final mem0_gemini_3072 collection:')
    print(f'   - Total memories: {info.points_count}')
    print(f'   - Dimensions: {info.config.params.vectors.size}')

asyncio.run(main())
"'''
    
    print("🔄 Running migration script...")
    success, stdout, stderr = run_docker_command(migration_cmd, timeout=300)  # 5 minute timeout
    
    if success:
        print("✅ Migration completed successfully!")
        print(f"Output:\n{stdout}")
    else:
        print("❌ Migration failed!")
        print(f"Error:\n{stderr}")
        return False
    
    return True

if __name__ == "__main__":
    success = migrate_memories()
    if success:
        print("\n🎉 Memory migration to 3072 dimensions completed successfully!")
        print("\n📝 Next steps:")
        print("1. Test memory search functionality")
        print("2. Verify all memories are accessible")
        print("3. Consider cleaning up old collections if everything works well")
    else:
        print("\n❌ Migration failed. Please check the errors above.")