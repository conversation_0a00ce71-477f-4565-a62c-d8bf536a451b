#!/usr/bin/env python3
"""
Migration Script: Run inside pipelines container
Migrate memories from 3072 dimensions to 768 dimensions
"""

import asyncio
import json
import os
import requests
import time
from typing import List, Dict, Any

# Configuration
QDRANT_HOST = "qdrant"
QDRANT_PORT = 6333
GEMINI_API_KEY = "AIzaSyDHsg-8CCSEBUO9N3V0vaRsCSNq2iS4oec"

# Source collection (3072 dimensions)
SOURCE_COLLECTION = "mem0_gemini_3072_fixed"

# Target collection (768 dimensions)
TARGET_COLLECTION = "mem0_gemini_gemi_768"

def get_collection_info(collection_name: str) -> Dict[str, Any]:
    """Get collection information"""
    try:
        response = requests.get(f"http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{collection_name}")
        if response.status_code == 200:
            return response.json()["result"]
        else:
            return None
    except Exception as e:
        print(f"❌ Error getting collection info: {e}")
        return None

def export_memories_from_collection(collection_name: str) -> List[Dict[str, Any]]:
    """Export memories from source collection"""
    print(f"📤 Exporting memories from {collection_name}...")
    
    try:
        # Get all points from collection using scroll
        response = requests.post(
            f"http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{collection_name}/points/scroll",
            json={
                "limit": 10000,
                "with_payload": True,
                "with_vector": False
            }
        )
        
        if response.status_code != 200:
            print(f"❌ Error scrolling collection: {response.status_code}")
            return []
        
        data = response.json()
        points = data["result"]["points"]
        
        memories = []
        for point in points:
            payload = point["payload"]
            
            # Extract memory text and metadata
            memory_text = payload.get("content", payload.get("memory", payload.get("text", "")))
            memory_data = {
                "id": str(point["id"]),
                "memory": memory_text,
                "user_id": payload.get("user_id", "default_user"),
                "session_id": payload.get("session_id", ""),
                "timestamp": payload.get("timestamp", ""),
                "metadata": {
                    "session_id": payload.get("session_id", ""),
                    "timestamp": payload.get("timestamp", ""),
                    "dimensions": payload.get("dimensions", 3072)
                }
            }

            if memory_data["memory"]:  # Only include non-empty memories
                memories.append(memory_data)
        
        print(f"📊 Exported {len(memories)} memories from {collection_name}")
        return memories
        
    except Exception as e:
        print(f"❌ Error exporting from {collection_name}: {e}")
        return []

async def setup_target_memory_client():
    """Setup target memory client with 768 dimensions"""
    try:
        from mem0 import AsyncMemory
        
        config = {
            "vector_store": {
                "provider": "qdrant",
                "config": {
                    "host": QDRANT_HOST,
                    "port": QDRANT_PORT,
                    "collection_name": TARGET_COLLECTION,
                },
            },
            "llm": {
                "provider": "gemini",
                "config": {
                    "api_key": GEMINI_API_KEY,
                    "model": "gemini-2.5-flash",
                    "temperature": 0.1,
                    "max_tokens": 1000
                },
            },
            "embedder": {
                "provider": "gemini",
                "config": {
                    "api_key": GEMINI_API_KEY,
                    "model": "text-embedding-004",
                    "embedding_dims": 768
                },
            },
        }
        
        memory_client = await AsyncMemory.from_config(config)
        print(f"✅ Target memory client initialized: {TARGET_COLLECTION}")
        return memory_client
        
    except Exception as e:
        print(f"❌ Error setting up memory client: {e}")
        return None

async def import_memories_to_target(memory_client, memories: List[Dict[str, Any]]) -> int:
    """Import memories to target collection with 768 dimensions"""
    if not memories:
        return 0

    print(f"📥 Importing {len(memories)} memories to {TARGET_COLLECTION}...")
    imported_count = 0

    # Process in smaller batches to avoid rate limits
    batch_size = 5  # Small batch size for Gemini API

    for i in range(0, len(memories), batch_size):
        batch = memories[i:i + batch_size]
        print(f"   🔄 Processing batch {i//batch_size + 1}/{(len(memories) + batch_size - 1)//batch_size}")

        for j, memory_data in enumerate(batch):
            try:
                # Add memory using mem0 client (will re-embed with 768 dims)
                await memory_client.add(
                    messages=memory_data["memory"],
                    user_id=memory_data["user_id"],
                    metadata=memory_data.get("metadata", {})
                )
                imported_count += 1

                # Rate limiting - wait between requests
                if j < len(batch) - 1:  # Don't wait after last item in batch
                    await asyncio.sleep(0.5)  # 500ms delay between requests

            except Exception as e:
                error_msg = str(e)
                if "429" in error_msg or "quota" in error_msg.lower():
                    print(f"   ⏳ Rate limit hit, waiting 10 seconds...")
                    await asyncio.sleep(10)
                    # Retry once
                    try:
                        await memory_client.add(
                            messages=memory_data["memory"],
                            user_id=memory_data["user_id"],
                            metadata=memory_data.get("metadata", {})
                        )
                        imported_count += 1
                    except Exception as retry_e:
                        print(f"   ❌ Retry failed for memory {memory_data['id']}: {retry_e}")
                else:
                    print(f"   ❌ Error importing memory {memory_data['id']}: {e}")
                continue

        # Wait between batches
        if i + batch_size < len(memories):
            print(f"   ⏳ Waiting 2 seconds before next batch...")
            await asyncio.sleep(2)

        print(f"   ✅ Batch completed. Total imported: {imported_count}/{len(memories)}")

    print(f"✅ Successfully imported {imported_count} memories")
    return imported_count

def verify_target_collection():
    """Verify target collection exists and show stats"""
    try:
        info = get_collection_info(TARGET_COLLECTION)
        if info:
            print(f"\n📊 Target collection stats:")
            print(f"   - Collection: {TARGET_COLLECTION}")
            print(f"   - Total points: {info['points_count']}")
            print(f"   - Vector size: {info['config']['params']['vectors']['size']}")
            print(f"   - Distance: {info['config']['params']['vectors']['distance']}")
            return True
        else:
            print(f"❌ Target collection {TARGET_COLLECTION} not found")
            return False
    except Exception as e:
        print(f"❌ Error verifying target collection: {e}")
        return False

async def main():
    """Main migration process"""
    print("🚀 Starting migration from 3072 dimensions to 768 dimensions")
    print(f"📍 Qdrant: {QDRANT_HOST}:{QDRANT_PORT}")
    print(f"🎯 Source: {SOURCE_COLLECTION}")
    print(f"🎯 Target: {TARGET_COLLECTION}")
    
    # Check source collection
    source_info = get_collection_info(SOURCE_COLLECTION)
    if not source_info:
        print(f"❌ Source collection {SOURCE_COLLECTION} not found")
        return
    
    print(f"📦 Source collection: {source_info['points_count']} points, {source_info['config']['params']['vectors']['size']} dims")
    
    # Export memories from source
    memories = export_memories_from_collection(SOURCE_COLLECTION)
    if not memories:
        print("ℹ️  No memories found to migrate")
        return
    
    # Setup target memory client
    memory_client = await setup_target_memory_client()
    if not memory_client:
        print("❌ Failed to setup target memory client")
        return
    
    # Import to target
    imported_count = await import_memories_to_target(memory_client, memories)
    
    # Verify results
    print(f"\n🎉 Migration completed!")
    print(f"📊 Total memories migrated: {imported_count}")
    
    verify_target_collection()
    
    # Recommendations
    print(f"\n💡 Next steps:")
    print(f"   1. Update pipeline valves to use collection: {TARGET_COLLECTION}")
    print(f"   2. Set embedder_dims to 768 in pipeline configuration")
    print(f"   3. Test the new pipeline configuration")
    print(f"   4. Consider backing up source collection before deletion")

if __name__ == "__main__":
    asyncio.run(main())
