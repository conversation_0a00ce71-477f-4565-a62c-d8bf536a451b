#!/bin/bash

# 🚀 Llama.cpp Performance Optimization Script
# Advanced ARM64 Neoverse-N1 optimization for llama.cpp deployment

set -e

echo "🚀 LLAMA.CPP PERFORMANCE OPTIMIZATION SUITE"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_error "This script should not be run as root for safety"
        exit 1
    fi
}

# Function to create optimized systemd service
create_optimized_service() {
    print_info "Creating optimized systemd service configuration..."
    
    sudo tee /etc/systemd/system/llama-cpp-optimized.service > /dev/null << 'EOF'
[Unit]
Description=Optimized Llama.cpp Server (ARM64 Neoverse-N1)
After=network-online.target
Wants=network-online.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/opt/llama-cpp

# Dynamic model switching capability
ExecStart=/opt/llama-cpp/bin/llama-server \
    --model /opt/llama-cpp/models/gemma-3-4b-it-q4_k_m.gguf \
    --port 11434 \
    --host 0.0.0.0 \
    --threads 28 \
    --ctx-size 8192 \
    --batch-size 1024 \
    --parallel 8 \
    --flash-attn \
    --cont-batching \
    --metrics \
    --slots \
    --embedding \
    --log-format text

# ARM64 Neoverse-N1 Performance Optimizations
Environment="GGML_NEON=1"
Environment="GGML_NEON_I8MM=1"
Environment="GGML_NEON_FP16=1"
Environment="OMP_NUM_THREADS=28"
Environment="GGML_N_THREADS=28"
Environment="GGML_BLAS=1"
Environment="MALLOC_TRIM_THRESHOLD_=131072"
Environment="OMP_SCHEDULE=dynamic,1"
Environment="OMP_PROC_BIND=spread"
Environment="OMP_PLACES=cores"

# Memory Management
Environment="MALLOC_ARENA_MAX=4"
Environment="MALLOC_MMAP_THRESHOLD_=131072"
Environment="MALLOC_TOP_PAD_=131072"

# Resource Limits (Optimized)
LimitNOFILE=131072
LimitNPROC=65536
LimitAS=infinity
LimitMEMLOCK=infinity

# Process Priority (Higher priority for better performance)
Nice=-10
IOSchedulingClass=1
IOSchedulingPriority=4

# Security (Still maintain security while optimizing)
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/opt/llama-cpp

# Restart Settings
Restart=always
RestartSec=5
TimeoutStartSec=120
TimeoutStopSec=30

# Advanced Logging
StandardOutput=append:/opt/llama-cpp/logs/optimized-server.log
StandardError=append:/opt/llama-cpp/logs/optimized-error.log
SyslogIdentifier=llama-cpp-optimized

[Install]
WantedBy=multi-user.target
EOF

    print_status "Optimized systemd service created"
}

# Function to create model management script
create_model_manager() {
    print_info "Creating model management script..."
    
    cat > /opt/llama-cpp/model-manager.sh << 'EOF'
#!/bin/bash

# 🎯 Llama.cpp Model Management Script
# Easily switch between models and optimize performance

MODELS_DIR="/opt/llama-cpp/models"
SERVICE_NAME="llama-cpp-optimized"

# Available models with metadata
declare -A MODELS=(
    ["gemma-3-4b"]="/opt/llama-cpp/models/gemma-3-4b-it-q4_k_m.gguf"
    ["gemma-3-12b"]="/opt/llama-cpp/models/gemma-3-12b-it-q4_k_m.gguf"
    ["llama-3.2-1b"]="/opt/llama-cpp/models/llama-3.2-1b-instruct-q4_k_m.gguf"
    ["qwen-0.5b"]="/opt/llama-cpp/models/qwen2.5-0.5b-instruct-q4_k_m.gguf"
    ["gemma2-2b"]="/opt/llama-cpp/models/gemma2-2b.gguf"
)

declare -A MODEL_CONFIGS=(
    ["gemma-3-4b"]="--ctx-size 8192 --batch-size 1024 --parallel 8"
    ["gemma-3-12b"]="--ctx-size 4096 --batch-size 512 --parallel 4"
    ["llama-3.2-1b"]="--ctx-size 4096 --batch-size 1024 --parallel 12"
    ["qwen-0.5b"]="--ctx-size 8192 --batch-size 2048 --parallel 16"
    ["gemma2-2b"]="--ctx-size 8192 --batch-size 1024 --parallel 10"
)

list_models() {
    echo "📋 Available Models:"
    for model in "${!MODELS[@]}"; do
        if [[ -f "${MODELS[$model]}" ]]; then
            size=$(du -h "${MODELS[$model]}" | cut -f1)
            echo "  ✅ $model (${size})"
        else
            echo "  ❌ $model (missing file)"
        fi
    done
}

switch_model() {
    local model_name="$1"
    
    if [[ -z "$model_name" ]]; then
        echo "❌ Please specify a model name"
        list_models
        return 1
    fi
    
    if [[ ! "${MODELS[$model_name]+isset}" ]]; then
        echo "❌ Unknown model: $model_name"
        list_models
        return 1
    fi
    
    local model_path="${MODELS[$model_name]}"
    local model_config="${MODEL_CONFIGS[$model_name]}"
    
    if [[ ! -f "$model_path" ]]; then
        echo "❌ Model file not found: $model_path"
        return 1
    fi
    
    echo "🔄 Switching to model: $model_name"
    echo "📁 Model path: $model_path"
    echo "⚙️ Configuration: $model_config"
    
    # Update systemd service
    sudo sed -i "s|--model [^ ]*|--model $model_path|" /etc/systemd/system/$SERVICE_NAME.service
    sudo sed -i "s|--ctx-size [0-9]* --batch-size [0-9]* --parallel [0-9]*|$model_config|" /etc/systemd/system/$SERVICE_NAME.service
    
    # Reload and restart service
    sudo systemctl daemon-reload
    sudo systemctl restart $SERVICE_NAME
    
    echo "✅ Model switched successfully!"
    echo "🔍 Checking service status..."
    sleep 3
    sudo systemctl --no-pager status $SERVICE_NAME
}

show_status() {
    echo "📊 Current Service Status:"
    sudo systemctl --no-pager status $SERVICE_NAME
    
    echo ""
    echo "📈 Performance Metrics:"
    curl -s http://localhost:11434/metrics 2>/dev/null || echo "❌ Metrics endpoint not available"
}

show_logs() {
    echo "📜 Recent Logs:"
    sudo journalctl -u $SERVICE_NAME --no-pager -n 20
}

benchmark_model() {
    local model_name="$1"
    echo "🏃 Benchmarking model: $model_name"
    
    # Simple benchmark
    time curl -X POST http://localhost:11434/v1/chat/completions \
        -H "Content-Type: application/json" \
        -d '{
            "model": "current",
            "messages": [{"role": "user", "content": "Count from 1 to 10 in Vietnamese."}],
            "max_tokens": 100
        }' \
        -s | jq .
}

case "$1" in
    "list")
        list_models
        ;;
    "switch")
        switch_model "$2"
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    "benchmark")
        benchmark_model "$2"
        ;;
    *)
        echo "🎯 Llama.cpp Model Manager"
        echo "Usage: $0 {list|switch|status|logs|benchmark} [model_name]"
        echo ""
        echo "Commands:"
        echo "  list      - List available models"
        echo "  switch    - Switch to a different model"
        echo "  status    - Show current service status"
        echo "  logs      - Show recent logs"
        echo "  benchmark - Run performance benchmark"
        echo ""
        echo "Example: $0 switch gemma-3-4b"
        ;;
esac
EOF

    sudo chmod +x /opt/llama-cpp/model-manager.sh
    sudo ln -sf /opt/llama-cpp/model-manager.sh /usr/local/bin/llamacpp-manager
    
    print_status "Model manager created at /usr/local/bin/llamacpp-manager"
}

# Function to create performance monitoring
create_performance_monitor() {
    print_info "Creating performance monitoring tools..."
    
    cat > /opt/llama-cpp/monitor-performance.sh << 'EOF'
#!/bin/bash

# 📊 Llama.cpp Performance Monitor
# Real-time monitoring and optimization insights

echo "📊 LLAMA.CPP PERFORMANCE MONITOR"
echo "================================="

# System resources
echo "🖥️ System Resources:"
echo "CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')%"
echo "Memory Usage: $(free -h | awk 'NR==2{printf "%.1f%% (%s/%s)", $3*100/$2, $3, $2}')"
echo "Load Average: $(uptime | awk -F'load average:' '{print $2}')"

echo ""
echo "🚀 Llama.cpp Process:"
ps aux | grep llama-server | grep -v grep | while read line; do
    pid=$(echo $line | awk '{print $2}')
    cpu=$(echo $line | awk '{print $3}')
    mem=$(echo $line | awk '{print $4}')
    echo "PID: $pid, CPU: ${cpu}%, Memory: ${mem}%"
done

echo ""
echo "🌐 API Status:"
if curl -s http://localhost:11434/health >/dev/null 2>&1; then
    echo "✅ API is responsive"
    
    # Get model info
    model_info=$(curl -s http://localhost:11434/v1/models | jq -r '.data[0].id' 2>/dev/null)
    echo "📋 Current Model: ${model_info:-"Unknown"}"
    
    # Get metrics if available
    if curl -s http://localhost:11434/metrics >/dev/null 2>&1; then
        echo "📈 Metrics available at http://localhost:11434/metrics"
    fi
else
    echo "❌ API is not responding"
fi

echo ""
echo "💾 Model Storage:"
du -sh /opt/llama-cpp/models/* 2>/dev/null | sort -h

echo ""
echo "📜 Recent Logs:"
sudo journalctl -u llama-cpp-optimized --no-pager -n 5 --since "10 minutes ago" 2>/dev/null || echo "No recent logs"
EOF

    chmod +x /opt/llama-cpp/monitor-performance.sh
    sudo ln -sf /opt/llama-cpp/monitor-performance.sh /usr/local/bin/llamacpp-monitor
    
    print_status "Performance monitor created at /usr/local/bin/llamacpp-monitor"
}

# Function to optimize system settings
optimize_system() {
    print_info "Applying ARM64 system optimizations..."
    
    # Create optimized sysctl settings
    sudo tee /etc/sysctl.d/99-llama-cpp-optimization.conf > /dev/null << 'EOF'
# Llama.cpp ARM64 Performance Optimizations

# Memory management
vm.swappiness=10
vm.dirty_ratio=15
vm.dirty_background_ratio=5
vm.vfs_cache_pressure=50

# Network optimizations
net.core.rmem_max=134217728
net.core.wmem_max=134217728
net.ipv4.tcp_rmem=4096 87380 134217728
net.ipv4.tcp_wmem=4096 65536 134217728

# File system optimizations
fs.file-max=2097152
fs.inotify.max_user_watches=524288

# Kernel optimizations for AI workloads
kernel.sched_autogroup_enabled=0
kernel.sched_migration_cost_ns=5000000
EOF

    sudo sysctl -p /etc/sysctl.d/99-llama-cpp-optimization.conf
    
    print_status "System optimizations applied"
}

# Function to create backup and restore functionality
create_backup_system() {
    print_info "Creating backup and restore system..."
    
    cat > /opt/llama-cpp/backup-restore.sh << 'EOF'
#!/bin/bash

# 💾 Llama.cpp Backup and Restore System

BACKUP_DIR="/opt/llama-cpp/backups"
CONFIG_BACKUP="$BACKUP_DIR/config-$(date +%Y%m%d-%H%M%S).tar.gz"

create_backup() {
    echo "💾 Creating backup..."
    mkdir -p "$BACKUP_DIR"
    
    tar -czf "$CONFIG_BACKUP" \
        /etc/systemd/system/llama-cpp-optimized.service \
        /opt/llama-cpp/model-manager.sh \
        /opt/llama-cpp/monitor-performance.sh \
        /etc/sysctl.d/99-llama-cpp-optimization.conf \
        2>/dev/null
    
    echo "✅ Backup created: $CONFIG_BACKUP"
}

restore_backup() {
    local backup_file="$1"
    if [[ -z "$backup_file" ]]; then
        echo "📋 Available backups:"
        ls -la "$BACKUP_DIR"/*.tar.gz 2>/dev/null || echo "No backups found"
        return 1
    fi
    
    if [[ ! -f "$backup_file" ]]; then
        echo "❌ Backup file not found: $backup_file"
        return 1
    fi
    
    echo "🔄 Restoring from backup: $backup_file"
    sudo tar -xzf "$backup_file" -C /
    sudo systemctl daemon-reload
    
    echo "✅ Backup restored successfully"
}

case "$1" in
    "create")
        create_backup
        ;;
    "restore")
        restore_backup "$2"
        ;;
    *)
        echo "💾 Llama.cpp Backup System"
        echo "Usage: $0 {create|restore} [backup_file]"
        ;;
esac
EOF

    chmod +x /opt/llama-cpp/backup-restore.sh
    
    print_status "Backup system created"
}

# Main execution
main() {
    print_info "Starting llama.cpp optimization process..."
    
    check_root
    
    # Create backup first
    print_info "Creating backup of current configuration..."
    create_backup_system
    /opt/llama-cpp/backup-restore.sh create
    
    # Apply optimizations
    create_optimized_service
    create_model_manager
    create_performance_monitor
    optimize_system
    
    print_info "Stopping current service..."
    sudo systemctl stop llama-cpp-native 2>/dev/null || true
    
    print_info "Starting optimized service..."
    sudo systemctl daemon-reload
    sudo systemctl enable llama-cpp-optimized
    sudo systemctl start llama-cpp-optimized
    
    # Wait for service to start
    sleep 5
    
    print_status "Optimization complete!"
    print_info "Available commands:"
    echo "  • llamacpp-manager - Model management"
    echo "  • llamacpp-monitor - Performance monitoring"
    echo "  • systemctl status llama-cpp-optimized - Service status"
    
    print_info "Testing optimized setup..."
    llamacpp-monitor
}

# Run main function
main "$@" 