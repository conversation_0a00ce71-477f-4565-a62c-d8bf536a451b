#!/bin/bash

# Build script for AI Assistant WebView Wrapper
echo "🚀 Building AI Assistant WebView Wrapper..."

# Navigate to wrapper app directory
cd ai_assistant_webview_wrapper

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Check for any issues
echo "🔍 Running analysis..."
flutter analyze

# Build APK
echo "🔨 Building APK..."
flutter build apk --release

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo "📱 APK location: build/app/outputs/flutter-apk/app-release.apk"
    
    # Copy APK to root directory with descriptive name
    cp build/app/outputs/flutter-apk/app-release.apk ../ai_assistant_webview_wrapper_vps.apk
    echo "📋 Copied APK to: ai_assistant_webview_wrapper_vps.apk"
    
    echo ""
    echo "🎯 Next steps:"
    echo "1. Install the APK on your Android device:"
    echo "   adb install ai_assistant_webview_wrapper_vps.apk"
    echo ""
    echo "2. Your Open WebUI server is running on VPS:"
    echo "   - Server URL: http://**************:3001"
    echo "   - Accessible from anywhere with internet connection"
    echo ""
    echo "3. Open the AI Assistant app on your device"
    echo "4. The app will auto-detect your server and load Open WebUI"
    echo "5. Use Settings (gear icon) to change server URL if needed"
    echo ""
    echo "🔧 If you need to change the server URL:"
    echo "   Edit ai_assistant_webview_wrapper/lib/core/app/app_config.dart"
    echo "   Update the 'defaultServerUrl' value (currently: **************:3001)"
    
else
    echo "❌ Build failed!"
    exit 1
fi 