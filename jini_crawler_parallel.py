#!/usr/bin/env python3
"""
<PERSON><PERSON> - Simplified version without <PERSON><PERSON>
Uses requests + BeautifulSoup + Gemini for web scraping
All dependencies included in this single file
"""

import asyncio
import logging
import time
import os
import aiohttp
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup

# Setup logger first
logger = logging.getLogger(__name__)

# Import TLS client for Cloudflare bypass
try:
    import tls_client
    from fake_useragent import UserAgent
    TLS_CLIENT_AVAILABLE = True
except ImportError:
    TLS_CLIENT_AVAILABLE = False
    logger.warning("⚠️ TLS client not available. Install with: pip install tls-client fake-useragent")

# Import cleanup manager
from utils.cleanup_manager import (
    register_session_for_cleanup,
    register_connector_for_cleanup,
    register_tls_session_for_cleanup,
    cleanup_all_resources
)

# Import external GeminiProcessor with batch processing
from gemini_processor import GeminiProcessor as ExternalGeminiProcessor

@dataclass
class GeminiConfig:
    """Configuration for Gemini API."""
    api_key: str
    model_name: str = "gemini-2.5-flash-lite"
    api_url: str = "https://generativelanguage.googleapis.com/v1beta/models"
    timeout: int = 30
    max_retries: int = 3

class GeminiProcessor:
    """
    Fast content processor using Google's Gemini 2.5 Flash API.
    """
    
    def __init__(self, gemini_config: Optional[GeminiConfig] = None):
        self.config = gemini_config or self._load_config()
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Vietnamese-optimized prompts
        self.prompts = {
            "html_to_markdown": """Convert the following HTML content to clean, well-formatted Markdown. Preserve Vietnamese text exactly, maintain proper formatting, and extract the main content while removing navigation, ads, and irrelevant elements.

HTML Content:
{content}

Markdown Output:""",
            
            "summarize": """Summarize the following Vietnamese content in a concise manner while preserving key information:

Content:
{content}

Summary:""",
            
            "clean": """Clean and format the following Vietnamese content to readable Markdown:

Content:
{content}

Cleaned Markdown:"""
        }
        
    def _load_config(self) -> GeminiConfig:
        """Load Gemini configuration from environment variables."""
        api_key = os.getenv("GEMINI_API_KEY", "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM")
        if not api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
            
        return GeminiConfig(
            api_key=api_key,
            model_name=os.getenv("GEMINI_MODEL_NAME", "gemini-2.5-flash-lite"),
            api_url=os.getenv("GEMINI_API_URL", "https://generativelanguage.googleapis.com/v1beta/models"),
            timeout=int(os.getenv("GEMINI_TIMEOUT", "30")),
            max_retries=int(os.getenv("GEMINI_MAX_RETRIES", "3"))
        )
    
    async def initialize(self) -> bool:
        """Initialize the Gemini processor."""
        try:
            if not self.session:
                timeout = aiohttp.ClientTimeout(total=self.config.timeout)
                self.session = aiohttp.ClientSession(timeout=timeout)
            
            logger.info("✅ Gemini processor initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Gemini processor: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Cleanup resources."""
        if self.session:
            await self.session.close()
            self.session = None
        logger.info("✅ Gemini processor cleanup completed")
    
    async def process_content(
        self,
        content: str,
        task_type: str = "html_to_markdown",
        max_length: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Process content using Gemini API.
        """
        start_time = time.time()
        
        try:
            # Prepare prompt
            prompt = self._prepare_prompt(content, task_type, max_length)
            
            # Call Gemini API
            result = await self._call_gemini_api(prompt)
            
            processing_time = time.time() - start_time
            
            logger.info(f"Performance: gemini_processing completed in {processing_time:.3f}s")
            
            return {
                "success": True,
                "original_length": len(content),
                "processed_content": result,
                "output_length": len(result),
                "processing_time": processing_time,
                "model": self.config.model_name,
                "task_type": task_type
            }
            
        except Exception as e:
            logger.error(f"Gemini processing error: {e}")
            return {
                "success": False,
                "original_length": len(content),
                "processed_content": "",
                "processing_time": 0,
                "model": self.config.model_name,
                "error": str(e),
                "output_length": 0
            }
    
    def _prepare_prompt(self, content: str, task_type: str, max_length: Optional[int]) -> str:
        """Prepare prompt for Gemini API."""
        # Smart truncation for performance
        max_content_length = max_length or 8000  # Gemini can handle longer context
        if len(content) > max_content_length:
            content = self._smart_truncate(content, max_content_length)
        
        # Get appropriate prompt template
        prompt_template = self.prompts.get(task_type, self.prompts["html_to_markdown"])
        return prompt_template.format(content=content)
    
    def _smart_truncate(self, content: str, max_length: int) -> str:
        """Smart content truncation."""
        if len(content) <= max_length:
            return content
        
        # Try to find a good breaking point
        truncated = content[:max_length]
        
        # Vietnamese sentence endings
        vietnamese_endings = [
            '. ', '.\n', '? ', '?\n', '! ', '!\n',
            '." ', '."', '?" ', '?"', '!" ', '!"',
            '.)', '.)', '?)', '?)', '!)', '!)'
        ]
        
        # Find the last complete Vietnamese sentence within limit
        last_sentence_end = -1
        for ending in vietnamese_endings:
            pos = truncated.rfind(ending)
            if pos > last_sentence_end and pos > max_length * 0.6:  # At least 60% of content
                last_sentence_end = pos + len(ending) - 1
        
        if last_sentence_end > 0:
            return content[:last_sentence_end + 1]
        
        # Fallback: find last space to avoid cutting words
        last_space = truncated.rfind(' ')
        if last_space > max_length * 0.8:  # At least 80% of content
            return content[:last_space]
        
        return truncated + "..."
    
    async def _call_gemini_api(self, prompt: str) -> str:
        """Call Gemini API with retry logic."""
        if not self.session:
            await self.initialize()
        
        url = f"{self.config.api_url}/{self.config.model_name}:generateContent"
        params = {"key": self.config.api_key}
        
        headers = {
            "Content-Type": "application/json"
        }
        
        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.0,  # Deterministic for consistency
                "maxOutputTokens": 2048,
                "topK": 1,
                "topP": 1.0
            }
        }
        
        for attempt in range(self.config.max_retries + 1):
            try:
                async with self.session.post(url, params=params, headers=headers, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        # Extract generated text
                        if "candidates" in result and len(result["candidates"]) > 0:
                            candidate = result["candidates"][0]
                            if "content" in candidate and "parts" in candidate["content"]:
                                parts = candidate["content"]["parts"]
                                if len(parts) > 0:
                                    return parts[0].get("text", "").strip()
                        
                        # Fallback if no content found
                        return ""
                    
                    elif response.status == 429:
                        # Rate limited, wait and retry
                        if attempt < self.config.max_retries:
                            wait_time = 2 ** attempt  # Exponential backoff
                            logger.warning(f"Rate limited, waiting {wait_time}s before retry {attempt + 1}")
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            raise Exception(f"Rate limited after {self.config.max_retries} retries")
                    
                    else:
                        # Other error
                        error_text = await response.text()
                        raise Exception(f"Gemini API error {response.status}: {error_text}")
                        
            except Exception as e:
                if attempt < self.config.max_retries:
                    wait_time = 2 ** attempt  # Exponential backoff
                    logger.warning(f"Gemini API call failed, waiting {wait_time}s before retry {attempt + 1}: {e}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    raise
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on Gemini processor."""
        try:
            # Test with simple Vietnamese content
            test_content = "<h1>Tin tức công nghệ</h1><p>Trí tuệ nhân tạo đang phát triển mạnh mẽ.</p>"
            test_result = await self.process_content(test_content, "html_to_markdown")
            
            return {
                "status": "healthy" if test_result.get("success") else "error",
                "model_name": self.config.model_name,
                "api_available": True,
                "test_successful": test_result.get("success", False),
                "test_processing_time": test_result.get("processing_time", 0)
            }
            
        except Exception as e:
            logger.error(f"Gemini health check failed: {e}")
            return {
                "status": "error",
                "model_name": self.config.model_name,
                "api_available": False,
                "error": str(e)
            }

@dataclass
class PageSnapshot:
    """Simplified page snapshot without Playwright dependencies"""
    title: str = ""
    description: str = ""
    href: str = ""
    html: str = ""
    text: str = ""
    markdown: str = ""  # 🚀 NEW: Structured markdown content
    status: Optional[int] = None
    status_text: str = ""
    parsed: Optional[Dict] = None
    imgs: List[Dict] = None
    max_elem_depth: int = 0
    elem_count: int = 0
    is_intermediate: bool = False
    
    def __post_init__(self):
        if self.imgs is None:
            self.imgs = []

@dataclass
class JiniCrawlResult:
    """Result from Jini crawler processing"""
    success: bool
    url: str
    title: Optional[str] = None
    original_content: Optional[str] = None
    cleaned_content: Optional[str] = None
    processed_content: Optional[str] = None
    processing_time: float = 0.0
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class JiniCrawler:
    """
    Simplified Jini Crawler using requests + BeautifulSoup + Gemini
    No Playwright dependencies - lightweight and fast
    """
    
    def __init__(self):
        self.session = None
        self.gemini_processor = ExternalGeminiProcessor()  # Use external processor with batch support
        self._initialized = False
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        self.timeout = 45  # Increased timeout for TLS client
        # TLS client for Cloudflare bypass
        self.tls_session = None
        self.use_tls_client = TLS_CLIENT_AVAILABLE
        self.user_agent_generator = None
        
    async def initialize(self) -> bool:
        """Initialize the crawler components"""
        try:
            if not self._initialized:
                # Create aiohttp session for async requests
                timeout = aiohttp.ClientTimeout(total=self.timeout)
                connector = aiohttp.TCPConnector(
                    limit=100,
                    limit_per_host=10,
                    ttl_dns_cache=300,
                    use_dns_cache=True,
                    enable_cleanup_closed=True
                )

                self.session = aiohttp.ClientSession(
                    timeout=timeout,
                    headers={'User-Agent': self.user_agent},
                    connector=connector
                )

                # Register for cleanup
                register_session_for_cleanup(self.session)
                register_connector_for_cleanup(connector)

                # Initialize TLS client for Cloudflare bypass
                if self.use_tls_client:
                    try:
                        # Initialize user agent generator
                        self.user_agent_generator = UserAgent()

                        # Create TLS session with Chrome fingerprint
                        self.tls_session = tls_client.Session(
                            random_tls_extension_order=True,
                            client_identifier='chrome_120'
                        )

                        # Set realistic headers
                        self._update_tls_headers()

                        # Register TLS session for cleanup
                        register_tls_session_for_cleanup(self.tls_session)

                        logger.info("✅ TLS client initialized for Cloudflare bypass")
                    except Exception as e:
                        logger.warning(f"⚠️ TLS client initialization failed: {e}")
                        self.use_tls_client = False

                await self.gemini_processor.initialize()
                self._initialized = True
                logger.info("✅ Jini Crawler initialized successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to initialize Jini Crawler: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Cleanup resources"""
        try:
            # Cleanup TLS session
            if self.tls_session:
                try:
                    self.tls_session.close()
                    self.tls_session = None
                except Exception as e:
                    logger.warning(f"⚠️ TLS session cleanup warning: {e}")

            if self.session:
                await self.session.close()
                self.session = None
            await self.gemini_processor.cleanup()

            # Use comprehensive cleanup manager
            await cleanup_all_resources(
                force=True,
                delay=0.5,
                gc_cycles=5
            )

            self._initialized = False
            logger.info("✅ Jini Crawler cleanup completed")
        except Exception as e:
            logger.error(f"❌ Error during Jini Crawler cleanup: {e}")

    def _update_tls_headers(self):
        """Update TLS session headers with realistic browser headers"""
        if not self.tls_session:
            return

        try:
            # Get random user agent
            user_agent = self.user_agent_generator.random if self.user_agent_generator else self.user_agent

            # Set realistic headers
            headers = {
                'User-Agent': user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Cache-Control': 'max-age=0'
            }

            self.tls_session.headers.update(headers)

        except Exception as e:
            logger.warning(f"⚠️ Failed to update TLS headers: {e}")

    def _fetch_with_tls_bypass(self, url: str) -> Optional[Dict[str, Any]]:
        """Fetch URL using TLS client bypass (synchronous)"""
        if not self.tls_session:
            return None

        try:
            # Update headers for each request
            self._update_tls_headers()

            # Set timeout
            self.tls_session.timeout_seconds = self.timeout

            logger.debug(f"🔒 TLS bypass request: {url}")

            # Make request
            response = self.tls_session.get(url)

            if response.status_code == 200:
                result = {
                    'content': response.text,
                    'status_code': response.status_code,
                    'headers': dict(response.headers),
                    'url': str(response.url)
                }
                logger.debug(f"✅ TLS bypass successful: {len(response.text)} chars")
                return result
            else:
                logger.warning(f"⚠️ TLS bypass failed: HTTP {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"❌ TLS bypass error: {e}")
            return None
    

    def _get_cache_key(self, url: str) -> str:
        """Generate cache key for URL"""
        return hashlib.md5(url.encode()).hexdigest()
    
    def _get_cache_path(self, cache_key: str) -> str:
        """Get cache file path"""
        cache_dir = "/tmp/jina_cache"
        os.makedirs(cache_dir, exist_ok=True)
        return os.path.join(cache_dir, f"{cache_key}.pkl")
    
    def _is_cache_valid(self, cache_path: str, max_age: int = 3600) -> bool:
        """Check if cache is still valid"""
        if not os.path.exists(cache_path):
            return False
        
        cache_age = time.time() - os.path.getmtime(cache_path)
        return cache_age < max_age
    
    def _save_to_cache(self, cache_key: str, data: dict):
        """Save data to cache"""
        try:
            cache_path = self._get_cache_path(cache_key)
            with open(cache_path, 'wb') as f:
                pickle.dump(data, f)
        except Exception as e:
            logger.debug(f"Cache save error: {e}")
    
    def _load_from_cache(self, cache_key: str) -> Optional[dict]:
        """Load data from cache"""
        try:
            cache_path = self._get_cache_path(cache_key)
            if self._is_cache_valid(cache_path):
                with open(cache_path, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            logger.debug(f"Cache load error: {e}")
        return None

    async def scrap_url(self, url: str, options: Optional[Dict[str, Any]] = None):
        """
        Scrape URL using requests + BeautifulSoup (generator for compatibility)
        
        Args:
            url: URL to scrape
            options: Optional parameters (not used in simple version)
            
        Yields:
            PageSnapshot with scraped content
        """
        if not self._initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            logger.info(f"🕷️ Scraping URL with simple crawler: {url}")

            html_content = None
            status = 200
            status_text = "OK"

            # Try regular request first
            try:
                async with self.session.get(url) as response:
                    status = response.status
                    status_text = response.reason or ""
                    html_content = await response.text()

                    # Check if we got blocked by Cloudflare
                    if status == 403 or "cloudflare" in html_content.lower() or "checking your browser" in html_content.lower():
                        logger.warning(f"⚠️ Cloudflare detected for {url}, trying anti-Cloudflare methods")
                        html_content = None  # Reset to try fallback

            except Exception as e:
                logger.warning(f"⚠️ Regular request failed for {url}: {e}")

            # Try TLS bypass if regular request failed or was blocked
            if not html_content and self.use_tls_client and self.tls_session:
                logger.info(f"🔒 Using TLS bypass for Cloudflare resistance: {url}")
                try:
                    # Run TLS bypass in executor to avoid blocking
                    loop = asyncio.get_event_loop()
                    tls_result = await loop.run_in_executor(
                        None, self._fetch_with_tls_bypass, url
                    )

                    if tls_result:
                        html_content = tls_result['content']
                        status = tls_result['status_code']
                        status_text = "OK (TLS Bypass)"
                        logger.info(f"✅ TLS bypass successful for {url}")
                    else:
                        logger.warning(f"⚠️ TLS bypass failed for {url}")

                except Exception as e:
                    logger.error(f"❌ TLS bypass failed for {url}: {e}")

            # 🚀 JINA READER FALLBACK for blocked sites (403, 429, 503)
            if not html_content and status in [403, 429, 503]:
                logger.info(f"📖 Trying Jina Reader API fallback for blocked URL: {url}")
                try:
                    reader_url = f"https://r.jina.ai/{url}"
                    reader_headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'text/plain,text/html,application/xhtml+xml',
                    }

                    async with self.session.get(reader_url, headers=reader_headers, timeout=60) as response:
                        if response.status == 200:
                            reader_content = await response.text()
                            logger.info(f"📖 Jina Reader got {len(reader_content)} chars")

                            # Simple validation - just check if we got meaningful content
                            if len(reader_content.strip()) > 100:
                                html_content = reader_content
                                status = 200
                                logger.info(f"✅ Jina Reader API bypass successful for {url}")
                            else:
                                logger.warning(f"⚠️ Jina Reader returned insufficient content: {len(reader_content)} chars")
                        else:
                            logger.warning(f"⚠️ Jina Reader API failed with status {response.status}")

                except Exception as e:
                    logger.error(f"❌ Jina Reader API error: {e}")

            # If still no content, raise error
            if not html_content:
                raise Exception(f"Failed to fetch content from {url} (status: {status})")
            
            # Parse with BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Extract basic information
            title = soup.title.string.strip() if soup.title else ""
            
            # Extract meta description
            description = ""
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc:
                description = meta_desc.get('content', '').strip()
            
            # Clean HTML - remove unwanted elements
            for element in soup(['script', 'style', 'nav', 'header', 'footer', 'aside',
                               'iframe', 'noscript', 'form', 'button']):
                element.decompose()
            
            # Remove ads and social media elements
            for element in soup.find_all(class_=lambda x: x and any(
                keyword in x.lower() for keyword in ['ad', 'advertisement', 'social', 'share', 'comment', 'sidebar']
            )):
                element.decompose()
            
            # Extract main content
            main_content = None
            for selector in ['main', 'article', '.content', '.main-content', '.post-content', '.entry-content']:
                if selector.startswith('.'):
                    main_element = soup.select_one(selector)
                else:
                    main_element = soup.find(selector)
                
                if main_element and len(main_element.get_text(strip=True)) > 200:
                    main_content = main_element
                    break
            
            if not main_content:
                main_content = soup.find('body') or soup
            
            # Get cleaned HTML and text
            cleaned_html = str(main_content)
            text_content = main_content.get_text(separator=' ', strip=True)

            # 🚀 NEW: Generate structured markdown from BeautifulSoup
            structured_markdown = self._html_to_markdown(main_content, title)
            
            # Extract images
            imgs = []
            for img in soup.find_all('img'):
                src = img.get('src', '')
                if src:
                    # Convert relative URLs to absolute
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = urljoin(url, src)
                    elif not src.startswith(('http://', 'https://')):
                        src = urljoin(url, src)
                    
                    imgs.append({
                        'src': src,
                        'alt': img.get('alt', ''),
                        'width': img.get('width', 0),
                        'height': img.get('height', 0)
                    })
            
            # Calculate basic analytics
            elem_count = len(soup.find_all())
            max_elem_depth = self._calculate_max_depth(soup)
            
            # Create parsed content for compatibility
            parsed = {
                'title': title,
                'content': cleaned_html,
                'textContent': text_content,
                'markdown': structured_markdown,  # 🚀 NEW: Add structured markdown
                'length': len(text_content),
                'excerpt': text_content[:200] if text_content else '',
                'byline': '',
                'dir': soup.get('dir', 'ltr') if hasattr(soup, 'get') else 'ltr',
                'siteName': '',
                'lang': soup.get('lang', 'en') if hasattr(soup, 'get') else 'en',
                'publishedTime': ''
            }
            
            # Create snapshot
            snapshot = PageSnapshot(
                title=title,
                description=description,
                href=url,
                html=cleaned_html,
                text=text_content,
                markdown=structured_markdown,  # 🚀 NEW: Add structured markdown
                status=status,
                status_text=status_text,
                parsed=parsed,
                imgs=imgs,
                max_elem_depth=max_elem_depth,
                elem_count=elem_count,
                is_intermediate=False
            )
            
            yield snapshot
            
        except Exception as e:
            logger.error(f"❌ Error scraping {url}: {e}")
            # Yield error snapshot
            error_snapshot = PageSnapshot(
                href=url,
                title="Error",
                text=f"Error scraping {url}: {str(e)}",
                status=500,
                is_intermediate=False
            )
            yield error_snapshot
    
    def _html_to_markdown(self, soup_element, title=""):
        """
        🚀 ENHANCED: Convert BeautifulSoup element to structured markdown
        This reduces Gemini's workload and improves output quality
        """
        if not soup_element:
            return ""

        markdown_lines = []

        # Add title if provided
        if title:
            markdown_lines.append(f"# {title}\n")

        # Process headers
        for header in soup_element.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
            text = header.get_text(strip=True)
            if text:
                level = int(header.name[1])
                markdown_lines.append(f"{'#' * level} {text}\n")

        # Process paragraphs
        for para in soup_element.find_all('p'):
            text = para.get_text(strip=True)
            if text and len(text) > 10:  # Skip very short paragraphs
                markdown_lines.append(f"{text}\n")

        # Process lists
        for ul in soup_element.find_all('ul'):
            for li in ul.find_all('li', recursive=False):
                text = li.get_text(strip=True)
                if text:
                    markdown_lines.append(f"- {text}")
            markdown_lines.append("")  # Empty line after list

        for ol in soup_element.find_all('ol'):
            for i, li in enumerate(ol.find_all('li', recursive=False), 1):
                text = li.get_text(strip=True)
                if text:
                    markdown_lines.append(f"{i}. {text}")
            markdown_lines.append("")  # Empty line after list

        # Process blockquotes
        for quote in soup_element.find_all('blockquote'):
            text = quote.get_text(strip=True)
            if text:
                markdown_lines.append(f"> {text}\n")

        # If no structured content found, use plain text with paragraphs
        if not markdown_lines or (len(markdown_lines) == 1 and title):
            text_content = soup_element.get_text(separator='\n\n', strip=True)
            paragraphs = [p.strip() for p in text_content.split('\n\n') if p.strip() and len(p.strip()) > 20]
            markdown_lines.extend([f"{para}\n" for para in paragraphs])

        return '\n'.join(markdown_lines).strip()

    def _calculate_max_depth(self, soup) -> int:
        """Calculate maximum element depth in the DOM"""
        try:
            max_depth = 0
            for element in soup.find_all():
                depth = len(list(element.parents))
                max_depth = max(max_depth, depth)
            return max_depth
        except:
            return 0
    
    async def crawl_batch_raw(self, urls: List[str], max_content_length: int = 100000) -> List[Dict[str, str]]:
        """
        🚀 BATCH CRAWLING - Crawl multiple URLs and return raw content for batch processing

        Args:
            urls: List of URLs to crawl
            max_content_length: Maximum content length per URL

        Returns:
            List of dicts with 'url' and 'raw_content' keys
        """
        if not self._initialized:
            await self.initialize()

        logger.info(f"🕷️ Starting batch crawl of {len(urls)} URLs")

        batch_data = []

        for i, url in enumerate(urls, 1):
            try:
                logger.debug(f"🕷️ Crawling {i}/{len(urls)}: {url}")

                # Get raw content without Gemini processing
                raw_content = await self._fetch_content_only(url, max_content_length)

                if raw_content:
                    batch_data.append({
                        'url': url,
                        'raw_content': raw_content
                    })
                    logger.debug(f"✅ Raw content extracted: {len(raw_content)} chars")
                else:
                    logger.warning(f"⚠️ No content from {url}")
                    batch_data.append({
                        'url': url,
                        'raw_content': ''
                    })

            except Exception as e:
                logger.error(f"❌ Error crawling {url}: {e}")
                batch_data.append({
                    'url': url,
                    'raw_content': ''
                })

        logger.info(f"✅ Batch crawl completed: {len(batch_data)} URLs processed")
        return batch_data

    async def process_batch_with_gemini(self, batch_data: List[Dict[str, str]], query: str = "") -> List[JiniCrawlResult]:
        """
        🤖 BATCH GEMINI PROCESSING - Process multiple raw contents with single Gemini call

        Args:
            batch_data: List of dicts with 'url' and 'raw_content' keys
            query: Search query for context

        Returns:
            List of JiniCrawlResult objects
        """
        if not batch_data:
            return []

        try:
            logger.info(f"🤖 Starting batch Gemini processing for {len(batch_data)} URLs")

            # Single Gemini batch call - HUGE efficiency gain!
            processed_results = await self.gemini_processor.process_batch(batch_data, query)

            # Convert to JiniCrawlResult objects
            results = []
            for i, processed in enumerate(processed_results):
                original_url = batch_data[i]['url'] if i < len(batch_data) else 'unknown'

                result = JiniCrawlResult(
                    url=original_url,
                    success=True,
                    processed_content=processed.get('processed_content', ''),
                    title=processed.get('title', 'No title'),
                    error=None,
                    metadata={
                        'key_points': processed.get('key_points', []),
                        'relevance_score': processed.get('relevance_score', 0.5),
                        'processing_method': 'batch_gemini'
                    }
                )
                results.append(result)

            logger.info(f"✅ Batch Gemini processing completed: {len(results)} results")
            return results

        except Exception as e:
            logger.error(f"❌ Batch Gemini processing failed: {e}")

            # Fallback: create basic results
            results = []
            for item in batch_data:
                result = JiniCrawlResult(
                    url=item['url'],
                    success=False,
                    processed_content=item.get('raw_content', '')[:2000],
                    title='Processing failed',
                    error=str(e),
                    metadata={'processing_method': 'fallback'}
                )
                results.append(result)

            return results

    async def _fetch_content_only(self, url: str, max_content_length: int = 100000) -> str:
        """
        🕷️ FETCH RAW CONTENT - Crawl URL and return raw content without Gemini processing

        Args:
            url: URL to crawl
            max_content_length: Maximum content length

        Returns:
            Raw content string
        """
        try:
            logger.debug(f"🕷️ Fetching raw content from {url}")

            # Use existing crawling logic
            crawl_result = None
            async for snapshot in self.scrap_url(url):
                if not snapshot.is_intermediate:
                    crawl_result = snapshot
                    break

            if crawl_result:
                # 🚀 ENHANCED: Prioritize structured markdown over raw HTML
                content = ""

                # Priority 1: Use structured markdown (best for Gemini)
                if hasattr(crawl_result, 'markdown') and crawl_result.markdown:
                    content = crawl_result.markdown
                    logger.debug(f"✅ Using structured markdown: {len(content)} chars")
                # Priority 2: Use plain text (good for Gemini)
                elif hasattr(crawl_result, 'text') and crawl_result.text:
                    content = crawl_result.text
                    logger.debug(f"✅ Using plain text: {len(content)} chars")
                # Priority 3: Use HTML (requires more Gemini processing)
                elif hasattr(crawl_result, 'content') and crawl_result.content:
                    content = crawl_result.content
                    logger.debug(f"⚠️ Using raw HTML: {len(content)} chars")

                if content:
                    # Truncate content to fit within limits
                    truncated_content = content[:max_content_length]
                    logger.debug(f"✅ Raw content fetched: {len(truncated_content)} chars")
                    return truncated_content
                else:
                    logger.warning(f"⚠️ No content extracted from {url}")
                    return ""
            else:
                logger.warning(f"⚠️ No crawl result from {url}")
                return ""

        except Exception as e:
            logger.error(f"❌ Error fetching content from {url}: {e}")
            return ""

    async def crawl_and_process(self, url: str, max_content_length: int = 10000) -> JiniCrawlResult:
        """
        Crawl a URL and process it through the complete pipeline
        
        Args:
            url: URL to crawl
            max_content_length: Maximum content length to send to Gemini (to avoid API limits)
            
        Returns:
            JiniCrawlResult with processing results
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            # Step 1: Crawl with simple crawler
            logger.info(f"🕷️ Crawling {url} with simple crawler...")
            crawl_result = None
            async for snapshot in self.scrap_url(url):
                if not snapshot.is_intermediate:
                    crawl_result = snapshot
                    break
            
            if not crawl_result:
                return JiniCrawlResult(
                    success=False,
                    url=url,
                    error="Failed to crawl URL"
                )
            
            # Step 2: Process with Gemini
            logger.info(f"🤖 Processing content with Gemini...")
            
            # Limit content length to avoid API limits
            content_to_process = crawl_result.html[:max_content_length]
            
            gemini_result = await self.gemini_processor.process_content(
                content_to_process, "html_to_markdown"
            )
            
            if not gemini_result.get("success"):
                return JiniCrawlResult(
                    success=False,
                    url=url,
                    title=crawl_result.title,
                    original_content=crawl_result.html[:500],  # First 500 chars
                    error=f"Gemini processing failed: {gemini_result.get('error', 'Unknown error')}"
                )
            
            return JiniCrawlResult(
                success=True,
                url=url,
                title=crawl_result.title,
                original_content=crawl_result.html,
                cleaned_content=crawl_result.html,  # Already cleaned by simple crawler
                processed_content=gemini_result.get("processed_content", ""),
                processing_time=gemini_result.get("processing_time", 0.0),
                metadata={
                    "original_length": gemini_result.get("original_length", 0),
                    "output_length": gemini_result.get("output_length", 0),
                    "model": gemini_result.get("model", "unknown"),
                    "task_type": gemini_result.get("task_type", "html_to_markdown")
                }
            )
            
        except Exception as e:
            logger.error(f"❌ Error processing {url}: {e}")
            return JiniCrawlResult(
                success=False,
                url=url,
                error=str(e)
            )
    
    async def crawl_full_article(self, url: str, max_content_length: int = 50000) -> JiniCrawlResult:
        """
        Crawl a URL and extract the COMPLETE article content (not summarized)
        
        Args:
            url: URL to crawl
            max_content_length: Maximum content length to send to Gemini (higher limit for full articles)
            
        Returns:
            JiniCrawlResult with full article content
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            # Step 1: Crawl with simple crawler
            logger.info(f"🕷️ Crawling full article from {url}...")
            crawl_result = None
            async for snapshot in self.scrap_url(url):
                if not snapshot.is_intermediate:
                    crawl_result = snapshot
                    break
            
            if not crawl_result:
                return JiniCrawlResult(
                    success=False,
                    url=url,
                    error="Failed to crawl URL"
                )
            
            # Step 2: Process with Gemini using full_article mode
            logger.info(f"🤖 Extracting full article content with Gemini...")
            
            # Use higher content length limit for full articles
            content_to_process = crawl_result.html[:max_content_length]
            
            gemini_result = await self.gemini_processor.process_content(
                content_to_process, "full_article"
            )
            
            if not gemini_result.get("success"):
                return JiniCrawlResult(
                    success=False,
                    url=url,
                    title=crawl_result.title,
                    original_content=crawl_result.html[:500],  # First 500 chars
                    error=f"Gemini full article processing failed: {gemini_result.get('error', 'Unknown error')}"
                )
            
            return JiniCrawlResult(
                success=True,
                url=url,
                title=crawl_result.title,
                original_content=crawl_result.html,
                cleaned_content=crawl_result.html,  # Already cleaned by simple crawler
                processed_content=gemini_result.get("processed_content", ""),
                processing_time=gemini_result.get("processing_time", 0.0),
                metadata={
                    "original_length": gemini_result.get("original_length", 0),
                    "output_length": gemini_result.get("output_length", 0),
                    "model": gemini_result.get("model", "unknown"),
                    "task_type": "full_article",
                    "content_type": "complete_article"
                }
            )
            
        except Exception as e:
            logger.error(f"❌ Error processing full article from {url}: {e}")
            return JiniCrawlResult(
                success=False,
                url=url,
                error=str(e)
            )
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the crawler"""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Simple health check - test basic functionality
            test_result = {
                "status": "healthy",
                "crawler_initialized": self._initialized,
                "session_active": self.session is not None
            }
            
            # Test Gemini processor
            gemini_health = await self.gemini_processor.health_check()
            test_result["gemini_processor"] = gemini_health
            
            # Overall status
            if gemini_health.get("status") != "healthy":
                test_result["status"] = "degraded"
            
            return test_result
            
        except Exception as e:
            logger.error(f"❌ Health check failed: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

# Convenience functions
async def create_jini_crawler() -> JiniCrawler:
    """Create and initialize a Jini crawler"""
    crawler = JiniCrawler()
    await crawler.initialize()
    return crawler

async def quick_crawl(url: str, max_content_length: int = 10000) -> JiniCrawlResult:
    """Quick crawl and process a URL"""
    crawler = JiniCrawler()
    try:
        await crawler.initialize()
        return await crawler.crawl_and_process(url, max_content_length)
    finally:
        await crawler.cleanup()

# Test function
async def test_jini_crawler():
    """Test Jini crawler"""
    print("🧪 Testing Jini Crawler")
    print("=" * 50)
    
    crawler = JiniCrawler()
    await crawler.initialize()
    
    # Test with a simple URL
    test_url = "https://httpbin.org/html"
    
    result = await crawler.crawl_and_process(test_url)
    
    print(f"✅ Success: {result.success}")
    if result.success:
        print(f"   Title: {result.title}")
        print(f"   Processed content length: {len(result.processed_content or '')} characters")
        print(f"   Processing time: {result.processing_time:.2f}s")
        if result.processed_content:
            preview = result.processed_content[:200] + "..." if len(result.processed_content) > 200 else result.processed_content
            print(f"   Content preview: {preview}")
    else:
        print(f"   Error: {result.error}")
    
    # Health check
    health = await crawler.health_check()
    print(f"\n🏥 Health check: {health['status']}")
    
    await crawler.cleanup()
    print("\n✅ Test completed")

if __name__ == "__main__":
    asyncio.run(test_jini_crawler())