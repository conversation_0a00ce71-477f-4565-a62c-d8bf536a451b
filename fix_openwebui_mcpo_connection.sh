#!/bin/bash
# Script to fix OpenWebUI-MCPO connection issue
# Based on official MCPO documentation analysis

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 OpenWebUI-MCPO Connection Fix Script${NC}"
echo -e "${BLUE}======================================${NC}"

# Step 1: Identify the problem
echo -e "${YELLOW}📋 Step 1: Analyzing current setup...${NC}"

# Check current OpenWebUI container
CURRENT_CONTAINER=$(docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}" | grep open-webui || echo "No OpenWebUI container found")
echo -e "Current OpenWebUI: $CURRENT_CONTAINER"

# Check MCPO servers
MCPO_SERVERS=$(ps aux | grep mcpo | grep -v grep | wc -l)
echo -e "MCPO servers running: $MCPO_SERVERS"

# Check MCPO connectivity
if curl -s http://localhost:8000/openapi.json > /dev/null; then
    echo -e "${GREEN}✅ MCPO server is accessible${NC}"
    MCPO_ACCESSIBLE=true
else
    echo -e "${RED}❌ MCPO server is not accessible${NC}"
    MCPO_ACCESSIBLE=false
fi

echo ""
echo -e "${YELLOW}📋 Step 2: Problem Analysis${NC}"
echo -e "${RED}🚨 IDENTIFIED ISSUE: OpenWebUI is NOT configured for MCP integration${NC}"
echo -e "   - Current OpenWebUI lacks MCP environment variables"
echo -e "   - No MCP servers configuration found in container"
echo -e "   - Missing MCPO connection settings"

echo ""
echo -e "${YELLOW}📋 Step 3: Solution Options${NC}"
echo -e "${BLUE}Option 1: Update current container with MCP support (Recommended)${NC}"
echo -e "${BLUE}Option 2: Deploy new OpenWebUI with MCP integration${NC}"
echo -e "${BLUE}Option 3: Use MCPO as external tool server${NC}"

echo ""
read -p "Choose option (1/2/3): " OPTION

case $OPTION in
    1)
        echo -e "${GREEN}🔄 Updating current OpenWebUI container...${NC}"
        
        # Stop current container
        echo -e "${YELLOW}Stopping current OpenWebUI...${NC}"
        docker stop open-webui || true
        
        # Backup current data
        echo -e "${YELLOW}Creating backup...${NC}"
        docker run --rm -v acca_open_webui_data:/data -v $(pwd):/backup alpine tar czf /backup/openwebui_backup_$(date +%Y%m%d_%H%M%S).tar.gz -C /data .
        
        # Update with MCP support
        echo -e "${YELLOW}Starting OpenWebUI with MCP support...${NC}"
        docker run -d \
            --name open-webui \
            --restart unless-stopped \
            -p 3000:8080 \
            -v acca_open_webui_data:/app/backend/data \
            -v $(pwd)/mem0-owui/config/mcp_servers.json:/app/backend/data/mcp_servers.json:ro \
            -e ENABLE_MCP_SERVERS=true \
            -e MCP_SERVERS_CONFIG_PATH=/app/backend/data/mcp_servers.json \
            -e MCPO_BASE_URL=http://host.docker.internal:8000 \
            -e MCPO_API_KEY=acca-enhanced-rag-mcp-key-2025 \
            -e WEBUI_NAME="AccA OpenWebUI with MCP Support" \
            --add-host host.docker.internal:host-gateway \
            --network acca-network \
            ghcr.io/open-webui/open-webui:latest
        
        echo -e "${GREEN}✅ OpenWebUI updated with MCP support${NC}"
        ;;
        
    2)
        echo -e "${GREEN}🚀 Deploying new OpenWebUI with MCP integration...${NC}"
        
        # Stop current container
        docker stop open-webui || true
        docker rm open-webui || true
        
        # Deploy using new docker-compose
        echo -e "${YELLOW}Deploying with docker-compose...${NC}"
        docker-compose -f docker-compose-openwebui-mcpo.yml up -d
        
        echo -e "${GREEN}✅ New OpenWebUI with MCP deployed${NC}"
        ;;
        
    3)
        echo -e "${GREEN}🔗 Configuring MCPO as external tool server...${NC}"
        
        # Create tool server configuration
        mkdir -p ./webui-data/tools
        cat > ./webui-data/tools/mcpo_tools.json << EOF
{
  "tools": [
    {
      "name": "MCPO Tools",
      "url": "http://host.docker.internal:8000",
      "api_key": "acca-enhanced-rag-mcp-key-2025",
      "description": "MCP servers via MCPO proxy"
    }
  ]
}
EOF
        
        # Restart OpenWebUI with tool server support
        docker restart open-webui
        
        echo -e "${GREEN}✅ MCPO configured as external tool server${NC}"
        ;;
        
    *)
        echo -e "${RED}❌ Invalid option${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${YELLOW}📋 Step 4: Verification${NC}"

# Wait for container to start
sleep 10

# Check if OpenWebUI is running
if docker ps | grep -q open-webui; then
    echo -e "${GREEN}✅ OpenWebUI container is running${NC}"
    
    # Check if accessible
    if curl -s http://localhost:3000 > /dev/null; then
        echo -e "${GREEN}✅ OpenWebUI is accessible at http://localhost:3000${NC}"
    else
        echo -e "${RED}❌ OpenWebUI is not accessible${NC}"
    fi
    
    # Check MCP integration
    if [ "$OPTION" = "1" ] || [ "$OPTION" = "2" ]; then
        echo -e "${YELLOW}Checking MCP integration...${NC}"
        docker exec open-webui env | grep -E "(MCP|MCPO)" || echo "MCP environment variables not found"
    fi
    
else
    echo -e "${RED}❌ OpenWebUI container is not running${NC}"
fi

echo ""
echo -e "${BLUE}📋 Step 5: Next Steps${NC}"
echo -e "1. Access OpenWebUI at: ${GREEN}http://localhost:3000${NC}"
echo -e "2. Go to Admin Panel → Settings → Tools"
echo -e "3. Configure MCP servers or MCPO integration"
echo -e "4. Test MCP server functionality"

echo ""
echo -e "${GREEN}🎉 OpenWebUI-MCPO connection fix completed!${NC}"

# Show final status
echo ""
echo -e "${BLUE}📊 Final Status:${NC}"
echo -e "OpenWebUI: $(docker ps --format '{{.Status}}' --filter name=open-webui)"
echo -e "MCPO Servers: $MCPO_SERVERS running"
echo -e "MCPO API: $(curl -s http://localhost:8000/openapi.json | jq -r '.info.title' 2>/dev/null || echo 'Not accessible')"

echo ""
echo -e "${YELLOW}💡 Troubleshooting:${NC}"
echo -e "- Check logs: ${BLUE}docker logs open-webui${NC}"
echo -e "- Test MCPO: ${BLUE}curl http://localhost:8000/docs${NC}"
echo -e "- Verify MCP config: ${BLUE}cat mem0-owui/config/mcp_servers.json${NC}"