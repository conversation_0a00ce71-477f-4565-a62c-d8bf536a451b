#!/bin/bash
# Batch upload script for migrated documents

echo "🚀 BATCH DOCUMENT UPLOAD HELPER"
echo "================================"

DOCS_DIR="migrated_docs"
WEBUI_URL="http://localhost:3001"

if [ ! -d "$DOCS_DIR" ]; then
    echo "❌ migrated_docs/ directory not found"
    exit 1
fi

echo "📁 Documents to upload:"
ls -la "$DOCS_DIR"/*.txt | wc -l | xargs echo "   Total files:"

echo ""
echo "📋 UPLOAD INSTRUCTIONS:"
echo "1. Open browser: $WEBUI_URL"
echo "2. Go to Knowledge section"
echo "3. Create collection: MDS-MBF"
echo "4. Upload files from: $DOCS_DIR/"
echo ""
echo "💡 Files are ready for upload!"
echo "   All documents are in proper format with metadata"
echo "   Optimized for enhanced RAG system"
