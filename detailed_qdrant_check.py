#!/usr/bin/env python3
"""
Kiểm tra chi tiết Qdrant collections với query cụ thể
"""

from qdrant_client import QdrantClient
from qdrant_client.models import Filter, FieldCondition, MatchValue
import json

def detailed_qdrant_check():
    print("🔍 Detailed Qdrant check...")
    
    try:
        client = QdrantClient(host="localhost", port=6333)
        collections = client.get_collections()
        
        print(f"📊 Found {len(collections.collections)} collections")
        
        for col in collections.collections:
            try:
                # Get collection info with more details
                info = client.get_collection(col.name)
                
                # Try different ways to get count
                count_methods = []
                
                # Method 1: vectors_count
                count1 = info.vectors_count
                count_methods.append(f"vectors_count: {count1}")
                
                # Method 2: count via scroll
                try:
                    scroll_result = client.scroll(
                        collection_name=col.name,
                        limit=1,
                        with_payload=False,
                        with_vectors=False
                    )
                    count2 = len(scroll_result[0]) if scroll_result[0] else 0
                    count_methods.append(f"scroll_count: {count2}")
                except Exception as e:
                    count_methods.append(f"scroll_error: {e}")
                
                # Method 3: count via search
                try:
                    search_result = client.search(
                        collection_name=col.name,
                        query_vector=[0.0] * info.config.params.vectors.size,
                        limit=1000,
                        with_payload=True
                    )
                    count3 = len(search_result)
                    count_methods.append(f"search_count: {count3}")
                except Exception as e:
                    count_methods.append(f"search_error: {e}")
                
                print(f"\n📊 {col.name}:")
                print(f"  📏 Dimensions: {info.config.params.vectors.size}")
                for method in count_methods:
                    print(f"  📈 {method}")
                
                # If any method shows > 0, get samples
                if count1 and count1 > 0:
                    try:
                        points = client.scroll(
                            collection_name=col.name,
                            limit=3,
                            with_payload=True
                        )[0]
                        
                        print(f"  📝 Sample data:")
                        for i, point in enumerate(points, 1):
                            payload = point.payload
                            text_keys = ['text', 'content', 'message', 'data']
                            text = None
                            for key in text_keys:
                                if key in payload:
                                    text = str(payload[key])[:60] + "..."
                                    break
                            if not text:
                                text = str(payload)[:60] + "..."
                            print(f"    {i}. {text}")
                    except Exception as e:
                        print(f"  ❌ Sample error: {e}")
                        
            except Exception as e:
                print(f"❌ Error checking {col.name}: {e}")
        
    except Exception as e:
        print(f"❌ Connection error: {e}")

if __name__ == "__main__":
    detailed_qdrant_check()