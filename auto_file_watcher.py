#!/usr/bin/env python3
"""
👁️ Auto File Watcher + OCR Pipeline
Tự động chạy pipeline khi có file mới
"""

import time
import subprocess
import sys
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    handlers=[
        logging.FileHandler('auto_pipeline.log'),
        logging.StreamHandler()
    ]
)

class FileWatcher(FileSystemEventHandler):
    def __init__(self):
        self.processed_files = set()
        self.processing = False
        
    def on_created(self, event):
        """Triggered when new file is created"""
        if event.is_directory:
            return
            
        file_path = Path(event.src_path)
        
        # Only process PDF, DOCX, TXT files
        if file_path.suffix.lower() in ['.pdf', '.docx', '.txt', '.doc']:
            logging.info(f"🔍 New file detected: {file_path.name}")
            self.process_new_file(file_path)
    
    def on_moved(self, event):
        """Triggered when file is moved/copied"""
        if event.is_directory:
            return
            
        file_path = Path(event.dest_path)
        
        if file_path.suffix.lower() in ['.pdf', '.docx', '.txt', '.doc']:
            logging.info(f"📁 File moved/copied: {file_path.name}")
            self.process_new_file(file_path)
    
    def process_new_file(self, file_path):
        """Process new file through the pipeline"""
        
        if self.processing:
            logging.info("⏳ Pipeline already running, queuing...")
            return
            
        if str(file_path) in self.processed_files:
            logging.info("✅ File already processed, skipping...")
            return
        
        self.processing = True
        
        try:
            logging.info(f"🚀 Starting auto pipeline for: {file_path.name}")
            
            # Wait a bit for file to be fully written
            time.sleep(2)
            
            # Run the complete pipeline
            result = subprocess.run([
                sys.executable, "complete_table_pipeline.py"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logging.info("✅ Pipeline completed successfully!")
                logging.info("📦 Files ready at: /tmp/webui_uploads_auto_corrected/")
                
                # Send notification
                self.send_notification("✅ Auto Pipeline Complete!", 
                                     f"File {file_path.name} processed successfully")
                
            else:
                logging.error(f"❌ Pipeline failed: {result.stderr}")
                self.send_notification("❌ Pipeline Failed!", 
                                     f"Error processing {file_path.name}")
            
            self.processed_files.add(str(file_path))
            
        except Exception as e:
            logging.error(f"❌ Error in pipeline: {e}")
            
        finally:
            self.processing = False
    
    def send_notification(self, title, message):
        """Send desktop notification"""
        try:
            subprocess.run([
                "notify-send", title, message
            ], check=False)
        except:
            pass  # Ignore if notify-send not available

def main():
    """Main function"""
    
    print("👁️ Auto File Watcher + OCR Pipeline")
    print("=" * 50)
    
    # Check dependencies
    try:
        from watchdog.observers import Observer
        from watchdog.events import FileSystemEventHandler
    except ImportError:
        print("❌ Installing watchdog...")
        subprocess.run([sys.executable, "-m", "pip", "install", "watchdog"])
        from watchdog.observers import Observer
        from watchdog.events import FileSystemEventHandler
    
    # Watch directory
    watch_dir = Path("knowledge_files")
    watch_dir.mkdir(exist_ok=True)
    
    print(f"👁️ Watching directory: {watch_dir.absolute()}")
    print("📋 Supported files: PDF, DOCX, TXT, DOC")
    print("🚀 Auto pipeline will run when new files are added")
    print("📋 Logs: auto_pipeline.log")
    print("\n⏳ Waiting for files... (Ctrl+C to stop)")
    
    # Setup file watcher
    event_handler = FileWatcher()
    observer = Observer()
    observer.schedule(event_handler, str(watch_dir), recursive=True)
    
    try:
        observer.start()
        
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Stopping file watcher...")
        observer.stop()
        
    observer.join()
    print("✅ File watcher stopped")

if __name__ == "__main__":
    main() 