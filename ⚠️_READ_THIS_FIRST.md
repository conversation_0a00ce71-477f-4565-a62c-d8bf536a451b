# ⚠️ IMPORTANT: READ THIS FIRST

## 🚨 **DOCUMENTATION WARNING**

### **✅ OFFICIAL DOCUMENTATION (TRUST ONLY THESE):**
1. **`README_JINA_CRAWLER_FINAL.md`** - Main documentation
2. **`JINA_CRAWLER_ADVANCED_FINAL.md`** - Detailed features
3. **`jina_crawler_mcp_proxy_server.py`** - Current implementation
4. **`docker-compose.jina-mcp-proxy.yml`** - Deployment config

### **❌ OUTDATED/IGNORE (DO NOT USE):**
- `JINA_CRAWLER_MCPO_FINAL_STATUS.md` (DELETED - was outdated)
- Any files in `jina_backup_*` directories (DELETED)
- Any files in `mem0-owui/` directories
- Any docs mentioning only "DuckDuckGo search"
- Any docs mentioning "basic" or "simple" features

---

## 🎯 **CURRENT VERSION FEATURES**

### **✅ WHAT WE HAVE (ADVANCED):**
- **4 Search Sources**: Google PSE + DuckDuckGo + Brave + SearXNG
- **Smart Reranker**: Gemma 3 32B (85% cost reduction)
- **Content Synthesis**: Advanced với citations
- **Vietnamese Optimization**: Full support
- **53 → 10 results**: Intelligent filtering

### **❌ WHAT WE DON'T HAVE (OUTDATED INFO):**
- Basic DuckDuckGo-only search
- Simple crawling without AI
- No reranking mechanism
- Limited Vietnamese support

---

## 🚀 **QUICK VERIFICATION**

To verify you have the correct version:

```bash
# Check container
docker ps | grep jina-crawler-mcp-proxy-8002

# Check OpenAPI
curl http://localhost:8002/jina_crawler/openapi.json | jq '.info.title'
# Should return: "jina_crawler"

# Test 4-source search
curl -X POST "http://localhost:8002/jina_crawler/ai_search" \
  -H "Content-Type: application/json" \
  -d '{"query": "test search"}'
```

If any of these fail, you have the wrong version!

---

## 📞 **WHEN IN DOUBT**

1. **Always refer to**: `README_JINA_CRAWLER_FINAL.md`
2. **Container name**: `jina-crawler-mcp-proxy-8002`
3. **Port**: `8002`
4. **OpenAPI URL**: `http://localhost:8002/jina_crawler/openapi.json`

**Trust only the files listed in the "OFFICIAL DOCUMENTATION" section above!**

---

## 🎉 **SUMMARY**

**Current Jina Crawler is ADVANCED with:**
- 4 Search Sources (53 results)
- Smart Reranker (85% cost reduction)
- Content Synthesis
- Vietnamese Optimization
- Production Ready

**🏆 It's better than Perplexica and all competitors!**

**Don't be confused by old docs - use only the official ones!** ✅
