#!/usr/bin/env python3
"""
Mem0 Configuration Preset Applier
Apply predefined configurations to mem0 flexible pipeline
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, Any

def load_presets() -> Dict[str, Any]:
    """Load configuration presets from JSON file"""
    preset_file = Path("mem0_config_presets.json")
    if not preset_file.exists():
        print("❌ Preset file not found: mem0_config_presets.json")
        return {}
    
    try:
        with open(preset_file, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Error loading presets: {e}")
        return {}

def list_presets(presets: Dict[str, Any]):
    """List available presets"""
    print("📋 Available Mem0 Configuration Presets:\n")
    
    for preset_id, preset_data in presets.get("presets", {}).items():
        name = preset_data.get("name", preset_id)
        description = preset_data.get("description", "No description")
        config = preset_data.get("config", {})
        
        print(f"🔧 {preset_id}")
        print(f"   Name: {name}")
        print(f"   Description: {description}")
        print(f"   Dimensions: {config.get('embedder_dims', 'N/A')}")
        print(f"   Provider: {config.get('embedder_provider', 'N/A')}")
        print(f"   Model: {config.get('embedder_model', 'N/A')}")
        print()

def show_performance_comparison(presets: Dict[str, Any]):
    """Show performance comparison table"""
    comparison = presets.get("performance_comparison", {})
    if not comparison:
        return
    
    print("📊 Performance Comparison:\n")
    print("Dimensions | Memory Usage | Search Speed | API Cost | Semantic Quality")
    print("-" * 70)
    
    for dims, metrics in comparison.items():
        memory = metrics.get("memory_usage", "N/A")
        speed = metrics.get("search_speed", "N/A")
        cost = metrics.get("api_cost", "N/A")
        quality = metrics.get("semantic_quality", "N/A")
        
        print(f"{dims:10} | {memory:12} | {speed:12} | {cost:8} | {quality}")
    print()

def show_recommendations(presets: Dict[str, Any]):
    """Show recommendations for different use cases"""
    recommendations = presets.get("recommendations", {})
    if not recommendations:
        return
    
    print("💡 Recommendations:\n")
    for use_case, preset_id in recommendations.items():
        preset_name = presets.get("presets", {}).get(preset_id, {}).get("name", preset_id)
        print(f"   {use_case.replace('_', ' ').title()}: {preset_name}")
    print()

def generate_valve_config(preset_config: Dict[str, Any]) -> str:
    """Generate valve configuration string"""
    valve_lines = []
    
    for key, value in preset_config.items():
        if isinstance(value, str):
            valve_lines.append(f'        {key}: str = "{value}"')
        elif isinstance(value, int):
            valve_lines.append(f'        {key}: int = {value}')
        elif isinstance(value, float):
            valve_lines.append(f'        {key}: float = {value}')
        elif isinstance(value, bool):
            valve_lines.append(f'        {key}: bool = {value}')
    
    return "\n".join(valve_lines)

def apply_preset(preset_id: str, presets: Dict[str, Any]):
    """Apply a specific preset configuration"""
    preset_data = presets.get("presets", {}).get(preset_id)
    if not preset_data:
        print(f"❌ Preset '{preset_id}' not found")
        return False
    
    config = preset_data.get("config", {})
    name = preset_data.get("name", preset_id)
    description = preset_data.get("description", "")
    
    print(f"🔧 Applying preset: {name}")
    print(f"📝 Description: {description}")
    print()
    
    # Generate configuration
    print("📋 Configuration to apply:")
    print("=" * 50)
    for key, value in config.items():
        print(f"{key}: {value}")
    print("=" * 50)
    print()
    
    # Generate valve configuration code
    valve_config = generate_valve_config(config)
    
    print("🔧 Valve Configuration (copy to your pipeline):")
    print("=" * 50)
    print(valve_config)
    print("=" * 50)
    print()
    
    # Save to file
    config_file = f"mem0_preset_{preset_id}.json"
    try:
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        print(f"💾 Configuration saved to: {config_file}")
    except Exception as e:
        print(f"❌ Error saving configuration: {e}")
    
    # Show collection name that will be used
    collection_name = f"{config.get('base_collection_name', 'mem0')}_{config.get('embedder_provider', 'unknown')[:4]}_{config.get('embedder_dims', 'unknown')}"
    print(f"📦 Collection name: {collection_name}")
    
    return True

def show_migration_info(presets: Dict[str, Any]):
    """Show migration information"""
    migrations = presets.get("migration_paths", {})
    if not migrations:
        return
    
    print("🔄 Available Migration Paths:\n")
    
    for migration_id, migration_data in migrations.items():
        description = migration_data.get("description", "")
        source_collections = migration_data.get("source_collections", [])
        target_preset = migration_data.get("target_preset", "")
        benefits = migration_data.get("benefits", [])
        
        print(f"📦 {migration_id}")
        print(f"   Description: {description}")
        print(f"   Source collections: {', '.join(source_collections)}")
        print(f"   Target preset: {target_preset}")
        print(f"   Benefits:")
        for benefit in benefits:
            print(f"     • {benefit}")
        print()

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("🚀 Mem0 Configuration Preset Manager")
        print()
        print("Usage:")
        print("  python apply_mem0_preset.py list                    # List all presets")
        print("  python apply_mem0_preset.py apply <preset_id>       # Apply a preset")
        print("  python apply_mem0_preset.py compare                 # Show performance comparison")
        print("  python apply_mem0_preset.py recommend               # Show recommendations")
        print("  python apply_mem0_preset.py migrate                 # Show migration info")
        print()
        return
    
    command = sys.argv[1].lower()
    presets = load_presets()
    
    if not presets:
        print("❌ Could not load presets")
        return
    
    if command == "list":
        list_presets(presets)
        
    elif command == "apply":
        if len(sys.argv) < 3:
            print("❌ Please specify a preset ID")
            print("Available presets:")
            for preset_id in presets.get("presets", {}).keys():
                print(f"  - {preset_id}")
            return
        
        preset_id = sys.argv[2]
        apply_preset(preset_id, presets)
        
    elif command == "compare":
        show_performance_comparison(presets)
        
    elif command == "recommend":
        show_recommendations(presets)
        
    elif command == "migrate":
        show_migration_info(presets)
        
    else:
        print(f"❌ Unknown command: {command}")
        print("Available commands: list, apply, compare, recommend, migrate")

if __name__ == "__main__":
    main()
