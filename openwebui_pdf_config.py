"""
Open WebUI Enhanced PDF Configuration
Improve PDF parsing for better RAG performance
"""

import os
import json

# Enhanced PDF parsing configuration for Open WebUI
PDF_CONFIG = {
    "parsers": {
        "default": "enhanced_pymupdf",
        "fallback": "pdfplumber",
        "table_parser": "pdfplumber"
    },
    
    "chunking": {
        "strategy": "semantic",
        "chunk_size": 512,
        "chunk_overlap": 100,
        "min_chunk_size": 50,
        "preserve_formatting": True,
        "preserve_tables": True
    },
    
    "preprocessing": {
        "remove_headers_footers": True,
        "remove_page_numbers": True,
        "normalize_whitespace": True,
        "extract_metadata": True
    },
    
    "quality_filters": {
        "min_confidence_score": 0.4,
        "filter_broken_text": True,
        "filter_short_chunks": True,
        "deduplicate_content": True
    }
}

# Enhanced document processing settings
DOCUMENT_SETTINGS = {
    "RAG_EMBEDDING_ENGINE": "sentence-transformers",
    "RAG_EMBEDDING_MODEL": "all-MiniLM-L6-v2",
    "RAG_RERANKING_MODEL": "ms-marco-MiniLM-L-6-v2",
    "RAG_TOP_K": 10,
    "RAG_RELEVANCE_THRESHOLD": 0.3,
    "CHUNK_SIZE": 512,
    "CHUNK_OVERLAP": 100,
    "HYBRID_SEARCH": True,
    "ENABLE_RAG_WEB_SEARCH": True,
    "ENABLE_CITATION": True
}

def generate_openwebui_env_config():
    """Generate environment variables for Open WebUI"""
    env_vars = []
    
    for key, value in DOCUMENT_SETTINGS.items():
        if isinstance(value, bool):
            value = str(value).lower()
        env_vars.append(f"{key}={value}")
    
    return env_vars

# Vietnamese-specific PDF processing
VIETNAMESE_PDF_CONFIG = {
    "language_models": {
        "primary": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
        "fallback": "sentence-transformers/all-MiniLM-L6-v2"
    },
    
    "text_processing": {
        "normalize_vietnamese": True,
        "handle_diacritics": True,
        "split_sentences_vietnamese": True
    },
    
    "chunking_rules": {
        "respect_vietnamese_punctuation": True,
        "preserve_vietnamese_titles": True,
        "handle_mixed_languages": True
    }
}

# Better table extraction configuration
TABLE_EXTRACTION_CONFIG = {
    "detection_methods": ["border_detection", "text_alignment", "spacing_analysis"],
    "extraction_libraries": ["pdfplumber", "camelot", "tabula"],
    "output_format": "markdown",
    "preserve_structure": True,
    "merge_cells": True,
    "confidence_threshold": 0.6
} 