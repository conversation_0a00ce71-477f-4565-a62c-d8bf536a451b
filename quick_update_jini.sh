#!/bin/bash

echo "🔄 Updating jina-crawler-mcp container..."

# Find the process running on localhost:8009
PID=$(lsof -ti:8009 | head -1)
if [ -z "$PID" ]; then
    echo "❌ No process found on port 8009"
    exit 1
fi

# Get the working directory of that process
WORKDIR=$(pwdx $PID 2>/dev/null | cut -d: -f2 | xargs)
if [ -z "$WORKDIR" ]; then
    echo "❌ Could not find working directory"
    exit 1
fi

echo "📁 Found server at: $WORKDIR"

# Backup current container code
echo "📦 Creating backup..."
docker cp jina-crawler-mcp:/app ./jina_backup_$(date +%s)

# Copy new code to container
echo "🔄 Copying new code..."
docker cp "$WORKDIR/." jina-crawler-mcp:/app/

# Restart container
echo "🔄 Restarting container..."
docker restart jina-crawler-mcp

# Wait for container
echo "⏳ Waiting for container..."
sleep 10

# Test
echo "🧪 Testing..."
docker exec jina-crawler-mcp curl -s http://localhost:8009/health || echo "❌ Health check failed"

echo "✅ Update completed!"
