
def jina_crawler_tool(url: str, method: str = "tls_bypass", max_content_length: int = 10000) -> str:
    """
    Smart web crawler with AI-powered content processing using Jina and Gemini.
    Uses containerized Jina Crawler HTTP server.

    Args:
        url: URL to crawl
        method: Crawling method (tls_bypass or standard)
        max_content_length: Maximum content length

    Returns:
        Processed content from the URL
    """
    import requests
    import json

    try:
        # Use container hostname for internal Docker network communication
        # If Open WebUI is also in container, use: jina-crawler-http-8001:8001
        # If Open WebUI is on host, use: localhost:8001
        base_url = "http://jina-crawler-http-8001:8001"

        payload = {
            "url": url,
            "method": method,
            "process_content": True,
            "max_content_length": max_content_length
        }

        response = requests.post(
            f"{base_url}/jina_crawler/crawl",
            json=payload,
            timeout=60
        )

        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                title = data.get('title', 'N/A')
                content = data.get('content', '')
                processing_time = data.get('processing_time', 0)
                return f"**{title}**\n\n{content}\n\n*Processing time: {processing_time:.2f}s*"
            else:
                error = data.get('error', 'Unknown error')
                content = data.get('content', '')
                return f"Error: {error}\n\nContent: {content}"
        else:
            return f"HTTP Error: {response.status_code} - {response.text}"

    except Exception as e:
        # Fallback to localhost if container hostname fails
        try:
            response = requests.post(
                "http://localhost:8001/jina_crawler/crawl",
                json=payload,
                timeout=60
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    title = data.get('title', 'N/A')
                    content = data.get('content', '')
                    processing_time = data.get('processing_time', 0)
                    return f"**{title}**\n\n{content}\n\n*Processing time: {processing_time:.2f}s*"
                else:
                    error = data.get('error', 'Unknown error')
                    content = data.get('content', '')
                    return f"Error: {error}\n\nContent: {content}"
            else:
                return f"HTTP Error: {response.status_code} - {response.text}"
        except Exception as fallback_error:
            return f"Error calling Jina Crawler container: {str(e)}\nFallback error: {str(fallback_error)}"
