# Open WebUI Pandas MCP Integration

## Overview

This document describes how to integrate the pandas MCP server with Open WebUI through the MCPO (MCP OpenAPI Proxy) system. The pandas MCP server provides powerful data analysis and visualization capabilities that can be accessed directly from Open WebUI.

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Open WebUI Backend                       │
│              (http://localhost:8011)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                    MCPO Server                              │
│              (http://localhost:3001)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                 Pandas MCP Server                           │
│         (Integrated via mcp-integration/servers/pandas/)    │
└─────────────────────────────────────────────────────────────┘
```

## Available Endpoints

The pandas MCP integration provides the following endpoints in Open WebUI:

### 1. File Analysis (`/api/v1/pandas-mcp/analyze_file`)
- **Method**: POST
- **Description**: Upload and analyze Excel or CSV files to extract metadata
- **Parameters**: 
  - `file`: The file to analyze (multipart/form-data)

### 2. Code Execution (`/api/v1/pandas-mcp/execute_code`)
- **Method**: POST
- **Description**: Execute pandas code with security checks
- **Parameters**:
  - `code`: Python code string containing pandas operations
  - `file` (optional): Data file for processing

### 3. Chart Generation (`/api/v1/pandas-mcp/generate_chart`)
- **Method**: POST
- **Description**: Generate interactive Chart.js visualizations
- **Parameters**:
  - `data`: Structured data with columns
  - `chart_types`: List of chart types (bar, line, pie)
  - `title`: Chart title

### 4. Service Status (`/api/v1/pandas-mcp/status`)
- **Method**: GET
- **Description**: Check if pandas MCP service is available

### 5. Available Tools (`/api/v1/pandas-mcp/tools`)
- **Method**: GET
- **Description**: List all available pandas MCP tools

## Usage Examples

### 1. Analyzing a CSV File

```bash
curl -X POST "http://localhost:8011/api/v1/pandas-mcp/analyze_file" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@sales_data.csv"
```

### 2. Executing Pandas Code

```bash
curl -X POST "http://localhost:8011/api/v1/pandas-mcp/execute_code" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "import pandas as pd\ndf = pd.read_csv(\"/tmp/data.csv\")\nresult = df.groupby(\"Region\")[\"Sales\"].sum()"
  }'
```

### 3. Generating Charts

```bash
curl -X POST "http://localhost:8011/api/v1/pandas-mcp/generate_chart" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "columns": [
        {
          "name": "Region",
          "type": "string",
          "examples": ["North", "South", "East", "West"]
        },
        {
          "name": "Sales",
          "type": "number",
          "examples": [15000, 12000, 18000, 9000]
        }
      ]
    },
    "chart_types": ["bar"],
    "title": "Sales by Region"
  }'
```

## Features

### 1. File Metadata Analysis (`read_metadata`)
- **Purpose**: Extract comprehensive metadata from Excel and CSV files
- **Capabilities**:
  - File type, size, encoding, and structure analysis
  - Column names, data types, and sample values
  - Statistical summaries (null counts, unique values, min/max/mean)
  - Data quality warnings and suggested operations
  - Memory-optimized processing for large files

### 2. Pandas Code Execution (`run_pandas_code`)
- **Purpose**: Execute pandas operations with security and optimization
- **Capabilities**:
  - Security filtering against malicious code
  - Memory optimization for large datasets
  - Comprehensive error handling and debugging
  - Support for DataFrame, Series, and dictionary results

### 3. Interactive Chart Generation (`generate_chartjs`)
- **Purpose**: Generate interactive Chart.js visualizations
- **Supported Chart Types**:
  - **Bar charts** - For categorical comparisons
  - **Line charts** - For trend analysis
  - **Pie charts** - For proportional data
- **Features**:
  - Interactive HTML templates
  - Customization controls
  - Responsive design

## Security Features

- **Code Execution Sandboxing**: Prevents malicious code execution
- **Blacklisted Operations**: Blocks file system, network, and eval operations
- **Memory Usage Monitoring**: Tracks and limits memory consumption
- **Input Validation**: Sanitizes and validates all inputs

## Performance Optimization

- **Chunked Processing**: Handles large files efficiently
- **Automatic Garbage Collection**: Manages memory usage
- **Memory Usage Logging**: Monitors resource consumption
- **Dataset Size Limits**: Prevents memory overflow

## Troubleshooting

### Common Issues

1. **Service Unavailable**
   - Ensure MCPO is running on port 3001
   - Check that the pandas MCP server is properly configured in `mcp-integration/config/mcpo_config.json`

2. **Import Errors**
   - Ensure pandas_mcp directory is properly set up
   - Check PYTHONPATH environment variable

3. **File Not Found**
   - Verify file paths are absolute
   - Check file permissions

4. **Memory Issues**
   - Monitor memory usage in logs
   - Consider data sampling for large datasets

5. **Chart Generation Errors**
   - Verify data structure format
   - Check for required columns (string + numeric)

### Debug Mode

Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
# Then restart MCPO server
```

## Testing

Run the integration test:
```bash
cd mcp-integration
python test_pandas_mcp_integration.py
```

## Configuration

The pandas MCP server is configured in `mcp-integration/config/mcpo_config.json`:

```json
{
  "mcpServers": {
    "pandas": {
      "command": "python3",
      "args": [
        "/home/<USER>/AccA/AccA/mcp-integration/servers/pandas/server.py"
      ],
      "env": {
        "PYTHONPATH": "/home/<USER>/AccA/AccA/mcp-integration/servers/pandas_mcp"
      }
    }
  }
}
```

## License

This integration is based on the [pandas-mcp-server](https://github.com/marlonluo2018/pandas-mcp-server) project and is licensed under the MIT License.