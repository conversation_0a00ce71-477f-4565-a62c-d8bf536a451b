# Oracle Autonomous Database Integration với AI Assistant

## Tổng quan

Dự án này tích hợp Oracle Autonomous Database từ OCI với hệ thống AI Assistant hi<PERSON><PERSON> t<PERSON><PERSON>, sử dụng Oracle 19C với hybrid approach để kết hợp Oracle Text search và semantic search.

## 🌟 Tính năng chính

### Oracle 19C Features
- ✅ **Oracle Text**: Full-text search với CONTEXT indexes
- ✅ **JSON Data Guide**: Xử lý dữ liệu semi-structured
- ✅ **Hybrid Search**: Kết hợp Oracle và PostgreSQL
- ✅ **Connection Pooling**: Quản lý kết nối hiệu quả
- ✅ **OCI Integration**: Tích hợp với Oracle Cloud Infrastructure

### AI Assistant Features
- 🤖 **Semantic Search**: Sử dụng sentence-transformers
- 📄 **Document Management**: Upload và index documents
- 🔍 **Multi-source Search**: Tìm kiếm trên nhiều database
- ⚡ **Performance Optimized**: Caching và connection pooling

## 🏗️ Kiến trúc hệ thống

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Open WebUI    │───▶│  FastAPI Backend │───▶│  LLAMA.CPP      │
│   (Port 3001)   │    │   (Port 8010)    │    │  (Port 11434)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                    ┌──────────────────┐
                    │ Oracle Integration│
                    │   (Port 8025)     │
                    └──────────────────┘
                                │
                    ┌───────────┴────────────┐
                    ▼                        ▼
            ┌─────────────────┐    ┌─────────────────┐
            │ Oracle 19C      │    │ PostgreSQL      │
            │ Autonomous DB   │    │ + pgvector      │
            │ (OCI)           │    │ (Existing)      │
            └─────────────────┘    └─────────────────┘
```

## 📋 Yêu cầu hệ thống

### Hardware
- CPU: ARM64 hoặc x86_64
- RAM: Tối thiểu 4GB (8GB khuyến nghị)
- Storage: 10GB trống

### Software
- Ubuntu 20.04+ hoặc compatible Linux
- Python 3.8+
- Oracle Instant Client 19c
- OCI CLI (tùy chọn)

### Oracle Cloud Infrastructure
- Oracle Autonomous Database instance
- OCI Account với proper permissions
- Database wallet files

## 🚀 Cài đặt

### 1. Quick Setup
```bash
# Clone repository và chạy setup
chmod +x setup_oracle_integration.sh
./setup_oracle_integration.sh
```

### 2. Manual Setup

#### Bước 1: Install dependencies
```bash
sudo apt update
sudo apt install -y python3-dev python3-pip python3-venv build-essential libaio1 libaio-dev
```

#### Bước 2: Setup Python environment
```bash
python3 -m venv venv_oracle
source venv_oracle/bin/activate
pip install -r oracle_requirements.txt
pip install -r backend/requirements.txt
```

#### Bước 3: Install Oracle Instant Client
```bash
# Download từ Oracle website
# https://www.oracle.com/database/technologies/instant-client/downloads.html

# Extract và setup
sudo mkdir -p /opt/oracle
sudo unzip instantclient-basic-linux.x64-*********.0dbru.zip -d /opt/oracle
export LD_LIBRARY_PATH=/opt/oracle/instantclient_19_17:$LD_LIBRARY_PATH
```

## ⚙️ Cấu hình

### 1. Oracle Autonomous Database Setup

#### Tạo Autonomous Database trên OCI
1. Login vào OCI Console
2. Navigate to **Autonomous Database**
3. Click **Create Autonomous Database**
4. Chọn các thông số:
   - **Database Name**: ai-assistant-db
   - **Display Name**: AI Assistant Database
   - **Workload Type**: Data Warehouse hoặc Transaction Processing
   - **Deployment Type**: Shared Infrastructure
   - **Database Version**: 19c
   - **OCPU**: 1 (có thể scale up)
   - **Storage**: 20GB (có thể scale up)

#### Download Wallet
1. Từ Autonomous Database details page
2. Click **DB Connection**
3. Download **Instance Wallet**
4. Extract wallet files vào `./oracle_wallet/`

### 2. Environment Configuration

Chỉnh sửa `.env.oracle`:
```bash
# Oracle Database Connection
ORACLE_USER=ADMIN
ORACLE_PASSWORD=your_admin_password_here
ORACLE_DSN=your_db_name_high  # Từ tnsnames.ora
ORACLE_WALLET_LOCATION=./oracle_wallet
ORACLE_WALLET_PASSWORD=your_wallet_password

# Autonomous Database Information
AUTONOMOUS_DB_OCID=ocid1.autonomousdatabase.oc1.region.xxxxxxxxxxxxx
OCI_COMPARTMENT_ID=ocid1.compartment.oc1..xxxxxxxxxxxxx

# Connection Pool Settings
ORACLE_POOL_MIN=2
ORACLE_POOL_MAX=10
ORACLE_POOL_INCREMENT=1
```

### 3. OCI Configuration (Optional)

Setup OCI CLI:
```bash
oci setup config
```

## 🏃‍♂️ Chạy hệ thống

### Start All Services
```bash
./start_oracle_integration.sh
```

### Start Individual Services
```bash
# Oracle RAG Service
source venv_oracle/bin/activate
python oracle_rag_service.py  # Port 8030

# Oracle Backend Integration
python integrate_oracle_backend.py  # Port 8025

# Existing LLAMA.CPP + FastAPI
./start_complete_system.sh
```

### Verify Services
```bash
# Test Oracle connection
python test_oracle_integration.py

# Check health endpoints
curl http://localhost:8025/oracle/health
curl http://localhost:8025/oracle/status
```

## 📚 API Usage

### Add Document
```bash
curl -X POST "http://localhost:8025/oracle/documents" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Sample Document",
    "content": "This is a sample document for testing Oracle integration.",
    "file_type": "txt",
    "metadata": {"category": "test", "author": "user"}
  }'
```

### Search Documents
```bash
curl -X POST "http://localhost:8025/oracle/search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "sample document",
    "top_k": 5,
    "search_type": "hybrid"
  }'
```

### Get Status
```bash
curl http://localhost:8025/oracle/status
```

## 🔧 Oracle 19C Limitations và Workarounds

### Vector Search
Oracle 19C không có native vector search như Oracle 23ai.

**Workaround**: Hybrid approach
- Store embeddings as JSON CLOB
- Use external similarity calculation
- Combine với Oracle Text search

### JSON Search
Oracle 19C có limited JSON support.

**Workaround**: 
- Use JSON Data Guide
- Store metadata as JSON columns
- Use SQL/JSON functions

### Performance Optimization
```sql
-- Oracle Text indexes
CREATE INDEX idx_docs_content_text 
ON AI_DOCUMENTS(content) 
INDEXTYPE IS CTXSYS.CONTEXT;

-- JSON functional indexes
CREATE INDEX idx_docs_metadata_category 
ON AI_DOCUMENTS(JSON_VALUE(metadata, '$.category'));
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Oracle Connection Error
```
ORA-12541: TNS:no listener
```
**Solution**: Check DSN string và wallet location

#### 2. Python oracledb Error
```
DPI-1047: Cannot locate an Oracle Client library
```
**Solution**: Install Oracle Instant Client và set LD_LIBRARY_PATH

#### 3. Wallet Password Error
```
ORA-28040: No matching authentication protocol
```
**Solution**: Check wallet password và TNS configuration

### Debug Commands
```bash
# Test Oracle connectivity
python -c "import oracledb; print(oracledb.version)"

# Check environment variables
env | grep ORACLE

# Test SQL connection
sqlplus admin@your_db_name_high
```

## 📈 Performance Tuning

### Connection Pool Tuning
```python
# Trong oracle_oci_config.py
ORACLE_POOL_MIN=5
ORACLE_POOL_MAX=20
ORACLE_POOL_INCREMENT=2
```

### Oracle Text Performance
```sql
-- Optimize CTX indexes
EXEC CTX_DDL.OPTIMIZE_INDEX('idx_docs_content_text', 'FULL');

-- Sync indexes
EXEC CTX_DDL.SYNC_INDEX('idx_docs_content_text');
```

### Embedding Batch Processing
```python
# Trong ai_config
embedding_chunk_size = 1000  # Reduce for lower memory
```

## 🔒 Security

### Best Practices
1. **Wallet Security**: Protect wallet files và passwords
2. **Connection Encryption**: Always use wallet connection
3. **API Authentication**: Implement API keys cho production
4. **Network Security**: Use VPN hoặc private networks

### OCI Security
1. Use IAM policies cho database access
2. Enable Database Vault (if available)
3. Monitor audit logs
4. Regular security patches

## 📊 Monitoring

### Health Checks
```bash
# Service health
curl http://localhost:8025/oracle/health

# Detailed metrics
curl http://localhost:8025/oracle/metrics
```

### Database Monitoring
```sql
-- Connection usage
SELECT * FROM V$SESSION WHERE USERNAME = 'ADMIN';

-- Database size
SELECT SUM(BYTES)/1024/1024 MB_SIZE FROM USER_SEGMENTS;
```

## 🚀 Deployment to Production

### Docker Support (Coming Soon)
```dockerfile
# Dockerfile.oracle
FROM ubuntu:20.04
RUN apt-get update && apt-get install -y libaio1
COPY oracle_wallet /app/oracle_wallet
COPY . /app
WORKDIR /app
RUN pip install -r oracle_requirements.txt
CMD ["python", "integrate_oracle_backend.py"]
```

### Kubernetes Deployment
```yaml
# oracle-rag-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: oracle-rag
spec:
  replicas: 3
  selector:
    matchLabels:
      app: oracle-rag
  template:
    metadata:
      labels:
        app: oracle-rag
    spec:
      containers:
      - name: oracle-rag
        image: your-registry/oracle-rag:latest
        ports:
        - containerPort: 8025
        env:
        - name: ORACLE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: oracle-secrets
              key: password
```

## 🤝 Contributing

### Development Setup
```bash
git clone <repository>
cd AccA
./setup_oracle_integration.sh --with-oci-cli
source venv_oracle/bin/activate
python test_oracle_integration.py
```

### Testing
```bash
# Unit tests
python -m pytest tests/test_oracle_rag.py

# Integration tests
python test_oracle_integration.py

# Load testing
python tests/load_test_oracle.py
```

## 📝 Support

### Documentation
- [Oracle 19c Documentation](https://docs.oracle.com/en/database/oracle/oracle-database/19/)
- [OCI Python SDK](https://docs.oracle.com/en-us/iaas/tools/python/latest/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)

### Issues
Nếu gặp vấn đề, vui lòng:
1. Check troubleshooting section
2. Run diagnostic commands
3. Check logs trong `/var/log/oracle-rag/`
4. Create issue với full error logs

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Lưu ý**: Dự án này được thiết kế đặc biệt cho Oracle 19C Autonomous Database. Một số features có thể khác với Oracle 23ai do limitations của version 19C. 