#!/usr/bin/env python3
"""
Docling Image Processor Fix
Adds comprehensive image processing support to the Docling server
Fixes NoneType iteration errors in image upload pipeline
"""

import os
import sys
import json
import logging
import requests
import tempfile
import base64
from pathlib import Path
from typing import Dict, List, Any, Optional
from PIL import Image, ImageEnhance
import pytesseract
import cv2
import numpy as np

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DoclingImageProcessor:
    """Enhanced image processor for Docling server integration"""
    
    def __init__(self, docling_server_url: str = "http://localhost:5001"):
        self.docling_server_url = docling_server_url
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff', '.tif'}
        self.max_image_size = 10 * 1024 * 1024  # 10MB
        
        # OCR configuration
        self.tesseract_config = '--oem 3 --psm 6 -l vie+eng'  # Vietnamese + English
        
        # Image processing settings
        self.preprocessing_enabled = True
        self.enhancement_enabled = True
        
    def is_image_file(self, filename: str) -> bool:
        """Check if file is a supported image format"""
        ext = Path(filename).suffix.lower()
        return ext in self.supported_formats
    
    def validate_image_file(self, file_data: bytes, filename: str) -> Dict[str, Any]:
        """Validate uploaded image file"""
        validation_result = {
            "valid": False,
            "errors": [],
            "warnings": [],
            "file_info": {}
        }
        
        try:
            # Check file size
            if len(file_data) > self.max_image_size:
                validation_result["errors"].append(f"File too large: {len(file_data)} bytes (max: {self.max_image_size})")
                return validation_result
            
            # Check file extension
            if not self.is_image_file(filename):
                validation_result["errors"].append(f"Unsupported image format: {Path(filename).suffix}")
                return validation_result
            
            # Try to open image with PIL
            try:
                with tempfile.NamedTemporaryFile() as tmp_file:
                    tmp_file.write(file_data)
                    tmp_file.flush()
                    
                    with Image.open(tmp_file.name) as img:
                        validation_result["file_info"] = {
                            "format": img.format,
                            "mode": img.mode,
                            "size": img.size,
                            "width": img.width,
                            "height": img.height
                        }
                        
                        # Check image dimensions
                        if img.width < 50 or img.height < 50:
                            validation_result["warnings"].append("Image is very small, OCR may not work well")
                        
                        if img.width > 4000 or img.height > 4000:
                            validation_result["warnings"].append("Large image, processing may take longer")
                        
            except Exception as e:
                validation_result["errors"].append(f"Invalid image file: {str(e)}")
                return validation_result
            
            # If no errors, mark as valid
            validation_result["valid"] = len(validation_result["errors"]) == 0
            return validation_result
            
        except Exception as e:
            validation_result["errors"].append(f"Validation failed: {str(e)}")
            return validation_result
    
    def preprocess_image(self, image_path: str) -> str:
        """Preprocess image for better OCR results"""
        try:
            # Read image with OpenCV
            img = cv2.imread(image_path)
            if img is None:
                raise ValueError("Could not read image")
            
            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Apply noise reduction
            denoised = cv2.fastNlMeansDenoising(gray)
            
            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
            
            # Save preprocessed image
            preprocessed_path = image_path.replace('.', '_preprocessed.')
            cv2.imwrite(preprocessed_path, thresh)
            
            logger.info(f"✅ Image preprocessed: {preprocessed_path}")
            return preprocessed_path
            
        except Exception as e:
            logger.warning(f"⚠️ Image preprocessing failed: {e}, using original")
            return image_path
    
    def enhance_image(self, image_path: str) -> str:
        """Enhance image quality for better OCR"""
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Enhance contrast
                enhancer = ImageEnhance.Contrast(img)
                img = enhancer.enhance(1.2)
                
                # Enhance sharpness
                enhancer = ImageEnhance.Sharpness(img)
                img = enhancer.enhance(1.1)
                
                # Save enhanced image
                enhanced_path = image_path.replace('.', '_enhanced.')
                img.save(enhanced_path, quality=95)
                
                logger.info(f"✅ Image enhanced: {enhanced_path}")
                return enhanced_path
                
        except Exception as e:
            logger.warning(f"⚠️ Image enhancement failed: {e}, using original")
            return image_path
    
    def extract_text_from_image(self, image_path: str) -> Dict[str, Any]:
        """Extract text from image using OCR"""
        try:
            # Preprocess image if enabled
            processed_path = image_path
            if self.preprocessing_enabled:
                processed_path = self.preprocess_image(image_path)
            
            # Enhance image if enabled
            if self.enhancement_enabled:
                processed_path = self.enhance_image(processed_path)
            
            # Perform OCR
            logger.info(f"🔍 Performing OCR on {Path(processed_path).name}...")
            
            # Extract text with confidence scores
            ocr_data = pytesseract.image_to_data(
                processed_path, 
                config=self.tesseract_config,
                output_type=pytesseract.Output.DICT
            )
            
            # Extract plain text
            text = pytesseract.image_to_string(processed_path, config=self.tesseract_config)
            
            # Calculate confidence
            confidences = [int(conf) for conf in ocr_data['conf'] if int(conf) > 0]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0
            
            # Clean up temporary files
            if processed_path != image_path:
                try:
                    os.unlink(processed_path)
                except:
                    pass
            
            result = {
                "text": text.strip(),
                "confidence": avg_confidence / 100.0,  # Convert to 0-1 range
                "word_count": len(text.split()),
                "character_count": len(text),
                "language_detected": "vie+eng",
                "processing_method": "tesseract_ocr",
                "preprocessing_applied": self.preprocessing_enabled,
                "enhancement_applied": self.enhancement_enabled
            }
            
            logger.info(f"✅ OCR completed: {result['word_count']} words, confidence: {result['confidence']:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"❌ OCR failed: {e}")
            return {
                "text": "",
                "confidence": 0.0,
                "error": str(e),
                "processing_method": "failed"
            }
    
    def process_image_file(self, file_data: bytes, filename: str) -> Dict[str, Any]:
        """Process uploaded image file"""
        try:
            logger.info(f"🖼️ Processing image: {filename}")
            
            # Validate image
            validation = self.validate_image_file(file_data, filename)
            if not validation["valid"]:
                return {
                    "success": False,
                    "error": "Image validation failed",
                    "details": validation["errors"],
                    "filename": filename
                }
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix=Path(filename).suffix) as tmp_file:
                tmp_file.write(file_data)
                tmp_path = tmp_file.name
            
            try:
                # Extract text from image
                ocr_result = self.extract_text_from_image(tmp_path)
                
                # Format result for Docling compatibility
                result = {
                    "success": True,
                    "text": ocr_result.get("text", ""),
                    "filename": filename,
                    "status": "success",
                    "method": "image_ocr",
                    "length": len(ocr_result.get("text", "")),
                    "confidence": ocr_result.get("confidence", 0.0),
                    "file_info": validation["file_info"],
                    "processing_details": {
                        "word_count": ocr_result.get("word_count", 0),
                        "character_count": ocr_result.get("character_count", 0),
                        "language_detected": ocr_result.get("language_detected", "unknown"),
                        "preprocessing_applied": ocr_result.get("preprocessing_applied", False),
                        "enhancement_applied": ocr_result.get("enhancement_applied", False)
                    }
                }
                
                if validation["warnings"]:
                    result["warnings"] = validation["warnings"]
                
                return result
                
            finally:
                # Clean up temporary file
                try:
                    os.unlink(tmp_path)
                except:
                    pass
            
        except Exception as e:
            logger.error(f"❌ Image processing failed for {filename}: {e}")
            return {
                "success": False,
                "error": str(e),
                "filename": filename,
                "method": "image_ocr_failed"
            }
    
    def check_docling_server(self) -> bool:
        """Check if Docling server is running"""
        try:
            response = requests.get(f"{self.docling_server_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def send_to_docling_server(self, file_data: bytes, filename: str) -> Dict[str, Any]:
        """Send processed image to Docling server"""
        try:
            if not self.check_docling_server():
                return {
                    "success": False,
                    "error": "Docling server not available",
                    "server_url": self.docling_server_url
                }
            
            # Process image locally first
            local_result = self.process_image_file(file_data, filename)
            
            if not local_result.get("success", False):
                return local_result
            
            # Send to Docling server for additional processing if needed
            try:
                files = {'file': (filename, file_data, 'image/jpeg')}
                response = requests.post(
                    f"{self.docling_server_url}/convert",
                    files=files,
                    timeout=30
                )
                
                if response.status_code == 200:
                    server_result = response.json()
                    # Merge local OCR result with server response
                    local_result["server_processing"] = server_result
                    logger.info(f"✅ Image processed by both local OCR and Docling server")
                else:
                    logger.warning(f"⚠️ Docling server returned {response.status_code}, using local OCR only")
                    local_result["server_processing"] = {"error": f"Server error: {response.status_code}"}
                
            except Exception as server_error:
                logger.warning(f"⚠️ Docling server processing failed: {server_error}, using local OCR only")
                local_result["server_processing"] = {"error": str(server_error)}
            
            return local_result
            
        except Exception as e:
            logger.error(f"❌ Failed to process image with Docling: {e}")
            return {
                "success": False,
                "error": str(e),
                "filename": filename
            }


def create_enhanced_docling_server_with_images():
    """Create enhanced Docling server with image processing support"""
    
    enhanced_server_code = '''#!/usr/bin/env python3
"""
Enhanced Docling Server with Image Processing Support
Supports documents (.pdf, .docx, .txt, .doc) AND images (.jpg, .png, etc.)
"""

from flask import Flask, request, jsonify
from docling.document_converter import DocumentConverter
import tempfile
import os
import logging
import subprocess
import chardet
from pathlib import Path

# Import our image processor
import sys
sys.path.append('.')
from docling_image_processor_fix import DoclingImageProcessor

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize converters
document_converter = DocumentConverter()
image_processor = DoclingImageProcessor()

def detect_encoding(file_path):
    """Detect file encoding for text files"""
    with open(file_path, 'rb') as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        return result['encoding'] or 'utf-8'

def convert_txt_to_text(file_path):
    """Convert .txt file to text with proper encoding"""
    try:
        encoding = detect_encoding(file_path)
        with open(file_path, 'r', encoding=encoding) as f:
            content = f.read()
        
        # Format as markdown for consistency
        markdown_content = f"# Text Document\\n\\n{content}"
        return markdown_content
    except Exception as e:
        logger.error(f"Error reading TXT file: {e}")
        raise

def convert_doc_to_docx(file_path):
    """Convert .doc to .docx using LibreOffice"""
    try:
        output_dir = os.path.dirname(file_path)
        
        # Use LibreOffice to convert .doc to .docx
        cmd = [
            'libreoffice', '--headless', '--convert-to', 'docx',
            '--outdir', output_dir, file_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0:
            raise Exception(f"LibreOffice conversion failed: {result.stderr}")
        
        # Return path to converted .docx file
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        docx_path = os.path.join(output_dir, f"{base_name}.docx")
        
        if os.path.exists(docx_path):
            return docx_path
        else:
            raise Exception("Converted .docx file not found")
            
    except Exception as e:
        logger.error(f"Error converting DOC file: {e}")
        raise

def get_file_extension(filename):
    """Get file extension in lowercase"""
    return os.path.splitext(filename)[1].lower()

def is_image_file(filename):
    """Check if file is an image"""
    return image_processor.is_image_file(filename)

def is_document_file(filename):
    """Check if file is a document"""
    ext = get_file_extension(filename)
    return ext in ['.pdf', '.docx', '.pptx', '.md', '.html', '.csv', '.xlsx', '.txt', '.doc']

@app.route('/health', methods=['GET'])
def health():
    return jsonify({
        "status": "healthy", 
        "service": "enhanced-docling-server-with-images",
        "supported_formats": {
            "documents": [".pdf", ".docx", ".pptx", ".md", ".html", ".csv", ".xlsx", ".txt", ".doc"],
            "images": [".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp", ".tiff", ".tif"]
        }
    })

@app.route('/convert', methods=['POST'])
def convert_file():
    """
    Convert uploaded file (document or image) to text
    """
    try:
        if 'file' not in request.files:
            return jsonify({"error": "No file provided"}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({"error": "Empty filename"}), 400
        
        filename = file.filename
        file_ext = get_file_extension(filename)
        logger.info(f"Processing file: {filename} (extension: {file_ext})")
        
        # Read file data
        file_data = file.read()
        
        # Route to appropriate processor
        if is_image_file(filename):
            # Process as image
            logger.info(f"🖼️ Processing {filename} as image")
            result = image_processor.process_image_file(file_data, filename)
            
            if result.get("success", False):
                return jsonify({
                    "text": result.get("text", ""),
                    "filename": filename,
                    "status": "success",
                    "method": "image_ocr",
                    "length": len(result.get("text", "")),
                    "confidence": result.get("confidence", 0.0),
                    "file_type": "image",
                    "processing_details": result.get("processing_details", {})
                })
            else:
                return jsonify({
                    "error": result.get("error", "Image processing failed"),
                    "filename": filename,
                    "file_type": "image"
                }), 500
        
        elif is_document_file(filename):
            # Process as document
            logger.info(f"📄 Processing {filename} as document")
            
            # Save uploaded file temporarily
            with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as tmp_file:
                tmp_file.write(file_data)
                tmp_path = tmp_file.name
            
            try:
                text_content = ""
                conversion_method = "unknown"
                
                # Handle different document types
                if file_ext == '.txt':
                    text_content = convert_txt_to_text(tmp_path)
                    conversion_method = "direct_text_read"
                    
                elif file_ext == '.doc':
                    try:
                        docx_path = convert_doc_to_docx(tmp_path)
                        result = document_converter.convert(docx_path)
                        text_content = result.document.export_to_markdown()
                        conversion_method = "doc_to_docx_conversion"
                        
                        # Clean up converted file
                        if os.path.exists(docx_path):
                            os.unlink(docx_path)
                            
                    except Exception as doc_error:
                        logger.warning(f"DOC conversion failed, trying text extraction: {doc_error}")
                        text_content = convert_txt_to_text(tmp_path)
                        conversion_method = "doc_fallback_text"
                        
                else:
                    # Use native Docling for supported formats
                    result = document_converter.convert(tmp_path)
                    text_content = result.document.export_to_markdown()
                    conversion_method = "native_docling"
                
                logger.info(f"✅ Document converted using {conversion_method}, length: {len(text_content)}")
                
                return jsonify({
                    "text": text_content,
                    "filename": filename,
                    "status": "success",
                    "method": conversion_method,
                    "length": len(text_content),
                    "file_type": "document"
                })
                
            finally:
                # Clean up temporary file
                if os.path.exists(tmp_path):
                    os.unlink(tmp_path)
        
        else:
            return jsonify({
                "error": f"Unsupported file format: {file_ext}",
                "filename": filename,
                "supported_formats": {
                    "documents": [".pdf", ".docx", ".pptx", ".md", ".html", ".csv", ".xlsx", ".txt", ".doc"],
                    "images": [".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp", ".tiff", ".tif"]
                }
            }), 400
    
    except Exception as e:
        logger.error(f"Error processing file: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/extract', methods=['POST'])
def extract_content():
    """Alternative endpoint for content extraction"""
    return convert_file()

@app.route('/formats', methods=['GET'])
def supported_formats():
    """List all supported file formats"""
    return jsonify({
        "document_formats": {
            "pdf": "Portable Document Format",
            "docx": "Microsoft Word (new format)",
            "pptx": "Microsoft PowerPoint",
            "md": "Markdown",
            "html": "HTML documents",
            "csv": "Comma-separated values",
            "xlsx": "Microsoft Excel",
            "txt": "Plain text files (with encoding detection)",
            "doc": "Microsoft Word (legacy format, converted via LibreOffice)"
        },
        "image_formats": {
            "jpg": "JPEG images",
            "jpeg": "JPEG images",
            "png": "PNG images",
            "gif": "GIF images",
            "webp": "WebP images",
            "bmp": "Bitmap images",
            "tiff": "TIFF images",
            "tif": "TIFF images"
        },
        "processing_methods": {
            "documents": "Docling native processing or LibreOffice conversion",
            "images": "Tesseract OCR with Vietnamese + English support"
        }
    })

if __name__ == '__main__':
    print("🚀 Starting Enhanced Docling Server with Image Support...")
    print("📄 Document support: .pdf, .docx, .pptx, .md, .html, .csv, .xlsx, .txt, .doc")
    print("🖼️ Image support: .jpg, .jpeg, .png, .gif, .webp, .bmp, .tiff, .tif")
    print("🔗 Server will run on: http://localhost:5001")
    print("💡 Use this URL in Open WebUI external content extraction settings")
    
    app.run(host='0.0.0.0', port=5001, debug=False)
'''
    
    # Write the enhanced server
    with open('enhanced_docling_server_with_images.py', 'w', encoding='utf-8') as f:
        f.write(enhanced_server_code)
    
    logger.info("✅ Created enhanced_docling_server_with_images.py")


def fix_image_pipeline_integration():
    """Fix the image pipeline to properly handle Docling responses"""
    
    pipeline_fix_code = '''#!/usr/bin/env python3
"""
Fixed Image Pipeline Integration
Fixes NoneType iteration errors when processing images with Docling
"""

import logging
import requests
import json
from typing import Dict, List, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class FixedImagePipeline:
    """Fixed image processing pipeline with proper error handling"""
    
    def __init__(self, docling_server_url: str = "http://localhost:5001"):
        self.docling_server_url = docling_server_url
        self.timeout = 30
        
    def safe_process_image(self, image_data: bytes, filename: str) -> Dict[str, Any]:
        """Safely process image with comprehensive error handling"""
        try:
            # Validate inputs
            if image_data is None:
                return self._create_error_response("No image data provided", filename)
            
            if not filename:
                return self._create_error_response("No filename provided", "unknown")
            
            # Check server availability
            if not self._check_server_health():
                return self._create_error_response("Docling server not available", filename)
            
            # Send to Docling server
            response = self._send_to_docling(image_data, filename)
            
            # Process response safely
            return self._process_docling_response(response, filename)
            
        except Exception as e:
            logger.error(f"❌ Image pipeline error: {e}")
            return self._create_error_response(str(e), filename)
    
    def _check_server_health(self) -> bool:
        """Check if Docling server is healthy"""
        try:
            response = requests.get(f"{self.docling_server_url}/health", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.warning(f"⚠️ Server health check failed: {e}")
            return False
    
    def _send_to_docling(self, image_data: bytes, filename: str) -> requests.Response:
        """Send image to Docling server"""
        try:
            files = {'file': (filename, image_data, 'image/jpeg')}
            response = requests.post(
                f"{self.docling_server_url}/convert",
                files=files,
                timeout=self.timeout
            )
            return response
            
        except requests.exceptions.Timeout:
            raise Exception(f"Request timeout after {self.timeout} seconds")
        except requests.exceptions.ConnectionError:
            raise Exception("Could not connect to Docling server")
        except Exception as e:
            raise Exception(f"Request failed: {str(e)}")
    
    def _process_docling_response(self, response: requests.Response, filename: str) -> Dict[str, Any]:
        """Process Docling server response safely"""
        try:
            if response.status_code != 200:
                return self._create_error_response(
                    f"Server returned status {response.status_code}", 
                    filename
                )
            
            # Parse JSON response safely
            try:
                result = response.json()
            except json.JSONDecodeError as e:
                return self._create_error_response(f"Invalid JSON response: {e}", filename)
            
            # Validate response structure
            if not isinstance(result, dict):
                return self._create_error_response("Response is not a dictionary", filename)
            
            # Extract text safely
            text_content = result.get('text', '')
            if text_content is None:  # Explicit None check
                text_content = ''
            
            # Create successful response
            return {
                "success": True,
                "text": str(text_content),  # Ensure string type
                "filename": filename,
                "method": result.get('method', 'unknown'),
                "confidence": result.get('confidence', 0.0),
                "file_type": result.get('file_type', 'image'),
                "length": len(str(text_content)),
                "processing_details": result.get('processing_details', {}),
                "server_response": result  # Include full response for debugging
            }
            
        except Exception as e:
            logger.error(f"❌ Response processing error: {e}")
            return self._create_error_response(f"Response processing failed: {e}", filename)
    
    def _create_error_response(self, error_message: str, filename: str) -> Dict[str, Any]:
        """Create standardized error response"""
        return {
            "success": False,
            "error": error_message,
            "text": "",  # Always provide empty string, never None
            "filename": filename,
            "method": "error",
            "confidence": 0.0,
            "file_type": "unknown",
            "length": 0,
            "processing_details": {},
            "server_response": None
        }
    
    def process_multiple_images(self, image_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process multiple images safely"""
        if not image_list:  # Handle None or empty list
            return []
        
        if not isinstance(image_list, list):  # Handle non-list input
            logger.warning(f"⚠️ Expected list, got {type(image_list)}")
            return []
        
        results = []
        for i, image_item in enumerate(image_list):
            try:
                if not isinstance(image_item, dict):
                    logger.warning(f"⚠️ Image item {i} is not a dictionary")
                    continue
                
                image_data = image_item.get('data')
                filename = image_item.get('filename', f'image_{i}.jpg')
                
                if image_data is None:
                    logger.warning(f"⚠️ No image data for item {i}")
                    continue
                
                result = self.safe_process_image(image_data, filename)
                results.append(result)
                
            except Exception as e:
                logger.error(f"❌ Error processing image {i}: {e}")
                results.append(self._create_error_response(str(e), f'image_{i}.jpg'))
        
        return results


# Monkey patch for existing pipeline code
def safe_iterate(obj):
    """Safe iteration that handles None values"""
    if obj is None:
        return []
    if isinstance(obj, (list, tuple)):
        return obj
    if isinstance(obj, dict):
        return obj.items()
    try:
        return iter(obj)
    except TypeError:
        return []


def apply_pipeline_fixes():
    """Apply fixes to existing pipeline code"""
    logger.info("🔧 Applying image pipeline fixes...")
    
    # Create fixed pipeline instance
    fixed_pipeline = FixedImagePipeline()
    
    # Test the fixes
    test_result = fixed_pipeline._create_error_response("Test error", "test.jpg")
    assert test_result["text"] == ""  # Ensure empty string, not None
    assert test_result["success"] == False
    
    logger.info("✅ Pipeline fixes applied successfully")
    return fixed_pipeline


if __name__ == "__main__":
    # Test the fixes
    pipeline = apply_pipeline_fixes()
    
    # Test server health check
    health_ok = pipeline._check_server_health()
    print(f"🏥 Server health: {'✅ OK' if health_ok else '❌ Failed'}")
    
    # Test error handling
    error_response = pipeline._create_error_response("Test error", "test.jpg")
    print(f"🧪 Error response test: {error_response}")
    
    print("✅ Image pipeline fixes ready!")
'''
    
    # Write the pipeline fix
    with open('fixed_image_pipeline.py', '