#!/usr/bin/env python3
"""
Simple Docling Server for Table Extraction and Text Extraction
Serves on port 5001 as expected by the hybrid table processor
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any, List
from flask import Flask, request, jsonify
from flask_cors import CORS
import tempfile
import traceback

# Add current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from docling.document_converter import DocumentConverter
    from docling.datamodel.base_models import InputFormat
    from docling.datamodel.pipeline_options import PdfPipelineOptions, TesseractOcrOptions
    from docling.document_converter import PdfFormatOption
except ImportError as e:
    print(f"Error importing Docling: {e}")
    print("Please ensure Docling is installed: pip install docling")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, origins=["http://localhost:8888", "http://127.0.0.1:8888", "http://localhost:3000", "http://127.0.0.1:3000"])

class DoclingTableExtractor:
    def __init__(self):
        """Initialize Docling extractor. Converter will be created per-request."""
        # self.converter is no longer initialized here to allow dynamic OCR options.
        logger.info("DoclingTableExtractor initialized (converter to be created per-request).")

    def extract_content_from_file(self, file_path: str, ocr_engine: str, ocr_langs: List[str], extract_type: str = "both") -> Dict[str, Any]:
        """Extract content from document using Docling with dynamic OCR settings
        
        Args:
            file_path: Path to the document file
            ocr_engine: OCR engine to use
            ocr_langs: List of OCR languages
            extract_type: "text", "tables", or "both"
        """
        try:
            logger.info(f"Processing file: {file_path} with OCR engine: {ocr_engine}, langs: {ocr_langs}, extract_type: {extract_type}")

            # --- Using TesseractOCR ---
            logger.info("Using TesseractOCR for document processing.")
            actual_ocr_langs = ocr_langs if ocr_langs else ['vie', 'eng']
            current_ocr_options = TesseractOcrOptions(lang=actual_ocr_langs)
            logger.info(f"Using TesseractOcrOptions with languages: {actual_ocr_langs}")

            # Check DISABLE_OCR environment variable
            disable_ocr = os.environ.get('DISABLE_OCR', '0').lower() in ['1', 'true', 'yes']
            do_ocr_setting = not disable_ocr
            
            # Create pipeline options based on OCR setting
            if do_ocr_setting:
                pipeline_options_instance = PdfPipelineOptions(
                    do_ocr=True,
                    do_table_structure=True, # Keep table structure recognition enabled
                    ocr_options=current_ocr_options # Pass OCR options when OCR is enabled
                )
            else:
                pipeline_options_instance = PdfPipelineOptions(
                    do_ocr=False,
                    do_table_structure=True # Keep table structure recognition enabled
                    # Don't include ocr_options when OCR is disabled
                )
            
            # Support both PDF and IMAGE formats
            current_format_options = {
                InputFormat.PDF: PdfFormatOption(
                    pipeline_options=pipeline_options_instance
                ),
                InputFormat.IMAGE: PdfFormatOption(
                    pipeline_options=pipeline_options_instance
                )
            }
            
            # Create DocumentConverter instance for this specific request
            try:
                current_converter = DocumentConverter(format_options=current_format_options)
                logger.info("Dynamic DocumentConverter created successfully with per-request OCR options.")
            except Exception as e_converter_init:
                logger.error(f"Failed to initialize dynamic DocumentConverter: {e_converter_init}")
                logger.error(traceback.format_exc())
                logger.warning("Falling back to a default DocumentConverter due to dynamic init failure. OCR language settings from request might not be applied.")
                # Basic default, might not respect incoming lang if dynamic init fails
                if do_ocr_setting:
                    default_pipeline_opts = PdfPipelineOptions(do_ocr=True, do_table_structure=True) 
                else:
                    default_pipeline_opts = PdfPipelineOptions(do_ocr=False, do_table_structure=True)
                current_converter = DocumentConverter(format_options={InputFormat.PDF: PdfFormatOption(pipeline_options=default_pipeline_opts)})

            # Convert document
            result = current_converter.convert(file_path)
            
            # Extract tables and content
            tables = []
            all_content_parts = [] # To build content from multiple sources if needed
            full_markdown_content = "" # Initialize
            
            # Process the document
            if hasattr(result, 'document') and result.document:
                doc = result.document
                logger.info(f"Successfully obtained doc object: type={type(doc)}")

                # --- Table Extraction ---
                if extract_type in ["tables", "both"] and hasattr(doc, 'tables') and doc.tables:
                    logger.info(f"doc.tables found with {len(doc.tables)} items. Attempting direct extraction.")
                    for i, table_item in enumerate(doc.tables):
                        logger.info(f"Processing table_item {i}: type={type(table_item)}")
                        try:
                            table_md = self._table_to_markdown(table_item)
                            if table_md:
                                table_data = {
                                    'table_id': i,
                                    'content': str(table_item), # Or a better representation if available
                                    'markdown': table_md,
                                    'confidence': getattr(table_item, 'confidence', 0.88) # Default confidence if not present
                                }
                                tables.append(table_data)
                                logger.info(f"Successfully processed table_item {i} into markdown.")
                            else:
                                logger.warning(f"_table_to_markdown returned empty for table_item {i}.")
                        except Exception as e_table_item:
                            logger.error(f"Error processing table_item {i} directly: {e_table_item}")
                elif extract_type in ["tables", "both"]:
                    logger.info("doc.tables not found, is empty, or not iterable.")

                # --- Content Extraction ---
                if extract_type in ["text", "both"]:
                    extracted_content_source = None
                    if hasattr(doc, 'texts') and doc.texts:
                        logger.info(f"doc.texts found with {len(doc.texts)} items. Attempting to build content.")
                        extracted_content_source = "doc.texts"
                        for i, text_block in enumerate(doc.texts):
                            if isinstance(text_block, str):
                                all_content_parts.append(text_block)
                            elif hasattr(text_block, 'text') and isinstance(text_block.text, str):
                                all_content_parts.append(text_block.text)
                            else:
                                logger.warning(f"doc.texts item {i} (type: {type(text_block)}) is not a string and has no .text attribute. Using str(). Content: {str(text_block)[:100]}...")
                                all_content_parts.append(str(text_block))
                    elif hasattr(doc, 'text') and doc.text is not None:
                        logger.info("Falling back to doc.text for content.")
                        extracted_content_source = "doc.text"
                        if isinstance(doc.text, str):
                            all_content_parts.append(doc.text)
                        else:
                            logger.warning(f"doc.text is not a string (type: {type(doc.text)}). Using str().")
                            all_content_parts.append(str(doc.text))
                    else:
                        logger.warning("Could not find suitable content in doc.texts or doc.text.")
                        extracted_content_source = "none"
                    
                    if all_content_parts:
                        full_markdown_content = "\n\n".join(all_content_parts) # Join with double newline for separation
                        logger.info(f"Content successfully extracted from {extracted_content_source}. Length: {len(full_markdown_content)}")
                    else:
                        logger.info(f"No content parts extracted from {extracted_content_source}.")

            else:
                logger.warning("result.document is not available or is None after conversion.")
            
            # Ensure 'content' key in response is a list of strings
            response_content_parts = []
            if full_markdown_content:
                response_content_parts.append(full_markdown_content)
            
            # If tables were found, add their markdown to the content as well
            if tables:
                for table in tables:
                    if table.get('markdown'):
                        response_content_parts.append(table['markdown'])

            return {
                'success': True,
                'tables': tables,
                'content': response_content_parts, # Use the combined content
                'text': full_markdown_content,  # Add dedicated text field
                'total_tables': len(tables),
                'method': 'docling',
                'file_path': file_path,
                'extract_type': extract_type
            }
            
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': str(e),
                'tables': [],
                'content': [],
                'text': '',
                'total_tables': 0,
                'method': 'docling',
                'file_path': file_path,
                'extract_type': extract_type
            }

    def extract_tables_from_file(self, file_path: str, ocr_engine: str, ocr_langs: List[str]) -> Dict[str, Any]:
        """Legacy method for table extraction only"""
        return self.extract_content_from_file(file_path, ocr_engine, ocr_langs, "tables")

    def _table_to_markdown(self, table_item) -> str:
        """Convert table object (from doc.tables) or markdown table lines to markdown format"""
        try:
            # If table_item is an object with a 'to_markdown' method (ideal case for docling table objects)
            if hasattr(table_item, 'to_markdown') and callable(table_item.to_markdown):
                logger.info(f"Table item (type: {type(table_item)}) has 'to_markdown' method. Calling it.")
                md = table_item.to_markdown()
                if md is not None:
                    return str(md)
                else:
                    logger.warning(f"table_item.to_markdown() returned None for item: {str(table_item)[:100]}")
                    return "" # Explicitly return empty string for None
            
            # Fallback: if table_item has a 'data' attribute (list of lists)
            if hasattr(table_item, 'data'):
                logger.info(f"Table item (type: {type(table_item)}) has 'data' attribute. Using _data_to_markdown.")
                return self._data_to_markdown(table_item.data)

            # Fallback: if table_item itself is a string (could be pre-formatted markdown)
            if isinstance(table_item, str):
                logger.info("Table item is a string. Assuming it's already markdown or part of it.")
                return table_item # Assume it's already markdown

            # Fallback: if table_item is a list (could be from _extract_tables_from_markdown)
            if isinstance(table_item, list):
                logger.info("Table item is a list. Assuming it's lines of a markdown table.")
                return '\n'.join(table_item)
            
            logger.warning(f"_table_to_markdown: Unknown table_item type '{type(table_item)}'. Returning as str: {str(table_item)[:200]}...")
            return str(table_item) # Last resort
        except Exception as e_markdown_conv:
            logger.error(f"Error in _table_to_markdown for item type {type(table_item)}: {e_markdown_conv}")
            logger.error(traceback.format_exc())
            return str(table_item) # Return string representation on error

    def _data_to_markdown(self, data) -> str:
        """Convert table data to markdown format"""
        if not data:
            return ""
        
        try:
            # Assume data is a list of lists (rows)
            if isinstance(data, list) and len(data) > 0:
                markdown = ""
                
                # Header row
                if isinstance(data[0], list):
                    header = "| " + " | ".join(str(cell) for cell in data[0]) + " |"
                    separator = "|" + "|".join([" --- " for _ in data[0]]) + "|"
                    markdown = header + "\n" + separator + "\n"
                    
                    # Data rows
                    for row in data[1:]:
                        if isinstance(row, list):
                            row_md = "| " + " | ".join(str(cell) for cell in row) + " |"
                            markdown += row_md + "\n"
                
                return markdown
        except:
            pass
        
        return str(data)

    def _extract_tables_from_markdown(self, markdown_content: str) -> List[Dict[str, Any]]:
        """Extract tables from markdown content"""
        tables = []
        lines = markdown_content.split('\n')
        
        current_table = []
        table_id = 0
        
        for line in lines:
            line = line.strip()
            if line.startswith('|') and line.endswith('|'):
                current_table.append(line)
            else:
                if current_table and len(current_table) > 2:  # At least header + separator + 1 row
                    table_markdown = '\n'.join(current_table)
                    tables.append({
                        'table_id': table_id,
                        'content': table_markdown,
                        'markdown': table_markdown,
                        'confidence': 0.80
                    })
                    table_id += 1
                current_table = []
        
        # Don't forget the last table
        if current_table and len(current_table) > 2:
            table_markdown = '\n'.join(current_table)
            tables.append({
                'table_id': table_id,
                'content': table_markdown,
                'markdown': table_markdown,
                'confidence': 0.80
            })
        
        return tables

# Initialize extractor
try:
    extractor = DoclingTableExtractor()
except Exception as e:
    logger.error(f"Failed to initialize extractor: {e}")
    extractor = None

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'docling-content-extractor',
        'version': '1.1.0',
        'docling_available': extractor is not None,
        'endpoints': ['/extract_tables', '/extract_text', '/extract_content', '/health']
    })

@app.route('/v1alpha/convert/file', methods=['POST'])
@app.route('/extract_tables', methods=['POST'])
def extract_tables():
    """Extract tables from uploaded document"""
    logger.info(f"Received request on /extract_tables or /v1alpha/convert/file")
    return _process_file_request("tables")

@app.route('/extract_text', methods=['POST'])
def extract_text():
    """Extract text content from uploaded document"""
    logger.info(f"Received request on /extract_text")
    return _process_file_request("text")

@app.route('/extract_content', methods=['POST'])
def extract_content():
    """Extract both text and tables from uploaded document"""
    logger.info(f"Received request on /extract_content")
    return _process_file_request("both")

def _process_file_request(extract_type: str):
    """Common function to process file upload requests"""
    logger.info(f"Request files: {request.files}")
    logger.info(f"Request form data: {request.form}")
    logger.info(f"Request raw data: {request.data}")

    if not extractor:
        return jsonify({
            'success': False,
            'error': 'Docling extractor (DoclingTableExtractor instance) not initialized',
            'tables': [],
            'content': [],
            'text': '',
            'total_tables': 0
        }), 500

    try:
        # Get OCR settings from request form
        ocr_langs_from_request = request.form.getlist('ocr_lang')
        # --- Using TesseractOCR ---
        ocr_engine_from_request = 'tesserocr'

        # Check if file is uploaded
        logger.info(f"Inside main try block. Checking for 'files' in request.files: {'files' in request.files}")
        if 'files' not in request.files:
            logger.warning("'files' key not found in request.files.")
            return jsonify({
                'success': False,
                'error': 'No file uploaded. Expected file in \'files\' field.',
                'tables': [],
                'content': [],
                'text': '',
                'total_tables': 0
            }), 400
        
        file_obj = request.files['files']
        logger.info(f"Retrieved file_obj: type={type(file_obj)}, str='{str(file_obj)}'")
        
        if not hasattr(file_obj, 'filename'):
            logger.error(f"file_obj (type: {type(file_obj)}) does not have a 'filename' attribute.")
            return jsonify({
                'success': False,
                'error': 'Uploaded file object is invalid (missing filename attribute).',
                'tables': [],
                'content': [],
                'text': '',
                'total_tables': 0
                }), 400

        actual_filename = file_obj.filename
        logger.info(f"Actual filename for check: '{actual_filename}', Is empty: {actual_filename == ''}")

        if actual_filename == '':
            logger.warning("Filename is empty after retrieval.")
            return jsonify({
                'success': False,
                'error': 'No file selected (filename is empty).',
                'tables': [],
                'content': [],
                'text': '',
                'total_tables': 0
            }), 400
        
        logger.info(f"File checks passed for filename: '{actual_filename}'. Proceeding to save temporary file.")
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(actual_filename).suffix) as tmp_file:
            file_obj.save(tmp_file.name)
            tmp_file_path = tmp_file.name
        
        try:
            # Call the extractor method with the temp file path and OCR settings
            extraction_result = extractor.extract_content_from_file(tmp_file_path, ocr_engine_from_request, ocr_langs_from_request, extract_type)
            return jsonify(extraction_result)
        finally:
            # Clean up temporary file
            try:
                os.unlink(tmp_file_path)
            except:
                pass
    
    except Exception as e:
        logger.error(f"Error in file processing endpoint: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': str(e),
            'tables': [],
            'content': [],
            'text': '',
            'total_tables': 0
        }), 500

@app.route('/extract_tables_from_path', methods=['POST'])
def extract_tables_from_path():
    """Extract tables from file path (for internal use)"""
    if not extractor:
        return jsonify({
            'success': False,
            'error': 'Docling extractor not initialized',
            'tables': [],
            'content': [],
            'text': '',
            'total_tables': 0
        }), 500
    
    try:
        data = request.get_json()
        if not data or 'file_path' not in data:
            return jsonify({
                'success': False,
                'error': 'file_path required in JSON body',
                'tables': [],
                'content': [],
                'text': '',
                'total_tables': 0
            }), 400
        
        file_path = data['file_path']
        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'error': f'File not found: {file_path}',
                'tables': [],
                'content': [],
                'text': '',
                'total_tables': 0
            }), 404
        
        extract_type = data.get('extract_type', 'tables')
        
        # Extract content
        result = extractor.extract_content_from_file(file_path, 'tesserocr', ['vie'], extract_type)
        return jsonify(result)
    
    except Exception as e:
        logger.error(f"Error in extract_tables_from_path endpoint: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': str(e),
            'tables': [],
            'content': [],
            'text': '',
            'total_tables': 0
        }), 500

if __name__ == '__main__':
    print("🚀 Starting Docling Content Extraction Server...")
    print("📊 Optimized for Vietnamese business documents")
    print("🔗 Server will run on http://localhost:5001")
    print("📋 Endpoints:")
    print("   - GET  /health - Health check")
    print("   - POST /extract_tables - Extract tables from uploaded file")
    print("   - POST /extract_text - Extract text from uploaded file")
    print("   - POST /extract_content - Extract both text and tables")
    print("   - POST /extract_tables_from_path - Extract from file path")
    
    app.run(host='0.0.0.0', port=5001, debug=False)