"""
Gemini 2.5 Flash API Processor for Web Content
Fast and efficient content processing using Google's Gemini API
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List
import time
import asyncio
import aiohttp
from dataclasses import dataclass

try:
    from ..utils.config import config
    from ..utils.logging import LoggerMixin
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from utils.config import config
    from utils.logging import LoggerMixin

logger = logging.getLogger(__name__)

@dataclass
class GeminiConfig:
    """Configuration for Gemini API."""
    api_key: str
    model_name: str = "gemini-2.5-flash-lite"
    api_url: str = "https://generativelanguage.googleapis.com/v1beta/models"
    timeout: int = 30
    max_retries: int = 3

class GeminiProcessor(LoggerMixin):
    """
    Advanced content processor using Google's Gemini 2.5 Flash API for synthesis.
    
    Features:
    - High-quality synthesis with comprehensive analysis
    - Vietnamese content support
    - HTML to Markdown conversion
    - Content summarization and cleaning
    """
    
    def __init__(self, gemini_config: Optional[GeminiConfig] = None):
        self.config = gemini_config or self._load_config()
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Vietnamese-optimized prompts
        self.prompts = {
            "html_to_markdown": """Convert the following HTML content to clean, well-formatted Markdown. Extract ALL main content including article titles, paragraphs, lists, and important information. Preserve Vietnamese text exactly, maintain proper formatting, and remove only navigation, ads, and irrelevant elements. Include as much meaningful content as possible.

HTML Content:
{content}

Markdown Output (include all main content):""",
            
            "full_article": """Extract the COMPLETE article content from the following HTML. Include the full title, all paragraphs, all sections, quotes, lists, and any other meaningful content. Preserve Vietnamese text exactly and format as clean, readable Markdown. Do NOT summarize - extract the ENTIRE article content with all details, maintaining the original structure and information.

HTML Content:
{content}

Complete Article in Markdown (include EVERYTHING):""",
            
            "summarize": """Summarize the following Vietnamese content in a concise manner while preserving key information:

Content:
{content}

Summary:""",
            
            "clean": """Clean and format the following Vietnamese text content to readable Markdown. Extract ALL meaningful content including headlines, articles, and important information. Preserve Vietnamese text exactly and format properly with headers, paragraphs, and lists.

Content:
{content}

Cleaned Markdown (include all meaningful content):"""
        }
        
    def _load_config(self) -> GeminiConfig:
        """Load Gemini configuration from environment variables."""
        api_key = os.getenv("GEMINI_API_KEY", "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM")
        if not api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
            
        return GeminiConfig(
            api_key=api_key,
            model_name=os.getenv("GEMINI_MODEL_NAME", "gemini-2.5-flash-lite"),
            api_url=os.getenv("GEMINI_API_URL", "https://generativelanguage.googleapis.com/v1beta/models"),
            timeout=int(os.getenv("GEMINI_TIMEOUT", "30")),
            max_retries=int(os.getenv("GEMINI_MAX_RETRIES", "3"))
        )
    
    async def initialize(self) -> bool:
        """Initialize the Gemini processor."""
        try:
            if not self.session:
                timeout = aiohttp.ClientTimeout(total=self.config.timeout)
                self.session = aiohttp.ClientSession(timeout=timeout)
            
            logger.info("✅ Gemini processor initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Gemini processor: {e}")
            return False
    
    async def process_batch(self, batch_data: List[Dict[str, str]], query: str = "") -> List[Dict[str, Any]]:
        """
        🚀 BATCH PROCESSING - Process multiple URLs in a single request
        Maximizes 1M input tokens and reduces API calls by 90%

        Args:
            batch_data: List of dicts with 'url' and 'raw_content' keys
            query: Search query for context

        Returns:
            List of processed results for each URL
        """
        if not batch_data:
            return []

        try:
            logger.info(f"🚀 Starting batch processing for {len(batch_data)} URLs")

            # Build optimized batch prompt
            batch_prompt = self._build_batch_prompt(batch_data, query)

            # Single Gemini request for all URLs - HUGE efficiency gain!
            start_time = time.time()

            # Use Google AI SDK directly like in other methods
            import google.generativeai as genai

            # Configure API key
            genai.configure(api_key=self.config.api_key)

            # Create model instance
            model = genai.GenerativeModel(self.config.model_name)

            response = await asyncio.to_thread(
                model.generate_content,
                batch_prompt,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=65536,  # Max output for comprehensive processing
                    temperature=0.1,
                    candidate_count=1
                )
            )

            processing_time = time.time() - start_time

            if response and response.text:
                # Parse batch response
                results = self._parse_batch_response(response.text, batch_data)

                logger.info(f"✅ Batch processing completed: {len(batch_data)} URLs in {processing_time:.2f}s")
                logger.info(f"💰 API efficiency: 1 request instead of {len(batch_data)} requests!")
                return results
            else:
                logger.warning("⚠️ Empty batch response from Gemini")
                return self._create_fallback_batch_results(batch_data)

        except Exception as e:
            logger.error(f"❌ Batch processing failed: {e}")
            return self._create_fallback_batch_results(batch_data)

    def _build_batch_prompt(self, batch_data: List[Dict[str, str]], query: str) -> str:
        """Build optimized batch prompt for multiple URLs"""

        prompt = f"""🎯 BATCH CONTENT PROCESSING
Query: "{query}"

Process and synthesize the following web sources to provide comprehensive analysis.
Focus on cross-source synthesis, insights, and actionable information relevant to the query.
Return structured data for each source in JSON format with enhanced analysis.

REQUIRED OUTPUT FORMAT:
{{
  "batch_results": [
    {{
      "url": "source_url",
      "title": "extracted_title",
      "processed_content": "clean_markdown_content",
      "key_points": ["point1", "point2", "point3"],
      "relevance_score": 0.85
    }}
  ]
}}

SOURCES TO PROCESS:
"""

        # Add each source with clear delimiters
        for i, item in enumerate(batch_data, 1):
            url = item.get('url', f'unknown_url_{i}')
            content = item.get('raw_content', '')

            # Smart truncation to fit within token limits
            max_content_per_source = min(len(content), 40000)  # Reasonable limit per source
            truncated_content = content[:max_content_per_source]

            prompt += f"""
=== SOURCE {i}: {url} ===
{truncated_content}

"""

        prompt += """
🎯 PROCESSING INSTRUCTIONS:
1. Extract clean, relevant content for each source
2. Create informative titles
3. Generate 3-5 key points per source
4. Score relevance to query (0.0-1.0)
5. Format as clean markdown
6. Return complete JSON with all sources

Process all sources and return the structured JSON response.
"""

        return prompt

    def _parse_batch_response(self, response_text: str, batch_data: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """Parse batch response and extract results for each URL"""

        try:
            import json
            import re

            # Find JSON block in response
            json_match = re.search(r'\{.*"batch_results".*\}', response_text, re.DOTALL)

            if json_match:
                json_str = json_match.group(0)
                parsed_data = json.loads(json_str)

                if 'batch_results' in parsed_data:
                    results = []
                    batch_results = parsed_data['batch_results']

                    # Match results to original URLs
                    for i, original_item in enumerate(batch_data):
                        if i < len(batch_results):
                            result = batch_results[i]
                            results.append({
                                'url': original_item['url'],
                                'title': result.get('title', 'No title'),
                                'processed_content': result.get('processed_content', ''),
                                'key_points': result.get('key_points', []),
                                'relevance_score': result.get('relevance_score', 0.5)
                            })
                        else:
                            # Fallback for missing results
                            results.append(self._create_fallback_result(original_item))

                    logger.info(f"✅ Successfully parsed {len(results)} batch results")
                    return results

            # Fallback: try to parse as individual sections
            logger.warning("⚠️ JSON parsing failed, trying fallback parsing")
            return self._parse_batch_response_fallback(response_text, batch_data)

        except Exception as e:
            logger.warning(f"⚠️ Batch response parsing failed: {e}")
            return self._create_fallback_batch_results(batch_data)

    def _parse_batch_response_fallback(self, response_text: str, batch_data: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """Fallback parsing when JSON parsing fails"""

        results = []

        # Split response by source markers
        sections = response_text.split('=== SOURCE')

        for i, original_item in enumerate(batch_data):
            if i + 1 < len(sections):
                section_content = sections[i + 1]

                # Extract title and content from section
                lines = section_content.split('\n')
                title = 'Processed Content'
                content_lines = []

                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('==='):
                        if 'title' not in locals() or title == 'Processed Content':
                            title = line[:100]  # First meaningful line as title
                        content_lines.append(line)

                processed_content = '\n'.join(content_lines[:50])  # Limit content

                results.append({
                    'url': original_item['url'],
                    'title': title,
                    'processed_content': processed_content,
                    'key_points': [],
                    'relevance_score': 0.6
                })
            else:
                results.append(self._create_fallback_result(original_item))

        logger.info(f"✅ Fallback parsing completed for {len(results)} results")
        return results

    def _create_fallback_result(self, original_item: Dict[str, str]) -> Dict[str, Any]:
        """Create fallback result for failed processing"""

        raw_content = original_item.get('raw_content', '')

        # Extract basic title from content
        lines = raw_content.split('\n')
        title = 'Content Available'
        for line in lines[:10]:
            line = line.strip()
            if line and len(line) > 10 and len(line) < 100:
                title = line
                break

        return {
            'url': original_item['url'],
            'title': title,
            'processed_content': raw_content[:2000] if raw_content else 'No content available',
            'key_points': ['Content extracted from source'],
            'relevance_score': 0.3
        }

    def _create_fallback_batch_results(self, batch_data: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """Create fallback results for entire batch"""

        logger.warning(f"⚠️ Creating fallback results for {len(batch_data)} URLs")
        return [self._create_fallback_result(item) for item in batch_data]

    async def cleanup(self) -> None:
        """Cleanup resources."""
        if self.session:
            await self.session.close()
            self.session = None
        logger.info("✅ Gemini processor cleanup completed")
    
    async def process_content(
        self,
        content: str,
        task_type: str = "html_to_markdown",
        max_length: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Process content using Gemini API.
        
        Args:
            content: Input content to process
            task_type: Type of processing (html_to_markdown, summarize, clean)
            max_length: Maximum output length (approximate)
            
        Returns:
            Dictionary with processing results
        """
        start_time = time.time()
        
        try:
            # Prepare prompt
            prompt = self._prepare_prompt(content, task_type, max_length)
            
            # Call Gemini API
            result = await self._call_gemini_api(prompt)
            
            processing_time = time.time() - start_time
            
            logger.info(f"Performance: gemini_processing completed in {processing_time:.3f}s")
            
            return {
                "success": True,
                "original_length": len(content),
                "processed_content": result,
                "output_length": len(result),
                "processing_time": processing_time,
                "model": self.config.model_name,
                "task_type": task_type,
                "performance_metrics": {
                    "api_call_time": processing_time,
                    "model": self.config.model_name,
                    "task_type": task_type
                }
            }
            
        except Exception as e:
            logger.error(f"Gemini processing error: {e}")
            return {
                "success": False,
                "original_length": len(content),
                "processed_content": "",
                "processing_time": 0,
                "model": self.config.model_name,
                "error": str(e),
                "output_length": 0,
                "performance_metrics": {
                    "model": self.config.model_name,
                    "error": str(e)
                }
            }
    
    def _prepare_prompt(self, content: str, task_type: str, max_length: Optional[int]) -> str:
        """Prepare prompt for Gemini API."""
        # Smart truncation for performance
        max_content_length = max_length or 8000  # Gemini can handle longer context
        if len(content) > max_content_length:
            content = self._smart_truncate(content, max_content_length)
        
        # Get appropriate prompt template
        prompt_template = self.prompts.get(task_type, self.prompts["html_to_markdown"])
        return prompt_template.format(content=content)
    
    def _smart_truncate(self, content: str, max_length: int) -> str:
        """Smart content truncation."""
        if len(content) <= max_length:
            return content
        
        # Try to find a good breaking point
        truncated = content[:max_length]
        
        # Vietnamese sentence endings
        vietnamese_endings = [
            '. ', '.\n', '? ', '?\n', '! ', '!\n',
            '." ', '."', '?" ', '?"', '!" ', '!"',
            '.)', '.)', '?)', '?)', '!)', '!)'
        ]
        
        # Find the last complete Vietnamese sentence within limit
        last_sentence_end = -1
        for ending in vietnamese_endings:
            pos = truncated.rfind(ending)
            if pos > last_sentence_end and pos > max_length * 0.6:  # At least 60% of content
                last_sentence_end = pos + len(ending) - 1
        
        if last_sentence_end > 0:
            return content[:last_sentence_end + 1]
        
        # Fallback: find last space to avoid cutting words
        last_space = truncated.rfind(' ')
        if last_space > max_length * 0.8:  # At least 80% of content
            return content[:last_space]
        
        return truncated + "..."
    
    async def _call_gemini_api(self, prompt: str) -> str:
        """Call Gemini API with retry logic."""
        if not self.session:
            await self.initialize()
        
        url = f"{self.config.api_url}/{self.config.model_name}:generateContent"
        params = {"key": self.config.api_key}
        
        headers = {
            "Content-Type": "application/json"
        }
        
        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.0,  # Deterministic for consistency
                "maxOutputTokens": 65536,  # Max 64K tokens for comprehensive content
                "topK": 1,
                "topP": 1.0
            }
        }
        
        for attempt in range(self.config.max_retries + 1):
            try:
                async with self.session.post(url, params=params, headers=headers, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        # Extract generated text
                        if "candidates" in result and len(result["candidates"]) > 0:
                            candidate = result["candidates"][0]
                            if "content" in candidate and "parts" in candidate["content"]:
                                parts = candidate["content"]["parts"]
                                if len(parts) > 0:
                                    return parts[0].get("text", "").strip()
                        
                        # Fallback if no content found
                        return ""
                    
                    elif response.status == 429:
                        # Rate limited, wait and retry
                        if attempt < self.config.max_retries:
                            wait_time = 2 ** attempt  # Exponential backoff
                            logger.warning(f"Rate limited, waiting {wait_time}s before retry {attempt + 1}")
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            raise Exception(f"Rate limited after {self.config.max_retries} retries")
                    
                    else:
                        # Other error
                        error_text = await response.text()
                        raise Exception(f"Gemini API error {response.status}: {error_text}")
                        
            except Exception as e:
                if attempt < self.config.max_retries:
                    wait_time = 2 ** attempt  # Exponential backoff
                    logger.warning(f"Gemini API call failed, waiting {wait_time}s before retry {attempt + 1}: {e}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    raise
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on Gemini processor."""
        try:
            # Test with simple Vietnamese content
            test_content = "<h1>Tin tức công nghệ</h1><p>Trí tuệ nhân tạo đang phát triển mạnh mẽ.</p>"
            test_result = await self.process_content(test_content, "html_to_markdown")
            
            return {
                "status": "healthy" if test_result.get("success") else "error",
                "model_name": self.config.model_name,
                "api_available": True,
                "test_successful": test_result.get("success", False),
                "test_processing_time": test_result.get("processing_time", 0)
            }
            
        except Exception as e:
            logger.error(f"Gemini health check failed: {e}")
            return {
                "status": "error",
                "model_name": self.config.model_name,
                "api_available": False,
                "error": str(e)
            }

# Global Gemini processor instance
_gemini_processor_instance = None

async def get_gemini_processor() -> GeminiProcessor:
    """Get or create global Gemini processor instance."""
    global _gemini_processor_instance
    
    if _gemini_processor_instance is None:
        _gemini_processor_instance = GeminiProcessor()
        await _gemini_processor_instance.initialize()
    
    return _gemini_processor_instance

async def process_content_with_gemini(
    content: str,
    task_type: str = "html_to_markdown",
    max_length: Optional[int] = None
) -> str:
    """
    Process content using Gemini API.
    
    Args:
        content: Input content to process
        task_type: Type of processing (html_to_markdown, summarize, clean)
        max_length: Maximum output length (approximate)
        
    Returns:
        Processed content
    """
    processor = await get_gemini_processor()
    result = await processor.process_content(content, task_type, max_length)
    
    if result.get("success"):
        return result.get("processed_content", "")
    else:
        raise Exception(f"Gemini processing failed: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    # Simple test
    async def test_gemini():
        try:
            processor = GeminiProcessor()
            await processor.initialize()
            
            test_html = """
            <html>
            <head><title>Test</title></head>
            <body>
            <h1>Chào mừng đến với trang web</h1>
            <p>Đây là một đoạn văn bản thử nghiệm bằng tiếng Việt.</p>
            </body>
            </html>
            """
            
            result = await processor.process_content(test_html)
            print(f"Success: {result.get('success')}")
            print(f"Processing time: {result.get('processing_time'):.2f}s")
            print(f"Output length: {result.get('output_length')}")
            print(f"Content preview: {result.get('processed_content', '')[:200]}...")
            
            await processor.cleanup()
            
        except Exception as e:
            print(f"Error: {e}")
    
    asyncio.run(test_gemini())