#!/usr/bin/env python3
"""
Real AI Tokenizer - <PERSON><PERSON><PERSON> hợp với AI backends thực tế
Không hardcode responses, sử dụng AI thật để trả lời câu hỏi
"""

import asyncio
import aiohttp
import json
import logging
from typing import List, Optional, Dict, Any, AsyncGenerator
from dataclasses import dataclass
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TokenizerConfig:
    """Cấu hình cho tokenizer thực tế"""
    backend_type: str = "ollama"  # ollama, openai, huggingface, mediapipe
    backend_url: str = "http://localhost:11434"
    model_name: str = "gemma2"
    api_key: Optional[str] = None
    max_tokens: int = 2048
    temperature: float = 0.7

class RealAITokenizer:
    """
    Tokenizer thực tế tích hợp với AI backends
    Không hardcode, sử dụng AI thật để sinh response
    """
    
    def __init__(self, config: TokenizerConfig):
        self.config = config
        self.vocab_size_val = 262144
        self._session = None
        
        # Special tokens (chuẩn)
        self._bos_id = 2
        self._eos_id = 1
        self._unk_id = 3
        self._pad_id = 0
        
        logger.info(f"🚀 Real AI Tokenizer initialized with {config.backend_type} backend")
    
    async def __aenter__(self):
        """Async context manager entry"""
        self._session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self._session:
            await self._session.close()
    
    # Basic tokenizer interface (tương thích HuggingFace)
    def vocab_size(self) -> int:
        return self.vocab_size_val
    
    @property
    def eos_token_id(self) -> int:
        return self._eos_id
    
    @property
    def bos_token_id(self) -> int:
        return self._bos_id
    
    @property
    def pad_token_id(self) -> int:
        return self._pad_id
    
    @property
    def unk_token_id(self) -> int:
        return self._unk_id
    
    def encode(self, text: str) -> List[int]:
        """Encode text to token IDs (basic implementation)"""
        # Đây chỉ là approximation, thực tế sẽ dùng SentencePiece hoặc tiktoken
        tokens = [self._bos_id]
        for char in text:
            tokens.append(ord(char) % 1000 + 100)
        tokens.append(self._eos_id)
        return tokens
    
    def decode(self, token_ids: List[int], skip_special_tokens: bool = True) -> str:
        """Decode token IDs to text (basic implementation)"""
        filtered_ids = [tid for tid in token_ids 
                       if tid not in [self._bos_id, self._eos_id, self._pad_id]] if skip_special_tokens else token_ids
        return f"Decoded_{len(filtered_ids)}_tokens"
    
    def __call__(self, text: str, **kwargs):
        """HuggingFace compatible interface"""
        input_ids = self.encode(text)
        attention_mask = [1] * len(input_ids)
        
        if kwargs.get('return_tensors') == 'pt':
            try:
                import torch
                return {
                    'input_ids': torch.tensor([input_ids]),
                    'attention_mask': torch.tensor([attention_mask])
                }
            except ImportError:
                pass
        
        return {
            'input_ids': [input_ids],
            'attention_mask': [attention_mask]
        }
    
    # Real AI Integration
    async def generate_response(self, prompt: str, stream: bool = False) -> AsyncGenerator[str, None]:
        """Sinh response thực tế từ AI backend"""
        
        if self.config.backend_type == "ollama":
            async for chunk in self._generate_ollama(prompt, stream):
                yield chunk
        elif self.config.backend_type == "openai":
            async for chunk in self._generate_openai(prompt, stream):
                yield chunk
        elif self.config.backend_type == "huggingface":
            async for chunk in self._generate_huggingface(prompt, stream):
                yield chunk
        else:
            yield f"Backend {self.config.backend_type} chưa được hỗ trợ"
    
    async def _generate_ollama(self, prompt: str, stream: bool) -> AsyncGenerator[str, None]:
        """Tích hợp với Ollama"""
        if not self._session:
            self._session = aiohttp.ClientSession()
        
        payload = {
            "model": self.config.model_name,
            "prompt": prompt,
            "stream": stream,
            "options": {
                "temperature": self.config.temperature,
                "num_predict": self.config.max_tokens
            }
        }
        
                 try:
             async with self._session.post(
                 f"{self.config.backend_url}/api/generate",
                 json=payload
             ) as response:
                 
                 if not stream:
                     result = await response.json()
                     yield result.get("response", "Không có response từ Ollama")
                 else:
                     async for line in response.content:
                         if line:
                             try:
                                 data = json.loads(line.decode())
                                 if "response" in data:
                                     chunk_text = data["response"]
                                     if chunk_text:  # Only yield if there's actual content
                                         yield chunk_text
                                 if data.get("done", False):
                                     break
                             except json.JSONDecodeError:
                                 continue
                                
        except Exception as e:
            logger.error(f"Lỗi Ollama: {e}")
            yield f"Lỗi kết nối đến Ollama: {e}"
    
    async def _generate_openai(self, prompt: str, stream: bool) -> AsyncGenerator[str, None]:
        """Tích hợp với OpenAI API"""
        if not self.config.api_key:
            yield "Thiếu API key cho OpenAI"
            return
        
        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.config.model_name,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature,
            "stream": stream
        }
        
        try:
            if not self._session:
                self._session = aiohttp.ClientSession()
            
            async with self._session.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=payload
            ) as response:
                
                if not stream:
                    result = await response.json()
                    content = result["choices"][0]["message"]["content"]
                    yield content
                else:
                    async for line in response.content:
                        if line:
                            line_str = line.decode().strip()
                            if line_str.startswith("data: "):
                                data_str = line_str[6:]
                                if data_str != "[DONE]":
                                    try:
                                        data = json.loads(data_str)
                                        delta = data["choices"][0]["delta"]
                                        if "content" in delta:
                                            yield delta["content"]
                                    except json.JSONDecodeError:
                                        continue
                                        
        except Exception as e:
            logger.error(f"Lỗi OpenAI: {e}")
            yield f"Lỗi kết nối đến OpenAI: {e}"
    
    async def _generate_huggingface(self, prompt: str, stream: bool) -> AsyncGenerator[str, None]:
        """Tích hợp với HuggingFace Inference API"""
        if not self.config.api_key:
            yield "Thiếu API key cho HuggingFace"
            return
        
        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "inputs": prompt,
            "parameters": {
                "max_new_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "return_full_text": False
            }
        }
        
        try:
            if not self._session:
                self._session = aiohttp.ClientSession()
            
            async with self._session.post(
                f"https://api-inference.huggingface.co/models/{self.config.model_name}",
                headers=headers,
                json=payload
            ) as response:
                
                result = await response.json()
                if isinstance(result, list) and len(result) > 0:
                    generated_text = result[0].get("generated_text", "Không có response")
                    yield generated_text
                else:
                    yield "Không thể sinh response từ HuggingFace"
                    
        except Exception as e:
            logger.error(f"Lỗi HuggingFace: {e}")
            yield f"Lỗi kết nối đến HuggingFace: {e}"

class RealAISession:
    """Session để chat với AI thực tế"""
    
    def __init__(self, tokenizer: RealAITokenizer):
        self.tokenizer = tokenizer
        self.conversation_history = []
        self.session_id = f"session_{int(time.time())}"
    
    async def ask(self, question: str, stream: bool = False) -> AsyncGenerator[str, None]:
        """Hỏi AI và nhận response thực tế"""
        logger.info(f"❓ Question: {question}")
        
        # Thêm context từ lịch sử hội thoại
        context = ""
        if self.conversation_history:
            context = "\n".join([f"Q: {q}\nA: {a}" for q, a in self.conversation_history[-3:]])
            context += f"\nQ: {question}\nA: "
        else:
            context = f"Q: {question}\nA: "
        
        full_response = ""
        async for chunk in self.tokenizer.generate_response(context, stream=stream):
            full_response += chunk
            yield chunk
        
        # Lưu vào lịch sử
        self.conversation_history.append((question, full_response.strip()))
        
        # Giới hạn lịch sử
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]
    
    def reset(self):
        """Reset conversation"""
        self.conversation_history = []
        logger.info("🔄 Conversation reset")

async def test_real_ai_integration():
    """Test tích hợp AI thực tế"""
    print("🤖 Test Real AI Integration")
    print("=" * 50)
    
         # Cấu hình cho Ollama (local)
     config = TokenizerConfig(
         backend_type="ollama",
         backend_url="http://localhost:11434",
         model_name="gemma2:2b",  # Sử dụng model có sẵn
         max_tokens=1024,
         temperature=0.7
     )
    
    async with RealAITokenizer(config) as tokenizer:
        session = RealAISession(tokenizer)
        
        # Test questions
        questions = [
            "bạn có biết AMD có sản phẩm nào nổi tiếng không?",
            "So sánh AMD và Intel",
            "AMD Ryzen có ưu điểm gì?",
            "Xin chào! Bạn là ai?"
        ]
        
        for question in questions:
            print(f"\n❓ {question}")
            print("🤖 ", end="", flush=True)
            
            # Test streaming
            full_response = ""
            async for chunk in session.ask(question, stream=True):
                print(chunk, end="", flush=True)
                full_response += chunk
            
            print(f"\n✅ Complete response: {len(full_response)} characters")
            print("-" * 30)

async def test_multiple_backends():
    """Test nhiều AI backends"""
    print("\n🔄 Test Multiple AI Backends")
    print("=" * 50)
    
         backends = [
         TokenizerConfig(backend_type="ollama", model_name="gemma2:2b"),
         # TokenizerConfig(backend_type="openai", model_name="gpt-3.5-turbo", api_key="your-key"),
         # TokenizerConfig(backend_type="huggingface", model_name="microsoft/DialoGPT-medium", api_key="your-key")
     ]
    
    question = "Bạn biết gì về AMD?"
    
    for config in backends:
        print(f"\n🔧 Testing {config.backend_type} with {config.model_name}")
        
        try:
            async with RealAITokenizer(config) as tokenizer:
                session = RealAISession(tokenizer)
                
                response = ""
                async for chunk in session.ask(question, stream=False):
                    response += chunk
                
                print(f"✅ Response: {response[:100]}...")
                
        except Exception as e:
            print(f"❌ Failed: {e}")

if __name__ == "__main__":
    async def main():
        print("🚀 Real AI Tokenizer - Không hardcode!")
        print("=" * 60)
        
        await test_real_ai_integration()
        await test_multiple_backends()
        
        print("\n✅ Real AI integration test completed!")
        print("💡 Để sử dụng:")
        print("   1. Cài Ollama: curl -fsSL https://ollama.ai/install.sh | sh")
        print("   2. Pull model: ollama pull gemma2")
        print("   3. Chạy script này để test AI thực tế!")
    
    asyncio.run(main()) 