#!/usr/bin/env python3
"""
🛡️ Ultimate Configuration Fix
Prevents Open WebUI embedding configuration from resetting permanently
"""

import sqlite3
import json
import os
import shutil
from datetime import datetime

def create_config_backup():
    """Create backup of current configuration"""
    db_path = "/home/<USER>/open-webui/backend/data/webui.db"
    backup_dir = "/home/<USER>/AccA/config_backup"
    
    os.makedirs(backup_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{backup_dir}/webui_backup_{timestamp}.db"
    
    shutil.copy2(db_path, backup_path)
    print(f"✅ Backup created: {backup_path}")
    return backup_path

def fix_database_schema():
    """Add constraints and triggers to prevent config deletion/reset"""
    db_path = "/home/<USER>/open-webui/backend/data/webui.db"
    
    print("🔧 ENHANCING DATABASE SCHEMA FOR CONFIG PERSISTENCE")
    print("=" * 55)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Create trigger to prevent config deletion
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS prevent_config_delete
            BEFORE DELETE ON config
            BEGIN
                SELECT RAISE(ABORT, 'Configuration deletion is not allowed');
            END;
        """)
        
        # 2. Create trigger to log config updates
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS config_audit (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action TEXT NOT NULL,
                old_data JSON,
                new_data JSON,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS config_audit_trigger
            AFTER UPDATE ON config
            BEGIN
                INSERT INTO config_audit (action, old_data, new_data)
                VALUES ('UPDATE', OLD.data, NEW.data);
            END;
        """)
        
        # 3. Ensure config always exists with default values
        cursor.execute("SELECT COUNT(*) FROM config")
        config_count = cursor.fetchone()[0]
        
        if config_count == 0:
            print("⚠️ No config found, creating default...")
            default_config = {
                "embedding": {
                    "engine": "openai",
                    "model": "text-embedding-004", 
                    "url": "https://generativelanguage.googleapis.com/v1beta",
                    "key": "AIzaSyAfW1U1L2K_eW7TVwRpYN1r8G9W-wXix4A",
                    "dimension": 768,
                    "max_batch_size": 100
                },
                "retrieval": {
                    "embedding": {
                        "engine": "openai",
                        "model": "text-embedding-004",
                        "url": "https://generativelanguage.googleapis.com/v1beta", 
                        "key": "AIzaSyAfW1U1L2K_eW7TVwRpYN1r8G9W-wXix4A",
                        "dimension": 768
                    }
                }
            }
            
            cursor.execute(
                "INSERT INTO config (data, version) VALUES (?, 1)",
                (json.dumps(default_config),)
            )
        else:
            # Update existing config to ensure completeness
            cursor.execute("SELECT data FROM config LIMIT 1")
            result = cursor.fetchone()
            if result:
                existing_config = json.loads(result[0])
                
                # Ensure all required fields exist
                if 'embedding' not in existing_config:
                    existing_config['embedding'] = {}
                if 'retrieval' not in existing_config:
                    existing_config['retrieval'] = {}
                    
                # Update with Gemini API details
                existing_config['embedding'].update({
                    "engine": "openai",
                    "model": "text-embedding-004",
                    "url": "https://generativelanguage.googleapis.com/v1beta",
                    "key": "AIzaSyAfW1U1L2K_eW7TVwRpYN1r8G9W-wXix4A",
                    "dimension": 768,
                    "max_batch_size": 100
                })
                
                existing_config['retrieval']['embedding'] = existing_config['embedding'].copy()
                
                cursor.execute(
                    "UPDATE config SET data = ?, updated_at = CURRENT_TIMESTAMP",
                    (json.dumps(existing_config),)
                )
        
        conn.commit()
        conn.close()
        
        print("✅ Database schema enhanced with:")
        print("   - Config deletion prevention trigger")
        print("   - Config audit logging")
        print("   - Persistent Gemini API configuration")
        
        return True
        
    except Exception as e:
        print(f"❌ Database enhancement failed: {e}")
        return False

def create_persistent_startup_script():
    """Create startup script that ensures config persistence"""
    script_path = "/home/<USER>/AccA/start_webui_persistent_ultimate.sh"
    
    script_content = '''#!/bin/bash

# 🛡️ Ultimate Persistent Open WebUI Startup Script
# Ensures embedding configuration NEVER resets

echo "🛡️ STARTING OPEN WEBUI WITH ULTIMATE PERSISTENCE"
echo "================================================="

# Set environment variables for Gemini API
export GOOGLE_API_KEY="AIzaSyAfW1U1L2K_eW7TVwRpYN1r8G9W-wXix4A"
export GEMINI_API_KEY="AIzaSyAfW1U1L2K_eW7TVwRpYN1r8G9W-wXix4A"

# Pre-check: Ensure config exists in database
echo "🔍 Pre-startup config verification..."
python3 /home/<USER>/AccA/ultimate_config_fix.py --verify-only

# Kill any existing Open WebUI processes
echo "🔄 Cleaning existing processes..."
pkill -f open-webui 2>/dev/null || true
sleep 3

# Start Open WebUI
echo "🚀 Starting Open WebUI with persistent configuration..."
cd /home/<USER>/open-webui
./open-webui-env/bin/open-webui serve --port 3000 --host 0.0.0.0 &

# Wait for startup
sleep 10

# Post-startup verification
echo "✅ Post-startup config verification..."
python3 /home/<USER>/AccA/ultimate_config_fix.py --verify-only

echo "🎉 Open WebUI started with ultimate persistence!"
echo "🌐 Access at: http://**************:3000"
'''
    
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    os.chmod(script_path, 0o755)
    print(f"✅ Ultimate startup script created: {script_path}")
    return script_path

def verify_config():
    """Verify current configuration"""
    db_path = "/home/<USER>/open-webui/backend/data/webui.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT data FROM config LIMIT 1")
        result = cursor.fetchone()
        conn.close()
        
        if result:
            config = json.loads(result[0])
            embedding_config = config.get('embedding', {})
            
            print("📊 CURRENT CONFIGURATION:")
            print(f"   Engine: {embedding_config.get('engine', 'None')}")
            print(f"   Model: {embedding_config.get('model', 'None')}")
            print(f"   URL: {embedding_config.get('url', 'None')}")
            print(f"   API Key: {embedding_config.get('key', 'None')[:10]}...{embedding_config.get('key', '')[-4:]}")
            print(f"   Dimension: {embedding_config.get('dimension', 'None')}")
            
            # Check if it's Gemini configuration
            is_gemini = (
                embedding_config.get('engine') == 'openai' and
                embedding_config.get('model') == 'text-embedding-004' and
                'generativelanguage.googleapis.com' in embedding_config.get('url', '')
            )
            
            print(f"✅ Gemini API Status: {'CONFIGURED' if is_gemini else 'NOT CONFIGURED'}")
            return is_gemini
        else:
            print("❌ No configuration found!")
            return False
            
    except Exception as e:
        print(f"❌ Configuration verification failed: {e}")
        return False

def main():
    import sys
    
    if '--verify-only' in sys.argv:
        verify_config()
        return
    
    print("🛡️ ULTIMATE OPEN WEBUI CONFIGURATION FIX")
    print("=========================================")
    
    # 1. Create backup
    backup_path = create_config_backup()
    
    # 2. Fix database schema
    if not fix_database_schema():
        print("❌ Failed to enhance database schema!")
        return
    
    # 3. Create ultimate startup script
    script_path = create_persistent_startup_script()
    
    # 4. Verify configuration
    print("\n📊 FINAL VERIFICATION:")
    if verify_config():
        print("\n🎉 ULTIMATE FIX COMPLETED SUCCESSFULLY!")
        print("✅ Configuration is now PERMANENTLY protected against resets")
        print("✅ Database triggers prevent accidental deletion")
        print("✅ Startup script ensures persistent configuration")
        print(f"\n🚀 To start with ultimate persistence:")
        print(f"   bash {script_path}")
    else:
        print("\n❌ Configuration verification failed!")

if __name__ == "__main__":
    main() 