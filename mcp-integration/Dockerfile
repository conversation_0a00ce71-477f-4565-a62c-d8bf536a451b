FROM python:3.11-slim

WORKDIR /app

# Install system dependencies for Playwright
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install Playwright browsers
RUN playwright install --with-deps chromium firefox webkit

# Copy all server files
COPY servers/ ./servers/
COPY config/ ./config/

# Expose port 8000 by default for aggregator
EXPOSE 8000

# Copy custom MCPO server
COPY custom_mcpo_server.py .

# Start custom MCPO server on port 8000
ENV PORT=8000
CMD ["python", "custom_mcpo_server.py", "config/mcpo_config_docker.json", "8000"]