{"servers": {"jina_crawler": {"url": "http://localhost:8009", "api_key": "jina-crawler-secret-key-2025"}}, "mcpServers": {"document_processing": {"command": "python3", "args": ["/app/servers/document_processing/server.py"]}, "vietnamese_language": {"command": "python3", "args": ["/app/servers/vietnamese_language/server.py"]}, "web_automation": {"command": "python3", "args": ["/app/servers/web_automation/server_real_browser_bypass.py"], "env": {"NODE_PATH": "/usr/local/lib/node_modules:/app/servers/web_automation/node_modules", "DISPLAY": ":99", "XVFB_DISPLAY": ":99", "REAL_BROWSER": "true", "PYTHONPATH": "/app"}}, "time_utilities": {"command": "python3", "args": ["/app/servers/time_utilities/server.py"]}, "weather_service": {"command": "python3", "args": ["/app/servers/weather_service/server.py"]}, "filesystem": {"command": "python3", "args": ["/app/servers/filesystem/server.py"]}, "wikipedia": {"command": "python3", "args": ["/app/servers/wikipedia/server.py"]}, "sqlite": {"command": "python3", "args": ["/app/servers/sqlite/server.py"]}, "github": {"command": "python3", "args": ["/app/servers/github/server.py"]}, "brave_search": {"command": "python3", "args": ["/app/servers/brave_search/server.py"]}, "gemini_search_engine": {"command": "python3", "args": ["/app/servers/gemini_search_engine/server_with_grounding.py"], "env": {"GEMINI_API_KEY": "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"}}}}