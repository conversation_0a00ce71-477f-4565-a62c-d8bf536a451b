FROM python:3.12-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install additional dependencies for HTTP server
RUN pip install fastapi uvicorn

# Copy server files
COPY . .

# Create HTTP wrapper for MCP server
COPY http_wrapper.py .

# Expose port
EXPOSE 8000

# Run HTTP wrapper
CMD ["python", "http_wrapper.py"]