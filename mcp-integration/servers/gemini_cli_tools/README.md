# Gemini CLI Tools MCP Server

A comprehensive MCP (Model Context Protocol) server that provides powerful tools ported from the Gemini CLI Wrapper. This server offers 11 advanced tools for file operations, web interactions, code analysis, and system commands.

## 🛠️ Available Tools

### Core Tools (6 tools)
- **web_search** - Search the web using Gemini CLI
- **read_file** - Read and analyze files with security controls
- **write_file** - Write content to files with directory creation
- **execute_command** - Execute shell commands (restricted by default)
- **analyze_code** - Analyze code structure and dependencies
- **store_memory** - Store information for conversation context

### Advanced Tools (5 tools)
- **list_directory** - List directory contents with ignore patterns
- **search_file_content** - Search file content with regex patterns
- **glob** - Find files using glob patterns with sorting
- **web_fetch** - Fetch content from multiple URLs (up to 20)
- **read_many_files** - Read multiple files with glob support

## 🔒 Security Features

### RBAC (Role-Based Access Control)
- Configurable security levels
- Dangerous action warnings
- Access logging and monitoring

### Restricted Mode (Default)
- Limited to project directory
- Whitelisted safe commands only
- Path validation and sanitization

### Unrestricted Mode
- Full system access (when enabled)
- All commands allowed
- Absolute path support

## 🚀 Configuration

### Environment Variables

```bash
# Security Settings
GEMINI_UNRESTRICTED_MODE=false    # Enable full system access
GEMINI_RBAC_ENABLED=true          # Enable role-based access control

# Gemini CLI Integration
GEMINI_API_KEY=your_api_key_here  # Required for web_search and web_fetch
```

### Safe Commands (Restricted Mode)
```
ls, pwd, cat, grep, find, head, tail, wc, echo, date, whoami, df, free, ps, top, uname
```

## 📦 Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Ensure Gemini CLI is installed and configured:
```bash
# Install Gemini CLI (if not already installed)
# Configure with your API key
```

3. Run the server:
```bash
python server.py
```

## 🔧 Tool Examples

### Web Search
```json
{
  "name": "web_search",
  "arguments": {
    "query": "latest AI developments 2024",
    "max_results": 5
  }
}
```

### File Operations
```json
{
  "name": "read_many_files",
  "arguments": {
    "paths": ["*.py", "docs/*.md"],
    "include_metadata": true
  }
}
```

### Code Analysis
```json
{
  "name": "analyze_code",
  "arguments": {
    "path": "./src",
    "analysis_type": "structure"
  }
}
```

### Advanced Search
```json
{
  "name": "search_file_content",
  "arguments": {
    "pattern": "class\\s+\\w+",
    "directory": "./src",
    "file_pattern": "*.py",
    "recursive": true
  }
}
```

## 🌟 Features

- **High Performance**: Async/await throughout
- **Memory Management**: TTL-based memory store
- **Error Handling**: Comprehensive error reporting
- **Logging**: Detailed operation logging
- **Flexibility**: Glob pattern support
- **Security**: Multi-level access controls

## 🔍 Tool Categories

| Category | Tools | Description |
|----------|-------|-------------|
| **Web** | web_search, web_fetch | Internet interactions |
| **Filesystem** | read_file, write_file, list_directory, glob, read_many_files | File operations |
| **Search** | search_file_content | Content discovery |
| **System** | execute_command | Command execution |
| **Development** | analyze_code | Code analysis |
| **Memory** | store_memory | Context management |

## ⚠️ Security Considerations

1. **Default Restricted Mode**: Only safe operations allowed
2. **Path Validation**: Prevents directory traversal attacks
3. **Command Whitelisting**: Limited command execution
4. **RBAC Integration**: Role-based permissions
5. **Audit Logging**: All operations logged

## 🚨 Dangerous Tools

The following tools are marked as dangerous and trigger additional logging:
- `execute_command` - Can run system commands
- `write_file` - Can modify filesystem
- `web_fetch` - Can access external resources

## 📝 Logging

All operations are logged with:
- Timestamp and operation details
- Success/failure status
- Security warnings for dangerous actions
- RBAC authorization results

## 🔄 Integration

This server integrates seamlessly with:
- Open WebUI MCP systems
- Gemini CLI for web operations
- Standard MCP clients
- Custom applications via MCP protocol

## 📋 Requirements

- Python 3.8+
- MCP library
- aiofiles for async file operations
- aiohttp for web requests
- Gemini CLI (for web features)

## 🎯 Use Cases

- **Development Workflows**: Code analysis and file management
- **Research Tasks**: Web search and content fetching
- **System Administration**: Safe command execution
- **Content Processing**: Multi-file operations
- **AI Assistants**: Enhanced tool capabilities