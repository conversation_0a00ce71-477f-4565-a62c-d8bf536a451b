#!/usr/bin/env python3
"""
Gemini CLI Tools MCP Server
Comprehensive tool collection ported from Gemini CLI Wrapper
"""

import asyncio
import json
import logging
import os
import subprocess
import glob as glob_module
import re
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse
import aiohttp
import aiofiles

from mcp.server.models import InitializationOptions
from mcp.server import NotificationOptions, Server
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    TextContent,
    Tool,
    INVALID_PARAMS,
    INTERNAL_ERROR
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("gemini-cli-tools")

class GeminiCLIServer:
    def __init__(self):
        self.server = Server("gemini-cli-tools")
        self.memory_store = {}
        self.unrestricted_mode = os.getenv("GEMINI_UNRESTRICTED_MODE", "false").lower() == "true"
        self.project_root = os.getcwd()
        
        # RBAC configuration
        self.rbac_enabled = os.getenv("GEMINI_RBAC_ENABLED", "true").lower() == "true"
        self.dangerous_tools = {
            "execute_command", "write_file"
        }
        
        if self.unrestricted_mode:
            logger.warning("🚨 UNRESTRICTED MODE ENABLED - Full system access allowed")
        else:
            logger.info("🔒 Restricted mode - Limited to project directory and safe commands")
            
        self._register_handlers()

    def _register_handlers(self):
        """Register MCP handlers"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List all available tools"""
            return [
                # Core Tools
                Tool(
                    name="read_file",
                    description="Read and analyze a file in the project directory (or anywhere if unrestricted mode is enabled)",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "path": {
                                "type": "string",
                                "description": "Path to the file (relative or absolute if unrestricted)"
                            },
                            "encoding": {
                                "type": "string",
                                "description": "File encoding",
                                "default": "utf-8"
                            }
                        },
                        "required": ["path"]
                    }
                ),
                Tool(
                    name="write_file",
                    description="Write content to a file in the project directory (or anywhere if unrestricted mode is enabled)",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "path": {
                                "type": "string",
                                "description": "Path to the file (relative or absolute if unrestricted)"
                            },
                            "content": {
                                "type": "string",
                                "description": "Content to write to the file"
                            },
                            "encoding": {
                                "type": "string",
                                "description": "File encoding",
                                "default": "utf-8"
                            }
                        },
                        "required": ["path", "content"]
                    }
                ),
                Tool(
                    name="execute_command",
                    description="Execute a shell command (safely restricted by default, unrestricted if enabled)",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "command": {
                                "type": "string",
                                "description": "The command to execute"
                            },
                            "timeout": {
                                "type": "number",
                                "description": "Timeout in seconds",
                                "default": 30
                            },
                            "working_directory": {
                                "type": "string",
                                "description": "Working directory for command execution"
                            }
                        },
                        "required": ["command"]
                    }
                ),
                Tool(
                    name="analyze_code",
                    description="Analyze code structure and dependencies",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "path": {
                                "type": "string",
                                "description": "Path to analyze (file or directory)"
                            },
                            "analysis_type": {
                                "type": "string",
                                "description": "Type of analysis to perform",
                                "enum": ["structure", "dependencies", "complexity", "security"],
                                "default": "structure"
                            }
                        },
                        "required": ["path"]
                    }
                ),
                Tool(
                    name="store_memory",
                    description="Store information for later retrieval in conversation context",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "key": {
                                "type": "string",
                                "description": "Memory key"
                            },
                            "value": {
                                "type": "string",
                                "description": "Value to store"
                            },
                            "ttl": {
                                "type": "number",
                                "description": "Time to live in seconds",
                                "default": 3600
                            }
                        },
                        "required": ["key", "value"]
                    }
                ),
                # Advanced Tools
                Tool(
                    name="list_directory",
                    description="Lists files and subdirectories in a given directory. Can optionally ignore entries matching provided glob patterns.",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "path": {
                                "type": "string",
                                "description": "Directory path to list (relative or absolute if unrestricted)",
                                "default": "."
                            },
                            "ignore_patterns": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Glob patterns to ignore (e.g., ['*.log', 'node_modules'])"
                            }
                        },
                        "required": ["path"]
                    }
                ),
                Tool(
                    name="search_file_content",
                    description="Searches for a regular expression pattern within the content of files in a specified directory. Returns lines containing matches with file paths and line numbers.",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "pattern": {
                                "type": "string",
                                "description": "Regular expression pattern to search for"
                            },
                            "directory": {
                                "type": "string",
                                "description": "Directory to search in (default: current working directory)",
                                "default": "."
                            },
                            "file_pattern": {
                                "type": "string",
                                "description": "Glob pattern to filter files (e.g., '*.js', '*.md')",
                                "default": "*"
                            },
                            "recursive": {
                                "type": "boolean",
                                "description": "Search recursively in subdirectories",
                                "default": True
                            }
                        },
                        "required": ["pattern"]
                    }
                ),
                Tool(
                    name="glob",
                    description="Efficiently finds files matching specific glob patterns (e.g., src/**/*.ts, **/*.md), returning absolute paths sorted by modification time (newest first).",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "pattern": {
                                "type": "string",
                                "description": "Glob pattern to match files (e.g., 'src/**/*.js', '**/*.md')"
                            },
                            "base_directory": {
                                "type": "string",
                                "description": "Base directory to search from",
                                "default": "."
                            },
                            "sort_by_time": {
                                "type": "boolean",
                                "description": "Sort results by modification time (newest first)",
                                "default": True
                            }
                        },
                        "required": ["pattern"]
                    }
                ),
                Tool(
                    name="read_many_files",
                    description="Reads content from multiple files specified by paths or glob patterns. Concatenates text files and can process images/PDFs if explicitly requested.",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "paths": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Array of file paths or glob patterns"
                            },
                            "target_directory": {
                                "type": "string",
                                "description": "Target directory for relative paths",
                                "default": "."
                            },
                            "include_metadata": {
                                "type": "boolean",
                                "description": "Include file metadata (size, modified time)",
                                "default": True
                            }
                        },
                        "required": ["paths"]
                    }
                )
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> CallToolResult:
            """Handle tool execution"""
            try:
                # RBAC check
                if self.rbac_enabled and name in self.dangerous_tools:
                    logger.warning(f"🚨 DANGEROUS ACTION: Executing {name}")
                
                # Route to appropriate handler
                if name == "read_file":
                    result = await self._read_file(arguments)
                elif name == "write_file":
                    result = await self._write_file(arguments)
                elif name == "execute_command":
                    result = await self._execute_command(arguments)
                elif name == "analyze_code":
                    result = await self._analyze_code(arguments)
                elif name == "store_memory":
                    result = await self._store_memory(arguments)
                elif name == "list_directory":
                    result = await self._list_directory(arguments)
                elif name == "search_file_content":
                    result = await self._search_file_content(arguments)
                elif name == "glob":
                    result = await self._glob(arguments)
                elif name == "read_many_files":
                    result = await self._read_many_files(arguments)
                else:
                    return CallToolResult(
                        content=[TextContent(type="text", text=f"Unknown tool: {name}")],
                        isError=True
                    )

                return CallToolResult(
                    content=[TextContent(type="text", text=json.dumps(result, indent=2, default=str))]
                )

            except Exception as e:
                logger.error(f"Tool {name} failed: {str(e)}")
                return CallToolResult(
                    content=[TextContent(type="text", text=f"Error executing {name}: {str(e)}")],
                    isError=True
                )

    def _validate_path(self, file_path: str) -> str:
        """Validate and resolve file path based on security settings"""
        if self.unrestricted_mode:
            if os.path.isabs(file_path):
                return file_path
            return os.path.abspath(file_path)
        else:
            # Security check - only allow files in project directory
            full_path = os.path.abspath(file_path)
            if not full_path.startswith(self.project_root):
                raise ValueError("Access denied: File outside project directory. Set GEMINI_UNRESTRICTED_MODE=true to allow.")
            return full_path


    async def _read_file(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Read file content"""
        file_path = args["path"]
        encoding = args.get("encoding", "utf-8")
        
        full_path = self._validate_path(file_path)
        
        try:
            async with aiofiles.open(full_path, 'r', encoding=encoding) as f:
                content = await f.read()
            
            stat = os.stat(full_path)
            
            return {
                "success": True,
                "path": file_path,
                "fullPath": full_path,
                "content": content,
                "size": stat.st_size,
                "modified": stat.st_mtime,
                "unrestricted": self.unrestricted_mode,
                "source": "read_file"
            }
            
        except Exception as e:
            raise Exception(f"Failed to read file: {str(e)}")

    async def _write_file(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Write file content"""
        file_path = args["path"]
        content = args["content"]
        encoding = args.get("encoding", "utf-8")
        
        full_path = self._validate_path(file_path)
        
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            
            async with aiofiles.open(full_path, 'w', encoding=encoding) as f:
                await f.write(content)
            
            return {
                "success": True,
                "path": file_path,
                "fullPath": full_path,
                "size": len(content.encode(encoding)),
                "unrestricted": self.unrestricted_mode,
                "source": "write_file"
            }
            
        except Exception as e:
            raise Exception(f"Failed to write file: {str(e)}")

    async def _execute_command(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Execute shell command"""
        command = args["command"]
        timeout = args.get("timeout", 30)
        working_directory = args.get("working_directory")
        
        if self.unrestricted_mode:
            logger.warning(f"🚨 Unrestricted command execution: {command}")
            cwd = working_directory or os.getcwd()
        else:
            # Security: whitelist safe commands
            safe_commands = ['ls', 'pwd', 'cat', 'grep', 'find', 'head', 'tail', 'wc', 'echo', 'date', 'whoami', 'df', 'free', 'ps', 'top', 'uname']
            cmd_parts = command.strip().split()
            base_cmd = cmd_parts[0] if cmd_parts else ""
            
            if base_cmd not in safe_commands:
                raise Exception(f"Command '{base_cmd}' not allowed for security reasons. Set GEMINI_UNRESTRICTED_MODE=true to allow all commands.")
            
            cwd = self.project_root
        
        try:
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=cwd
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=timeout)
            except asyncio.TimeoutError:
                process.kill()
                raise Exception(f"Command timed out after {timeout} seconds")
            
            return {
                "success": process.returncode == 0,
                "command": command,
                "stdout": stdout.decode().strip(),
                "stderr": stderr.decode().strip(),
                "exitCode": process.returncode,
                "unrestricted": self.unrestricted_mode,
                "workingDirectory": cwd,
                "source": "execute_command"
            }
            
        except Exception as e:
            raise Exception(f"Command execution failed: {str(e)}")

    async def _analyze_code(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze code structure"""
        target_path = args["path"]
        analysis_type = args.get("analysis_type", "structure")
        
        full_path = self._validate_path(target_path)
        
        try:
            stat = os.stat(full_path)
            result = {
                "success": True,
                "path": target_path,
                "fullPath": full_path,
                "type": "directory" if os.path.isdir(full_path) else "file",
                "analysis_type": analysis_type,
                "unrestricted": self.unrestricted_mode,
                "source": "analyze_code"
            }
            
            if analysis_type == "structure":
                if os.path.isdir(full_path):
                    result["structure"] = await self._get_directory_structure(full_path)
                else:
                    async with aiofiles.open(full_path, 'r', encoding='utf-8') as f:
                        content = await f.read()
                    result["lines"] = len(content.split('\n'))
                    result["size"] = stat.st_size
            elif analysis_type == "dependencies" and "package.json" in target_path:
                async with aiofiles.open(full_path, 'r', encoding='utf-8') as f:
                    content = await f.read()
                pkg = json.loads(content)
                result["dependencies"] = pkg.get("dependencies", {})
                result["devDependencies"] = pkg.get("devDependencies", {})
            else:
                result["analysis"] = f"Analysis type '{analysis_type}' not yet implemented"
            
            return result
            
        except Exception as e:
            raise Exception(f"Code analysis failed: {str(e)}")

    async def _get_directory_structure(self, dir_path: str, max_depth: int = 3, current_depth: int = 0) -> List[Dict]:
        """Get directory structure recursively"""
        if current_depth >= max_depth:
            return []
        
        try:
            items = []
            for item in os.listdir(dir_path):
                if item.startswith('.'):
                    continue
                
                item_path = os.path.join(dir_path, item)
                stat = os.stat(item_path)
                
                structure_item = {
                    "name": item,
                    "type": "directory" if os.path.isdir(item_path) else "file",
                    "size": stat.st_size,
                    "modified": stat.st_mtime
                }
                
                if os.path.isdir(item_path) and current_depth < max_depth - 1:
                    structure_item["children"] = await self._get_directory_structure(item_path, max_depth, current_depth + 1)
                
                items.append(structure_item)
            
            return items
        except Exception as e:
            logger.warning(f"Failed to read directory {dir_path}: {str(e)}")
            return []

    async def _store_memory(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Store memory for conversation context"""
        key = args["key"]
        value = args["value"]
        ttl = args.get("ttl", 3600)
        
        expires_at = time.time() + ttl
        self.memory_store[key] = {"value": value, "expires_at": expires_at}
        
        # Cleanup expired entries
        current_time = time.time()
        expired_keys = [k for k, v in self.memory_store.items() if v["expires_at"] < current_time]
        for k in expired_keys:
            del self.memory_store[k]
        
        return {
            "success": True,
            "key": key,
            "stored": True,
            "expires_at": expires_at,
            "source": "store_memory"
        }

    async def _list_directory(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """List directory contents"""
        dir_path = args.get("path", ".")
        ignore_patterns = args.get("ignore_patterns", [])
        
        full_path = self._validate_path(dir_path)
        
        try:
            items = []
            for item in os.listdir(full_path):
                # Check ignore patterns
                should_ignore = any(
                    re.match(pattern.replace('*', '.*').replace('?', '.'), item)
                    for pattern in ignore_patterns
                )
                
                if not should_ignore:
                    item_path = os.path.join(full_path, item)
                    stat = os.stat(item_path)
                    
                    items.append({
                        "name": item,
                        "type": "directory" if os.path.isdir(item_path) else "file",
                        "size": stat.st_size,
                        "modified": stat.st_mtime,
                        "path": os.path.relpath(item_path, self.project_root)
                    })
            
            return {
                "success": True,
                "directory": dir_path,
                "fullPath": full_path,
                "items": items,
                "count": len(items),
                "unrestricted": self.unrestricted_mode,
                "source": "list_directory"
            }
            
        except Exception as e:
            raise Exception(f"Failed to list directory: {str(e)}")

    async def _search_file_content(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Search file content with regex"""
        pattern = args["pattern"]
        directory = args.get("directory", ".")
        file_pattern = args.get("file_pattern", "*")
        recursive = args.get("recursive", True)
        
        full_path = self._validate_path(directory)
        
        try:
            regex = re.compile(pattern, re.IGNORECASE)
            file_regex = re.compile(file_pattern.replace('*', '.*').replace('?', '.'))
            results = []
            
            async def search_in_directory(dir_path: str):
                for item in os.listdir(dir_path):
                    item_path = os.path.join(dir_path, item)
                    
                    if os.path.isdir(item_path) and recursive:
                        await search_in_directory(item_path)
                    elif os.path.isfile(item_path) and file_regex.match(item):
                        try:
                            async with aiofiles.open(item_path, 'r', encoding='utf-8') as f:
                                content = await f.read()
                            
                            lines = content.split('\n')
                            for line_num, line in enumerate(lines, 1):
                                matches = regex.findall(line)
                                if matches:
                                    results.append({
                                        "file": os.path.relpath(item_path, self.project_root),
                                        "line": line_num,
                                        "content": line.strip(),
                                        "matches": matches
                                    })
                        except Exception:
                            # Skip files that can't be read as text
                            pass
            
            await search_in_directory(full_path)
            
            return {
                "success": True,
                "pattern": pattern,
                "directory": directory,
                "file_pattern": file_pattern,
                "recursive": recursive,
                "matches": results,
                "count": len(results),
                "unrestricted": self.unrestricted_mode,
                "source": "search_file_content"
            }
            
        except Exception as e:
            raise Exception(f"Search failed: {str(e)}")

    async def _glob(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Find files matching glob patterns"""
        pattern = args["pattern"]
        base_directory = args.get("base_directory", ".")
        sort_by_time = args.get("sort_by_time", True)
        
        full_base_path = self._validate_path(base_directory)
        
        try:
            # Use glob to find matching files
            search_pattern = os.path.join(full_base_path, pattern)
            matches = glob_module.glob(search_pattern, recursive=True)
            
            results = []
            for match in matches:
                if os.path.isfile(match):
                    stat = os.stat(match)
                    results.append({
                        "path": match,
                        "relativePath": os.path.relpath(match, full_base_path),
                        "size": stat.st_size,
                        "modified": stat.st_mtime
                    })
            
            # Sort by modification time if requested
            if sort_by_time:
                results.sort(key=lambda x: x["modified"], reverse=True)
            
            return {
                "success": True,
                "pattern": pattern,
                "base_directory": base_directory,
                "matches": results,
                "count": len(results),
                "sorted_by_time": sort_by_time,
                "unrestricted": self.unrestricted_mode,
                "source": "glob"
            }
            
        except Exception as e:
            raise Exception(f"Glob search failed: {str(e)}")


    async def _read_many_files(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Read multiple files"""
        paths = args["paths"]
        target_directory = args.get("target_directory", ".")
        include_metadata = args.get("include_metadata", True)
        
        full_target_path = self._validate_path(target_directory)
        
        results = []
        concatenated_content = ""
        
        for file_path in paths:
            try:
                # Handle glob patterns
                if '*' in file_path or '?' in file_path:
                    glob_results = await self._glob({
                        "pattern": file_path,
                        "base_directory": target_directory
                    })
                    
                    for match in glob_results["matches"]:
                        async with aiofiles.open(match["path"], 'r', encoding='utf-8') as f:
                            content = await f.read()
                        
                        file_result = {
                            "path": match["relativePath"],
                            "fullPath": match["path"],
                            "content": content,
                            "success": True
                        }
                        
                        if include_metadata:
                            file_result.update({
                                "size": match["size"],
                                "modified": match["modified"]
                            })
                        
                        results.append(file_result)
                        concatenated_content += f"\n--- {match['relativePath']} ---\n{content}\n"
                    continue
                
                # Handle regular file paths# Handle regular file paths
                if os.path.isabs(file_path):
                    full_path = file_path
                else:
                    full_path = os.path.join(full_target_path, file_path)
                
                if not self.unrestricted_mode and not full_path.startswith(self.project_root):
                    raise Exception("Access denied: File outside project directory")
                
                stat = os.stat(full_path)
                async with aiofiles.open(full_path, 'r', encoding='utf-8') as f:
                    content = await f.read()
                
                file_result = {
                    "path": file_path,
                    "fullPath": full_path,
                    "content": content,
                    "success": True
                }
                
                if include_metadata:
                    file_result.update({
                        "size": stat.st_size,
                        "modified": stat.st_mtime
                    })
                
                results.append(file_result)
                concatenated_content += f"\n--- {file_path} ---\n{content}\n"
                
            except Exception as e:
                results.append({
                    "path": file_path,
                    "success": False,
                    "error": str(e)
                })
        
        return {
            "success": True,
            "target_directory": target_directory,
            "paths": paths,
            "files": results,
            "concatenated_content": concatenated_content.strip(),
            "successful_reads": len([r for r in results if r["success"]]),
            "failed_reads": len([r for r in results if not r["success"]]),
            "include_metadata": include_metadata,
            "unrestricted": self.unrestricted_mode,
            "source": "read_many_files"
        }

    def get_memory(self, key: str) -> Optional[str]:
        """Get stored memory value"""
        if key not in self.memory_store:
            return None
        
        stored = self.memory_store[key]
        if stored["expires_at"] < time.time():
            del self.memory_store[key]
            return None
        
        return stored["value"]

    async def run(self):
        """Run the MCP server"""
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="gemini-cli-tools",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={}
                    )
                )
            )

def main():
    """Main entry point"""
    import sys
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stderr)
        ]
    )
    
    # Create and run server
    server = GeminiCLIServer()
    
    try:
        asyncio.run(server.run())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()