#!/usr/bin/env python3
"""
Test script for the fixed real browser MCP server
"""
import asyncio
import json
import sys
from mcp.client.stdio import stdio_client
from mcp.types import ClientCapabilities, InitializationOptions

async def test_real_browser_server():
    """Test the real browser server by launching a browser."""
    try:
        # Use the fixed server
        server_cmd = [
            sys.executable, 
            "server_real_browser_bypass.py"
        ]
        
        async with stdio_client(server_cmd) as (read, write):
            # Initialize the connection
            init_options = InitializationOptions(
                server_name="test-client",
                server_version="1.0.0",
                capabilities=ClientCapabilities(
                    experimental={}
                )
            )
            
            # Send initialization request
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": init_options.dict()
            }
            
            write(init_request)
            await asyncio.sleep(1)
            
            # List tools
            list_tools_request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list"
            }
            
            write(list_tools_request)
            await asyncio.sleep(1)
            
            # Try to launch browser
            launch_request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": "launch_real_browser",
                    "arguments": {
                        "headless": False,
                        "width": 1920,
                        "height": 1080
                    }
                }
            }
            
            write(launch_request)
            await asyncio.sleep(5)  # Give time for browser to launch
            
            print("Test completed")
            
    except Exception as e:
        print(f"Error testing real browser server: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_real_browser_server())