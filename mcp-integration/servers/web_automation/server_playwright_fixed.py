#!/usr/bin/env python3
"""
Web Automation MCP Server using Playwright - Fixed for Headless Environments
Provides web automation capabilities through <PERSON>wright with proper headless fallback
"""

import asyncio
import json
import logging
import os
import subprocess
from typing import Any, Dict, List, Optional, Union
import base64
from urllib.parse import urljoin, urlparse

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    Browser = None
    Page = None
    BrowserContext = None

from mcp.server.models import InitializationOptions
import mcp.types as types
from mcp.server import NotificationOptions, Server
import mcp.server.stdio

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("web-automation-playwright-fixed")

# Global browser instance
playwright_instance = None
browser: Optional[Browser] = None
context: Optional[BrowserContext] = None
current_page: Optional[Page] = None

# Server instance
server = Server("web-automation-fixed")

def detect_display_environment():
    """Detect if we're in a display environment or headless server."""
    # Check for DISPLAY environment variable
    if os.environ.get('DISPLAY'):
        return True
    
    # Check if we're in a Docker container
    if os.path.exists('/.dockerenv'):
        return False
    
    # Check if X server is running
    try:
        result = subprocess.run(['xset', 'q'], capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
        pass
    
    # Check for Wayland
    if os.environ.get('WAYLAND_DISPLAY'):
        return True
    
    # Default to headless for server environments
    return False

def get_browser_args():
    """Get browser arguments optimized for headless server environments."""
    args = [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-features=TranslateUI',
        '--disable-ipc-flooding-protection',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-images',  # Faster loading
        '--disable-javascript',  # Can be overridden if needed
        '--disable-default-apps',
        '--disable-sync',
        '--disable-background-networking',
        '--disable-background-timer-throttling',
        '--disable-client-side-phishing-detection',
        '--disable-component-update',
        '--disable-domain-reliability',
        '--disable-features=AudioServiceOutOfProcess',
        '--disable-hang-monitor',
        '--disable-prompt-on-repost',
        '--disable-web-security',
        '--metrics-recording-only',
        '--no-first-run',
        '--safebrowsing-disable-auto-update',
        '--enable-automation',
        '--password-store=basic',
        '--use-mock-keychain'
    ]
    
    # Add memory optimization for server environments
    args.extend([
        '--memory-pressure-off',
        '--max_old_space_size=4096'
    ])
    
    return args

@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """List available web automation tools."""
    if not PLAYWRIGHT_AVAILABLE:
        return [
            types.Tool(
                name="check_dependencies",
                description="Check if web automation dependencies are available",
                inputSchema={
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            )
        ]
    
    return [
        types.Tool(
            name="launch_browser",
            description="Launch a new browser instance (automatically uses headless mode in server environments)",
            inputSchema={
                "type": "object",
                "properties": {
                    "headless": {
                        "type": "boolean",
                        "description": "Run browser in headless mode (auto-detected for server environments)",
                        "default": True
                    },
                    "width": {
                        "type": "integer",
                        "description": "Browser window width",
                        "default": 1280
                    },
                    "height": {
                        "type": "integer",
                        "description": "Browser window height",
                        "default": 720
                    },
                    "browser_type": {
                        "type": "string",
                        "description": "Browser type to use",
                        "enum": ["chromium", "firefox", "webkit"],
                        "default": "chromium"
                    },
                    "enable_javascript": {
                        "type": "boolean",
                        "description": "Enable JavaScript execution",
                        "default": True
                    }
                },
                "required": []
            }
        ),
        types.Tool(
            name="navigate_to_url",
            description="Navigate to a specific URL",
            inputSchema={
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "URL to navigate to"
                    },
                    "wait_until": {
                        "type": "string",
                        "description": "When to consider navigation complete",
                        "enum": ["load", "domcontentloaded", "networkidle"],
                        "default": "load"
                    }
                },
                "required": ["url"]
            }
        ),
        types.Tool(
            name="take_screenshot",
            description="Take a screenshot of the current page",
            inputSchema={
                "type": "object",
                "properties": {
                    "full_page": {
                        "type": "boolean",
                        "description": "Capture full page screenshot",
                        "default": False
                    },
                    "element_selector": {
                        "type": "string",
                        "description": "CSS selector of element to screenshot (optional)"
                    }
                },
                "required": []
            }
        ),
        types.Tool(
            name="click_element",
            description="Click on an element",
            inputSchema={
                "type": "object",
                "properties": {
                    "selector": {
                        "type": "string",
                        "description": "CSS selector of element to click"
                    },
                    "wait_for_selector": {
                        "type": "boolean",
                        "description": "Wait for selector to be available",
                        "default": True
                    },
                    "timeout": {
                        "type": "integer",
                        "description": "Timeout in milliseconds",
                        "default": 30000
                    }
                },
                "required": ["selector"]
            }
        ),
        types.Tool(
            name="type_text",
            description="Type text into an input field",
            inputSchema={
                "type": "object",
                "properties": {
                    "selector": {
                        "type": "string",
                        "description": "CSS selector of input element"
                    },
                    "text": {
                        "type": "string",
                        "description": "Text to type"
                    },
                    "clear_first": {
                        "type": "boolean",
                        "description": "Clear field before typing",
                        "default": True
                    }
                },
                "required": ["selector", "text"]
            }
        ),
        types.Tool(
            name="get_page_content",
            description="Get page content (HTML or text)",
            inputSchema={
                "type": "object",
                "properties": {
                    "content_type": {
                        "type": "string",
                        "description": "Type of content to retrieve",
                        "enum": ["html", "text", "title"],
                        "default": "html"
                    }
                },
                "required": []
            }
        ),
        types.Tool(
            name="wait_for_element",
            description="Wait for an element to appear on the page",
            inputSchema={
                "type": "object",
                "properties": {
                    "selector": {
                        "type": "string",
                        "description": "CSS selector to wait for"
                    },
                    "timeout": {
                        "type": "integer",
                        "description": "Timeout in milliseconds",
                        "default": 30000
                    }
                },
                "required": ["selector"]
            }
        ),
        types.Tool(
            name="execute_javascript",
            description="Execute JavaScript code on the page",
            inputSchema={
                "type": "object",
                "properties": {
                    "code": {
                        "type": "string",
                        "description": "JavaScript code to execute"
                    }
                },
                "required": ["code"]
            }
        ),
        types.Tool(
            name="close_browser",
            description="Close the browser instance",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),
        types.Tool(
            name="get_page_info",
            description="Get current page information",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),
        types.Tool(
            name="check_environment",
            description="Check the current display environment and browser capabilities",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: dict) -> list[types.TextContent]:
    """Handle tool calls for web automation."""
    global playwright_instance, browser, context, current_page
    
    try:
        if name == "check_dependencies":
            if PLAYWRIGHT_AVAILABLE:
                return [types.TextContent(
                    type="text",
                    text="✅ Playwright is available and ready for web automation"
                )]
            else:
                return [types.TextContent(
                    type="text", 
                    text="❌ Playwright is not available. Install with: pip install playwright && playwright install"
                )]
        
        if name == "check_environment":
            has_display = detect_display_environment()
            display_info = os.environ.get('DISPLAY', 'Not set')
            docker_env = os.path.exists('/.dockerenv')
            
            env_info = f"""🔍 Environment Check:
- Display available: {'✅ Yes' if has_display else '❌ No (headless mode required)'}
- DISPLAY variable: {display_info}
- Docker environment: {'✅ Yes' if docker_env else '❌ No'}
- Playwright available: {'✅ Yes' if PLAYWRIGHT_AVAILABLE else '❌ No'}

Recommended mode: {'Headed (if needed)' if has_display else 'Headless (forced)'}"""
            
            return [types.TextContent(type="text", text=env_info)]
        
        if not PLAYWRIGHT_AVAILABLE:
            return [types.TextContent(
                type="text",
                text="❌ Playwright is not available. Cannot perform web automation."
            )]
        
        if name == "launch_browser":
            headless = arguments.get("headless", True)
            width = arguments.get("width", 1280)
            height = arguments.get("height", 720)
            browser_type = arguments.get("browser_type", "chromium")
            enable_javascript = arguments.get("enable_javascript", True)
            
            # Force headless mode if no display is available
            has_display = detect_display_environment()
            if not has_display:
                headless = True
                logger.info("No display detected, forcing headless mode")
            
            # Close existing browser if any
            if browser:
                await browser.close()
            
            if playwright_instance:
                await playwright_instance.stop()
            
            # Launch new playwright instance
            playwright_instance = await async_playwright().start()
            
            # Get optimized browser arguments
            browser_args = get_browser_args()
            
            # Enable JavaScript if requested
            if enable_javascript:
                browser_args = [arg for arg in browser_args if arg != '--disable-javascript']
            
            # Launch browser with proper error handling
            try:
                if browser_type == "firefox":
                    browser = await playwright_instance.firefox.launch(
                        headless=headless,
                        args=browser_args
                    )
                elif browser_type == "webkit":
                    browser = await playwright_instance.webkit.launch(
                        headless=headless,
                        args=browser_args
                    )
                else:  # chromium (default)
                    browser = await playwright_instance.chromium.launch(
                        headless=headless,
                        args=browser_args
                    )
                
                # Create context and page
                context = await browser.new_context(
                    viewport={'width': width, 'height': height},
                    ignore_https_errors=True,
                    java_script_enabled=enable_javascript
                )
                current_page = await context.new_page()
                
                mode_info = "headless" if headless else "headed"
                display_status = "✅ Display available" if has_display else "❌ No display (forced headless)"
                
                return [types.TextContent(
                    type="text",
                    text=f"✅ Browser launched successfully\n- Type: {browser_type}\n- Mode: {mode_info}\n- Size: {width}x{height}\n- JavaScript: {'enabled' if enable_javascript else 'disabled'}\n- Environment: {display_status}"
                )]
                
            except Exception as e:
                # If headed mode fails, try headless
                if not headless:
                    logger.warning(f"Headed mode failed ({e}), trying headless mode")
                    try:
                        if browser_type == "firefox":
                            browser = await playwright_instance.firefox.launch(
                                headless=True,
                                args=browser_args
                            )
                        elif browser_type == "webkit":
                            browser = await playwright_instance.webkit.launch(
                                headless=True,
                                args=browser_args
                            )
                        else:  # chromium (default)
                            browser = await playwright_instance.chromium.launch(
                                headless=True,
                                args=browser_args
                            )
                        
                        context = await browser.new_context(
                            viewport={'width': width, 'height': height},
                            ignore_https_errors=True,
                            java_script_enabled=enable_javascript
                        )
                        current_page = await context.new_page()
                        
                        return [types.TextContent(
                            type="text",
                            text=f"✅ Browser launched in headless mode (fallback)\n- Type: {browser_type}\n- Size: {width}x{height}\n- JavaScript: {'enabled' if enable_javascript else 'disabled'}\n- Note: Headed mode failed, using headless"
                        )]
                    except Exception as fallback_error:
                        raise Exception(f"Both headed and headless modes failed. Headed: {e}, Headless: {fallback_error}")
                else:
                    raise e
        
        if not browser or not current_page:
            return [types.TextContent(
                type="text",
                text="❌ No browser instance available. Please launch browser first."
            )]
        
        if name == "navigate_to_url":
            url = arguments["url"]
            wait_until = arguments.get("wait_until", "load")
            
            await current_page.goto(url, wait_until=wait_until, timeout=60000)
            title = await current_page.title()
            
            return [types.TextContent(
                type="text",
                text=f"✅ Navigated to: {url}\nPage title: {title}"
            )]
        
        elif name == "take_screenshot":
            full_page = arguments.get("full_page", False)
            element_selector = arguments.get("element_selector")
            
            if element_selector:
                element = await current_page.query_selector(element_selector)
                if element:
                    screenshot = await element.screenshot()
                else:
                    return [types.TextContent(
                        type="text",
                        text=f"❌ Element not found: {element_selector}"
                    )]
            else:
                screenshot = await current_page.screenshot(full_page=full_page)
            
            # Convert to base64
            screenshot_b64 = base64.b64encode(screenshot).decode()
            
            return [types.TextContent(
                type="text",
                text=f"✅ Screenshot captured (base64 length: {len(screenshot_b64)})\nBase64 data: data:image/png;base64,{screenshot_b64[:100]}..."
            )]
        
        elif name == "click_element":
            selector = arguments["selector"]
            wait_for_selector = arguments.get("wait_for_selector", True)
            timeout = arguments.get("timeout", 30000)
            
            if wait_for_selector:
                await current_page.wait_for_selector(selector, timeout=timeout)
            
            await current_page.click(selector)
            
            return [types.TextContent(
                type="text",
                text=f"✅ Clicked element: {selector}"
            )]
        
        elif name == "type_text":
            selector = arguments["selector"]
            text = arguments["text"]
            clear_first = arguments.get("clear_first", True)
            
            await current_page.wait_for_selector(selector)
            
            if clear_first:
                await current_page.fill(selector, "")  # Clear field
            
            await current_page.type(selector, text)
            
            return [types.TextContent(
                type="text",
                text=f"✅ Typed text into {selector}: {text}"
            )]
        
        elif name == "get_page_content":
            content_type = arguments.get("content_type", "html")
            
            if content_type == "html":
                content = await current_page.content()
            elif content_type == "text":
                content = await current_page.evaluate('() => document.body.innerText')
            elif content_type == "title":
                content = await current_page.title()
            else:
                return [types.TextContent(
                    type="text",
                    text=f"❌ Invalid content type: {content_type}"
                )]
            
            return [types.TextContent(
                type="text",
                text=f"✅ Page {content_type}:\n{content}"
            )]
        
        elif name == "wait_for_element":
            selector = arguments["selector"]
            timeout = arguments.get("timeout", 30000)
            
            await current_page.wait_for_selector(selector, timeout=timeout)
            
            return [types.TextContent(
                type="text",
                text=f"✅ Element appeared: {selector}"
            )]
        
        elif name == "execute_javascript":
            code = arguments["code"]
            
            result = await current_page.evaluate(code)
            
            return [types.TextContent(
                type="text",
                text=f"✅ JavaScript executed successfully\nResult: {result}"
            )]
        
        elif name == "get_page_info":
            url = current_page.url
            title = await current_page.title()
            
            return [types.TextContent(
                type="text",
                text=f"✅ Current page info:\nURL: {url}\nTitle: {title}"
            )]
        
        elif name == "close_browser":
            if browser:
                await browser.close()
                browser = None
                context = None
                current_page = None
            
            if playwright_instance:
                await playwright_instance.stop()
                playwright_instance = None
            
            return [types.TextContent(
                type="text",
                text="✅ Browser closed successfully"
            )]
        
        else:
            return [types.TextContent(
                type="text",
                text=f"❌ Unknown tool: {name}"
            )]
    
    except Exception as e:
        logger.error(f"Error in {name}: {str(e)}")
        return [types.TextContent(
            type="text",
            text=f"❌ Error in {name}: {str(e)}"
        )]

async def main():
    # Run the server using stdin/stdout streams
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="web-automation-fixed",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )

if __name__ == "__main__":
    asyncio.run(main())