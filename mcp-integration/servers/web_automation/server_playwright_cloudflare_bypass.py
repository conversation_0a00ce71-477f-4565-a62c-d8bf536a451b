#!/usr/bin/env python3
"""
Web Automation MCP Server with Cloudflare Bypass
Provides web automation capabilities with advanced anti-detection and Cloudflare bypass
"""

import asyncio
import json
import logging
import os
import random
import subprocess
import time
from typing import Any, Dict, List, Optional, Union
import base64
from urllib.parse import urljoin, urlparse

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    Browser = None
    Page = None
    BrowserContext = None

from mcp.server.models import InitializationOptions
import mcp.types as types
from mcp.server import NotificationOptions, Server
import mcp.server.stdio

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("web-automation-cloudflare-bypass")

# Global browser instance
playwright_instance = None
browser: Optional[Browser] = None
context: Optional[BrowserContext] = None
current_page: Optional[Page] = None

# Server instance
server = Server("web-automation-cloudflare-bypass")

# User agents for rotation
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0",
]

def detect_display_environment():
    """Detect if we're in a display environment or headless server."""
    if os.environ.get('DISPLAY'):
        return True
    if os.path.exists('/.dockerenv'):
        return False
    try:
        result = subprocess.run(['xset', 'q'], capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
        pass
    if os.environ.get('WAYLAND_DISPLAY'):
        return True
    return False

def get_stealth_browser_args():
    """Get browser arguments optimized for stealth and Cloudflare bypass."""
    args = [
        # Basic stealth
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-blink-features=AutomationControlled',
        '--disable-features=VizDisplayCompositor',
        
        # Anti-detection
        '--disable-web-security',
        '--disable-features=TranslateUI',
        '--disable-ipc-flooding-protection',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows',
        '--disable-background-timer-throttling',
        '--disable-background-networking',
        '--disable-client-side-phishing-detection',
        '--disable-component-update',
        '--disable-default-apps',
        '--disable-domain-reliability',
        '--disable-extensions',
        '--disable-features=AudioServiceOutOfProcess',
        '--disable-hang-monitor',
        '--disable-prompt-on-repost',
        '--disable-sync',
        '--metrics-recording-only',
        '--no-first-run',
        '--safebrowsing-disable-auto-update',
        '--enable-automation',
        '--password-store=basic',
        '--use-mock-keychain',
        
        # Performance
        '--memory-pressure-off',
        '--max_old_space_size=4096',
        
        # Cloudflare specific
        '--disable-features=VizDisplayCompositor,VizHitTestSurfaceLayer',
        '--disable-background-timer-throttling',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows',
        '--disable-features=TranslateUI,BlinkGenPropertyTrees',
        '--disable-ipc-flooding-protection',
        '--enable-features=NetworkService,NetworkServiceLogging',
        '--force-color-profile=srgb',
        '--disable-features=AudioServiceOutOfProcess,VizDisplayCompositor',
    ]
    
    return args

async def setup_stealth_page(page: Page):
    """Setup page with stealth configurations to bypass detection."""
    
    # Remove webdriver property
    await page.add_init_script("""
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
    """)
    
    # Override plugins
    await page.add_init_script("""
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
    """)
    
    # Override languages
    await page.add_init_script("""
        Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en'],
        });
    """)
    
    # Override permissions
    await page.add_init_script("""
        const originalQuery = window.navigator.permissions.query;
        return window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );
    """)
    
    # Override chrome property
    await page.add_init_script("""
        window.chrome = {
            runtime: {},
        };
    """)
    
    # Override iframe contentWindow
    await page.add_init_script("""
        const getParameter = WebGLRenderingContext.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
            if (parameter === 37445) {
                return 'Intel Inc.';
            }
            if (parameter === 37446) {
                return 'Intel Iris OpenGL Engine';
            }
            return getParameter(parameter);
        };
    """)

async def wait_for_cloudflare_challenge(page: Page, timeout: int = 30000):
    """Wait for Cloudflare challenge to complete."""
    try:
        # Wait for either success or challenge page
        await page.wait_for_load_state('networkidle', timeout=timeout)
        
        # Check if we're on a Cloudflare challenge page
        cf_challenge = await page.query_selector('[data-ray]')
        if cf_challenge:
            logger.info("Cloudflare challenge detected, waiting for completion...")
            
            # Wait for challenge to complete (up to 30 seconds)
            start_time = time.time()
            while time.time() - start_time < 30:
                try:
                    # Check if challenge is still present
                    cf_check = await page.query_selector('[data-ray]')
                    if not cf_check:
                        logger.info("Cloudflare challenge completed!")
                        break
                    
                    # Wait a bit before checking again
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    logger.debug(f"Challenge check error: {e}")
                    break
            
            # Final wait for page to stabilize
            await page.wait_for_load_state('networkidle', timeout=10000)
            
        return True
        
    except Exception as e:
        logger.warning(f"Cloudflare challenge handling error: {e}")
        return False

@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """List available web automation tools with Cloudflare bypass."""
    if not PLAYWRIGHT_AVAILABLE:
        return [
            types.Tool(
                name="check_dependencies",
                description="Check if web automation dependencies are available",
                inputSchema={
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            )
        ]
    
    return [
        types.Tool(
            name="launch_stealth_browser",
            description="Launch a stealth browser optimized for Cloudflare bypass",
            inputSchema={
                "type": "object",
                "properties": {
                    "headless": {
                        "type": "boolean",
                        "description": "Run browser in headless mode (auto-detected for server environments)",
                        "default": True
                    },
                    "width": {
                        "type": "integer",
                        "description": "Browser window width",
                        "default": 1920
                    },
                    "height": {
                        "type": "integer",
                        "description": "Browser window height",
                        "default": 1080
                    },
                    "browser_type": {
                        "type": "string",
                        "description": "Browser type to use",
                        "enum": ["chromium", "firefox"],
                        "default": "chromium"
                    },
                    "proxy": {
                        "type": "string",
                        "description": "Proxy server (format: http://host:port or socks5://host:port)"
                    },
                    "user_agent": {
                        "type": "string",
                        "description": "Custom user agent (random if not specified)"
                    }
                },
                "required": []
            }
        ),
        types.Tool(
            name="navigate_with_bypass",
            description="Navigate to URL with Cloudflare bypass techniques",
            inputSchema={
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "URL to navigate to"
                    },
                    "wait_for_challenge": {
                        "type": "boolean",
                        "description": "Wait for Cloudflare challenge to complete",
                        "default": True
                    },
                    "max_retries": {
                        "type": "integer",
                        "description": "Maximum number of retry attempts",
                        "default": 3
                    },
                    "delay_range": {
                        "type": "array",
                        "description": "Random delay range in seconds [min, max]",
                        "items": {"type": "number"},
                        "default": [1, 3]
                    }
                },
                "required": ["url"]
            }
        ),
        types.Tool(
            name="bypass_cloudflare_challenge",
            description="Specifically handle Cloudflare challenge on current page",
            inputSchema={
                "type": "object",
                "properties": {
                    "timeout": {
                        "type": "integer",
                        "description": "Timeout in milliseconds",
                        "default": 30000
                    }
                },
                "required": []
            }
        ),
        types.Tool(
            name="take_screenshot",
            description="Take a screenshot of the current page",
            inputSchema={
                "type": "object",
                "properties": {
                    "full_page": {
                        "type": "boolean",
                        "description": "Capture full page screenshot",
                        "default": False
                    },
                    "element_selector": {
                        "type": "string",
                        "description": "CSS selector of element to screenshot (optional)"
                    }
                },
                "required": []
            }
        ),
        types.Tool(
            name="click_element",
            description="Click on an element with human-like behavior",
            inputSchema={
                "type": "object",
                "properties": {
                    "selector": {
                        "type": "string",
                        "description": "CSS selector of element to click"
                    },
                    "human_like": {
                        "type": "boolean",
                        "description": "Use human-like clicking behavior",
                        "default": True
                    },
                    "timeout": {
                        "type": "integer",
                        "description": "Timeout in milliseconds",
                        "default": 30000
                    }
                },
                "required": ["selector"]
            }
        ),
        types.Tool(
            name="type_text_human",
            description="Type text with human-like typing patterns",
            inputSchema={
                "type": "object",
                "properties": {
                    "selector": {
                        "type": "string",
                        "description": "CSS selector of input element"
                    },
                    "text": {
                        "type": "string",
                        "description": "Text to type"
                    },
                    "human_like": {
                        "type": "boolean",
                        "description": "Use human-like typing speed and patterns",
                        "default": True
                    },
                    "clear_first": {
                        "type": "boolean",
                        "description": "Clear field before typing",
                        "default": True
                    }
                },
                "required": ["selector", "text"]
            }
        ),
        types.Tool(
            name="get_page_content",
            description="Get page content (HTML or text)",
            inputSchema={
                "type": "object",
                "properties": {
                    "content_type": {
                        "type": "string",
                        "description": "Type of content to retrieve",
                        "enum": ["html", "text", "title"],
                        "default": "html"
                    }
                },
                "required": []
            }
        ),
        types.Tool(
            name="wait_for_element",
            description="Wait for an element to appear on the page",
            inputSchema={
                "type": "object",
                "properties": {
                    "selector": {
                        "type": "string",
                        "description": "CSS selector to wait for"
                    },
                    "timeout": {
                        "type": "integer",
                        "description": "Timeout in milliseconds",
                        "default": 30000
                    }
                },
                "required": ["selector"]
            }
        ),
        types.Tool(
            name="execute_javascript",
            description="Execute JavaScript code on the page",
            inputSchema={
                "type": "object",
                "properties": {
                    "code": {
                        "type": "string",
                        "description": "JavaScript code to execute"
                    }
                },
                "required": ["code"]
            }
        ),
        types.Tool(
            name="close_browser",
            description="Close the browser instance",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),
        types.Tool(
            name="get_page_info",
            description="Get current page information including Cloudflare status",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),
        types.Tool(
            name="check_cloudflare_protection",
            description="Check if current page has Cloudflare protection",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: dict) -> list[types.TextContent]:
    """Handle tool calls for web automation with Cloudflare bypass."""
    global playwright_instance, browser, context, current_page
    
    try:
        if name == "check_dependencies":
            if PLAYWRIGHT_AVAILABLE:
                return [types.TextContent(
                    type="text",
                    text="✅ Playwright is available with Cloudflare bypass capabilities"
                )]
            else:
                return [types.TextContent(
                    type="text", 
                    text="❌ Playwright is not available. Install with: pip install playwright && playwright install"
                )]
        
        if not PLAYWRIGHT_AVAILABLE:
            return [types.TextContent(
                type="text",
                text="❌ Playwright is not available. Cannot perform web automation."
            )]
        
        if name == "launch_stealth_browser":
            headless = arguments.get("headless", True)
            width = arguments.get("width", 1920)
            height = arguments.get("height", 1080)
            browser_type = arguments.get("browser_type", "chromium")
            proxy = arguments.get("proxy")
            user_agent = arguments.get("user_agent")
            
            # Force headless mode if no display is available
            has_display = detect_display_environment()
            if not has_display:
                headless = True
                logger.info("No display detected, forcing headless mode")
            
            # Close existing browser if any
            if browser:
                await browser.close()
            if playwright_instance:
                await playwright_instance.stop()
            
            # Launch new playwright instance
            playwright_instance = await async_playwright().start()
            
            # Get stealth browser arguments
            browser_args = get_stealth_browser_args()
            
            # Setup proxy if provided
            proxy_config = None
            if proxy:
                proxy_config = {"server": proxy}
            
            # Select random user agent if not provided
            if not user_agent:
                user_agent = random.choice(USER_AGENTS)
            
            try:
                if browser_type == "firefox":
                    browser = await playwright_instance.firefox.launch(
                        headless=headless,
                        args=browser_args,
                        proxy=proxy_config
                    )
                else:  # chromium (default)
                    browser = await playwright_instance.chromium.launch(
                        headless=headless,
                        args=browser_args,
                        proxy=proxy_config
                    )
                
                # Create context with stealth settings
                context = await browser.new_context(
                    viewport={'width': width, 'height': height},
                    user_agent=user_agent,
                    ignore_https_errors=True,
                    java_script_enabled=True,
                    # Additional stealth settings
                    extra_http_headers={
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'DNT': '1',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                    }
                )
                
                current_page = await context.new_page()
                
                # Setup stealth configurations
                await setup_stealth_page(current_page)
                
                mode_info = "headless" if headless else "headed"
                display_status = "✅ Display available" if has_display else "❌ No display (forced headless)"
                proxy_info = f"✅ Proxy: {proxy}" if proxy else "❌ No proxy"
                
                return [types.TextContent(
                    type="text",
                    text=f"✅ Stealth browser launched successfully\n- Type: {browser_type}\n- Mode: {mode_info}\n- Size: {width}x{height}\n- User Agent: {user_agent[:50]}...\n- Environment: {display_status}\n- {proxy_info}\n- Cloudflare bypass: ✅ Enabled"
                )]
                
            except Exception as e:
                if not headless:
                    logger.warning(f"Headed mode failed ({e}), trying headless mode")
                    try:
                        if browser_type == "firefox":
                            browser = await playwright_instance.firefox.launch(
                                headless=True,
                                args=browser_args,
                                proxy=proxy_config
                            )
                        else:
                            browser = await playwright_instance.chromium.launch(
                                headless=True,
                                args=browser_args,
                                proxy=proxy_config
                            )
                        
                        context = await browser.new_context(
                            viewport={'width': width, 'height': height},
                            user_agent=user_agent,
                            ignore_https_errors=True,
                            java_script_enabled=True
                        )
                        current_page = await context.new_page()
                        await setup_stealth_page(current_page)
                        
                        return [types.TextContent(
                            type="text",
                            text=f"✅ Stealth browser launched in headless mode (fallback)\n- Type: {browser_type}\n- Size: {width}x{height}\n- Cloudflare bypass: ✅ Enabled"
                        )]
                    except Exception as fallback_error:
                        raise Exception(f"Both headed and headless modes failed. Headed: {e}, Headless: {fallback_error}")
                else:
                    raise e
        
        if not browser or not current_page:
            return [types.TextContent(
                type="text",
                text="❌ No browser instance available. Please launch stealth browser first."
            )]
        
        if name == "navigate_with_bypass":
            url = arguments["url"]
            wait_for_challenge = arguments.get("wait_for_challenge", True)
            max_retries = arguments.get("max_retries", 3)
            delay_range = arguments.get("delay_range", [1, 3])
            
            for attempt in range(max_retries):
                try:
                    # Random delay before navigation
                    delay = random.uniform(delay_range[0], delay_range[1])
                    await asyncio.sleep(delay)
                    
                    # Navigate to URL
                    await current_page.goto(url, wait_until='domcontentloaded', timeout=60000)
                    
                    # Wait for Cloudflare challenge if enabled
                    if wait_for_challenge:
                        await wait_for_cloudflare_challenge(current_page)
                    
                    title = await current_page.title()
                    
                    return [types.TextContent(
                        type="text",
                        text=f"✅ Successfully navigated with bypass (attempt {attempt + 1})\nURL: {url}\nTitle: {title}"
                    )]
                    
                except Exception as e:
                    if attempt < max_retries - 1:
                        logger.warning(f"Navigation attempt {attempt + 1} failed: {e}, retrying...")
                        await asyncio.sleep(random.uniform(2, 5))
                        continue
                    else:
                        raise e
        
        elif name == "bypass_cloudflare_challenge":
            timeout = arguments.get("timeout", 30000)
            
            success = await wait_for_cloudflare_challenge(current_page, timeout)
            
            if success:
                return [types.TextContent(
                    type="text",
                    text="✅ Cloudflare challenge bypassed successfully"
                )]
            else:
                return [types.TextContent(
                    type="text",
                    text="⚠️ Cloudflare challenge handling completed (may need manual verification)"
                )]
        
        elif name == "check_cloudflare_protection":
            # Check for Cloudflare indicators
            cf_indicators = []
            
            # Check for Cloudflare challenge page
            cf_challenge = await current_page.query_selector('[data-ray]')
            if cf_challenge:
                cf_indicators.append("Challenge page detected")
            
            # Check for Cloudflare headers or scripts
            cf_script = await current_page.query_selector('script[src*="cloudflare"]')
            if cf_script:
                cf_indicators.append("Cloudflare script detected")
            
            # Check page title for Cloudflare
            title = await current_page.title()
            if "cloudflare" in title.lower() or "checking your browser" in title.lower():
                cf_indicators.append("Cloudflare title detected")
            
            # Check for specific Cloudflare elements
            cf_elements = await current_page.query_selector_all('.cf-browser-verification, .cf-checking-browser, #cf-wrapper')
            if cf_elements:
                cf_indicators.append(f"{len(cf_elements)} Cloudflare elements found")
            
            if cf_indicators:
                return [types.TextContent(
                    type="text",
                    text=f"🛡️ Cloudflare protection detected:\n" + "\n".join(f"- {indicator}" for indicator in cf_indicators)
                )]
            else:
                return [types.TextContent(
                    type="text",
                    text="✅ No Cloudflare protection detected on current page"
                )]
        
        elif name == "click_element":
            selector = arguments["selector"]
            human_like = arguments.get("human_like", True)
            timeout = arguments.get("timeout", 30000)
            
            await current_page.wait_for_selector(selector, timeout=timeout)
            
            if human_like:
                # Human-like clicking with random delay and movement
                element = await current_page.query_selector(selector)
                if element:
                    # Get element position
                    box = await element.bounding_box()
                    if box:
                        # Random position within element
                        x = box['x'] + random.uniform(0.2, 0.8) * box['width']
                        y = box['y'] + random.uniform(0.2, 0.8) * box['height']
                        
                        # Move mouse to element with delay
                        await current_page.mouse.move(x, y)
                        await asyncio.sleep(random.uniform(0.1, 0.3))
                        
                        # Click with random delay
                        await current_page.mouse.click(x, y)
                        await asyncio.sleep(random.uniform(0.1, 0.5))
            else:
                await current_page.click(selector)
            
            return [types.TextContent(
                type="text",
                text=f"✅ Clicked element: {selector} ({'human-like' if human_like else 'direct'})"
            )]
        
        elif name == "type_text_human":
            selector = arguments["selector"]
            text = arguments["text"]
            human_like = arguments.get("human_like", True)
            clear_first = arguments.get("clear_first", True)
            
            await current_page.wait_for_selector(selector)
            
            if clear_first:
                await current_page.fill(selector, "")
            
            if human_like:
                # Human-like typing with random delays
                for char in text:
                    await current_page.type(selector, char, delay=random.uniform(50, 150))
                    if random.random() < 0.1:  # 10% chance of longer pause
                        await asyncio.sleep(random.uniform(0.2, 0.5))
            else:
                await current_page.type(selector, text)
            
            return [types.TextContent(
                type="text",
                text=f"✅ Typed text into {selector}: {text} ({'human-like' if human_like else 'direct'})"
            )]
        
        elif name == "take_screenshot":
            full_page = arguments.get("full_page", False)
            element_selector = arguments.get("element_selector")
            
            if element_selector:
                element = await current_page.query_selector(element_selector)
                if element:
                    screenshot = await element.screenshot()
                else:
                    return [types.TextContent(
                        type="text",
                        text=f"❌ Element not found: {element_selector}"
                    )]
            else:
                screenshot = await current_page.screenshot(full_page=full_page)
            
            screenshot_b64 = base64.b64encode(screenshot).decode()
            
            return [types.TextContent(
                type="text",
                text=f"✅ Screenshot captured (base64 length: {len(screenshot_b64)})\nBase64 data: data:image/png;base64,{screenshot_b64[:100]}..."
            )]
        
        elif name == "get_page_content":
            content_type = arguments.get("content_type", "html")
            
            if content_type == "html":
                content = await current_page.content()
            elif content_type == "text":
                content = await current_page.evaluate('() => document.body.innerText')
            elif content_type == "title":
                content = await current_page.title()
            else:
                return [types.TextContent(
                    type="text",
                    text=f"❌ Invalid content type: {content_type}"
                )]
            
            return [types.TextContent(
                type="text",
                text=f"✅ Page {content_type}:\n{content}"
            )]
        
        elif name == "wait_for_element":
            selector = arguments["selector"]
            timeout = arguments.get("timeout", 30000)
            
            await current_page.wait_for_selector(selector, timeout=timeout)
            
            return [types.TextContent(
                type="text",
                text=f"✅ Element appeared: {selector}"
            )]
        
        elif name == "execute_javascript":
            code = arguments["code"]
            
            result = await current_page.evaluate(code)
            
            return [types.TextContent(
                type="text",
                text=f"✅ JavaScript executed successfully\nResult: {result}"
            )]
        
        elif name == "get_page_info":
            url = current_page.url
            title = await current_page.title()
            
            # Check for Cloudflare
            cf_status = "Unknown"
            try:
                cf_challenge = await current_page.query_selector('[data-ray]')
                if cf_challenge:
                    cf_status = "Challenge page detected"
                else:
                    cf_script = await current_page.query_selector('script[src*="cloudflare"]')
                    if cf_script:
                        cf_status = "Protected (script detected)"
                    else:
                        cf_status = "No protection detected"
            except:
                cf_status = "Check failed"
            
            return [types.TextContent(
                type="text",
                text=f"✅ Current page info:\nURL: {url}\nTitle: {title}\nCloudflare Status: {cf_status}"
            )]
        
        elif name == "close_browser":
            if browser:
                await browser.close()
                browser = None
                context = None
                current_page = None
            
            if playwright_instance:
                await playwright_instance.stop()
                playwright_instance = None
            
            return [types.TextContent(
                type="text",
                text="✅ Stealth browser closed successfully"
            )]
        
        else:
            return [types.TextContent(
                type="text",
                text=f"❌ Unknown tool: {name}"
            )]
    
    except Exception as e:
.error(f"Error in {name}: {str(e)}")
        return [types.TextContent(
            type="text",
            text=f"❌ Error in {name}: {str(e)}"
        )]

async def main():
    # Run the server using stdin/stdout streams
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="web-automation-cloudflare-bypass",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )

if __name__ == "__main__":
    asyncio.run(main())
        logger