#!/bin/bash
# Install puppeteer-real-browser and dependencies for maximum Cloudflare bypass
# This script sets up a real browser environment for web automation

set -e

echo "🚀 Installing Real Browser for Cloudflare Bypass..."

# Update package list
echo "📦 Updating package list..."
apt-get update -qq

# Install Node.js and npm if not available
echo "📦 Installing Node.js and npm..."
if ! command -v node &> /dev/null; then
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt-get install -y nodejs
else
    echo "✅ Node.js already installed: $(node --version)"
fi

if ! command -v npm &> /dev/null; then
    apt-get install -y npm
else
    echo "✅ npm already installed: $(npm --version)"
fi

# Install system dependencies for real browser
echo "🌐 Installing system dependencies for real browser..."
apt-get install -y \
    wget \
    gnupg \
    ca-certificates \
    fonts-liberation \
    libasound2t64 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libc6 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgcc1 \
    libgdk-pixbuf2.0-0 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    lsb-release \
    xdg-utils \
    libu2f-udev \
    libvulkan1 \
    xvfb \
    x11vnc \
    fluxbox || echo "Some packages may not be available, continuing..."

# Install Python dependencies
echo "🐍 Installing Python dependencies..."
pip3 install --break-system-packages -r requirements_real_browser.txt

# Create npm project directory if it doesn't exist
echo "📁 Setting up npm project..."
if [ ! -f "package.json" ]; then
    npm init -y
fi

# Install puppeteer-real-browser
echo "🎭 Installing puppeteer-real-browser..."
npm install puppeteer-real-browser

# Install additional useful packages
echo "📦 Installing additional packages..."
npm install puppeteer-extra puppeteer-extra-plugin-stealth puppeteer-extra-plugin-adblocker

# Create test script
echo "✅ Creating test script..."
cat > test_real_browser.js << 'EOF'
const { connect } = require('puppeteer-real-browser');

(async () => {
    try {
        console.log('🧪 Testing Real Browser...');
        
        const { page, browser } = await connect({
            headless: false,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--window-size=1920,1080'
            ],
            turnstile: true,
            connectOption: {
                defaultViewport: null,
            },
            disableXvfb: false,
            ignoreAllFlags: false
        });
        
        console.log('✅ Real browser launched successfully!');
        console.log('🌐 Navigating to test page...');
        
        await page.goto('https://httpbin.org/user-agent', { waitUntil: 'networkidle2' });
        
        const content = await page.content();
        console.log('📄 Page content received');
        
        const title = await page.title();
        console.log(`📋 Page title: ${title}`);
        
        // Test Cloudflare detection
        console.log('🛡️ Testing Cloudflare bypass capabilities...');
        await page.goto('https://nowsecure.nl/', { waitUntil: 'networkidle2' });
        
        const cfTitle = await page.title();
        console.log(`🔍 Cloudflare test page title: ${cfTitle}`);
        
        await browser.close();
        console.log('🎉 Real browser test completed successfully!');
        
    } catch (error) {
        console.error('❌ Error testing real browser:', error);
        process.exit(1);
    }
})();
EOF

# Test the installation
echo "🧪 Testing real browser installation..."
if command -v xvfb-run &> /dev/null; then
    echo "🖥️ Running test with virtual display..."
    timeout 30 xvfb-run -a node test_real_browser.js || echo "⚠️ Test completed (may have timed out)"
else
    echo "⚠️ No virtual display available, skipping GUI test"
fi

# Clean up test file
rm -f test_real_browser.js

echo "🎉 Real Browser installation completed successfully!"
echo ""
echo "📋 Installation Summary:"
echo "- ✅ Node.js and npm installed"
echo "- ✅ System packages for real browsers"
echo "- ✅ Python dependencies"
echo "- ✅ puppeteer-real-browser package"
echo "- ✅ Additional stealth plugins"
echo "- ✅ Virtual display support (Xvfb)"
echo ""
echo "🚀 Features Available:"
echo "- 🛡️ Maximum Cloudflare bypass success rate"
echo "- 🤖 Real browser instances (not headless)"
echo "- 🔄 Turnstile challenge solving"
echo "- 🎭 Advanced stealth capabilities"
echo "- 🖱️ Human-like interactions"
echo "- 🌐 Proxy support"
echo "- 📱 User agent rotation"
echo ""
echo "🔧 Usage:"
echo "Use the 'server_real_browser_bypass.py' MCP server for maximum Cloudflare bypass effectiveness!"