#!/bin/bash
# Install dependencies for headless browser operation
# This script ensures proper setup for Playwright in server environments

set -e

echo "🔧 Installing headless browser dependencies..."

# Update package list
echo "📦 Updating package list..."
apt-get update -qq

# Install essential packages for headless browser operation
echo "🌐 Installing browser dependencies..."
apt-get install -y \
    wget \
    gnupg \
    ca-certificates \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libc6 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgcc1 \
    libgconf-2-4 \
    libgdk-pixbuf2.0-0 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    lsb-release \
    xdg-utils \
    libu2f-udev \
    libvulkan1

# Install Python dependencies
echo "🐍 Installing Python dependencies..."
pip3 install --upgrade pip
pip3 install playwright>=1.40.0
pip3 install mcp>=1.0.0

# Install Playwright browsers
echo "🎭 Installing Playwright browsers..."
playwright install chromium
playwright install firefox
playwright install webkit

# Install browser dependencies
echo "🔧 Installing browser system dependencies..."
playwright install-deps

# Create a test script to verify installation
echo "✅ Creating verification script..."
cat > /tmp/test_playwright_headless.py << 'EOF'
#!/usr/bin/env python3
import asyncio
from playwright.async_api import async_playwright

async def test_headless():
    async with async_playwright() as p:
        try:
            browser = await p.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage'
                ]
            )
            page = await browser.new_page()
            await page.goto('https://example.com')
            title = await page.title()
            print(f"✅ Success! Page title: {title}")
            await browser.close()
            return True
        except Exception as e:
            print(f"❌ Error: {e}")
            return False

if __name__ == "__main__":
    success = asyncio.run(test_headless())
    exit(0 if success else 1)
EOF

# Run the test
echo "🧪 Testing Playwright installation..."
python3 /tmp/test_playwright_headless.py

# Clean up
rm -f /tmp/test_playwright_headless.py

echo "🎉 Headless browser dependencies installed successfully!"
echo ""
echo "📋 Installation Summary:"
echo "- ✅ System packages for headless browsers"
echo "- ✅ Python Playwright library"
echo "- ✅ Chromium, Firefox, and WebKit browsers"
echo "- ✅ Browser system dependencies"
echo "- ✅ Headless operation verified"
echo ""
echo "🚀 You can now use the fixed web automation server!"