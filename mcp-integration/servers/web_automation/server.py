#!/usr/bin/env python3
"""
Web Automation MCP Server using P<PERSON><PERSON><PERSON>r
Provides web automation capabilities through <PERSON><PERSON><PERSON><PERSON><PERSON> (Python Puppeteer wrapper)
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Union
import base64
import os
from urllib.parse import urljoin, urlparse

try:
    from pyppeteer import launch
    from pyppeteer.browser import Browser
    from pyppeteer.page import Page
    PYPPETEER_AVAILABLE = True
except ImportError:
    PYPPETEER_AVAILABLE = False
    Browser = None
    Page = None

from mcp.server.models import InitializationOptions
import mcp.types as types
from mcp.server import NotificationOptions, Server
import mcp.server.stdio

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("web-automation")

# Global browser instance
browser: Optional[Browser] = None
current_page: Optional[Page] = None

# Server instance
server = Server("web-automation")

@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """List available web automation tools."""
    if not PYPPETEER_AVAILABLE:
        return [
            types.Tool(
                name="check_dependencies",
                description="Check if web automation dependencies are available",
                inputSchema={
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            )
        ]
    
    return [
        types.Tool(
            name="launch_browser",
            description="Launch a new browser instance",
            inputSchema={
                "type": "object",
                "properties": {
                    "headless": {
                        "type": "boolean",
                        "description": "Run browser in headless mode",
                        "default": True
                    },
                    "width": {
                        "type": "integer",
                        "description": "Browser window width",
                        "default": 1280
                    },
                    "height": {
                        "type": "integer",
                        "description": "Browser window height",
                        "default": 720
                    }
                },
                "required": []
            }
        ),
        types.Tool(
            name="navigate_to_url",
            description="Navigate to a specific URL",
            inputSchema={
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "URL to navigate to"
                    },
                    "wait_until": {
                        "type": "string",
                        "description": "When to consider navigation complete",
                        "enum": ["load", "domcontentloaded", "networkidle0", "networkidle2"],
                        "default": "load"
                    }
                },
                "required": ["url"]
            }
        ),
        types.Tool(
            name="take_screenshot",
            description="Take a screenshot of the current page",
            inputSchema={
                "type": "object",
                "properties": {
                    "full_page": {
                        "type": "boolean",
                        "description": "Capture full page screenshot",
                        "default": False
                    },
                    "element_selector": {
                        "type": "string",
                        "description": "CSS selector of element to screenshot (optional)"
                    }
                },
                "required": []
            }
        ),
        types.Tool(
            name="click_element",
            description="Click on an element",
            inputSchema={
                "type": "object",
                "properties": {
                    "selector": {
                        "type": "string",
                        "description": "CSS selector of element to click"
                    },
                    "wait_for_selector": {
                        "type": "boolean",
                        "description": "Wait for selector to be available",
                        "default": True
                    }
                },
                "required": ["selector"]
            }
        ),
        types.Tool(
            name="type_text",
            description="Type text into an input field",
            inputSchema={
                "type": "object",
                "properties": {
                    "selector": {
                        "type": "string",
                        "description": "CSS selector of input element"
                    },
                    "text": {
                        "type": "string",
                        "description": "Text to type"
                    },
                    "clear_first": {
                        "type": "boolean",
                        "description": "Clear field before typing",
                        "default": True
                    }
                },
                "required": ["selector", "text"]
            }
        ),
        types.Tool(
            name="get_page_content",
            description="Get page content (HTML or text)",
            inputSchema={
                "type": "object",
                "properties": {
                    "content_type": {
                        "type": "string",
                        "description": "Type of content to retrieve",
                        "enum": ["html", "text", "title"],
                        "default": "html"
                    }
                },
                "required": []
            }
        ),
        types.Tool(
            name="wait_for_element",
            description="Wait for an element to appear on the page",
            inputSchema={
                "type": "object",
                "properties": {
                    "selector": {
                        "type": "string",
                        "description": "CSS selector to wait for"
                    },
                    "timeout": {
                        "type": "integer",
                        "description": "Timeout in milliseconds",
                        "default": 30000
                    }
                },
                "required": ["selector"]
            }
        ),
        types.Tool(
            name="execute_javascript",
            description="Execute JavaScript code on the page",
            inputSchema={
                "type": "object",
                "properties": {
                    "code": {
                        "type": "string",
                        "description": "JavaScript code to execute"
                    }
                },
                "required": ["code"]
            }
        ),
        types.Tool(
            name="close_browser",
            description="Close the browser instance",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: dict) -> list[types.TextContent]:
    """Handle tool calls for web automation."""
    global browser, current_page
    
    try:
        if name == "check_dependencies":
            if PYPPETEER_AVAILABLE:
                return [types.TextContent(
                    type="text",
                    text="✅ Pyppeteer is available and ready for web automation"
                )]
            else:
                return [types.TextContent(
                    type="text", 
                    text="❌ Pyppeteer is not available. Install with: pip install pyppeteer"
                )]
        
        if not PYPPETEER_AVAILABLE:
            return [types.TextContent(
                type="text",
                text="❌ Pyppeteer is not available. Cannot perform web automation."
            )]
        
        if name == "launch_browser":
            headless = arguments.get("headless", True)
            width = arguments.get("width", 1280)
            height = arguments.get("height", 720)
            
            if browser:
                await browser.close()
            
            browser = await launch(
                headless=headless,
                executablePath='/usr/bin/chromium',
                args=[
                    f'--window-size={width},{height}',
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-features=TranslateUI',
                    '--disable-ipc-flooding-protection',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            current_page = await browser.newPage()
            await current_page.setViewport({'width': width, 'height': height})
            
            return [types.TextContent(
                type="text",
                text=f"✅ Browser launched successfully (headless: {headless}, size: {width}x{height})"
            )]
        
        if not browser or not current_page:
            return [types.TextContent(
                type="text",
                text="❌ No browser instance available. Please launch browser first."
            )]
        
        if name == "navigate_to_url":
            url = arguments["url"]
            wait_until = arguments.get("wait_until", "load")
            
            await current_page.goto(url, {'waitUntil': wait_until})
            title = await current_page.title()
            
            return [types.TextContent(
                type="text",
                text=f"✅ Navigated to: {url}\nPage title: {title}"
            )]
        
        elif name == "take_screenshot":
            full_page = arguments.get("full_page", False)
            element_selector = arguments.get("element_selector")
            
            if element_selector:
                element = await current_page.querySelector(element_selector)
                if element:
                    screenshot = await element.screenshot()
                else:
                    return [types.TextContent(
                        type="text",
                        text=f"❌ Element not found: {element_selector}"
                    )]
            else:
                screenshot = await current_page.screenshot(fullPage=full_page)
            
            # Convert to base64
            screenshot_b64 = base64.b64encode(screenshot).decode()
            
            return [types.TextContent(
                type="text",
                text=f"✅ Screenshot captured (base64 length: {len(screenshot_b64)})\nBase64 data: data:image/png;base64,{screenshot_b64[:100]}..."
            )]
        
        elif name == "click_element":
            selector = arguments["selector"]
            wait_for_selector = arguments.get("wait_for_selector", True)
            
            if wait_for_selector:
                await current_page.waitForSelector(selector)
            
            await current_page.click(selector)
            
            return [types.TextContent(
                type="text",
                text=f"✅ Clicked element: {selector}"
            )]
        
        elif name == "type_text":
            selector = arguments["selector"]
            text = arguments["text"]
            clear_first = arguments.get("clear_first", True)
            
            await current_page.waitForSelector(selector)
            
            if clear_first:
                await current_page.click(selector, {'clickCount': 3})  # Select all
            
            await current_page.type(selector, text)
            
            return [types.TextContent(
                type="text",
                text=f"✅ Typed text into {selector}: {text}"
            )]
        
        elif name == "get_page_content":
            content_type = arguments.get("content_type", "html")
            
            if content_type == "html":
                content = await current_page.content()
            elif content_type == "text":
                content = await current_page.evaluate('() => document.body.innerText')
            elif content_type == "title":
                content = await current_page.title()
            else:
                return [types.TextContent(
                    type="text",
                    text=f"❌ Invalid content type: {content_type}"
                )]
            
            return [types.TextContent(
                type="text",
                text=f"✅ Page {content_type}:\n{content}"
            )]
        
        elif name == "wait_for_element":
            selector = arguments["selector"]
            timeout = arguments.get("timeout", 30000)
            
            await current_page.waitForSelector(selector, {'timeout': timeout})
            
            return [types.TextContent(
                type="text",
                text=f"✅ Element appeared: {selector}"
            )]
        
        elif name == "execute_javascript":
            code = arguments["code"]
            
            result = await current_page.evaluate(code)
            
            return [types.TextContent(
                type="text",
                text=f"✅ JavaScript executed successfully\nResult: {result}"
            )]
        
        elif name == "close_browser":
            if browser:
                await browser.close()
                browser = None
                current_page = None
            
            return [types.TextContent(
                type="text",
                text="✅ Browser closed successfully"
            )]
        
        else:
            return [types.TextContent(
                type="text",
                text=f"❌ Unknown tool: {name}"
            )]
    
    except Exception as e:
        logger.error(f"Error in {name}: {str(e)}")
        return [types.TextContent(
            type="text",
            text=f"❌ Error in {name}: {str(e)}"
        )]

async def main():
    # Run the server using stdin/stdout streams
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="web-automation",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )

if __name__ == "__main__":
    asyncio.run(main())