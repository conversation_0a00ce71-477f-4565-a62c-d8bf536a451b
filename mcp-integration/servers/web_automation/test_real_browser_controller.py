#!/usr/bin/env python3
"""
Direct test of the RealBrowserController class
"""
import asyncio
import sys
import os

# Add the current directory to the path so we can import the server module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server_real_browser_bypass import RealBrowserController

async def test_real_browser_controller():
    """Test the RealBrowserController class directly."""
    print("Testing RealBrowserController...")
    
    # Create controller instance
    controller = RealBrowserController()
    
    try:
        # Try to start browser
        print("Attempting to start real browser...")
        success = await controller.start_browser(
            headless=True,  # Use headless for testing
            width=1280,
            height=720
        )
        
        if success:
            print("✅ Browser started successfully!")
            print(f"WebSocket URL: {controller.ws_url}")
            print(f"Launch output: {controller.launch_output}")
            
            # Try to close browser
            print("Closing browser...")
            await controller.close()
            print("✅ Browser closed successfully!")
        else:
            print("❌ Failed to start browser")
            print(f"Launch output: {controller.launch_output}")
            
    except Exception as e:
        print(f"❌ Error testing RealBrowserController: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_real_browser_controller())