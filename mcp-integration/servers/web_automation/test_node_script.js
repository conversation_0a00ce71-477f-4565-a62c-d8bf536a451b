const { connect } = require('puppeteer-real-browser');

(async () => {
    try {
        console.log('Testing puppeteer-real-browser...');
        
        const { page, browser } = await connect({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--window-size=1280,720'
            ],
            turnstile: true,
            connectOption: {
                defaultViewport: null,
            },
            disableXvfb: false,
            ignoreAllFlags: false
        });
        
        console.log('Real browser started successfully');
        console.log('BROWSER_WS_URL:' + browser.wsEndpoint());
        
        // Close browser after test
        await browser.close();
        console.log('<PERSON><PERSON>er closed successfully');
        
    } catch (error) {
        console.error('Error testing puppeteer-real-browser:', error);
        process.exit(1);
    }
})();