#!/usr/bin/env python3
"""
<PERSON><PERSON> - Simplified version without <PERSON><PERSON>
Uses requests + BeautifulSoup + Gemini for web scraping
All dependencies included in this single file
"""

import asyncio
import logging
import time
import os
import aiohttp
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup

# Setup logger first
logger = logging.getLogger(__name__)

# Import TLS client for Cloudflare bypass
try:
    import tls_client
    from fake_useragent import UserAgent
    TLS_CLIENT_AVAILABLE = True
except ImportError:
    TLS_CLIENT_AVAILABLE = False
    logger.warning("⚠️ TLS client not available. Install with: pip install tls-client fake-useragent")

# Import cleanup manager
from utils.cleanup_manager import (
    register_session_for_cleanup,
    register_connector_for_cleanup,
    register_tls_session_for_cleanup,
    cleanup_all_resources
)

# Import external GeminiProcessor with batch processing
from gemini_processor import GeminiProcessor as ExternalGeminiProcessor

# Import advanced site handler for problematic sites
from advanced_site_handler import AdvancedSiteHandler

@dataclass
class GeminiConfig:
    """Configuration for Gemini API."""
    api_key: str
    model_name: str = "gemini-2.5-flash-lite"
    api_url: str = "https://generativelanguage.googleapis.com/v1beta/models"
    timeout: int = 30
    max_retries: int = 3

class GeminiProcessor:
    """
    Fast content processor using Google's Gemini 2.5 Flash API.
    """
    
    def __init__(self, gemini_config: Optional[GeminiConfig] = None):
        self.config = gemini_config or self._load_config()
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Vietnamese-optimized prompts
        self.prompts = {
            "html_to_markdown": """Convert the following HTML content to clean, well-formatted Markdown.

IMPORTANT: Extract ALL meaningful content - do NOT summarize or shorten. Include:

1. **ALL Text Content**: Every paragraph, sentence, list item, caption
2. **ALL Headlines**: Main titles, subtitles, section headers
3. **ALL Data**: Numbers, statistics, quotes, names, dates
4. **Proper Structure**: Use Markdown formatting (# ## ### **bold** *italic* > quotes)
5. **Vietnamese Preservation**: Maintain all Vietnamese diacritics perfectly
6. **Content Completeness**: Remove only navigation, ads, comments - KEEP ALL ARTICLE CONTENT

OUTPUT: Provide detailed, complete content (aim for 2,000+ characters minimum). Do NOT create summaries.

HTML Content:
{content}

COMPLETE MARKDOWN OUTPUT (NOT SUMMARY):""",

            "summarize": """Summarize the following Vietnamese content in a concise manner while preserving key information:

Content:
{content}

Summary:""",

            "clean": """Clean and format the following Vietnamese content to readable Markdown:

Content:
{content}

Cleaned Markdown:""",

            "full_article": """Extract and format the COMPLETE FULL article content from the following HTML/text.

IMPORTANT: You MUST provide the FULL, DETAILED content - NOT a summary. Extract EVERYTHING from the article including:

1. **ALL Headlines and Subheadings**: Every title, subtitle, section header
2. **COMPLETE Body Text**: Every paragraph, sentence, detail - DO NOT summarize or shorten
3. **ALL Quotes**: Every quote, statement, interview excerpt in full
4. **ALL Statistics and Data**: Every number, percentage, figure, chart data
5. **ALL Names and Details**: Every person, company, location, date mentioned
6. **ALL Context**: Background information, explanations, descriptions
7. **Captions and Metadata**: Image captions, author info, publication details

FORMAT REQUIREMENTS:
- Use proper Markdown formatting (# ## ### for headings, **bold**, *italic*, > for quotes)
- Preserve Vietnamese diacritics perfectly
- Structure with clear sections but include ALL content
- Remove only ads, navigation, comments - KEEP ALL ARTICLE CONTENT

OUTPUT LENGTH: Aim for 5,000-15,000 characters minimum. Use the full 65K output capacity available.
DO NOT TRUNCATE OR SUMMARIZE - provide the COMPLETE article text.

Source Content:
{content}

COMPLETE FULL ARTICLE (NOT SUMMARY):""",

            "news_summary": """Create a comprehensive news summary from the following content. Provide:

1. **Executive Summary**: 2-3 sentence overview of main news
2. **Key Points**: Detailed bullet points of important information
3. **Context & Background**: Relevant background information
4. **Impact Analysis**: Potential implications and effects
5. **Quotes & Sources**: Important quotes and source attribution
6. **Timeline**: Chronological events if applicable
7. **Related Information**: Connected news or context

Preserve Vietnamese language perfectly and provide extensive detail using the full 65K output capacity.

Content:
{content}

Comprehensive News Summary:""",

            "enhanced_extraction": """Perform advanced content extraction and analysis. Create a detailed, structured output with:

1. **Main Content**: Complete article/page content with proper formatting
2. **Metadata**: Title, author, date, category, tags, source
3. **Key Information**: Important facts, figures, quotes, statistics
4. **Content Analysis**: Topic classification, sentiment, key themes
5. **Structured Data**: Tables, lists, contact info if present
6. **Related Links**: Important internal/external references
7. **Vietnamese Language**: Perfect preservation of all Vietnamese text

Use the full output capacity to provide comprehensive, detailed extraction.

Source:
{content}

Enhanced Extraction Output:"""
        }
        
    def _load_config(self) -> GeminiConfig:
        """Load Gemini configuration from environment variables."""
        api_key = os.getenv("GEMINI_API_KEY", "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM")
        if not api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
            
        return GeminiConfig(
            api_key=api_key,
            model_name=os.getenv("GEMINI_MODEL_NAME", "gemini-2.5-flash-lite"),
            api_url=os.getenv("GEMINI_API_URL", "https://generativelanguage.googleapis.com/v1beta/models"),
            timeout=int(os.getenv("GEMINI_TIMEOUT", "30")),
            max_retries=int(os.getenv("GEMINI_MAX_RETRIES", "3"))
        )
    
    async def initialize(self) -> bool:
        """Initialize the Gemini processor."""
        try:
            if not self.session:
                timeout = aiohttp.ClientTimeout(total=self.config.timeout)
                self.session = aiohttp.ClientSession(timeout=timeout)
            
            logger.info("✅ Gemini processor initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Gemini processor: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Cleanup resources."""
        if self.session:
            await self.session.close()
            self.session = None
        logger.info("✅ Gemini processor cleanup completed")
    
    async def process_content(
        self,
        content: str,
        task_type: str = "html_to_markdown",
        max_length: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Process content using Gemini API.
        """
        start_time = time.time()
        
        try:
            # Prepare prompt
            prompt = self._prepare_prompt(content, task_type, max_length)
            
            # Call Gemini API
            result = await self._call_gemini_api(prompt)
            
            processing_time = time.time() - start_time
            
            logger.info(f"Performance: gemini_processing completed in {processing_time:.3f}s")
            
            return {
                "success": True,
                "original_length": len(content),
                "processed_content": result,
                "output_length": len(result),
                "processing_time": processing_time,
                "model": self.config.model_name,
                "task_type": task_type
            }
            
        except Exception as e:
            logger.error(f"Gemini processing error: {e}")
            return {
                "success": False,
                "original_length": len(content),
                "processed_content": "",
                "processing_time": 0,
                "model": self.config.model_name,
                "error": str(e),
                "output_length": 0
            }
    
    def _prepare_prompt(self, content: str, task_type: str, max_length: Optional[int]) -> str:
        """Prepare prompt for Gemini API with enhanced capacity."""
        # Gemini 2.5 Flash supports 1M input tokens (~800K characters
        # Use much higher limits to leverage full capacity
        if task_type == "full_article":
            max_content_length = max_length or 800000  # Near 1M token limit for full articles
        elif task_type == "news_summary":
            max_content_length = max_length or 400000  # 400K for comprehensive news
        else:
            max_content_length = max_length or 200000  # 200K for regular content

        if len(content) > max_content_length:
            content = self._smart_truncate(content, max_content_length)

        # Get appropriate prompt template
        prompt_template = self.prompts.get(task_type, self.prompts["html_to_markdown"])
        return prompt_template.format(content=content)
    
    def _smart_truncate(self, content: str, max_length: int) -> str:
        """Smart content truncation."""
        if len(content) <= max_length:
            return content
        
        # Try to find a good breaking point
        truncated = content[:max_length]
        
        # Vietnamese sentence endings
        vietnamese_endings = [
            '. ', '.\n', '? ', '?\n', '! ', '!\n',
            '." ', '."', '?" ', '?"', '!" ', '!"',
            '.)', '.)', '?)', '?)', '!)', '!)'
        ]
        
        # Find the last complete Vietnamese sentence within limit
        last_sentence_end = -1
        for ending in vietnamese_endings:
            pos = truncated.rfind(ending)
            if pos > last_sentence_end and pos > max_length * 0.6:  # At least 60% of content
                last_sentence_end = pos + len(ending) - 1
        
        if last_sentence_end > 0:
            return content[:last_sentence_end + 1]
        
        # Fallback: find last space to avoid cutting words
        last_space = truncated.rfind(' ')
        if last_space > max_length * 0.8:  # At least 80% of content
            return content[:last_space]
        
        return truncated + "..."
    
    async def _call_gemini_api(self, prompt: str) -> str:
        """Call Gemini API with retry logic."""
        if not self.session:
            await self.initialize()
        
        url = f"{self.config.api_url}/{self.config.model_name}:generateContent"
        params = {"key": self.config.api_key}
        
        headers = {
            "Content-Type": "application/json"
        }
        
        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.0,  # Deterministic for consistency
                "maxOutputTokens": 2048,
                "topK": 1,
                "topP": 1.0
            }
        }
        
        for attempt in range(self.config.max_retries + 1):
            try:
                async with self.session.post(url, params=params, headers=headers, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        # Extract generated text
                        if "candidates" in result and len(result["candidates"]) > 0:
                            candidate = result["candidates"][0]
                            if "content" in candidate and "parts" in candidate["content"]:
                                parts = candidate["content"]["parts"]
                                if len(parts) > 0:
                                    return parts[0].get("text", "").strip()
                        
                        # Fallback if no content found
                        return ""
                    
                    elif response.status == 429:
                        # Rate limited, wait and retry
                        if attempt < self.config.max_retries:
                            wait_time = 2 ** attempt  # Exponential backoff
                            logger.warning(f"Rate limited, waiting {wait_time}s before retry {attempt + 1}")
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            raise Exception(f"Rate limited after {self.config.max_retries} retries")
                    
                    else:
                        # Other error
                        error_text = await response.text()
                        raise Exception(f"Gemini API error {response.status}: {error_text}")
                        
            except Exception as e:
                if attempt < self.config.max_retries:
                    wait_time = 2 ** attempt  # Exponential backoff
                    logger.warning(f"Gemini API call failed, waiting {wait_time}s before retry {attempt + 1}: {e}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    raise
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on Gemini processor."""
        try:
            # Test with simple Vietnamese content
            test_content = "<h1>Tin tức công nghệ</h1><p>Trí tuệ nhân tạo đang phát triển mạnh mẽ.</p>"
            test_result = await self.process_content(test_content, "html_to_markdown")
            
            return {
                "status": "healthy" if test_result.get("success") else "error",
                "model_name": self.config.model_name,
                "api_available": True,
                "test_successful": test_result.get("success", False),
                "test_processing_time": test_result.get("processing_time", 0)
            }
            
        except Exception as e:
            logger.error(f"Gemini health check failed: {e}")
            return {
                "status": "error",
                "model_name": self.config.model_name,
                "api_available": False,
                "error": str(e)
            }

@dataclass
class PageSnapshot:
    """Simplified page snapshot without Playwright dependencies"""
    title: str = ""
    description: str = ""
    href: str = ""
    html: str = ""
    text: str = ""
    markdown: str = ""  # 🚀 NEW: Structured markdown content
    status: Optional[int] = None
    status_text: str = ""
    parsed: Optional[Dict] = None
    imgs: List[Dict] = None
    max_elem_depth: int = 0
    elem_count: int = 0
    is_intermediate: bool = False
    
    def __post_init__(self):
        if self.imgs is None:
            self.imgs = []

@dataclass
class JiniCrawlResult:
    """Result from Jini crawler processing"""
    success: bool
    url: str
    title: Optional[str] = None
    original_content: Optional[str] = None
    cleaned_content: Optional[str] = None
    processed_content: Optional[str] = None
    processing_time: float = 0.0
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class JiniCrawler:
    """
    Simplified Jini Crawler using requests + BeautifulSoup + Gemini
    No Playwright dependencies - lightweight and fast
    """
    
    def __init__(self):
        self.session = None
        self.gemini_processor = ExternalGeminiProcessor()  # Use external processor with batch support
        self.advanced_handler = AdvancedSiteHandler()  # 🔧 NEW: Advanced site handler
        self._initialized = False
        self._cleanup_done = False
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        self.timeout = 15  # Optimized timeout for faster crawling
        # TLS client for Cloudflare bypass
        self.tls_session = None
        self.use_tls_client = TLS_CLIENT_AVAILABLE
        self.user_agent_generator = None

    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with guaranteed cleanup"""
        await self.cleanup()

    async def initialize(self) -> bool:
        """Initialize the crawler components"""
        try:
            if not self._initialized:
                # Create aiohttp session for async requests
                timeout = aiohttp.ClientTimeout(total=self.timeout)
                connector = aiohttp.TCPConnector(
                    limit=200,  # Increased total connection pool
                    limit_per_host=20,  # Increased per-host limit for better concurrency
                    ttl_dns_cache=300,
                    use_dns_cache=True,
                    enable_cleanup_closed=True
                )

                self.session = aiohttp.ClientSession(
                    timeout=timeout,
                    headers={'User-Agent': self.user_agent},
                    connector=connector
                )

                # Register for cleanup
                register_session_for_cleanup(self.session)
                register_connector_for_cleanup(connector)

                # Initialize TLS client for Cloudflare bypass
                if self.use_tls_client:
                    try:
                        # Initialize user agent generator
                        self.user_agent_generator = UserAgent()

                        # Create TLS session with Chrome fingerprint
                        self.tls_session = tls_client.Session(
                            random_tls_extension_order=True,
                            client_identifier='chrome_120'
                        )

                        # Set realistic headers
                        self._update_tls_headers()

                        # Register TLS session for cleanup
                        register_tls_session_for_cleanup(self.tls_session)

                        logger.info("✅ TLS client initialized for Cloudflare bypass")
                    except Exception as e:
                        logger.warning(f"⚠️ TLS client initialization failed: {e}")
                        self.use_tls_client = False

                await self.gemini_processor.initialize()
                self._initialized = True
                logger.info("✅ Jini Crawler initialized successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to initialize Jini Crawler: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Cleanup resources with proper session management"""
        if self._cleanup_done:
            return

        try:
            logger.debug("🧹 Starting JiniCrawler cleanup...")

            # Cleanup TLS session first
            if self.tls_session:
                try:
                    self.tls_session.close()
                    self.tls_session = None
                    logger.debug("✅ TLS session closed")
                except Exception as e:
                    logger.warning(f"⚠️ TLS session cleanup warning: {e}")

            # Cleanup aiohttp session with proper error handling
            if self.session and not self.session.closed:
                try:
                    await self.session.close()
                    logger.debug("✅ Aiohttp session closed")
                except Exception as e:
                    logger.warning(f"⚠️ Aiohttp session cleanup warning: {e}")
                finally:
                    self.session = None

            # Cleanup Gemini processor
            if self.gemini_processor:
                try:
                    await self.gemini_processor.cleanup()
                    logger.debug("✅ Gemini processor cleaned up")
                except Exception as e:
                    logger.warning(f"⚠️ Gemini processor cleanup warning: {e}")

            # Use comprehensive cleanup manager
            await cleanup_all_resources(
                force=True,
                delay=0.5,
                gc_cycles=5
            )

            self._initialized = False
            self._cleanup_done = True
            logger.info("✅ Jini Crawler cleanup completed")
        except Exception as e:
            logger.error(f"❌ Error during Jini Crawler cleanup: {e}")
        finally:
            self._cleanup_done = True

    def _update_tls_headers(self, url: str = ""):
        """Update TLS session headers with site-specific optimization"""
        if not self.tls_session:
            return

        try:
            # Get domain for site-specific optimization
            domain = self.advanced_handler.get_domain(url) if url else ""

            # Get site-specific config
            site_config = self.advanced_handler.get_site_config(url) if url else {}

            # Use site-specific user agent or fallback to random
            if site_config and 'user_agents' in site_config:
                import random
                user_agent = random.choice(site_config['user_agents'])
            else:
                user_agent = self.user_agent_generator.random if self.user_agent_generator else self.user_agent

            # Base headers
            headers = {
                'User-Agent': user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Cache-Control': 'max-age=0'
            }

            # 🔧 ENHANCED: Add Vietnamese-specific headers for VN sites
            if any(vn_domain in domain for vn_domain in ['vnexpress.net', 'dantri.com.vn', 'thanhnien.vn', 'vietnamnet.vn']):
                import random
                headers.update({
                    'Accept-Language': 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
                    'X-Forwarded-For': f"14.161.{random.randint(1,255)}.{random.randint(1,255)}",
                    'CF-IPCountry': 'VN',
                    'CF-RAY': f"{random.randint(100000000000000000, 999999999999999999)}-SIN",
                    'CF-Visitor': '{"scheme":"https"}',
                    'Referer': 'https://www.google.com.vn/',
                    'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                    'Sec-Ch-Ua-Mobile': '?0',
                    'Sec-Ch-Ua-Platform': '"Windows"'
                })

            # Apply site-specific headers if available
            if site_config and 'headers' in site_config:
                headers.update(site_config['headers'])

            self.tls_session.headers.update(headers)

        except Exception as e:
            logger.warning(f"⚠️ Failed to update TLS headers: {e}")

    def _fetch_with_tls_bypass(self, url: str, max_retries: int = 2) -> Optional[Dict[str, Any]]:
        """Enhanced TLS client bypass with retry logic and site-specific optimization"""
        if not self.tls_session:
            return None

        # Get site-specific config for delays and retries
        site_config = self.advanced_handler.get_site_config(url)
        delay_range = site_config.get('delay_range', (1, 3))
        retry_count = site_config.get('retry_count', max_retries)

        for attempt in range(retry_count):
            try:
                # Update headers for each request with URL-specific optimization
                self._update_tls_headers(url)

                # Set timeout
                self.tls_session.timeout_seconds = self.timeout

                # Add progressive delay for Vietnamese sites
                if attempt > 0:
                    import random
                    import time
                    delay = random.uniform(*delay_range) * (attempt + 1)
                    logger.debug(f"⏳ TLS retry delay: {delay:.2f}s (attempt {attempt + 1})")
                    time.sleep(delay)

                logger.debug(f"🔒 TLS bypass request (attempt {attempt + 1}/{retry_count}): {url}")

                # Make request
                response = self.tls_session.get(url)

                if response.status_code == 200:
                    content_length = len(response.text)

                    # Validate content quality (not just empty or error pages)
                    if content_length > 500:  # Minimum content threshold
                        result = {
                            'content': response.text,
                            'status_code': response.status_code,
                            'headers': dict(response.headers),
                            'url': str(response.url)
                        }
                        logger.info(f"✅ TLS bypass successful (attempt {attempt + 1}): {content_length} chars")
                        return result
                    else:
                        logger.warning(f"⚠️ TLS bypass got minimal content: {content_length} chars")

                elif response.status_code in [403, 406, 429]:
                    logger.warning(f"⚠️ TLS bypass blocked: HTTP {response.status_code} (attempt {attempt + 1})")
                    # Continue to retry for these status codes
                else:
                    logger.warning(f"⚠️ TLS bypass failed: HTTP {response.status_code} (attempt {attempt + 1})")
                    return None  # Don't retry for other status codes

            except Exception as e:
                logger.warning(f"⚠️ TLS bypass error (attempt {attempt + 1}): {e}")
                if attempt == retry_count - 1:  # Last attempt
                    logger.error(f"❌ TLS bypass failed after {retry_count} attempts: {e}")

        return None

    async def _fetch_with_jina_reader(self, url: str) -> Optional[Dict[str, Any]]:
        """Fetch URL using Jina Reader API as fallback (async)"""
        try:
            # Jina Reader API endpoint
            jina_url = f"https://r.jina.ai/{url}"

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/plain, text/html, application/json',
                'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            logger.debug(f"🔄 Jina Reader API request: {jina_url}")

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.get(jina_url, headers=headers) as response:
                    if response.status == 200:
                        content = await response.text()

                        # Jina Reader returns clean markdown/text
                        # Convert to HTML-like format for compatibility
                        html_content = f"""
                        <html>
                        <head><title>Content from Jina Reader</title></head>
                        <body>
                        <div class="jina-reader-content">
                        {content.replace(chr(10), '<br>')}
                        </div>
                        </body>
                        </html>
                        """

                        result = {
                            'content': html_content,
                            'status_code': 200,
                            'headers': dict(response.headers),
                            'url': url,
                            'source': 'jina_reader_api'
                        }

                        logger.debug(f"✅ Jina Reader API successful: {len(content)} chars")
                        return result
                    else:
                        logger.warning(f"⚠️ Jina Reader API failed: HTTP {response.status}")
                        return None

        except Exception as e:
            logger.error(f"❌ Jina Reader API error: {e}")
            return None

    async def scrap_url(self, url: str, options: Optional[Dict[str, Any]] = None):
        """
        Scrape URL using requests + BeautifulSoup (generator for compatibility)
        
        Args:
            url: URL to scrape
            options: Optional parameters (not used in simple version)
            
        Yields:
            PageSnapshot with scraped content
        """
        if not self._initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            logger.info(f"🕷️ Scraping URL with simple crawler: {url}")

            html_content = None
            status = 200
            status_text = "OK"

            # 🚀 NEW: TLS client as DEFAULT (not fallback)
            if self.use_tls_client and self.tls_session:
                logger.debug(f"🔒 Using TLS client as primary method: {url}")
                try:
                    # Run TLS bypass in executor to avoid blocking
                    loop = asyncio.get_event_loop()
                    tls_result = await loop.run_in_executor(
                        None, self._fetch_with_tls_bypass, url
                    )

                    if tls_result:
                        html_content = tls_result['content']
                        status = tls_result['status_code']
                        status_text = "OK (TLS Primary)"
                        logger.debug(f"✅ TLS primary successful for {url}: {len(html_content)} chars")
                    else:
                        logger.warning(f"⚠️ TLS primary failed for {url}")

                except Exception as e:
                    logger.warning(f"⚠️ TLS primary error for {url}: {e}")

            # Fallback to regular request only if TLS failed
            if not html_content:
                logger.debug(f"🔄 Fallback to regular request: {url}")
                try:
                    async with self.session.get(url) as response:
                        status = response.status
                        status_text = response.reason or ""
                        html_content = await response.text()

                        if status == 200 and html_content:
                            logger.debug(f"✅ Regular fallback successful for {url}: {len(html_content)} chars")
                        else:
                            logger.warning(f"⚠️ Regular fallback failed: HTTP {status}")
                            html_content = None  # Reset for next fallback

                except Exception as e:
                    logger.warning(f"⚠️ Regular fallback error for {url}: {e}")

            # Try Jina Reader API as final fallback
            if not html_content:
                logger.info(f"🔄 Trying Jina Reader API fallback for {url}")
                try:
                    jina_result = await self._fetch_with_jina_reader(url)
                    if jina_result:
                        html_content = jina_result['content']
                        status = 200
                        status_text = "OK (Jina Reader API)"
                        logger.info(f"✅ Jina Reader API successful for {url}")
                    else:
                        logger.warning(f"⚠️ Jina Reader API failed for {url}")
                except Exception as e:
                    logger.error(f"❌ Jina Reader API failed for {url}: {e}")

            # If still no content, raise error
            if not html_content:
                raise Exception(f"Failed to fetch content from {url} (status: {status})")
            
            # Parse with BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Extract basic information
            title = soup.title.string.strip() if soup.title else ""
            
            # Extract meta description
            description = ""
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc:
                description = meta_desc.get('content', '').strip()
            
            # Clean HTML - remove unwanted elements
            for element in soup(['script', 'style', 'nav', 'header', 'footer', 'aside',
                               'iframe', 'noscript', 'form', 'button']):
                element.decompose()
            
            # Remove ads and social media elements
            for element in soup.find_all(class_=lambda x: x and any(
                keyword in x.lower() for keyword in ['ad', 'advertisement', 'social', 'share', 'comment', 'sidebar']
            )):
                element.decompose()
            
            # Extract main content
            main_content = None
            for selector in ['main', 'article', '.content', '.main-content', '.post-content', '.entry-content']:
                if selector.startswith('.'):
                    main_element = soup.select_one(selector)
                else:
                    main_element = soup.find(selector)
                
                if main_element and len(main_element.get_text(strip=True)) > 200:
                    main_content = main_element
                    break
            
            if not main_content:
                main_content = soup.find('body') or soup
            
            # Get cleaned HTML and text
            cleaned_html = str(main_content)
            text_content = main_content.get_text(separator=' ', strip=True)

            # 🚀 NEW: Generate structured markdown from BeautifulSoup
            structured_markdown = self._html_to_markdown(main_content, title)
            
            # Extract images
            imgs = []
            for img in soup.find_all('img'):
                src = img.get('src', '')
                if src:
                    # Convert relative URLs to absolute
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = urljoin(url, src)
                    elif not src.startswith(('http://', 'https://')):
                        src = urljoin(url, src)
                    
                    imgs.append({
                        'src': src,
                        'alt': img.get('alt', ''),
                        'width': img.get('width', 0),
                        'height': img.get('height', 0)
                    })
            
            # Calculate basic analytics
            elem_count = len(soup.find_all())
            max_elem_depth = self._calculate_max_depth(soup)
            
            # Create parsed content for compatibility
            parsed = {
                'title': title,
                'content': cleaned_html,
                'textContent': text_content,
                'markdown': structured_markdown,  # 🚀 NEW: Add structured markdown
                'length': len(text_content),
                'excerpt': text_content[:200] if text_content else '',
                'byline': '',
                'dir': soup.get('dir', 'ltr') if hasattr(soup, 'get') else 'ltr',
                'siteName': '',
                'lang': soup.get('lang', 'en') if hasattr(soup, 'get') else 'en',
                'publishedTime': ''
            }
            
            # Create snapshot
            snapshot = PageSnapshot(
                title=title,
                description=description,
                href=url,
                html=cleaned_html,
                text=text_content,
                markdown=structured_markdown,  # 🚀 NEW: Add structured markdown
                status=status,
                status_text=status_text,
                parsed=parsed,
                imgs=imgs,
                max_elem_depth=max_elem_depth,
                elem_count=elem_count,
                is_intermediate=False
            )
            
            yield snapshot
            
        except Exception as e:
            logger.error(f"❌ Error scraping {url}: {e}")
            # Yield error snapshot
            error_snapshot = PageSnapshot(
                href=url,
                title="Error",
                text=f"Error scraping {url}: {str(e)}",
                status=500,
                is_intermediate=False
            )
            yield error_snapshot
    
    def _html_to_markdown(self, soup_element, title=""):
        """
        🚀 ENHANCED: Convert BeautifulSoup element to structured markdown
        This reduces Gemini's workload and improves output quality
        """
        if not soup_element:
            return ""

        markdown_lines = []

        # Add title if provided
        if title:
            markdown_lines.append(f"# {title}\n")

        # Process headers
        for header in soup_element.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
            text = header.get_text(strip=True)
            if text:
                level = int(header.name[1])
                markdown_lines.append(f"{'#' * level} {text}\n")

        # Process paragraphs
        for para in soup_element.find_all('p'):
            text = para.get_text(strip=True)
            if text and len(text) > 10:  # Skip very short paragraphs
                markdown_lines.append(f"{text}\n")

        # Process lists
        for ul in soup_element.find_all('ul'):
            for li in ul.find_all('li', recursive=False):
                text = li.get_text(strip=True)
                if text:
                    markdown_lines.append(f"- {text}")
            markdown_lines.append("")  # Empty line after list

        for ol in soup_element.find_all('ol'):
            for i, li in enumerate(ol.find_all('li', recursive=False), 1):
                text = li.get_text(strip=True)
                if text:
                    markdown_lines.append(f"{i}. {text}")
            markdown_lines.append("")  # Empty line after list

        # Process blockquotes
        for quote in soup_element.find_all('blockquote'):
            text = quote.get_text(strip=True)
            if text:
                markdown_lines.append(f"> {text}\n")

        # If no structured content found, use plain text with paragraphs
        if not markdown_lines or (len(markdown_lines) == 1 and title):
            text_content = soup_element.get_text(separator='\n\n', strip=True)
            paragraphs = [p.strip() for p in text_content.split('\n\n') if p.strip() and len(p.strip()) > 20]
            markdown_lines.extend([f"{para}\n" for para in paragraphs])

        return '\n'.join(markdown_lines).strip()

    def _calculate_max_depth(self, soup) -> int:
        """Calculate maximum element depth in the DOM"""
        try:
            max_depth = 0
            for element in soup.find_all():
                depth = len(list(element.parents))
                max_depth = max(max_depth, depth)
            return max_depth
        except:
            return 0
    
    async def crawl_batch_raw(self, urls: List[str], max_content_length: int = 100000, max_concurrent: int = 10) -> List[Dict[str, str]]:
        """
        🚀 BATCH CRAWLING - Crawl multiple URLs concurrently and return raw content for batch processing

        Args:
            urls: List of URLs to crawl
            max_content_length: Maximum content length per URL
            max_concurrent: Maximum number of concurrent crawl operations

        Returns:
            List of dicts with 'url' and 'raw_content' keys
        """
        if not self._initialized:
            await self.initialize()

        logger.info(f"🕷️ Starting concurrent batch crawl of {len(urls)} URLs (max_concurrent: {max_concurrent})")

        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(max_concurrent)

        async def crawl_single_url(url: str) -> Dict[str, str]:
            """Crawl a single URL with semaphore control"""
            async with semaphore:
                try:
                    logger.debug(f"🕷️ Crawling: {url}")

                    # Get raw content without Gemini processing
                    raw_content = await self._fetch_content_only(url, max_content_length)

                    if raw_content:
                        logger.debug(f"✅ Raw content extracted from {url}: {len(raw_content)} chars")
                        return {
                            'url': url,
                            'raw_content': raw_content
                        }
                    else:
                        logger.warning(f"⚠️ No content from {url}")
                        return {
                            'url': url,
                            'raw_content': ''
                        }

                except Exception as e:
                    logger.error(f"❌ Error crawling {url}: {e}")
                    return {
                        'url': url,
                        'raw_content': ''
                    }

        # Execute all crawls concurrently
        start_time = time.time()
        batch_data = await asyncio.gather(*[crawl_single_url(url) for url in urls])
        crawl_time = time.time() - start_time

        logger.info(f"✅ Concurrent batch crawl completed: {len(batch_data)} URLs processed in {crawl_time:.2f}s")
        return batch_data

    async def process_batch_with_gemini(self, batch_data: List[Dict[str, str]], query: str = "", force_detailed: bool = False) -> List[JiniCrawlResult]:
        """
        🤖 BATCH GEMINI PROCESSING - Process multiple raw contents with single Gemini call

        Args:
            batch_data: List of dicts with 'url' and 'raw_content' keys
            query: Search query for context

        Returns:
            List of JiniCrawlResult objects
        """
        if not batch_data:
            return []

        try:
            logger.info(f"🤖 Starting batch Gemini processing for {len(batch_data)} URLs")
            logger.info(f"🔧 Force detailed mode: {force_detailed}")
            logger.info(f"🔧 Gemini processor available: {self.gemini_processor is not None}")

            # Single Gemini batch call - HUGE efficiency gain!
            processed_results = await self.gemini_processor.process_batch(batch_data, query, force_detailed)
            logger.info(f"✅ Batch processing completed, got {len(processed_results)} results")

            # Convert to JiniCrawlResult objects
            results = []
            for i, processed in enumerate(processed_results):
                original_url = batch_data[i]['url'] if i < len(batch_data) else 'unknown'

                result = JiniCrawlResult(
                    url=original_url,
                    success=True,
                    processed_content=processed.get('processed_content', ''),
                    title=processed.get('title', 'No title'),
                    error=None,
                    metadata={
                        'key_points': processed.get('key_points', []),
                        'relevance_score': processed.get('relevance_score', 0.5),
                        'processing_method': 'batch_gemini'
                    }
                )
                results.append(result)

            logger.info(f"✅ Batch Gemini processing completed: {len(results)} results")
            return results

        except Exception as e:
            logger.error(f"❌ Batch Gemini processing failed: {e}")
            logger.error(f"❌ Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"❌ Full traceback: {traceback.format_exc()}")

            # Fallback: create results with basic HTML extraction
            results = []
            for item in batch_data:
                raw_content = item.get('raw_content', '')

                # Basic HTML extraction as fallback
                fallback_content = self._extract_basic_content(raw_content)
                title = self._extract_title_from_html(raw_content)

                result = JiniCrawlResult(
                    url=item['url'],
                    success=True,  # Mark as success with fallback content
                    raw_content=raw_content[:1000] + "..." if len(raw_content) > 1000 else raw_content,
                    processed_content=fallback_content,
                    title=title,
                    error=None,  # Don't show error, use fallback
                    metadata={'processing_method': 'html_fallback'}
                )
                results.append(result)

            return results

    def _extract_basic_content(self, html_content: str) -> str:
        """Extract basic content from HTML as fallback when Gemini fails"""
        try:
            import re

            # Extract title
            title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
            title = title_match.group(1) if title_match else ""

            # Extract meta description
            desc_match = re.search(r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\']', html_content, re.IGNORECASE)
            description = desc_match.group(1) if desc_match else ""

            # Extract some text content (basic)
            text_content = re.sub(r'<[^>]+>', ' ', html_content)
            text_content = re.sub(r'\s+', ' ', text_content).strip()

            # Build fallback content
            result = []
            if title:
                result.append(f"# {title}")
            if description:
                result.append(f"\n{description}")

            # Add some text content (limited)
            if text_content:
                words = text_content.split()[:100]  # First 100 words
                result.append(f"\n\n{' '.join(words)}...")

            return '\n'.join(result) if result else "Content extraction failed"

        except Exception as e:
            logger.warning(f"⚠️ Basic content extraction failed: {e}")
            return "Content extraction failed"

    def _extract_title_from_html(self, html_content: str) -> str:
        """Extract title from HTML"""
        try:
            import re
            title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
            return title_match.group(1).strip() if title_match else "No title"
        except:
            return "No title"

    async def _fetch_content_only(self, url: str, max_content_length: int = 100000) -> str:
        """
        🕷️ FETCH RAW CONTENT - Smart fetch with advanced site handling

        Args:
            url: URL to crawl
            max_content_length: Maximum content length

        Returns:
            Raw content string
        """
        try:
            logger.debug(f"🕷️ Smart fetch from {url}")

            # 🚀 NEW: TLS client as PRIMARY method for ALL sites
            if self.use_tls_client and self.tls_session:
                logger.debug(f"🔒 Using TLS client as primary for {url}")
                try:
                    # Run TLS bypass in executor
                    loop = asyncio.get_event_loop()
                    tls_result = await loop.run_in_executor(
                        None, self._fetch_with_tls_bypass, url
                    )

                    if tls_result and tls_result.get('content'):
                        content = tls_result['content'][:max_content_length]
                        logger.debug(f"✅ TLS primary fetch: {len(content)} chars from {url}")
                        return content
                    else:
                        logger.debug(f"⚠️ TLS primary failed for {url}, trying fallback")

                except Exception as e:
                    logger.debug(f"⚠️ TLS primary error for {url}: {e}")

            # 🔧 ENHANCED: Check if this is a problematic site (secondary method)
            domain = self.advanced_handler.get_domain(url)
            if any(site in domain for site in ['vnexpress.net', 'gsmarena.com']):
                logger.debug(f"🎯 Using advanced handler for {domain}")
                content = await self.advanced_handler.smart_fetch(url)
                if content:
                    # Truncate to max length
                    content = content[:max_content_length]
                    logger.debug(f"✅ Advanced fetch: {len(content)} chars from {url}")
                    return content
                else:
                    logger.debug(f"⚠️ Advanced handler failed for {url}, trying final fallback")

            # Final fallback to simple HTTP request
            import aiohttp
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=15)) as session:
                async with session.get(
                    url,
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                    }
                ) as response:
                    if response.status == 200:
                        html_content = await response.text()
                        if html_content:
                            # Truncate to max length
                            content = html_content[:max_content_length]
                            logger.debug(f"✅ Final fallback fetch: {len(content)} chars from {url}")
                            return content

                    logger.warning(f"⚠️ HTTP {response.status} for {url}")
                    return ""

        except Exception as e:
            logger.error(f"❌ Fetch error for {url}: {e}")
            return ""

    async def crawl_and_process(self, url: str, max_content_length: int = 10000) -> JiniCrawlResult:
        """
        Crawl a URL and process it through the complete pipeline
        
        Args:
            url: URL to crawl
            max_content_length: Maximum content length to send to Gemini (to avoid API limits)
            
        Returns:
            JiniCrawlResult with processing results
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            # Step 1: Crawl with simple crawler
            logger.info(f"🕷️ Crawling {url} with simple crawler...")
            crawl_result = None
            async for snapshot in self.scrap_url(url):
                if not snapshot.is_intermediate:
                    crawl_result = snapshot
                    break
            
            if not crawl_result:
                return JiniCrawlResult(
                    success=False,
                    url=url,
                    error="Failed to crawl URL"
                )
            
            # Step 2: Process with Gemini
            logger.info(f"🤖 Processing content with Gemini...")
            
            # Limit content length to avoid API limits
            content_to_process = crawl_result.html[:max_content_length]
            
            gemini_result = await self.gemini_processor.process_content(
                content_to_process, "html_to_markdown"
            )
            
            if not gemini_result.get("success"):
                return JiniCrawlResult(
                    success=False,
                    url=url,
                    title=crawl_result.title,
                    original_content=crawl_result.html[:500],  # First 500 chars
                    error=f"Gemini processing failed: {gemini_result.get('error', 'Unknown error')}"
                )
            
            return JiniCrawlResult(
                success=True,
                url=url,
                title=crawl_result.title,
                original_content=crawl_result.html,
                cleaned_content=crawl_result.html,  # Already cleaned by simple crawler
                processed_content=gemini_result.get("processed_content", ""),
                processing_time=gemini_result.get("processing_time", 0.0),
                metadata={
                    "original_length": gemini_result.get("original_length", 0),
                    "output_length": gemini_result.get("output_length", 0),
                    "model": gemini_result.get("model", "unknown"),
                    "task_type": gemini_result.get("task_type", "html_to_markdown")
                }
            )
            
        except Exception as e:
            logger.error(f"❌ Error processing {url}: {e}")
            return JiniCrawlResult(
                success=False,
                url=url,
                error=str(e)
            )
    
    async def crawl_and_process_with_task(self, url: str, max_content_length: int = 200000, task_type: str = "html_to_markdown") -> JiniCrawlResult:
        """
        Crawl a URL and process it with specific task type

        Args:
            url: URL to crawl
            max_content_length: Maximum content length to process
            task_type: Type of processing task (html_to_markdown, full_article, news_summary, enhanced_extraction)

        Returns:
            JiniCrawlResult with processed content
        """
        try:
            start_time = time.time()
            logger.info(f"🚀 Starting {task_type} crawl for: {url}")

            # Fetch raw content
            raw_content = await self._fetch_content_only(url, max_content_length)

            if not raw_content:
                return JiniCrawlResult(
                    success=False,
                    url=url,
                    error="Failed to fetch content"
                )

            # Process with Gemini using specific task type
            if self.gemini_processor:
                processed_result = await self.gemini_processor.process_content(
                    content=raw_content,
                    task_type=task_type,
                    max_length=max_content_length
                )

                processing_time = time.time() - start_time

                return JiniCrawlResult(
                    success=True,
                    url=url,
                    raw_content=raw_content[:1000] + "..." if len(raw_content) > 1000 else raw_content,
                    processed_content=processed_result.get("content", ""),
                    title=processed_result.get("title", ""),
                    processing_time=processing_time,
                    metadata={
                        "task_type": task_type,
                        "original_length": len(raw_content),
                        "output_length": len(processed_result.get("content", "")),
                        "model": self.gemini_processor.config.model_name,
                        "processing_time": processing_time
                    }
                )
            else:
                # Fallback without Gemini processing
                return JiniCrawlResult(
                    success=True,
                    url=url,
                    raw_content=raw_content,
                    processed_content=raw_content,
                    title="Raw Content",
                    processing_time=time.time() - start_time
                )

        except Exception as e:
            logger.error(f"❌ Error in {task_type} crawl for {url}: {e}")
            return JiniCrawlResult(
                success=False,
                url=url,
                error=str(e)
            )

    async def crawl_full_article(self, url: str, max_content_length: int = 800000) -> JiniCrawlResult:
        """
        Crawl a URL and extract the COMPLETE article content (not summarized)
        
        Args:
            url: URL to crawl
            max_content_length: Maximum content length to send to Gemini (higher limit for full articles)
            
        Returns:
            JiniCrawlResult with full article content
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            # Step 1: Crawl with simple crawler
            logger.info(f"🕷️ Crawling full article from {url}...")
            crawl_result = None
            async for snapshot in self.scrap_url(url):
                if not snapshot.is_intermediate:
                    crawl_result = snapshot
                    break
            
            if not crawl_result:
                return JiniCrawlResult(
                    success=False,
                    url=url,
                    error="Failed to crawl URL"
                )
            
            # Step 2: Process with Gemini using full_article mode
            logger.info(f"🤖 Extracting full article content with Gemini...")
            
            # Use higher content length limit for full articles
            content_to_process = crawl_result.html[:max_content_length]
            
            gemini_result = await self.gemini_processor.process_content(
                content_to_process, "full_article"
            )
            
            if not gemini_result.get("success"):
                return JiniCrawlResult(
                    success=False,
                    url=url,
                    title=crawl_result.title,
                    original_content=crawl_result.html[:500],  # First 500 chars
                    error=f"Gemini full article processing failed: {gemini_result.get('error', 'Unknown error')}"
                )
            
            return JiniCrawlResult(
                success=True,
                url=url,
                title=crawl_result.title,
                original_content=crawl_result.html,
                cleaned_content=crawl_result.html,  # Already cleaned by simple crawler
                processed_content=gemini_result.get("processed_content", ""),
                processing_time=gemini_result.get("processing_time", 0.0),
                metadata={
                    "original_length": gemini_result.get("original_length", 0),
                    "output_length": gemini_result.get("output_length", 0),
                    "model": gemini_result.get("model", "unknown"),
                    "task_type": "full_article",
                    "content_type": "complete_article"
                }
            )
            
        except Exception as e:
            logger.error(f"❌ Error processing full article from {url}: {e}")
            return JiniCrawlResult(
                success=False,
                url=url,
                error=str(e)
            )
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the crawler"""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Simple health check - test basic functionality
            test_result = {
                "status": "healthy",
                "crawler_initialized": self._initialized,
                "session_active": self.session is not None
            }
            
            # Test Gemini processor
            gemini_health = await self.gemini_processor.health_check()
            test_result["gemini_processor"] = gemini_health
            
            # Overall status
            if gemini_health.get("status") != "healthy":
                test_result["status"] = "degraded"
            
            return test_result
            
        except Exception as e:
            logger.error(f"❌ Health check failed: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

# Convenience functions
async def create_jini_crawler() -> JiniCrawler:
    """Create and initialize a Jini crawler"""
    crawler = JiniCrawler()
    await crawler.initialize()
    return crawler

async def quick_crawl(url: str, max_content_length: int = 10000) -> JiniCrawlResult:
    """Quick crawl and process a URL"""
    crawler = JiniCrawler()
    try:
        await crawler.initialize()
        return await crawler.crawl_and_process(url, max_content_length)
    finally:
        await crawler.cleanup()

# Test function
async def test_jini_crawler():
    """Test Jini crawler"""
    print("🧪 Testing Jini Crawler")
    print("=" * 50)
    
    crawler = JiniCrawler()
    await crawler.initialize()
    
    # Test with a simple URL
    test_url = "https://httpbin.org/html"
    
    result = await crawler.crawl_and_process(test_url)
    
    print(f"✅ Success: {result.success}")
    if result.success:
        print(f"   Title: {result.title}")
        print(f"   Processed content length: {len(result.processed_content or '')} characters")
        print(f"   Processing time: {result.processing_time:.2f}s")
        if result.processed_content:
            preview = result.processed_content[:200] + "..." if len(result.processed_content) > 200 else result.processed_content
            print(f"   Content preview: {preview}")
    else:
        print(f"   Error: {result.error}")
    
    # Health check
    health = await crawler.health_check()
    print(f"\n🏥 Health check: {health['status']}")
    
    await crawler.cleanup()
    print("\n✅ Test completed")

if __name__ == "__main__":
    asyncio.run(test_jini_crawler())