#!/usr/bin/env python3
"""
Improved HTTP Wrapper for Jina Crawler with better news extraction
"""

import asyncio
import json
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Import improved crawler
from improved_jini_crawler import ImprovedJiniCrawler

# Pydantic models for request validation
class CrawlUrlRequest(BaseModel):
    url: str = Field(..., description="URL to crawl and process", example="https://dantri.com.vn")
    max_content_length: Optional[int] = Field(
        20000, 
        description="Maximum content length to process (increased for better extraction)", 
        ge=1000, 
        le=50000
    )
    task_type: Optional[str] = Field(
        "news_extraction",
        description="Type of processing task",
        example="news_extraction"
    )

class CrawlBatchRequest(BaseModel):
    urls: list[str] = Field(..., description="List of URLs to crawl", example=["https://dantri.com.vn", "https://vnexpress.net"])
    max_content_length: Optional[int] = Field(
        20000, 
        description="Maximum content length per URL", 
        ge=1000, 
        le=50000
    )
    task_type: Optional[str] = Field(
        "news_extraction",
        description="Type of processing task",
        example="news_extraction"
    )

class EmptyRequest(BaseModel):
    pass

app = FastAPI(
    title="Improved Jina Crawler Tools",
    description="Improved HTTP wrapper for Jina Crawler with better news extraction",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global crawler instance
crawler = None

@app.on_event("startup")
async def startup_event():
    """Initialize crawler on startup"""
    global crawler
    crawler = ImprovedJiniCrawler()
    await crawler.initialize()
    print("🚀 Improved Jina Crawler HTTP server started")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    global crawler
    if crawler:
        await crawler.cleanup()
    print("✅ Improved Jina Crawler HTTP server stopped")

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Improved Jina Crawler HTTP Server", "tools": 6}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "server": "improved-jina-crawler"}

@app.post("/crawl_url", operation_id="crawl_url")
async def crawl_url(request: CrawlUrlRequest):
    """Crawl a single URL with improved extraction"""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        result = await crawler.crawl_and_process(
            request.url, 
            request.max_content_length,
            request.task_type
        )
        
        response = {
            "success": result.success,
            "url": result.url,
            "title": result.title,
            "processed_content": result.processed_content,
            "processing_time": result.processing_time,
            "error": result.error,
            "metadata": result.metadata,
            "crawler_type": "improved_jina_crawler"
        }
        
        return JSONResponse(content=response)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Crawl failed: {str(e)}")

@app.post("/crawl_batch", operation_id="crawl_batch")
async def crawl_batch(request: CrawlBatchRequest):
    """Crawl multiple URLs with improved extraction"""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        # Process URLs in parallel
        tasks = []
        for url in request.urls:
            task = crawler.crawl_and_process(
                url, 
                request.max_content_length,
                request.task_type
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Format results
        formatted_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                formatted_results.append({
                    "success": False,
                    "url": request.urls[i],
                    "error": str(result)
                })
            else:
                formatted_results.append({
                    "success": result.success,
                    "url": result.url,
                    "title": result.title,
                    "processed_content": result.processed_content,
                    "processing_time": result.processing_time,
                    "error": result.error,
                    "metadata": result.metadata
                })
        
        response = {
            "batch_size": len(request.urls),
            "successful_crawls": sum(1 for r in formatted_results if r["success"]),
            "failed_crawls": sum(1 for r in formatted_results if not r["success"]),
            "results": formatted_results,
            "crawler_type": "improved_jina_batch"
        }
        
        return JSONResponse(content=response)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Batch crawl failed: {str(e)}")

@app.post("/health_check", operation_id="health_check")
async def health_check_tool(request: EmptyRequest = None):
    """Health check tool"""
    global crawler
    
    if not crawler:
        return JSONResponse(content={"status": "error", "message": "Crawler not initialized"})
    
    try:
        health = await crawler.health_check()
        return JSONResponse(content={
            "status": "healthy",
            "crawler_health": health,
            "server_type": "improved_jina_crawler"
        })
    except Exception as e:
        return JSONResponse(content={"status": "error", "error": str(e)})

@app.post("/get_stats", operation_id="get_stats")
async def get_stats(request: EmptyRequest = None):
    """Get crawler statistics"""
    return JSONResponse(content={
        "server_type": "improved_jina_crawler",
        "features": [
            "Full OpenAPI 3.0 compliance",
            "Pydantic request validation",
            "Compatible with Open WebUI",
            "No authentication required",
            "Async crawling with aiohttp",
            "BeautifulSoup HTML cleaning",
            "Improved Gemini AI content processing",
            "Better Vietnamese news extraction",
            "Batch processing support",
            "Increased content limits (20K chars)"
        ],
        "tools_available": 6,
        "auth_required": False,
        "improved_features": {
            "news_extraction_prompt": "Custom prompt for better news extraction",
            "content_limit": "20000 characters (vs 10000)",
            "output_tokens": "8192 tokens (vs 2048)",
            "task_types": ["news_extraction", "html_to_markdown", "summarize", "clean"]
        }
    })

@app.post("/test_crawl", operation_id="test_crawl")
async def test_crawl(request: EmptyRequest = None):
    """Test crawl with a sample URL"""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        # Test with a simple URL
        test_url = "https://httpbin.org/html"
        result = await crawler.crawl_and_process(test_url, 5000, "news_extraction")
        
        return JSONResponse(content={
            "test_successful": result.success,
            "test_url": test_url,
            "title": result.title,
            "content_length": len(result.processed_content or ""),
            "processing_time": result.processing_time,
            "error": result.error,
            "message": "Test crawl completed successfully" if result.success else "Test crawl failed"
        })
        
    except Exception as e:
        return JSONResponse(content={
            "test_successful": False,
            "error": str(e),
            "message": "Test crawl failed with exception"
        })

@app.post("/extract_news", operation_id="extract_news")
async def extract_news(request: CrawlUrlRequest):
    """Extract news with improved prompt (convenience endpoint)"""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        # Force news_extraction task type
        result = await crawler.crawl_and_process(
            request.url, 
            request.max_content_length,
            "news_extraction"
        )
        
        response = {
            "success": result.success,
            "url": result.url,
            "title": result.title,
            "processed_content": result.processed_content,
            "processing_time": result.processing_time,
            "error": result.error,
            "metadata": result.metadata,
            "crawler_type": "improved_jina_crawler_news"
        }
        
        return JSONResponse(content=response)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"News extraction failed: {str(e)}")

if __name__ == "__main__":
    import os
    port = int(os.environ.get("PORT", 8008))
    uvicorn.run(
        "improved_http_wrapper:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info"
    )