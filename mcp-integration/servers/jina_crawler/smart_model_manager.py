#!/usr/bin/env python3
"""
Smart Model Manager for Gemini API
Handles intelligent model selection and fallback mechanisms for rate limit management
"""

import os
import time
import logging
import asyncio
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import re

logger = logging.getLogger(__name__)

@dataclass
class ModelConfig:
    """Configuration for a specific Gemini model."""
    name: str
    input_tokens_per_minute: int
    output_tokens_max: int
    cost_per_1k_input: float
    cost_per_1k_output: float
    api_url: str = "https://generativelanguage.googleapis.com/v1beta/models"

@dataclass
class UsageStats:
    """Track model usage statistics."""
    tokens_used: int = 0
    requests_made: int = 0
    last_reset: datetime = None
    rate_limited_count: int = 0
    
    def reset_if_needed(self):
        """Reset stats if a minute has passed."""
        now = datetime.now()
        if self.last_reset is None or (now - self.last_reset).total_seconds() >= 60:
            self.tokens_used = 0
            self.requests_made = 0
            self.rate_limited_count = 0
            self.last_reset = now

class TokenEstimator:
    """Estimate token count from content."""
    
    # Token estimation ratios for different content types
    RATIOS = {
        'vietnamese': 3.5,  # chars per token for Vietnamese
        'english': 4.0,     # chars per token for English
        'html': 5.0,        # chars per token for HTML (more markup)
        'mixed': 3.8        # chars per token for mixed content
    }
    
    @classmethod
    def estimate_tokens(cls, content: str, content_type: str = 'mixed', safety_margin: float = 0.2) -> int:
        """
        Estimate token count from content.
        
        Args:
            content: Text content to estimate
            content_type: Type of content ('vietnamese', 'english', 'html', 'mixed')
            safety_margin: Safety margin to add (default 20%)
            
        Returns:
            Estimated token count
        """
        if not content:
            return 0
            
        char_count = len(content)
        ratio = cls.RATIOS.get(content_type, cls.RATIOS['mixed'])
        
        # Basic estimation
        estimated_tokens = int(char_count / ratio)
        
        # Add safety margin
        estimated_tokens = int(estimated_tokens * (1 + safety_margin))
        
        # Additional adjustments based on content analysis
        if cls._has_complex_formatting(content):
            estimated_tokens = int(estimated_tokens * 1.1)
            
        return max(estimated_tokens, 1)  # Minimum 1 token
    
    @classmethod
    def _has_complex_formatting(cls, content: str) -> bool:
        """Check if content has complex formatting that might increase token count."""
        # Check for HTML tags, markdown, special characters
        html_tags = len(re.findall(r'<[^>]+>', content))
        markdown_elements = len(re.findall(r'[*_`#\[\]()]', content))
        special_chars = len(re.findall(r'[^\w\s\u00C0-\u024F\u1E00-\u1EFF]', content))
        
        return (html_tags > 10 or markdown_elements > 20 or special_chars > 50)
    
    @classmethod
    def detect_content_type(cls, content: str) -> str:
        """Detect content type for better token estimation."""
        if not content:
            return 'mixed'
            
        # Check for HTML
        if re.search(r'<[^>]+>', content):
            return 'html'
            
        # Check for Vietnamese characters
        vietnamese_chars = len(re.findall(r'[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]', content.lower()))
        total_chars = len(re.findall(r'[a-zA-Zàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]', content))
        
        if total_chars > 0 and vietnamese_chars / total_chars > 0.1:
            return 'vietnamese'
        else:
            return 'english'

class SmartModelManager:
    """
    Smart model manager that handles model selection and fallback mechanisms.
    """
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        
        # Define available models
        self.models = {
            'gemini-2.5-flash-lite': ModelConfig(
                name='gemini-2.5-flash-lite',
                input_tokens_per_minute=250000,
                output_tokens_max=65536,
                cost_per_1k_input=0.075,  # $0.075 per 1K input tokens
                cost_per_1k_output=0.30   # $0.30 per 1K output tokens
            ),
            'gemini-2.0-flash': ModelConfig(
                name='gemini-2.0-flash',
                input_tokens_per_minute=1000000,
                output_tokens_max=8192,
                cost_per_1k_input=0.15,   # $0.15 per 1K input tokens (estimated)
                cost_per_1k_output=0.60   # $0.60 per 1K output tokens (estimated)
            ),
            'gemini-2.0-flash-lite': ModelConfig(
                name='gemini-2.0-flash-lite',
                input_tokens_per_minute=500000,  # Higher than 2.5-flash-lite
                output_tokens_max=8192,
                cost_per_1k_input=0.075,  # Same as 2.5-flash-lite
                cost_per_1k_output=0.30   # Same as 2.5-flash-lite
            )
        }
        
        # Usage tracking
        self.usage_stats = {
            model_name: UsageStats() for model_name in self.models.keys()
        }
        
        # Configuration with multiple fallback levels
        self.primary_model = os.getenv('GEMINI_PRIMARY_MODEL', 'gemini-2.5-flash-lite')

        # Define fallback sequence: primary -> fallback1 -> fallback2
        self.fallback_sequence = [
            'gemini-2.5-flash-lite',  # Primary
            'gemini-2.0-flash',       # Fallback 1
            'gemini-2.0-flash-lite'   # Fallback 2 (final)
        ]

        self.token_threshold = int(os.getenv('GEMINI_TOKEN_THRESHOLD', '200000'))
        self.enable_smart_selection = os.getenv('GEMINI_SMART_SELECTION', 'true').lower() == 'true'

        logger.info(f"SmartModelManager initialized with fallback sequence: {' -> '.join(self.fallback_sequence)}")
    
    def select_model(self, content: str, prompt_template: str = "") -> Tuple[str, str]:
        """
        Select the best model for the given content.
        
        Args:
            content: Content to process
            prompt_template: Prompt template to include in estimation
            
        Returns:
            Tuple of (model_name, reason)
        """
        if not self.enable_smart_selection:
            return self.primary_model, "smart_selection_disabled"
        
        # Estimate total tokens (content + prompt)
        content_type = TokenEstimator.detect_content_type(content)
        content_tokens = TokenEstimator.estimate_tokens(content, content_type)
        prompt_tokens = TokenEstimator.estimate_tokens(prompt_template, 'english')
        total_tokens = content_tokens + prompt_tokens
        
        logger.info(f"Token estimation: content={content_tokens}, prompt={prompt_tokens}, total={total_tokens}")
        
        # Reset usage stats if needed
        for stats in self.usage_stats.values():
            stats.reset_if_needed()
        
        # Check if primary model can handle the load
        primary_stats = self.usage_stats[self.primary_model]
        primary_config = self.models[self.primary_model]
        
        # Check if we would exceed rate limit with primary model
        if (primary_stats.tokens_used + total_tokens) > primary_config.input_tokens_per_minute:
            fallback_model = self.fallback_sequence[1]  # First fallback
            return fallback_model, f"primary_rate_limit_would_exceed ({primary_stats.tokens_used + total_tokens} > {primary_config.input_tokens_per_minute})"

        # Check if content is too large for primary model threshold
        if total_tokens > self.token_threshold:
            fallback_model = self.fallback_sequence[1]  # First fallback
            return fallback_model, f"content_too_large ({total_tokens} > {self.token_threshold})"

        # Check if primary model has been rate limited recently
        if primary_stats.rate_limited_count > 0:
            fallback_model = self.fallback_sequence[1]  # First fallback
            return fallback_model, f"primary_recently_rate_limited ({primary_stats.rate_limited_count} times)"
        
        return self.primary_model, f"optimal_choice ({total_tokens} tokens)"
    
    def record_usage(self, model_name: str, tokens_used: int, success: bool, rate_limited: bool = False):
        """Record usage statistics for a model."""
        if model_name in self.usage_stats:
            stats = self.usage_stats[model_name]
            stats.reset_if_needed()
            
            if success:
                stats.tokens_used += tokens_used
                stats.requests_made += 1
            
            if rate_limited:
                stats.rate_limited_count += 1
                logger.warning(f"Rate limit recorded for {model_name}. Count: {stats.rate_limited_count}")
    
    def get_model_config(self, model_name: str) -> Optional[ModelConfig]:
        """Get configuration for a specific model."""
        return self.models.get(model_name)
    
    def get_usage_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get current usage statistics for all models."""
        stats = {}
        for model_name, usage in self.usage_stats.items():
            usage.reset_if_needed()
            stats[model_name] = {
                'tokens_used': usage.tokens_used,
                'requests_made': usage.requests_made,
                'rate_limited_count': usage.rate_limited_count,
                'last_reset': usage.last_reset.isoformat() if usage.last_reset else None,
                'tokens_remaining': self.models[model_name].input_tokens_per_minute - usage.tokens_used
            }
        return stats
    
    def should_fallback(self, model_name: str, error_message: str) -> Tuple[bool, str]:
        """
        Determine if we should fallback to another model based on error.

        Args:
            model_name: Current model that failed
            error_message: Error message from API

        Returns:
            Tuple of (should_fallback, fallback_model)
        """
        # Check for rate limit errors or other failures
        if ("429" in error_message or
            "rate limit" in error_message.lower() or
            "quota" in error_message.lower() or
            "unavailable" in error_message.lower() or
            "overloaded" in error_message.lower()):

            # Find current model in fallback sequence
            try:
                current_index = self.fallback_sequence.index(model_name)
                # If not the last model in sequence, fallback to next
                if current_index < len(self.fallback_sequence) - 1:
                    next_model = self.fallback_sequence[current_index + 1]
                    logger.info(f"🔄 Fallback: {model_name} -> {next_model}")
                    return True, next_model
                else:
                    # Already at final fallback, no more options
                    logger.warning(f"❌ No more fallback options after {model_name}")
                    return False, ""
            except ValueError:
                # Model not in sequence, fallback to primary fallback
                logger.warning(f"⚠️ Unknown model {model_name}, falling back to {self.fallback_sequence[1]}")
                return True, self.fallback_sequence[1]

        return False, ""

# Global instance
_smart_model_manager = None

def get_smart_model_manager() -> SmartModelManager:
    """Get or create global SmartModelManager instance."""
    global _smart_model_manager
    
    if _smart_model_manager is None:
        api_key = os.getenv("GEMINI_API_KEY", "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM")
        _smart_model_manager = SmartModelManager(api_key)
    
    return _smart_model_manager
