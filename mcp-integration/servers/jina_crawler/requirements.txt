# Core dependencies
aiohttp>=3.9.0
asyncio-throttle>=1.0.2
beautifulsoup4>=4.12.0
tenacity>=8.2.0
fake-useragent>=1.4.0
tls-client>=0.2.0
ddgs>=4.0.0
python-dotenv>=1.0.0

# Gemini AI
google-generativeai>=0.3.0

# Data processing
pandas>=2.0.0
numpy>=1.24.0

# HTTP server
fastapi>=0.104.0
uvicorn>=0.24.0
pydantic>=2.5.0

# Logging and monitoring
structlog>=23.2.0

# Optional dependencies for media parsing (disabled but kept for compatibility)
PyPDF2>=3.0.0
python-docx>=1.1.0
Pillow>=10.0.0

# MCP protocol
mcp>=1.0.0

# Additional utilities
requests>=2.31.0
lxml>=4.9.0
html5lib>=1.1