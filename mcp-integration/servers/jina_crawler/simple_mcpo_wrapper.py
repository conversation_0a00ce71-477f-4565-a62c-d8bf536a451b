#!/usr/bin/env python3
"""
Simple MCPO wrapper for Jina Crawler
Provides basic MCP tools that call the HTTP wrapper internally
"""

import asyncio
import json
import logging
import aiohttp
from typing import Any, Dict, List, Optional

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)
from mcp.server.lowlevel.server import NotificationOptions

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("jina-crawler-simple-wrapper")

# Initialize the MCP server
server = Server("jina_crawler")

# HTTP wrapper URL (internal)
HTTP_WRAPPER_URL = "http://localhost:8009"
API_KEY = "jina-crawler-secret-key-2025"

class SimpleJinaCrawlerWrapper:
    def __init__(self):
        self.session = None
    
    async def initialize(self):
        """Initialize HTTP session"""
        self.session = aiohttp.ClientSession()
        logger.info("Simple Jina Crawler wrapper initialized")
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.session:
            await self.session.close()
    
    async def call_http_tool(self, tool_name: str, params: dict) -> dict:
        """Call HTTP wrapper tool"""
        if not self.session:
            await self.initialize()
        
        try:
            headers = {
                "Authorization": f"Bearer {API_KEY}",
                "Content-Type": "application/json"
            }
            
            async with self.session.post(
                f"{HTTP_WRAPPER_URL}/tools/{tool_name}",
                json=params,
                headers=headers,
                timeout=60
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    return {"error": f"HTTP {response.status}: {error_text}"}
        
        except Exception as e:
            logger.error(f"Error calling HTTP tool {tool_name}: {e}")
            return {"error": str(e)}

# Global wrapper instance
wrapper = SimpleJinaCrawlerWrapper()

@server.list_resources()
async def list_resources() -> List[Resource]:
    """List available resources"""
    return [
        Resource(
            uri="jina://crawler",
            name="Jina Web Crawler",
            description="Advanced web crawler with AI processing",
            mimeType="application/json",
        )
    ]

@server.read_resource()
async def read_resource(uri: str) -> str:
    """Read resource content"""
    if uri == "jina://crawler":
        return json.dumps({
            "description": "Jina Crawler with AI-powered content processing",
            "features": [
                "Smart web crawling",
                "AI content processing with Gemini",
                "Paywall bypass capabilities",
                "Multi-source AI search",
                "Vietnamese content optimization"
            ]
        }, indent=2)
    
    raise ValueError(f"Unknown resource: {uri}")

@server.list_tools()
async def list_tools() -> List[Tool]:
    """List available tools"""
    return [
        Tool(
            name="crawl_url",
            description="📄 Smart web crawler with AI processing",
            inputSchema={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "URL to crawl"},
                    "max_content_length": {"type": "integer", "default": 10000}
                },
                "required": ["url"]
            }
        ),
        Tool(
            name="ai_search",
            description="🤖 AI Search Engine with multi-source support",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "Search query"},
                    "max_sources": {"type": "integer", "default": 10}
                },
                "required": ["query"]
            }
        ),
        Tool(
            name="crawl_full_article",
            description="📰 Complete article extractor",
            inputSchema={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "URL to extract full article"},
                    "max_content_length": {"type": "integer", "default": 50000}
                },
                "required": ["url"]
            }
        ),
        Tool(
            name="crawl_bypass_paywall",
            description="🔓 Bypass paywall and extract content",
            inputSchema={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "Paywall-protected URL"},
                    "max_content_length": {"type": "integer", "default": 50000}
                },
                "required": ["url"]
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Call a tool"""
    try:
        logger.info(f"Calling tool: {name} with args: {arguments}")
        
        # Call HTTP wrapper
        result = await wrapper.call_http_tool(name, arguments)
        
        if "error" in result:
            return [TextContent(
                type="text",
                text=f"Error: {result['error']}"
            )]
        
        # Format response
        if "success" in result and result["success"]:
            if "answer" in result:
                # AI search response
                response_text = f"Query: {result.get('query', 'N/A')}\n\n{result['answer']}"
                if "citations" in result:
                    response_text += f"\n\nSources: {len(result['citations'])} citations"
            elif "content" in result:
                # Crawl response
                response_text = result["content"]
            else:
                response_text = json.dumps(result, indent=2)
        else:
            response_text = json.dumps(result, indent=2)
        
        return [TextContent(
            type="text",
            text=response_text
        )]
    
    except Exception as e:
        logger.error(f"Error in tool {name}: {e}")
        return [TextContent(
            type="text",
            text=f"Error: {str(e)}"
        )]

async def main():
    """Main server function"""
    try:
        await wrapper.initialize()
        
        # Run MCP server
        async with stdio_server() as (read_stream, write_stream):
            await server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="jina_crawler",
                    server_version="1.0.0",
                    capabilities=server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )
    finally:
        await wrapper.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
