#!/usr/bin/env python3
"""
Cloudflare Resistant Jin<PERSON> Crawler
Integrates TLS client bypass with existing Jina crawler architecture
"""

import asyncio
import logging
import time
import os
import aiohttp
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup

# Try to import tls_client for Cloudflare bypass
try:
    import tls_client
    TLS_CLIENT_AVAILABLE = True
except ImportError:
    TLS_CLIENT_AVAILABLE = False
    print("Warning: tls_client not available. Install with: pip install tls-client")

try:
    from fake_useragent import UserAgent
    FAKE_USERAGENT_AVAILABLE = True
except ImportError:
    FAKE_USERAGENT_AVAILABLE = False
    print("Warning: fake_useragent not available. Install with: pip install fake-useragent")

# Import existing components
from jini_crawler import JiniCrawler, PageSnapshot, JiniCrawlResult
from improved_gemini_processor import ImprovedGeminiProcessor

logger = logging.getLogger(__name__)

@dataclass
class TLSBypassConfig:
    """Configuration for TLS bypass"""
    use_tls_bypass: bool = True
    use_user_agent_rotation: bool = True
    timeout: int = 30
    max_retries: int = 3

class TLSClientBypass:
    """TLS Client bypass implementation for Cloudflare resistance"""
    
    def __init__(self):
        self.available = TLS_CLIENT_AVAILABLE
        self.session = None
        self.headers = {}
        self.user_agent = ""
        self.identifier = ""
        
        if self.available:
            self.randomize_request()
            logger.info("🔒 TLS Client bypass initialized")
        else:
            logger.warning("⚠️ TLS Client not available")
    
    def randomize_request(self):
        """Randomize TLS client settings"""
        if not self.available:
            return
        
        try:
            # Select random browser identifier
            available_identifiers = [
                'chrome_103', 'chrome_104', 'chrome_105', 'chrome_106',
                'chrome_107', 'chrome_108', 'chrome_109', 'chrome_110',
                'chrome_111', 'chrome_112', 'firefox_102', 'firefox_104',
                'firefox_105', 'safari_15_6_1', 'safari_16_0',
                'opera_89', 'opera_90'
            ]
            self.identifier = 'chrome_120'  # Use latest Chrome
            
            # Create TLS session
            self.session = tls_client.Session(
                random_tls_extension_order=True,
                client_identifier=self.identifier
            )
            self.session.timeout_seconds = 60
            
            # Generate matching User-Agent
            if FAKE_USERAGENT_AVAILABLE:
                try:
                    self.user_agent = UserAgent(os=['Windows']).random
                except Exception:
                    self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            else:
                self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            
            # Set realistic headers
            self.headers = {
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'accept-language': 'en-US,en;q=0.9',
                'accept-encoding': 'gzip, deflate, br',
                'dnt': '1',
                'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'none',
                'sec-fetch-user': '?1',
                'upgrade-insecure-requests': '1',
                'user-agent': self.user_agent,
                'cache-control': 'max-age=0'
            }
            
            logger.debug(f"🔄 TLS randomized: {self.identifier}")
            
        except Exception as e:
            logger.error(f"❌ Error randomizing TLS request: {e}")
            self.available = False
    
    def fetch_with_tls_bypass(self, url: str, **kwargs) -> Optional[Dict[str, Any]]:
        """Fetch URL using TLS client bypass"""
        if not self.available or not self.session:
            return None
        
        try:
            # Randomize for each request
            self.randomize_request()
            
            # Prepare request parameters
            params = kwargs.get('params', {})
            headers = kwargs.get('headers', self.headers)
            timeout = kwargs.get('timeout', 30)
            
            logger.debug(f"🔒 TLS bypass request: {url[:50]}...")
            
            # Set timeout on session
            self.session.timeout_seconds = timeout
            
            # Make request with TLS client
            response = self.session.get(url, headers=headers, params=params)
            
            if response.status_code == 200:
                result = {
                    'content': response.text,
                    'status_code': response.status_code,
                    'headers': dict(response.headers),
                    'url': str(response.url)
                }
                logger.debug(f"✅ TLS bypass successful: {len(response.text)} chars")
                return result
            else:
                logger.warning(f"⚠️ TLS bypass failed: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ TLS bypass error: {e}")
            return None

class CloudflareResistantJiniCrawler(JiniCrawler):
    """
    Jini Crawler with Cloudflare resistance using TLS client bypass
    """
    
    def __init__(self):
        super().__init__()
        self.tls_bypass = TLSClientBypass() if TLS_CLIENT_AVAILABLE else None
        self.gemini_processor = ImprovedGeminiProcessor()  # Use improved processor
        self._initialized = False
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        self.timeout = 30
        
    async def initialize(self) -> bool:
        """Initialize the crawler components"""
        try:
            if not self._initialized:
                # Create aiohttp session for async requests
                timeout = aiohttp.ClientTimeout(total=self.timeout)
                self.session = aiohttp.ClientSession(
                    timeout=timeout,
                    headers={'User-Agent': self.user_agent}
                )
                await self.gemini_processor.initialize()
                self._initialized = True
                logger.info("✅ Cloudflare Resistant Jini Crawler initialized successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to initialize Cloudflare Resistant Jini Crawler: {e}")
            return False
    
    async def scrap_url(self, url: str, options: Optional[Dict[str, Any]] = None):
        """
        Scrape URL with Cloudflare resistance
        """
        if not self._initialized:
            await self.initialize()
        
        start_time = time.time()
        options = options or {}
        
        try:
            logger.info(f"🕷️ Scraping URL with Cloudflare resistance: {url}")
            
            # Try TLS bypass first for Cloudflare-protected sites
            html_content = None
            status = None
            status_text = ""
            
            if self.tls_bypass and self.tls_bypass.available:
                logger.info(f"🔒 Trying TLS bypass for Cloudflare resistance...")
                tls_result = await asyncio.get_event_loop().run_in_executor(
                    None, self.tls_bypass.fetch_with_tls_bypass, url
                )
                
                if tls_result:
                    html_content = tls_result['content']
                    status = tls_result['status_code']
                    status_text = f"TLS Bypass Success"
                    logger.info(f"✅ TLS bypass successful for {url}")
                else:
                    logger.warning(f"⚠️ TLS bypass failed, falling back to aiohttp...")
            
            # Fallback to aiohttp if TLS bypass failed or not available
            if not html_content:
                async with self.session.get(url) as response:
                    status = response.status
                    status_text = response.reason or ""
                    html_content = await response.text()
            
            # Parse with BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Extract basic information
            title = soup.title.string.strip() if soup.title else ""
            
            # Extract meta description
            description = ""
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc:
                description = meta_desc.get('content', '').strip()
            
            # Clean HTML - remove unwanted elements
            for element in soup(['script', 'style', 'nav', 'header', 'footer', 'aside',
                               'iframe', 'noscript', 'form', 'button']):
                element.decompose()
            
            # Remove ads and social media elements
            for element in soup.find_all(class_=lambda x: x and any(
                keyword in x.lower() for keyword in ['ad', 'advertisement', 'social', 'share', 'comment', 'sidebar']
            )):
                element.decompose()
            
            # Extract main content - more comprehensive for news sites
            main_content = None
            # Try multiple selectors for news sites
            selectors = [
                'main', 'article', '.content', '.main-content', '.post-content', '.entry-content',
                '.news-list', '.articles', '.posts', '.stories', '.news-container'
            ]
            
            for selector in selectors:
                if selector.startswith('.'):
                    main_element = soup.select_one(selector)
                else:
                    main_element = soup.find(selector)
                
                if main_element and len(main_element.get_text(strip=True)) > 200:
                    main_content = main_element
                    break
            
            if not main_content:
                main_content = soup.find('body') or soup
            
            # Get cleaned HTML and text
            cleaned_html = str(main_content)
            text_content = main_content.get_text(separator=' ', strip=True)
            
            # Extract images
            imgs = []
            for img in soup.find_all('img'):
                src = img.get('src', '')
                if src:
                    # Convert relative URLs to absolute
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = urljoin(url, src)
                    elif not src.startswith(('http://', 'https://')):
                        src = urljoin(url, src)
                    
                    imgs.append({
                        'src': src,
                        'alt': img.get('alt', ''),
                        'width': img.get('width', 0),
                        'height': img.get('height', 0)
                    })
            
            # Calculate basic analytics
            elem_count = len(soup.find_all())
            max_elem_depth = self._calculate_max_depth(soup)
            
            # Create parsed content for compatibility
            parsed = {
                'title': title,
                'content': cleaned_html,
                'textContent': text_content,
                'length': len(text_content),
                'excerpt': text_content[:200] if text_content else '',
                'byline': '',
                'dir': soup.get('dir', 'ltr') if hasattr(soup, 'get') else 'ltr',
                'siteName': '',
                'lang': soup.get('lang', 'en') if hasattr(soup, 'get') else 'en',
                'publishedTime': ''
            }
            
            # Create snapshot
            snapshot = PageSnapshot(
                title=title,
                description=description,
                href=url,
                html=cleaned_html,
                text=text_content,
                status=status,
                status_text=status_text,
                parsed=parsed,
                imgs=imgs,
                max_elem_depth=max_elem_depth,
                elem_count=elem_count,
                is_intermediate=False
            )
            
            yield snapshot
            
        except Exception as e:
            logger.error(f"❌ Error scraping {url}: {e}")
            # Yield error snapshot
            error_snapshot = PageSnapshot(
                href=url,
                title="Error",
                text=f"Error scraping {url}: {str(e)}",
                status=500,
                is_intermediate=False
            )
            yield error_snapshot
    
    async def crawl_and_process(self, url: str, max_content_length: int = 20000, task_type: str = "news_extraction") -> JiniCrawlResult:
        """
        Crawl a URL and process it through the complete pipeline with Cloudflare resistance
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            # Step 1: Crawl with Cloudflare resistance
            logger.info(f"🕷️ Crawling {url} with Cloudflare resistance...")
            crawl_result = None
            async for snapshot in self.scrap_url(url):
                if not snapshot.is_intermediate:
                    crawl_result = snapshot
                    break
            
            if not crawl_result:
                return JiniCrawlResult(
                    success=False,
                    url=url,
                    error="Failed to crawl URL"
                )
            
            # Step 2: Process with Improved Gemini
            logger.info(f"🤖 Processing content with Improved Gemini ({task_type})...")
            
            # Limit content length to avoid API limits (increased for better extraction)
            content_to_process = crawl_result.html[:max_content_length]
            
            gemini_result = await self.gemini_processor.process_content(
                content_to_process, task_type
            )
            
            if not gemini_result.get("success"):
                return JiniCrawlResult(
                    success=False,
                    url=url,
                    title=crawl_result.title,
                    original_content=crawl_result.html[:500],  # First 500 chars
                    error=f"Gemini processing failed: {gemini_result.get('error', 'Unknown error')}"
                )
            
            return JiniCrawlResult(
                success=True,
                url=url,
                title=crawl_result.title,
                original_content=crawl_result.html,
                cleaned_content=crawl_result.html,  # Already cleaned by crawler
                processed_content=gemini_result.get("processed_content", ""),
                processing_time=gemini_result.get("processing_time", 0.0),
                metadata={
                    "original_length": gemini_result.get("original_length", 0),
                    "output_length": gemini_result.get("output_length", 0),
                    "model": gemini_result.get("model", "unknown"),
                    "task_type": gemini_result.get("task_type", task_type),
                    "cloudflare_bypass_used": self.tls_bypass is not None and self.tls_bypass.available
                }
            )
            
        except Exception as e:
            logger.error(f"❌ Error processing {url}: {e}")
            return JiniCrawlResult(
                success=False,
                url=url,
                error=str(e)
            )
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the crawler"""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Simple health check - test basic functionality
            test_result = {
                "status": "healthy",
                "crawler_initialized": self._initialized,
                "session_active": self.session is not None,
                "tls_bypass_available": self.tls_bypass is not None and self.tls_bypass.available
            }
            
            # Test Improved Gemini processor
            gemini_health = await self.gemini_processor.health_check()
            test_result["gemini_processor"] = gemini_health
            
            # Overall status
            if gemini_health.get("status") != "healthy":
                test_result["status"] = "degraded"
            
            return test_result
            
        except Exception as e:
            logger.error(f"❌ Health check failed: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

# Convenience functions
async def create_cloudflare_resistant_jini_crawler() -> CloudflareResistantJiniCrawler:
    """Create and initialize a Cloudflare resistant Jini crawler"""
    crawler = CloudflareResistantJiniCrawler()
    await crawler.initialize()
    return crawler

async def quick_crawl_cloudflare_resistant(url: str, max_content_length: int = 20000, task_type: str = "news_extraction") -> JiniCrawlResult:
    """Quick crawl and process a URL with Cloudflare resistance"""
    crawler = CloudflareResistantJiniCrawler()
    try:
        await crawler.initialize()
        return await crawler.crawl_and_process(url, max_content_length, task_type)
    finally:
        await crawler.cleanup()

# Test function
async def test_cloudflare_resistant_jini_crawler():
    """Test Cloudflare resistant Jini crawler"""
    print("🧪 Testing Cloudflare Resistant Jini Crawler")
    print("=" * 50)
    
    crawler = CloudflareResistantJiniCrawler()
    await crawler.initialize()
    
    # Test with a simple URL
    test_url = "https://httpbin.org/html"
    
    result = await crawler.crawl_and_process(test_url, task_type="news_extraction")
    
    print(f"✅ Success: {result.success}")
    if result.success:
        print(f"   Title: {result.title}")
        print(f"   Processed content length: {len(result.processed_content or '')} characters")
        print(f"   Processing time: {result.processing_time:.2f}s")
        print(f"   Cloudflare bypass used: {result.metadata.get('cloudflare_bypass_used', False)}")
        if result.processed_content:
            preview = result.processed_content[:200] + "..." if len(result.processed_content) > 200 else result.processed_content
            print(f"   Content preview: {preview}")
    else:
        print(f"   Error: {result.error}")
    
    # Health check
    health = await crawler.health_check()
    print(f"\n🏥 Health check: {health['status']}")
    print(f"   TLS Bypass Available: {health.get('tls_bypass_available', False)}")
    
    await crawler.cleanup()
    print("\n✅ Test completed")

if __name__ == "__main__":
    asyncio.run(test_cloudflare_resistant_jini_crawler())