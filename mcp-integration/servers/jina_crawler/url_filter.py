#!/usr/bin/env python3
"""
🔧 URL FILTER
Smart URL filtering for AI search to avoid problematic sites
"""

import logging
from typing import List, Dict, Any
from urllib.parse import urlparse
import time

logger = logging.getLogger(__name__)

class URLFilter:
    """Smart URL filtering for AI search"""
    
    def __init__(self):
        # Sites that frequently fail or timeout
        self.problematic_domains = {
            'vnexpress.net': {
                'reason': 'HTTP 406 errors, requires advanced bypass',
                'success_rate': 0.3,
                'avg_time': 25.0,
                'priority': 'low'
            },
            'binance.com': {
                'reason': 'HTTP 202 errors, API responses',
                'success_rate': 0.1,
                'avg_time': 30.0,
                'priority': 'blacklist'
            },
            'x.com': {
                'reason': 'Header too long errors',
                'success_rate': 0.2,
                'avg_time': 20.0,
                'priority': 'blacklist'
            },
            'twitter.com': {
                'reason': 'Redirects to x.com',
                'success_rate': 0.1,
                'avg_time': 15.0,
                'priority': 'blacklist'
            }
        }
        
        # Preferred Vietnamese news sources (high success rate)
        self.preferred_vietnamese = {
            'dantri.com.vn': {
                'success_rate': 0.9,
                'avg_time': 5.0,
                'priority': 'high'
            },
            'thanhnien.vn': {
                'success_rate': 0.85,
                'avg_time': 6.0,
                'priority': 'high'
            },
            'vietnamnet.vn': {
                'success_rate': 0.8,
                'avg_time': 7.0,
                'priority': 'high'
            },
            'tuoitre.vn': {
                'success_rate': 0.8,
                'avg_time': 6.0,
                'priority': 'medium'
            },
            'zing.vn': {
                'success_rate': 0.75,
                'avg_time': 8.0,
                'priority': 'medium'
            }
        }
        
        # Success tracking
        self.success_stats = {}
    
    def get_domain(self, url: str) -> str:
        """Extract domain from URL"""
        try:
            return urlparse(url).netloc.lower()
        except:
            return ""
    
    def is_blacklisted(self, url: str) -> bool:
        """Check if URL should be blacklisted"""
        domain = self.get_domain(url)
        
        for problematic_domain, info in self.problematic_domains.items():
            if problematic_domain in domain:
                if info['priority'] == 'blacklist':
                    logger.info(f"🚫 Blacklisted URL: {url} (reason: {info['reason']})")
                    return True
        
        return False
    
    def get_url_priority(self, url: str) -> str:
        """Get priority level for URL"""
        domain = self.get_domain(url)
        
        # Check preferred Vietnamese sources
        for preferred_domain, info in self.preferred_vietnamese.items():
            if preferred_domain in domain:
                return info['priority']
        
        # Check problematic domains
        for problematic_domain, info in self.problematic_domains.items():
            if problematic_domain in domain:
                return info['priority']
        
        return 'medium'  # Default priority
    
    def filter_urls(self, urls: List[str], max_urls: int = 10) -> List[str]:
        """Filter and prioritize URLs for crawling"""
        if not urls:
            return []
        
        # Remove blacklisted URLs
        filtered_urls = [url for url in urls if not self.is_blacklisted(url)]
        
        if not filtered_urls:
            logger.warning("⚠️ All URLs were blacklisted, using original list")
            filtered_urls = urls
        
        # Sort by priority
        def get_sort_key(url):
            priority = self.get_url_priority(url)
            domain = self.get_domain(url)
            
            # Priority weights
            priority_weights = {
                'high': 0,
                'medium': 1,
                'low': 2,
                'blacklist': 3
            }
            
            # Success rate bonus
            success_bonus = 0
            if domain in self.preferred_vietnamese:
                success_bonus = -self.preferred_vietnamese[domain]['success_rate']
            elif domain in self.problematic_domains:
                success_bonus = self.problematic_domains[domain]['success_rate']
            
            return priority_weights.get(priority, 1) + success_bonus
        
        # Sort URLs by priority and success rate
        sorted_urls = sorted(filtered_urls, key=get_sort_key)
        
        # Limit to max_urls
        result = sorted_urls[:max_urls]
        
        logger.info(f"🔍 URL filtering: {len(urls)} → {len(result)} URLs")
        if len(result) < len(urls):
            removed_count = len(urls) - len(result)
            logger.info(f"   Removed {removed_count} low-priority/problematic URLs")
        
        return result
    
    def track_success(self, url: str, success: bool, response_time: float):
        """Track URL success for future filtering"""
        domain = self.get_domain(url)
        
        if domain not in self.success_stats:
            self.success_stats[domain] = {
                'total_requests': 0,
                'successful_requests': 0,
                'total_time': 0.0,
                'last_updated': time.time()
            }
        
        stats = self.success_stats[domain]
        stats['total_requests'] += 1
        stats['total_time'] += response_time
        stats['last_updated'] = time.time()
        
        if success:
            stats['successful_requests'] += 1
        
        # Calculate success rate
        success_rate = stats['successful_requests'] / stats['total_requests']
        avg_time = stats['total_time'] / stats['total_requests']
        
        logger.debug(f"📊 {domain}: {success_rate:.2f} success rate, {avg_time:.1f}s avg time")
        
        # Update problematic domains list if needed
        if stats['total_requests'] >= 5:  # Minimum sample size
            if success_rate < 0.3 and domain not in self.problematic_domains:
                logger.warning(f"⚠️ Adding {domain} to problematic domains (success rate: {success_rate:.2f})")
                self.problematic_domains[domain] = {
                    'reason': f'Low success rate: {success_rate:.2f}',
                    'success_rate': success_rate,
                    'avg_time': avg_time,
                    'priority': 'low'
                }
    
    def get_stats_summary(self) -> Dict[str, Any]:
        """Get filtering statistics summary"""
        total_domains = len(self.success_stats)
        problematic_count = len([d for d in self.success_stats.values() 
                               if d['total_requests'] >= 3 and 
                               d['successful_requests'] / d['total_requests'] < 0.5])
        
        return {
            'total_domains_tracked': total_domains,
            'problematic_domains': problematic_count,
            'blacklisted_domains': len([d for d in self.problematic_domains.values() 
                                      if d['priority'] == 'blacklist']),
            'preferred_vietnamese': len(self.preferred_vietnamese),
            'success_stats': self.success_stats
        }
    
    def suggest_alternatives(self, failed_url: str) -> List[str]:
        """Suggest alternative URLs for failed Vietnamese news sites"""
        domain = self.get_domain(failed_url)
        
        if 'vnexpress.net' in domain:
            return [
                'https://dantri.com.vn/cong-nghe.htm',
                'https://thanhnien.vn/cong-nghe.html',
                'https://vietnamnet.vn/cong-nghe'
            ]
        
        # For other failed sites, suggest top Vietnamese sources
        return [
            'https://dantri.com.vn/',
            'https://thanhnien.vn/',
            'https://vietnamnet.vn/'
        ]

# Test function
def test_url_filter():
    """Test URL filtering"""
    filter = URLFilter()
    
    test_urls = [
        'https://vnexpress.net/cong-nghe',
        'https://dantri.com.vn/cong-nghe.htm',
        'https://binance.com/en/news',
        'https://thanhnien.vn/cong-nghe.html',
        'https://x.com/some-tweet',
        'https://vietnamnet.vn/cong-nghe',
        'https://example.com/article'
    ]
    
    print("🧪 Testing URL Filter")
    print(f"Input URLs: {len(test_urls)}")
    
    filtered = filter.filter_urls(test_urls, max_urls=5)
    print(f"Filtered URLs: {len(filtered)}")
    
    for i, url in enumerate(filtered, 1):
        priority = filter.get_url_priority(url)
        print(f"  {i}. {url} (priority: {priority})")
    
    # Test success tracking
    filter.track_success('https://dantri.com.vn/test', True, 3.5)
    filter.track_success('https://vnexpress.net/test', False, 25.0)
    
    stats = filter.get_stats_summary()
    print(f"\nStats: {stats}")

if __name__ == "__main__":
    test_url_filter()
