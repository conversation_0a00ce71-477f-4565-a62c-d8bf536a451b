#!/usr/bin/env python3
"""
Script to refresh Open WebUI tools and force recognition of new endpoints
"""

import requests
import json
import time

def refresh_openwebui_tools():
    """Force Open WebUI to refresh and recognize new tools"""
    
    # URLs to try
    openwebui_urls = [
        "http://localhost:3000",
        "http://localhost:8080", 
        "http://localhost:80"
    ]
    
    crawler_url = "http://localhost:8009"
    
    print("🔄 Refreshing Open WebUI tools...")
    
    # First, verify our server is working
    try:
        response = requests.get(f"{crawler_url}/")
        data = response.json()
        print(f"✅ Crawler server: {data['message']}")
        print(f"📊 Tools available: {data['tools']}")
    except Exception as e:
        print(f"❌ Crawler server error: {e}")
        return
    
    # Check OpenAPI schema
    try:
        response = requests.get(f"{crawler_url}/openapi.json")
        openapi = response.json()
        paths = list(openapi['paths'].keys())
        print(f"🔍 OpenAPI endpoints: {len(paths)}")
        
        # Show new endpoints
        new_endpoints = [p for p in paths if 'full_article' in p or 'bypass_paywall' in p]
        print(f"🆕 New endpoints: {new_endpoints}")
        
    except Exception as e:
        print(f"❌ OpenAPI error: {e}")
        return
    
    # Try to refresh Open WebUI
    for webui_url in openwebui_urls:
        try:
            print(f"\n🔄 Trying to refresh {webui_url}...")
            
            # Try to access admin or tools refresh endpoint
            refresh_endpoints = [
                f"{webui_url}/api/v1/tools/refresh",
                f"{webui_url}/api/tools/refresh", 
                f"{webui_url}/admin/tools/refresh",
                f"{webui_url}/api/v1/functions/refresh"
            ]
            
            for endpoint in refresh_endpoints:
                try:
                    response = requests.post(endpoint, timeout=5)
                    if response.status_code == 200:
                        print(f"✅ Refreshed via {endpoint}")
                        break
                except:
                    continue
            
            # Also try to register the tool directly
            tool_config = {
                "name": "Jina Crawler with Paywall Bypass",
                "url": crawler_url,
                "description": "Web crawler with full article extraction and paywall bypass",
                "tools": [
                    {
                        "name": "crawl_full_article",
                        "description": "Extract COMPLETE article content (not summarized)",
                        "endpoint": "/crawl_full_article"
                    },
                    {
                        "name": "crawl_bypass_paywall", 
                        "description": "Bypass paywalls using 8 different techniques",
                        "endpoint": "/crawl_bypass_paywall"
                    }
                ]
            }
            
            register_endpoints = [
                f"{webui_url}/api/v1/tools/register",
                f"{webui_url}/api/tools/register"
            ]
            
            for endpoint in register_endpoints:
                try:
                    response = requests.post(
                        endpoint, 
                        json=tool_config,
                        headers={"Content-Type": "application/json"},
                        timeout=5
                    )
                    if response.status_code in [200, 201]:
                        print(f"✅ Registered tools via {endpoint}")
                        break
                except:
                    continue
                    
        except Exception as e:
            print(f"⚠️ Could not connect to {webui_url}: {e}")
            continue
    
    print("\n📋 Manual steps if auto-refresh failed:")
    print("1. Go to Open WebUI Admin panel")
    print("2. Navigate to Tools/Functions section")
    print("3. Add new tool with URL: http://localhost:8009")
    print("4. Or refresh existing Jina Crawler tool")
    print("5. The new endpoints should appear:")
    print("   - /crawl_full_article")
    print("   - /crawl_bypass_paywall")
    
    print(f"\n🧪 Test the new tools directly:")
    print(f"curl -X POST '{crawler_url}/crawl_bypass_paywall' \\")
    print(f"  -H 'Content-Type: application/json' \\")
    print(f"  -d '{{\"url\": \"https://medium.com/@author/article\", \"max_content_length\": 50000}}'")

if __name__ == "__main__":
    refresh_openwebui_tools()