#!/usr/bin/env python3
"""
Final OpenAPI Wrapper for Jina Crawler - Fully compatible with Open WebUI
"""

import asyncio
import json
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from jini_crawler import JiniCrawler

# Pydantic models for request validation
class CrawlUrlRequest(BaseModel):
    url: str = Field(..., description="URL to crawl and process", example="https://dantri.com.vn")
    max_content_length: Optional[int] = Field(
        10000, 
        description="Maximum content length to process", 
        ge=1000, 
        le=50000
    )

class CrawlBatchRequest(BaseModel):
    urls: list[str] = Field(..., description="List of URLs to crawl", example=["https://dantri.com.vn", "https://vnexpress.net"])
    max_content_length: Optional[int] = Field(
        10000, 
        description="Maximum content length per URL", 
        ge=1000, 
        le=50000
    )

class EmptyRequest(BaseModel):
    pass

app = FastAPI(
    title="Jina Crawler Tools",
    description="Final OpenAPI wrapper for Jina Crawler - Fully compatible with Open WebUI",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global crawler instance
crawler = None

@app.on_event("startup")
async def startup_event():
    """Initialize crawler on startup"""
    global crawler
    crawler = JiniCrawler()
    await crawler.initialize()
    print("🚀 Final Jina Crawler OpenAPI server started")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    global crawler
    if crawler:
        await crawler.cleanup()
    print("✅ Final Jina Crawler OpenAPI server stopped")

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Final Jina Crawler OpenAPI Server", "tools": 5}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "server": "final-jina-crawler"}

@app.post("/crawl_url", operation_id="crawl_url")
async def crawl_url(request: CrawlUrlRequest):
    """Crawl a single URL"""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        result = await crawler.crawl_and_process(request.url, request.max_content_length)
        
        response = {
            "success": result.success,
            "url": result.url,
            "title": result.title,
            "processed_content": result.processed_content,
            "processing_time": result.processing_time,
            "error": result.error,
            "metadata": result.metadata,
            "crawler_type": "final_jina_crawler"
        }
        
        return JSONResponse(content=response)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Crawl failed: {str(e)}")

@app.post("/crawl_batch", operation_id="crawl_batch")
async def crawl_batch(request: CrawlBatchRequest):
    """Crawl multiple URLs"""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        # Process URLs in parallel
        tasks = []
        for url in request.urls:
            task = crawler.crawl_and_process(url, request.max_content_length)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Format results
        formatted_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                formatted_results.append({
                    "success": False,
                    "url": request.urls[i],
                    "error": str(result)
                })
            else:
                formatted_results.append({
                    "success": result.success,
                    "url": result.url,
                    "title": result.title,
                    "processed_content": result.processed_content,
                    "processing_time": result.processing_time,
                    "error": result.error,
                    "metadata": result.metadata
                })
        
        response = {
            "batch_size": len(request.urls),
            "successful_crawls": sum(1 for r in formatted_results if r["success"]),
            "failed_crawls": sum(1 for r in formatted_results if not r["success"]),
            "results": formatted_results,
            "crawler_type": "final_jina_batch"
        }
        
        return JSONResponse(content=response)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Batch crawl failed: {str(e)}")

@app.post("/health_check", operation_id="health_check")
async def health_check_tool(request: EmptyRequest = None):
    """Health check tool"""
    global crawler
    
    if not crawler:
        return JSONResponse(content={"status": "error", "message": "Crawler not initialized"})
    
    try:
        health = await crawler.health_check()
        return JSONResponse(content={
            "status": "healthy",
            "crawler_health": health,
            "server_type": "final_jina_crawler"
        })
    except Exception as e:
        return JSONResponse(content={"status": "error", "error": str(e)})

@app.post("/get_stats", operation_id="get_stats")
async def get_stats(request: EmptyRequest = None):
    """Get crawler statistics"""
    return JSONResponse(content={
        "server_type": "final_jina_crawler",
        "features": [
            "Full OpenAPI 3.0 compliance",
            "Pydantic request validation",
            "Compatible with Open WebUI",
            "No authentication required",
            "Async crawling with aiohttp",
            "BeautifulSoup HTML cleaning",
            "Gemini AI content processing",
            "Vietnamese content optimization",
            "Batch processing support"
        ],
        "tools_available": 5,
        "auth_required": False
    })

@app.post("/test_crawl", operation_id="test_crawl")
async def test_crawl(request: EmptyRequest = None):
    """Test crawl with a sample URL"""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        # Test with a simple URL
        test_url = "https://httpbin.org/html"
        result = await crawler.crawl_and_process(test_url, 5000)
        
        return JSONResponse(content={
            "test_successful": result.success,
            "test_url": test_url,
            "title": result.title,
            "content_length": len(result.processed_content or ""),
            "processing_time": result.processing_time,
            "error": result.error,
            "message": "Test crawl completed successfully" if result.success else "Test crawl failed"
        })
        
    except Exception as e:
        return JSONResponse(content={
            "test_successful": False,
            "error": str(e),
            "message": "Test crawl failed with exception"
        })

if __name__ == "__main__":
    import os
    port = int(os.environ.get("PORT", 8007))
    uvicorn.run(
        "final_openapi_wrapper:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info"
    )