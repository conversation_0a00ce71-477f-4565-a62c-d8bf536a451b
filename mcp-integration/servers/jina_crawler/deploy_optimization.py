#!/usr/bin/env python3
"""
Deploy script để áp dụng các tối ưu hóa Vietnamese news
"""

import os
import shutil
import logging
import sys

logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def deploy_optimizations():
    """Deploy các tối ưu hóa"""
    
    logger.info("🚀 DEPLOYING VIETNAMESE NEWS OPTIMIZATIONS")
    logger.info("=" * 50)
    
    current_dir = os.path.dirname(__file__)
    
    # 1. Backup original files
    backup_files = [
        'jini_crawler.py',
        'gemini_processor.py',
        'ai_search/batch_crawler_service.py'
    ]
    
    logger.info("📦 Creating backups...")
    for file_path in backup_files:
        if os.path.exists(file_path):
            backup_path = f"{file_path}.backup"
            shutil.copy2(file_path, backup_path)
            logger.info(f"   ✅ Backed up: {file_path} -> {backup_path}")
    
    # 2. Check if optimized files exist
    optimized_files = [
        'utils/content_size_optimizer.py',
        'gemini_processor_optimized.py'
    ]
    
    logger.info("🔍 Checking optimized files...")
    all_files_exist = True
    for file_path in optimized_files:
        if os.path.exists(file_path):
            logger.info(f"   ✅ Found: {file_path}")
        else:
            logger.error(f"   ❌ Missing: {file_path}")
            all_files_exist = False
    
    if not all_files_exist:
        logger.error("❌ Some optimized files are missing!")
        return False
    
    # 3. Update imports in existing files (already done in jini_crawler.py)
    logger.info("✅ Files are already optimized")
    
    # 4. Set environment variables for optimization
    logger.info("🔧 Setting optimization environment variables...")
    
    env_vars = {
        'GEMINI_MAX_OUTPUT_TOKENS': '8192',
        'GEMINI_CONTEXT_LIMIT': '200000',
        'ENABLE_SIZE_OPTIMIZATION': 'true',
        'MAX_CONTENT_PER_URL': '15000',
        'VIETNAMESE_NEWS_OPTIMIZATION': 'true'
    }
    
    # Create .env file if it doesn't exist
    env_file = '.env'
    env_content = []
    
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            env_content = f.readlines()
    
    # Add/update environment variables
    for key, value in env_vars.items():
        found = False
        for i, line in enumerate(env_content):
            if line.startswith(f"{key}="):
                env_content[i] = f"{key}={value}\n"
                found = True
                break
        
        if not found:
            env_content.append(f"{key}={value}\n")
        
        logger.info(f"   ✅ Set {key}={value}")
    
    # Write updated .env file
    with open(env_file, 'w') as f:
        f.writelines(env_content)
    
    logger.info("=" * 50)
    logger.info("🎉 DEPLOYMENT COMPLETED!")
    logger.info("=" * 50)
    logger.info("📋 Next steps:")
    logger.info("1. Restart your application/server")
    logger.info("2. Run test: python quick_test.py")
    logger.info("3. Or run full test: python test_dantri_vnexpress.py")
    logger.info("=" * 50)
    
    return True

def rollback_optimizations():
    """Rollback về version cũ nếu cần"""
    
    logger.info("🔄 ROLLING BACK OPTIMIZATIONS")
    
    backup_files = [
        'jini_crawler.py.backup',
        'gemini_processor.py.backup',
        'ai_search/batch_crawler_service.py.backup'
    ]
    
    for backup_path in backup_files:
        if os.path.exists(backup_path):
            original_path = backup_path.replace('.backup', '')
            shutil.copy2(backup_path, original_path)
            logger.info(f"   ✅ Restored: {backup_path} -> {original_path}")
        else:
            logger.warning(f"   ⚠️ Backup not found: {backup_path}")
    
    logger.info("🔄 Rollback completed")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        rollback_optimizations()
    else:
        success = deploy_optimizations()
        if not success:
            logger.error("❌ Deployment failed!")
            sys.exit(1)