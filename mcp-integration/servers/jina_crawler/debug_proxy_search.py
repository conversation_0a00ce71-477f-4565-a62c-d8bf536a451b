#!/usr/bin/env python3
"""
Debug Proxy Search - <PERSON> ti<PERSON><PERSON> kiểm tra ProxyDDGS
"""

import asyncio
import logging
import sys
import os
import json
from pathlib import Path

# Add current directory to Python path
sys.path.append(str(Path(__file__).parent))

from utils.proxy_manager import ProxyManager
from utils.proxy_ddgs import ProxyDDGS

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def debug_proxy_search():
    """Debug proxy search step by step"""
    
    print("🔍 DEBUG PROXY SEARCH")
    print("=" * 60)
    
    try:
        # 1. Load proxy manager
        print("\n1️⃣ Loading Proxy Manager...")
        proxy_manager = ProxyManager("config/proxy_config.json")
        await proxy_manager.initialize()
        
        proxy = proxy_manager.get_next_proxy()
        if not proxy:
            print("❌ No proxy available")
            return
            
        print(f"✅ Using proxy: {proxy.host}:{proxy.port}")
        
        # 2. Initialize ProxyDDGS
        print("\n2️⃣ Initializing ProxyDDGS...")
        proxy_ddgs = ProxyDDGS(proxy_manager)
        await proxy_ddgs.initialize()
        print("✅ ProxyDDGS initialized")
        
        # 3. Test simple search
        print("\n3️⃣ Testing search...")
        query = "Python programming"
        
        try:
            # Get raw response first
            proxy_url = proxy.to_url()
            print(f"🌐 Proxy URL: {proxy_url}")
            
            params = {
                'q': query,
                'kl': 'wt-wt',
                'safe': 'moderate',
                's': '0',
                'dc': '15',
                'v': 'l',
                'o': 'json'
            }
            
            print(f"📝 Search params: {params}")
            
            # Make request
            async with proxy_ddgs.session.get(
                proxy_ddgs.search_url,
                params=params,
                proxy=proxy_url
            ) as response:
                
                print(f"📊 Response status: {response.status}")
                print(f"📊 Response headers: {dict(response.headers)}")
                
                if response.status == 200:
                    html_content = await response.text()
                    print(f"📄 HTML length: {len(html_content)} chars")
                    
                    # Save HTML for inspection
                    with open("debug_response.html", "w", encoding="utf-8") as f:
                        f.write(html_content)
                    print("💾 HTML saved to debug_response.html")
                    
                    # Try parsing
                    results = proxy_ddgs._parse_search_results(html_content)
                    print(f"🎯 Parsed results: {len(results)}")
                    
                    if results:
                        print("\n📋 First few results:")
                        for i, result in enumerate(results[:3]):
                            print(f"  {i+1}. {result.get('title', 'No title')}")
                            print(f"     URL: {result.get('href', 'No URL')}")
                            print(f"     Snippet: {result.get('body', 'No snippet')[:100]}...")
                    else:
                        print("❌ No results parsed from HTML")
                        
                        # Show first 1000 chars of HTML for debugging
                        print("\n🔍 HTML Preview (first 1000 chars):")
                        print("-" * 50)
                        print(html_content[:1000])
                        print("-" * 50)
                        
                else:
                    error_text = await response.text()
                    print(f"❌ Error response: {error_text[:500]}")
        
        except Exception as e:
            print(f"❌ Search error: {e}")
            import traceback
            traceback.print_exc()
        
        # 4. Cleanup
        print("\n4️⃣ Cleanup...")
        await proxy_ddgs.cleanup()
        print("✅ Cleanup completed")
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_proxy_search())