"""
SearXNG Search Service

Integrates with SearXNG instances to provide additional search results
for the multi-source AI search engine.
"""

import asyncio
import aiohttp
import logging
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from urllib.parse import urlencode, quote_plus

# Import cleanup manager
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from utils.cleanup_manager import register_session_for_cleanup

logger = logging.getLogger(__name__)

@dataclass
class SearXNGResult:
    """Single search result from SearXNG with metadata"""
    title: str
    url: str
    snippet: str
    engine: str
    # Metadata for intelligent scoring
    domain: str = ""     # Domain name for authority scoring
    score: float = 0.0   # SearXNG internal score if available
    position: int = 0

@dataclass
class SearXNGSearchResponse:
    """Response from SearXNG search"""
    query: str
    results: List[SearXNGResult]
    total_results: int
    search_time: float
    success: bool
    engines_used: List[str]
    error: Optional[str] = None

class SearXNGService:
    """
    🔍 SearXNG Search Service
    
    Features:
    - Multiple SearXNG instances for redundancy
    - Parallel search across instances
    - Engine filtering and optimization
    - Vietnamese content support
    - Timeout and error handling
    """
    
    def __init__(self,
                 instances: List[str] = None,
                 timeout: int = 5,  # Reduced from 8s to 5s
                 max_results: int = 10):
        """
        Initialize SearXNG service

        Args:
            instances: List of SearXNG instance URLs
            timeout: Request timeout in seconds
            max_results: Maximum results to return
        """
        # Use local SearXNG instance via Docker network
        self.instances = instances or [
            "http://searxng:8080",  # Local SearXNG instance via perplexica network
        ]
        
        self.timeout = timeout
        self.max_results = max_results
        self.session = None
        self._initialized = False
        
        # Optimized engines for different content types
        self.engines = {
            'general': ['google', 'bing', 'duckduckgo', 'startpage', 'qwant'],
            'vietnamese': ['google', 'bing', 'duckduckgo'],  # Best for Vietnamese
            'news': ['google news', 'bing news', 'yahoo news'],
            'academic': ['google scholar', 'semantic scholar', 'arxiv']
        }
    
    async def initialize(self) -> bool:
        """Initialize HTTP session and test instances"""
        try:
            # Create session with optimized settings
            connector = aiohttp.TCPConnector(
                limit=20,
                limit_per_host=5,
                ttl_dns_cache=300,
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )

            timeout = aiohttp.ClientTimeout(total=self.timeout)

            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }
            )
            # Register session for cleanup
            register_session_for_cleanup(self.session)
            
            # Test instances and keep only working ones
            working_instances = await self._test_instances()
            if working_instances:
                self.instances = working_instances
                self._initialized = True
                logger.info(f"✅ SearXNG initialized with {len(self.instances)} working instances")
                return True
            else:
                logger.error("❌ No working SearXNG instances found")
                return False
                
        except Exception as e:
            logger.error(f"❌ SearXNG initialization failed: {e}")
            return False
    
    async def _test_instances(self) -> List[str]:
        """Test SearXNG instances and return working ones"""
        working_instances = []
        
        # Test instances in parallel
        test_tasks = [
            self._test_single_instance(instance)
            for instance in self.instances
        ]
        
        try:
            results = await asyncio.gather(*test_tasks, return_exceptions=True)
            
            for i, result in enumerate(results):
                if result is True:  # Instance is working
                    working_instances.append(self.instances[i])
                elif isinstance(result, Exception):
                    logger.warning(f"Instance {self.instances[i]} failed test: {result}")
            
            logger.info(f"🔍 Tested {len(self.instances)} instances, {len(working_instances)} working")
            return working_instances
            
        except Exception as e:
            logger.error(f"❌ Instance testing failed: {e}")
            return self.instances[:2]  # Fallback to first 2 instances
    
    async def _test_single_instance(self, instance_url: str) -> bool:
        """Test a single SearXNG instance"""
        try:
            test_url = f"{instance_url}/search"
            params = {
                'q': 'test',
                'format': 'json',
                'engines': 'duckduckgo',
                'pageno': 1
            }
            
            async with self.session.get(test_url, params=params, timeout=3) as response:
                if response.status == 200:
                    data = await response.json()
                    return 'results' in data
                return False
                
        except Exception:
            return False
    
    async def search(self, 
                    query: str,
                    search_type: str = "general",
                    language: str = "auto") -> SearXNGSearchResponse:
        """
        Search using SearXNG instances
        
        Args:
            query: Search query
            search_type: Type of search (general, vietnamese, news, academic)
            language: Language preference
            
        Returns:
            SearXNGSearchResponse with results
        """
        if not self._initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            # Detect Vietnamese content
            if self._is_vietnamese_query(query):
                search_type = "vietnamese"
                language = "vi"
            
            # Select engines based on search type
            engines = self.engines.get(search_type, self.engines['general'])
            
            logger.info(f"🔍 SearXNG searching: '{query}' (type: {search_type}, engines: {engines[:3]})")
            
            # Search across multiple instances in parallel
            search_tasks = [
                self._search_single_instance(instance, query, engines, language)
                for instance in self.instances[:3]  # Use top 3 instances
            ]
            
            # Execute searches with timeout
            results = await asyncio.wait_for(
                asyncio.gather(*search_tasks, return_exceptions=True),
                timeout=self.timeout
            )
            
            # Combine and deduplicate results
            all_results = []
            engines_used = set()
            
            for result in results:
                if isinstance(result, SearXNGSearchResponse) and result.success:
                    all_results.extend(result.results)
                    engines_used.update(result.engines_used)
            
            # Deduplicate by URL
            seen_urls = set()
            unique_results = []
            
            for result in all_results:
                if result.url not in seen_urls:
                    seen_urls.add(result.url)
                    unique_results.append(result)
            
            # Sort by relevance and limit results
            unique_results = unique_results[:self.max_results]
            
            search_time = time.time() - start_time
            
            logger.info(f"✅ SearXNG found {len(unique_results)} unique results in {search_time:.2f}s")
            
            return SearXNGSearchResponse(
                query=query,
                results=unique_results,
                total_results=len(unique_results),
                search_time=search_time,
                success=True,
                engines_used=list(engines_used)
            )
            
        except asyncio.TimeoutError:
            search_time = time.time() - start_time
            logger.warning(f"⏰ SearXNG search timeout after {search_time:.2f}s")
            return SearXNGSearchResponse(
                query=query,
                results=[],
                total_results=0,
                search_time=search_time,
                success=False,
                engines_used=[],
                error="Search timeout"
            )
            
        except Exception as e:
            search_time = time.time() - start_time
            logger.error(f"❌ SearXNG search error: {e}")
            return SearXNGSearchResponse(
                query=query,
                results=[],
                total_results=0,
                search_time=search_time,
                success=False,
                engines_used=[],
                error=str(e)
            )
    
    async def _search_single_instance(self, 
                                     instance_url: str,
                                     query: str,
                                     engines: List[str],
                                     language: str) -> SearXNGSearchResponse:
        """Search a single SearXNG instance"""
        try:
            search_url = f"{instance_url}/search"
            
            params = {
                'q': query,
                'format': 'json',
                'engines': ','.join(engines[:3]),  # Limit engines for speed
                'pageno': 1,
                'language': language,
                'time_range': '',
                'safesearch': 0
            }
            
            async with self.session.get(search_url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    results = []
                    engines_used = set()
                    
                    for i, item in enumerate(data.get('results', [])[:self.max_results]):
                        # Extract domain from URL
                        url = item.get('url', '')
                        domain = ""
                        if url:
                            from urllib.parse import urlparse
                            domain = urlparse(url).netloc

                        results.append(SearXNGResult(
                            title=item.get('title', ''),
                            url=url,
                            snippet=item.get('content', ''),
                            engine=item.get('engine', 'unknown'),
                            domain=domain,
                            score=item.get('score', 0.0)  # SearXNG internal score
                        ))
                        engines_used.add(item.get('engine', 'unknown'))
                    
                    return SearXNGSearchResponse(
                        query=query,
                        results=results,
                        total_results=len(results),
                        search_time=0.1,  # Individual timing not critical
                        success=True,
                        engines_used=list(engines_used)
                    )
                else:
                    logger.warning(f"SearXNG instance {instance_url} returned {response.status}")
                    
        except Exception as e:
            logger.warning(f"SearXNG instance {instance_url} failed: {e}")
        
        return SearXNGSearchResponse(
            query=query,
            results=[],
            total_results=0,
            search_time=0.1,
            success=False,
            engines_used=[]
        )
    
    def _is_vietnamese_query(self, query: str) -> bool:
        """Detect if query contains Vietnamese content"""
        vietnamese_chars = 'àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ'
        return any(char in query.lower() for char in vietnamese_chars)
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.session and not self.session.closed:
                await self.session.close()
                # Wait for session to fully close
                await asyncio.sleep(0.1)
            self.session = None
            self._initialized = False
            logger.debug("✅ SearXNG service cleanup completed")
        except Exception as e:
            logger.warning(f"⚠️ SearXNG cleanup warning: {e}")
