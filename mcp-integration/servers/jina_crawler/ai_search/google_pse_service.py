"""
Google Programmable Search Engine (PSE) Service

Provides search functionality using Google Custom Search API.
Integrates with the multi-source AI search engine.
"""

import asyncio
import logging
import time
import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import aiohttp
from tenacity import retry, stop_after_attempt, wait_exponential
from google.auth.transport.requests import Request
from google.oauth2 import service_account

# Import cleanup manager
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from utils.cleanup_manager import register_session_for_cleanup

logger = logging.getLogger(__name__)

@dataclass
class GoogleSearchResult:
    """Represents a single Google search result with metadata"""
    title: str
    url: str
    snippet: str
    source: str = "google"
    # Metadata for intelligent scoring
    domain: str = ""    # Domain name for authority scoring
    page_rank: int = 0  # Position in Google results (authority indicator)

@dataclass
class GoogleSearchResponse:
    """Response from Google PSE search"""
    query: str
    results: List[GoogleSearchResult]
    total_results: int
    search_time: float
    success: bool
    error: Optional[str] = None

class GooglePSEService:
    """
    🔍 GOOGLE PROGRAMMABLE SEARCH ENGINE SERVICE
    
    Features:
    - Google Custom Search API integration
    - 10 results per request (as requested)
    - Async HTTP requests
    - Error handling and retries
    - Rate limiting compliance
    """
    
    def __init__(self,
                 service_account_info: Dict[str, Any] = None,
                 api_key: str = None,
                 search_engine_id: str = None,
                 timeout: int = 15):
        """
        Initialize Google PSE Service

        Args:
            service_account_info: Service account credentials dict
            api_key: Google API key for Custom Search (fallback)
            search_engine_id: Custom Search Engine ID (will create default if None)
            timeout: Request timeout in seconds
        """
        self.service_account_info = service_account_info
        self.api_key = api_key
        self.search_engine_id = search_engine_id or self._get_default_cse_id()
        self.timeout = timeout
        self.session: Optional[aiohttp.ClientSession] = None
        self.base_url = "https://www.googleapis.com/customsearch/v1"
        self.credentials = None
        self._initialized = False
        
    def _get_default_cse_id(self) -> str:
        """
        Get default Custom Search Engine ID

        Using a general web search CSE ID
        """
        # New CSE ID for better web search coverage
        return "f727df904bc0148e4"  # Updated CSE ID
        
    async def initialize(self) -> bool:
        """Initialize the Google PSE service"""
        try:
            if not self.session:
                connector = aiohttp.TCPConnector(
                    limit=10,
                    limit_per_host=5,
                    enable_cleanup_closed=True
                )
                timeout = aiohttp.ClientTimeout(total=self.timeout)
                self.session = aiohttp.ClientSession(
                    connector=connector,
                    timeout=timeout
                )
                # Register session for cleanup
                register_session_for_cleanup(self.session)

            # Initialize service account credentials if provided
            if self.service_account_info:
                self.credentials = service_account.Credentials.from_service_account_info(
                    self.service_account_info,
                    scopes=['https://www.googleapis.com/auth/cse']
                )
                logger.info("✅ Service account credentials loaded")
            elif self.api_key:
                logger.info("✅ Using API key authentication")
            else:
                raise Exception("Either service_account_info or api_key must be provided")

            self._initialized = True
            logger.info("✅ Google PSE service initialized")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to initialize Google PSE service: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Cleanup resources"""
        try:
            if self.session and not self.session.closed:
                await self.session.close()
                # Wait for session to fully close
                await asyncio.sleep(0.1)
            self.session = None
            self._initialized = False
            logger.debug("✅ Google PSE service cleaned up")
        except Exception as e:
            logger.warning(f"⚠️ Google PSE cleanup warning: {e}")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10)
    )
    async def search(self, 
                    query: str,
                    search_type: str = "web",
                    count: int = 10) -> GoogleSearchResponse:
        """
        Perform Google PSE search
        
        Args:
            query: Search query
            search_type: Type of search ("web" or "news")
            count: Number of results (max 10 per request)
            
        Returns:
            GoogleSearchResponse with search results
        """
        if not self._initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            logger.info(f"🔍 Google PSE search: '{query}' ({count} results)")

            # Get access token if using service account
            headers = {}
            if self.credentials:
                # Refresh token if needed
                if not self.credentials.valid:
                    self.credentials.refresh(Request())
                headers["Authorization"] = f"Bearer {self.credentials.token}"

            # Prepare parameters
            params = {
                "cx": self.search_engine_id,
                "q": query,
                "num": min(count, 10),  # Google allows max 10 per request
                "start": 1,
                "safe": "medium",
                "lr": "lang_en|lang_vi",  # English and Vietnamese
            }

            # Add API key if not using service account
            if not self.credentials and self.api_key:
                params["key"] = self.api_key
            
            # Add search type specific parameters
            if search_type == "news":
                params["tbm"] = "nws"  # News search
                params["sort"] = "date"  # Sort by date for news
            
            # Make request
            async with self.session.get(self.base_url, params=params, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    results = self._parse_results(data)
                    search_time = time.time() - start_time
                    
                    logger.info(f"✅ Google PSE found {len(results)} results in {search_time:.2f}s")
                    
                    return GoogleSearchResponse(
                        query=query,
                        results=results,
                        total_results=len(results),
                        search_time=search_time,
                        success=True
                    )
                else:
                    error_text = await response.text()
                    raise Exception(f"Google API error {response.status}: {error_text}")
                    
        except Exception as e:
            search_time = time.time() - start_time
            logger.error(f"❌ Google PSE search failed: {e}")
            
            return GoogleSearchResponse(
                query=query,
                results=[],
                total_results=0,
                search_time=search_time,
                success=False,
                error=str(e)
            )
    
    def _parse_results(self, data: Dict[str, Any]) -> List[GoogleSearchResult]:
        """
        Parse Google API response into GoogleSearchResult objects
        
        Args:
            data: Raw response from Google API
            
        Returns:
            List of GoogleSearchResult objects
        """
        results = []
        
        items = data.get("items", [])
        for i, item in enumerate(items):
            try:
                # Extract domain from URL
                url = item.get("link", "")
                domain = ""
                if url:
                    from urllib.parse import urlparse
                    domain = urlparse(url).netloc

                result = GoogleSearchResult(
                    title=item.get("title", ""),
                    url=url,
                    snippet=item.get("snippet", ""),
                    source="google",
                    domain=domain,
                    page_rank=i + 1  # Google ranking position
                )
                results.append(result)
                
            except Exception as e:
                logger.warning(f"⚠️ Failed to parse Google result: {e}")
                continue
        
        return results
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check service health
        
        Returns:
            Health status information
        """
        try:
            if not self._initialized:
                return {
                    "status": "not_initialized",
                    "api_key_configured": bool(self.api_key),
                    "cse_id_configured": bool(self.search_engine_id)
                }
            
            # Test with a simple query
            test_result = await self.search("test", count=1)
            
            return {
                "status": "healthy" if test_result.success else "unhealthy",
                "api_key_configured": bool(self.api_key),
                "cse_id_configured": bool(self.search_engine_id),
                "last_test_success": test_result.success,
                "last_test_error": test_result.error
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "api_key_configured": bool(self.api_key),
                "cse_id_configured": bool(self.search_engine_id)
            }

# Factory function
async def create_google_pse_service(
    service_account_info: Dict[str, Any] = None,
    api_key: str = None,
    search_engine_id: str = None
) -> GooglePSEService:
    """Create and initialize Google PSE service"""
    service = GooglePSEService(
        service_account_info=service_account_info,
        api_key=api_key,
        search_engine_id=search_engine_id
    )
    await service.initialize()
    return service

# Test function
async def test_google_pse_service():
    """Test Google PSE service functionality"""
    # Use the provided API key
    api_key = "AIzaSyC3vjdRuvxx6BuXgYnmwTB7ujz5PdemYxc"
    
    service = GooglePSEService(api_key=api_key)
    await service.initialize()
    
    try:
        # Test search
        test_query = "artificial intelligence latest news"
        result = await service.search(test_query, count=5)
        
        print(f"✅ Success: {result.success}")
        print(f"   Query: {result.query}")
        print(f"   Results: {result.total_results}")
        print(f"   Search time: {result.search_time:.2f}s")
        
        if result.results:
            print("   Top 3 results:")
            for i, res in enumerate(result.results[:3], 1):
                print(f"     {i}. {res.title[:60]}...")
                print(f"        {res.url}")
        
        # Health check
        health = await service.health_check()
        print(f"\n🏥 Health check: {health['status']}")
        
    finally:
        await service.cleanup()

if __name__ == "__main__":
    asyncio.run(test_google_pse_service())
