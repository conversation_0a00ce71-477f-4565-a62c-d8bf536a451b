"""
Search Engine Service

Provides integration with DuckDuckGo search engine to retrieve relevant URLs
for user queries. Supports both text and news search modes.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from ddgs import DDGS
# Removed tenacity imports - no retry logic

logger = logging.getLogger(__name__)

@dataclass
class SearchResult:
    """Represents a single search result"""
    title: str
    url: str
    snippet: str
    source: str = "duckduckgo"

@dataclass
class SearchResponse:
    """Represents the complete search response"""
    query: str
    results: List[SearchResult]
    total_results: int
    search_time: float
    success: bool
    error: Optional[str] = None

class SearchEngineService:
    """
    Service for searching the web using DuckDuckGo
    """
    
    def __init__(self, max_results: int = 10, timeout: int = 15):
        """
        Initialize the search engine service (DuckDuckGo only)

        Args:
            max_results: Maximum number of search results to return
            timeout: Timeout for search requests in seconds
        """
        self.max_results = max_results
        self.timeout = timeout
        self.ddgs = None

    def _detect_vietnamese_query(self, query: str) -> bool:
        """Detect if query contains Vietnamese text"""
        vietnamese_chars = ['ă', 'â', 'đ', 'ê', 'ô', 'ơ', 'ư', 'á', 'à', 'ả', 'ã', 'ạ',
                           'ấ', 'ầ', 'ẩ', 'ẫ', 'ậ', 'ắ', 'ằ', 'ẳ', 'ẵ', 'ặ', 'é', 'è',
                           'ẻ', 'ẽ', 'ẹ', 'ế', 'ề', 'ể', 'ễ', 'ệ', 'í', 'ì', 'ỉ', 'ĩ',
                           'ị', 'ó', 'ò', 'ỏ', 'õ', 'ọ', 'ố', 'ồ', 'ổ', 'ỗ', 'ộ', 'ớ',
                           'ờ', 'ở', 'ỡ', 'ợ', 'ú', 'ù', 'ủ', 'ũ', 'ụ', 'ứ', 'ừ', 'ử',
                           'ữ', 'ự', 'ý', 'ỳ', 'ỷ', 'ỹ', 'ỵ']
        return any(char in query.lower() for char in vietnamese_chars)

    def _enhance_vietnamese_query(self, query: str) -> str:
        """Enhance Vietnamese queries with site-specific searches"""
        if self._detect_vietnamese_query(query):
            # Add Vietnamese news sites to improve results
            vietnamese_sites = [
                "site:dantri.com.vn OR site:vnexpress.net OR site:tuoitre.vn OR site:thanhnien.vn OR site:vietnamnet.vn"
            ]
            enhanced_query = f"({query}) AND ({vietnamese_sites[0]})"
            logger.info(f"🇻🇳 Enhanced Vietnamese query: {enhanced_query}")
            return enhanced_query
        return query
        
    async def initialize(self):
        """Initialize the search engine"""
        try:
            # DuckDuckGo search doesn't require initialization
            # but we can use this for future enhancements
            logger.info("✅ Search Engine Service initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Search Engine Service: {e}")
            raise
    
    # Removed retry decorator - no retry for DuckDuckGo, fail fast
    async def search(
        self,
        query: str,
        search_type: str = "text",
        region: str = "wt-wt",
        safesearch: str = "moderate"
    ) -> SearchResponse:
        """
        Search for the given query using DuckDuckGo
        
        Args:
            query: Search query
            search_type: Type of search ("text", "news")
            region: Search region (default: worldwide)
            safesearch: Safe search setting ("strict", "moderate", "off")
            
        Returns:
            SearchResponse with results
        """
        import time
        start_time = time.time()
        
        try:
            # Enhance query for Vietnamese content
            enhanced_query = self._enhance_vietnamese_query(query)
            logger.info(f"🔍 Searching DuckDuckGo for: '{enhanced_query}'")

            # Run the search in a thread pool to avoid blocking with timeout
            loop = asyncio.get_event_loop()
            results = await asyncio.wait_for(
                loop.run_in_executor(
                    None,
                    self._perform_search,
                    enhanced_query, search_type, region, safesearch
                ),
                timeout=5.0  # 🚀 OPTIMIZATION: 5s timeout for DuckDuckGo specifically
            )
            
            search_time = time.time() - start_time
            
            # If DuckDuckGo returned results, use them
            if results:
                # Convert to SearchResult objects
                search_results = []
                for result in results[:self.max_results]:
                    search_results.append(SearchResult(
                        title=result.get('title', ''),
                        url=result.get('href', ''),
                        snippet=result.get('body', ''),
                        source="duckduckgo"
                    ))

                logger.info(f"✅ Found {len(search_results)} DuckDuckGo results in {search_time:.2f}s")

                return SearchResponse(
                    query=query,
                    results=search_results,
                    total_results=len(search_results),
                    search_time=search_time,
                    success=True
                )

            # If no results from DuckDuckGo
            else:
                logger.warning(f"⚠️ No results found from DuckDuckGo for: '{query}'")
            return SearchResponse(
                query=query,
                results=[],
                total_results=0,
                search_time=search_time,
                success=False,
                error="No search results found from any engine"
            )
            
        except Exception as e:
            search_time = time.time() - start_time
            logger.warning(f"⚠️ DuckDuckGo failed for query '{query}': {e}")

            # DuckDuckGo failed
            logger.error(f"❌ DuckDuckGo search failed for query '{query}'")
            return SearchResponse(
                query=query,
                results=[],
                total_results=0,
                search_time=search_time,
                success=False,
                error=str(e)
            )
    
    def _perform_search(
        self,
        query: str,
        search_type: str,
        region: str,
        safesearch: str
    ) -> List[Dict[str, Any]]:
        """
        Perform the actual search (synchronous) with retry strategies
        """
        import time

        # Try multiple search strategies if first one fails
        search_strategies = [
            query,  # Original query
            query.replace(":", "").replace("?", ""),  # Remove special chars
            " ".join(query.split()[:5]),  # Shorter query (first 5 words)
            query.split()[0] if query.split() else query  # Single keyword retry
        ]

        for i, search_query in enumerate(search_strategies):
            try:
                with DDGS() as ddgs:
                    if search_type == "news":
                        results = list(ddgs.news(
                            search_query,
                            region=region,
                            safesearch=safesearch,
                            max_results=self.max_results
                        ))
                    else:
                        results = list(ddgs.text(
                            search_query,
                            region=region,
                            safesearch=safesearch,
                            max_results=self.max_results
                        ))

                    if results:  # If we got results, return them
                        if i > 0:  # Log if we used a retry strategy
                            logger.info(f"🔄 Used retry search strategy {i+1}: '{search_query}'")
                        return results

                    # If no results, try next strategy (no sleep - fail fast)
                    if i < len(search_strategies) - 1:
                        continue  # No sleep, try next strategy immediately

            except Exception as e:
                logger.warning(f"⚠️ Search attempt {i+1} failed: {e}")
                # No sleep on error - fail fast
                continue

        # If all DuckDuckGo strategies failed, return empty list
        logger.warning(f"⚠️ All DuckDuckGo search strategies failed for query: '{query}'")
        return []
    
    async def search_multiple_queries(
        self, 
        queries: List[str],
        search_type: str = "text"
    ) -> List[SearchResponse]:
        """
        Search multiple queries concurrently
        
        Args:
            queries: List of search queries
            search_type: Type of search
            
        Returns:
            List of SearchResponse objects
        """
        logger.info(f"🔍 Searching {len(queries)} queries concurrently")
        
        tasks = [
            self.search(query, search_type) 
            for query in queries
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        search_responses = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Search failed for query '{queries[i]}': {result}")
                search_responses.append(SearchResponse(
                    query=queries[i],
                    results=[],
                    total_results=0,
                    search_time=0.0,
                    success=False,
                    error=str(result)
                ))
            else:
                search_responses.append(result)
        
        return search_responses
    
    async def get_urls_from_query(self, query: str) -> List[str]:
        """
        Get just the URLs from a search query (convenience method)

        Args:
            query: Search query

        Returns:
            List of URLs
        """
        response = await self.search(query)
        if response.success:
            return [result.url for result in response.results]
        else:
            logger.warning(f"⚠️ Search failed, returning empty URL list")
            return []

    async def cleanup(self):
        """Cleanup search engine resources"""
        try:
            # Force cleanup of any remaining connections
            import gc
            import asyncio

            # Wait a bit for any pending operations
            await asyncio.sleep(0.1)

            # Force garbage collection multiple times
            for _ in range(3):
                gc.collect()
                await asyncio.sleep(0.05)

            logger.debug("✅ Search Engine Service cleanup completed")
        except Exception as e:
            logger.warning(f"⚠️ Search Engine Service cleanup warning: {e}")

    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.cleanup()
