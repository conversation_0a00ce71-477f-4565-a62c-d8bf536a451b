"""
AI Search Engine Module

This module provides AI-powered search functionality similar to Perplexity,
integrating with the existing Jina Crawler infrastructure.

Components:
- QueryRefinementService: Refines user queries using Gemini 2.5 Flash Lite
- SearchEngineService: Searches DuckDuckGo for relevant URLs
- BatchCrawlerService: Crawls multiple URLs in parallel using JiniCrawler
- ContentSynthesisService: Synthesizes information using Gemini 2.5 Flash Lite
- AISearchEngine: Main orchestrator for the entire search process
"""

from .ai_search_engine import AISearchEngine
from .query_refinement_service import QueryRefinementService
from .search_engine_service import SearchEngineService
from .batch_crawler_service import BatchCrawlerService
from .content_synthesis_service import ContentSynthesisService

__all__ = [
    "AISearchEngine",
    "QueryRefinementService", 
    "SearchEngineService",
    "BatchCrawlerService",
    "ContentSynthesisService"
]

__version__ = "1.0.0"
