"""
Content Synthesis Service

Uses Gemini 2.5 Flash Lite to synthesize information from multiple crawled sources
into comprehensive, well-structured answers with proper citations.
"""

import asyncio
import logging
import time
import json
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import google.generativeai as genai
from tenacity import retry, stop_after_attempt, wait_exponential

from .batch_crawler_service import BatchCrawlResult
import sys; sys.path.append("/app/mcp-integration/servers/jina_crawler"); from smart_model_manager import get_smart_model_manager, TokenEstimator

logger = logging.getLogger(__name__)

@dataclass
class Citation:
    """Represents a source citation"""
    url: str
    title: str
    snippet: str
    relevance_score: float = 0.0

@dataclass
class SynthesizedAnswer:
    """Represents a synthesized answer with citations"""
    query: str
    answer: str
    citations: List[Citation]
    confidence: float
    processing_time: float
    word_count: int
    sources_used: int

@dataclass
class ContentSynthesisResponse:
    """Response from content synthesis service"""
    success: bool
    synthesized_answer: Optional[SynthesizedAnswer] = None
    error: Optional[str] = None

class ContentSynthesisService:
    """
    Service for synthesizing information from multiple sources using Gemini 2.5 Flash Lite
    """
    
    def __init__(self, api_key: Optional[str] = None, model_name: str = "gemini-2.5-flash-lite"):
        """
        Initialize the content synthesis service with Smart Model Manager

        Args:
            api_key: Google AI API key (if None, will use environment variable)
            model_name: Gemini model to use (will be overridden by smart selection)
        """
        self.api_key = api_key
        self.model_name = model_name
        self.model = None
        self._initialized = False
        self.smart_manager = get_smart_model_manager()
        
    async def initialize(self):
        """Initialize the Gemini model"""
        try:
            if self.api_key:
                genai.configure(api_key=self.api_key)
            
            # Configure the model for synthesis with Gemini 2.5 Flash Lite token limit
            self.model = genai.GenerativeModel(
                model_name=self.model_name,
                generation_config={
                    "temperature": 0.7,  # Faster generation
                    "top_p": 0.9,
                    "top_k": 40,
                    "max_output_tokens": 8192,  # Optimized for speed vs quality
                }
            )
            
            self._initialized = True
            logger.info(f"✅ Content Synthesis Service initialized with {self.model_name}")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Content Synthesis Service: {e}")
            raise
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=3, max=10)
    )
    async def synthesize_content(
        self, 
        query: str, 
        crawl_results: List[BatchCrawlResult],
        max_sources: int = 5
    ) -> ContentSynthesisResponse:
        """
        Synthesize information from crawled content into a comprehensive answer
        
        Args:
            query: Original user query
            crawl_results: List of crawled content results
            max_sources: Maximum number of sources to use
            
        Returns:
            ContentSynthesisResponse with synthesized answer
        """
        if not self._initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            logger.info(f"🤖 Synthesizing content for query: '{query}'")
            
            # Filter and prepare successful results
            successful_results = [r for r in crawl_results if r.success and r.content.strip()]
            
            if not successful_results:
                return ContentSynthesisResponse(
                    success=False,
                    error="No successful crawl results to synthesize"
                )
            
            # Limit to max_sources
            sources_to_use = successful_results[:max_sources]
            
            # Create synthesis prompt
            prompt = self._create_synthesis_prompt(query, sources_to_use)
            
            # Generate response using Gemini with smart model selection
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                self._generate_synthesis_response_smart,
                prompt,
                query
            )
            
            # Parse the response
            synthesized_answer = self._parse_synthesis_response(
                query, response, sources_to_use, start_time
            )
            
            logger.info(f"✅ Content synthesized: {synthesized_answer.word_count} words, {synthesized_answer.sources_used} sources")
            
            return ContentSynthesisResponse(
                success=True,
                synthesized_answer=synthesized_answer
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ Content synthesis failed: {e}")
            
            return ContentSynthesisResponse(
                success=False,
                error=str(e)
            )
    
    def _create_synthesis_prompt(self, query: str, sources: List[BatchCrawlResult]) -> str:
        """Create a prompt for content synthesis"""
        
        # Prepare sources text with maximum content for comprehensive synthesis
        sources_text = ""
        for i, source in enumerate(sources, 1):
            sources_text += f"\n--- SOURCE {i} ---\n"
            sources_text += f"URL: {source.url}\n"
            sources_text += f"Title: {source.title}\n"
            # Use much more content for comprehensive synthesis (up to 20K chars per source)
            content_preview = source.content[:20000] if len(source.content) > 20000 else source.content
            sources_text += f"Content: {content_preview}\n"
        
        return f"""
You are an expert research assistant and content synthesizer. Your task is to create a comprehensive, detailed answer by synthesizing information from multiple sources.

USER QUESTION: "{query}"

SOURCES:
{sources_text}

Create a comprehensive, in-depth answer that:
1. **MUST be extremely detailed and thorough** (aim for 1500-3000 words minimum)
2. Directly addresses the user's question with exceptional depth and nuance
3. Synthesizes information from ALL provided sources comprehensively
4. Uses proper structure with multiple headings, subheadings, and bullet points
5. Includes extensive examples, case studies, statistics, and detailed explanations
6. Provides comprehensive context, background, historical perspective, and future implications
7. Uses proper citations [1][2][3] throughout the text extensively
8. Maintains factual accuracy and acknowledges uncertainties and limitations
9. Includes comparative analysis between different viewpoints when applicable
10. Provides actionable insights and practical applications

Format your response as JSON:
{{
    "answer": "EXTREMELY COMPREHENSIVE and DETAILED answer with proper structure, extensive examples, in-depth explanations, and citations [1][2][3]. This should be substantial content (1500-3000 words) that thoroughly addresses the question with exceptional depth and nuance. Include specific details, case studies, statistics, historical context, comparative analysis, and comprehensive explanations across multiple sections and subsections.",
    "citations": [
        {{
            "id": 1,
            "url": "source_url",
            "title": "source_title",
            "snippet": "relevant excerpt from source (100-200 chars)",
            "relevance_score": 0.95
        }}
    ],
    "confidence": 0.85,
    "key_points": ["detailed_point1", "detailed_point2", "detailed_point3"],
    "limitations": "any limitations, caveats, or areas needing more research"
}}

CRITICAL GUIDELINES FOR COMPREHENSIVE RESPONSES:
- **Write extremely substantial, detailed content (1500-3000 words minimum)** - avoid brief or superficial answers
- Use ALL available sources extensively and cite them properly [1][2][3] throughout
- Include extensive specific examples, statistics, case studies, and detailed analysis from sources
- Structure longer answers with multiple clear headings, subheadings, and organized sections
- Provide comprehensive explanations with depth, context, and nuanced analysis
- **ENSURE VALID JSON FORMAT** - complete all JSON fields properly
- If sources contradict, provide detailed discussion of both perspectives with analysis
- Include extensive practical implications, applications, and actionable insights
- Provide historical context, current state, and future projections when relevant
- Use professional, engaging, and educational language that thoroughly informs the reader
- Create content that rivals academic papers or comprehensive reports in depth and quality

Respond only with valid JSON.
"""
    
    def _generate_synthesis_response_smart(self, prompt: str, query: str) -> str:
        """Generate synthesis response using Gemini with smart model selection (synchronous)"""
        # Smart model selection
        selected_model, selection_reason = self.smart_manager.select_model(prompt, query)
        logger.info(f"Content synthesis using model: {selected_model} ({selection_reason})")

        # Get model configuration
        model_config = self.smart_manager.get_model_config(selected_model)
        if not model_config:
            raise Exception(f"Unknown model: {selected_model}")

        # Estimate tokens for tracking
        estimated_tokens = TokenEstimator.estimate_tokens(prompt + query)

        # Configure model with selected model
        model = genai.GenerativeModel(
            model_name=selected_model,
            generation_config={
                "temperature": 0.7,  # Faster generation
                "top_p": 0.9,
                "top_k": 40,
                "max_output_tokens": min(8192, model_config.output_tokens_max),
            }
        )

        try:
            response = model.generate_content(prompt)
            # Record successful usage
            self.smart_manager.record_usage(selected_model, estimated_tokens, True, False)
            return response.text

        except Exception as e:
            error_msg = str(e)

            # Check if we should fallback to another model
            should_fallback, fallback_model = self.smart_manager.should_fallback(selected_model, error_msg)

            if should_fallback:
                logger.warning(f"Content synthesis failed on {selected_model}, falling back to {fallback_model}: {e}")
                self.smart_manager.record_usage(selected_model, 0, False, "429" in error_msg)

                # Try with fallback model
                fallback_config = self.smart_manager.get_model_config(fallback_model)
                fallback_model_instance = genai.GenerativeModel(
                    model_name=fallback_model,
                    generation_config={
                        "temperature": 0.7,  # Faster generation
                        "top_p": 0.9,
                        "top_k": 40,
                        "max_output_tokens": min(8192, fallback_config.output_tokens_max),
                    }
                )

                try:
                    response = fallback_model_instance.generate_content(prompt)
                    self.smart_manager.record_usage(fallback_model, estimated_tokens, True, False)
                    return response.text
                except Exception as fallback_error:
                    self.smart_manager.record_usage(fallback_model, 0, False, False)
                    raise fallback_error
            else:
                self.smart_manager.record_usage(selected_model, 0, False, "429" in error_msg)
                raise e

    def _generate_synthesis_response(self, prompt: str) -> str:
        """Legacy method for backward compatibility"""
        return self._generate_synthesis_response_smart(prompt, "")
    
    def _parse_synthesis_response(
        self, 
        query: str, 
        response: str, 
        sources: List[BatchCrawlResult],
        start_time: float
    ) -> SynthesizedAnswer:
        """Parse the Gemini response into a SynthesizedAnswer object"""
        
        processing_time = time.time() - start_time
        
        try:
            # Clean the response
            cleaned_response = response.strip()
            if cleaned_response.startswith("```json"):
                cleaned_response = cleaned_response[7:]
            if cleaned_response.endswith("```"):
                cleaned_response = cleaned_response[:-3]
            cleaned_response = cleaned_response.strip()

            # Try to fix common JSON issues
            # Remove any trailing commas before closing braces/brackets
            import re
            cleaned_response = re.sub(r',(\s*[}\]])', r'\1', cleaned_response)

            # If response is too long, try to extract just the JSON part
            if len(cleaned_response) > 100000:  # 100KB limit
                # Look for the main JSON structure
                json_start = cleaned_response.find('{')
                if json_start != -1:
                    # Find the matching closing brace
                    brace_count = 0
                    json_end = -1
                    for i, char in enumerate(cleaned_response[json_start:], json_start):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                json_end = i + 1
                                break

                    if json_end != -1:
                        cleaned_response = cleaned_response[json_start:json_end]
                        logger.debug(f"Truncated JSON response to {len(cleaned_response)} characters")

            # Parse JSON
            parsed = json.loads(cleaned_response)
            
            # Extract citations
            citations = []
            for citation_data in parsed.get("citations", []):
                citations.append(Citation(
                    url=citation_data.get("url", ""),
                    title=citation_data.get("title", ""),
                    snippet=citation_data.get("snippet", ""),
                    relevance_score=float(citation_data.get("relevance_score", 0.0))
                ))
            
            answer_text = parsed.get("answer", "")
            word_count = len(answer_text.split())
            
            return SynthesizedAnswer(
                query=query,
                answer=answer_text,
                citations=citations,
                confidence=float(parsed.get("confidence", 0.5)),
                processing_time=processing_time,
                word_count=word_count,
                sources_used=len(sources)
            )
            
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.warning(f"⚠️ Failed to parse synthesis response, trying enhanced fallback: {e}")

            # Enhanced fallback: try to extract partial content
            try:
                # Try to extract answer from truncated JSON
                if '"answer":' in response:
                    answer_start = response.find('"answer":') + 10
                    # Find the end of the answer field (look for next field or closing brace)
                    answer_end = response.find('","', answer_start)
                    if answer_end == -1:
                        answer_end = response.find('"}', answer_start)
                    if answer_end == -1:
                        answer_end = len(response) - 1

                    extracted_answer = response[answer_start:answer_end].strip('"')
                    if len(extracted_answer) > 100:  # If we got substantial content
                        logger.info(f"✅ Extracted partial answer from truncated JSON: {len(extracted_answer)} chars")

                        # Create basic citations from sources
                        citations = []
                        for i, source in enumerate(sources[:3], 1):
                            citations.append(Citation(
                                url=source.url,
                                title=source.title or "Source",
                                snippet=source.content[:200] if source.content else "",
                                relevance_score=0.7  # Higher confidence for extracted content
                            ))

                        return SynthesizedAnswer(
                            query=query,
                            answer=extracted_answer,
                            citations=citations,
                            confidence=0.6,  # Higher confidence for extracted content
                            processing_time=processing_time,
                            word_count=len(extracted_answer.split()),
                            sources_used=len(sources)
                        )
            except Exception as extract_error:
                logger.debug(f"Failed to extract partial content: {extract_error}")

            # Final fallback: create basic answer
            fallback_answer = f"Based on the available sources, here's what I found regarding '{query}':\n\n"

            for i, source in enumerate(sources[:3], 1):
                if source.content:
                    fallback_answer += f"According to {source.title or source.url} [{i}]:\n"
                    fallback_answer += f"{source.content[:800]}...\n\n"  # More content in fallback
            
            # Create basic citations
            citations = []
            for i, source in enumerate(sources[:3], 1):
                citations.append(Citation(
                    url=source.url,
                    title=source.title or "Source",
                    snippet=source.content[:200] if source.content else "",
                    relevance_score=0.5
                ))
            
            return SynthesizedAnswer(
                query=query,
                answer=fallback_answer,
                citations=citations,
                confidence=0.3,
                processing_time=processing_time,
                word_count=len(fallback_answer.split()),
                sources_used=len(sources)
            )

    async def cleanup(self):
        """Cleanup content synthesis service resources"""
        try:
            # Clear model reference and force garbage collection
            self.model = None
            self._initialized = False
            import gc
            gc.collect()
            logger.debug("✅ Content Synthesis Service cleanup completed")
        except Exception as e:
            logger.warning(f"⚠️ Content Synthesis Service cleanup warning: {e}")

    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.cleanup()
