#!/usr/bin/env python3
"""
Debug new CSE ID
"""

import asyncio
import aiohttp
import json

async def debug_new_cse():
    """Debug the new CSE ID"""
    api_key = "AIzaSyBrTm59kxOlERAmm-oxa-U0ZTXcZykujFA"
    cse_id = "f727df904bc0148e4"
    
    base_url = "https://www.googleapis.com/customsearch/v1"
    
    test_queries = [
        "artificial intelligence",
        "python programming",
        "lễ duyệt binh 2025",
        "Vietnam news"
    ]
    
    async with aiohttp.ClientSession() as session:
        for query in test_queries:
            print(f"\n{'='*60}")
            print(f"Testing query: '{query}'")
            print(f"CSE ID: {cse_id}")
            print(f"{'='*60}")
            
            params = {
                "key": api_key,
                "cx": cse_id,
                "q": query,
                "num": 5,
                "start": 1,
                "safe": "medium",
            }
            
            try:
                async with session.get(base_url, params=params) as response:
                    print(f"Status: {response.status}")
                    print(f"Headers: {dict(response.headers)}")
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        print(f"Response keys: {list(data.keys())}")
                        
                        # Search information
                        search_info = data.get('searchInformation', {})
                        print(f"Search info: {search_info}")
                        
                        # Items
                        items = data.get('items', [])
                        print(f"Items count: {len(items)}")
                        
                        if items:
                            print("Results:")
                            for i, item in enumerate(items, 1):
                                print(f"  {i}. {item.get('title', 'No title')}")
                                print(f"     {item.get('link', 'No URL')}")
                                print(f"     {item.get('snippet', 'No snippet')[:100]}...")
                        else:
                            print("No items found")
                            print(f"Full response: {json.dumps(data, indent=2)}")
                            
                    else:
                        error_text = await response.text()
                        print(f"Error response: {error_text}")
                        
            except Exception as e:
                print(f"Exception: {e}")
                import traceback
                traceback.print_exc()
            
            await asyncio.sleep(2)

if __name__ == "__main__":
    asyncio.run(debug_new_cse())
