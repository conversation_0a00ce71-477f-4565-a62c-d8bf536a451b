"""
Query Refinement Service

Uses Gemini 2.5 Flash Lite to refine and optimize user queries for better search results.
Analyzes user intent and reformulates queries to be more specific and search-friendly.
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import google.generativeai as genai
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger(__name__)

@dataclass
class RefinedQuery:
    """Represents a refined search query"""
    original_query: str
    refined_query: str
    search_keywords: List[str]
    intent: str
    confidence: float
    processing_time: float

@dataclass
class QueryRefinementResponse:
    """Response from query refinement service"""
    success: bool
    refined_query: Optional[RefinedQuery] = None
    error: Optional[str] = None

class QueryRefinementService:
    """
    Service for refining user queries using Gemini 2.5 Flash Lite
    """
    
    def __init__(self, api_key: Optional[str] = None, model_name: str = "gemini-2.5-flash-lite"):
        """
        Initialize the query refinement service
        
        Args:
            api_key: Google AI API key (if None, will use environment variable)
            model_name: Gemini model to use
        """
        self.api_key = api_key
        self.model_name = model_name
        self.model = None
        self._initialized = False
        
    async def initialize(self):
        """Initialize the Gemini model"""
        try:
            if self.api_key:
                genai.configure(api_key=self.api_key)
            
            # Configure the model
            self.model = genai.GenerativeModel(
                model_name=self.model_name,
                generation_config={
                    "temperature": 0.3,
                    "top_p": 0.8,
                    "top_k": 40,
                    "max_output_tokens": 1024,
                }
            )
            
            self._initialized = True
            logger.info(f"✅ Query Refinement Service initialized with {self.model_name}")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Query Refinement Service: {e}")
            raise
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=8)
    )
    async def refine_query(self, user_query: str) -> QueryRefinementResponse:
        """
        Refine a user query for better search results
        
        Args:
            user_query: Original user query
            
        Returns:
            QueryRefinementResponse with refined query
        """
        if not self._initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            logger.info(f"🔍 Refining query: '{user_query}'")
            
            # Prepare the prompt for query refinement
            prompt = self._create_refinement_prompt(user_query)
            
            # Generate response using Gemini
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                self._generate_response,
                prompt
            )
            
            # Parse the response
            refined_query = self._parse_refinement_response(user_query, response, start_time)
            
            logger.info(f"✅ Query refined: '{user_query}' -> '{refined_query.refined_query}'")
            
            return QueryRefinementResponse(
                success=True,
                refined_query=refined_query
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ Query refinement failed: {e}")
            
            return QueryRefinementResponse(
                success=False,
                error=str(e)
            )
    
    def _create_refinement_prompt(self, user_query: str) -> str:
        """Create a prompt for query refinement"""
        return f"""
You are an expert search query optimizer. Your task is to analyze the user's query and refine it for better web search results.

Original Query: "{user_query}"

Please analyze this query and provide:
1. A refined search query that is more specific and likely to return relevant results
2. Key search keywords (3-5 words)
3. The user's intent (informational, navigational, transactional, etc.)
4. Your confidence level (0.0 to 1.0)

Format your response as JSON:
{{
    "refined_query": "optimized search query",
    "keywords": ["keyword1", "keyword2", "keyword3"],
    "intent": "intent_type",
    "confidence": 0.85,
    "reasoning": "brief explanation of changes made"
}}

Guidelines:
- Make queries more specific and searchable
- Add relevant context if the query is too vague
- Remove unnecessary words that don't help search
- Consider current events and trending topics
- Optimize for factual, reliable sources
- Keep the original meaning and intent

Respond only with valid JSON.
"""
    
    def _generate_response(self, prompt: str) -> str:
        """Generate response using Gemini (synchronous)"""
        response = self.model.generate_content(prompt)
        return response.text
    
    def _parse_refinement_response(
        self, 
        original_query: str, 
        response: str, 
        start_time: float
    ) -> RefinedQuery:
        """Parse the Gemini response into a RefinedQuery object"""
        import json
        
        processing_time = time.time() - start_time
        
        try:
            # Clean the response (remove markdown formatting if present)
            cleaned_response = response.strip()
            if cleaned_response.startswith("```json"):
                cleaned_response = cleaned_response[7:]
            if cleaned_response.endswith("```"):
                cleaned_response = cleaned_response[:-3]
            cleaned_response = cleaned_response.strip()
            
            # Parse JSON
            parsed = json.loads(cleaned_response)
            
            return RefinedQuery(
                original_query=original_query,
                refined_query=parsed.get("refined_query", original_query),
                search_keywords=parsed.get("keywords", []),
                intent=parsed.get("intent", "informational"),
                confidence=float(parsed.get("confidence", 0.5)),
                processing_time=processing_time
            )
            
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.warning(f"⚠️ Failed to parse refinement response, using fallback: {e}")
            
            # Fallback: return original query with basic analysis
            return RefinedQuery(
                original_query=original_query,
                refined_query=original_query,
                search_keywords=original_query.split()[:5],
                intent="informational",
                confidence=0.3,
                processing_time=processing_time
            )
    
    async def refine_multiple_queries(self, queries: List[str]) -> List[QueryRefinementResponse]:
        """
        Refine multiple queries concurrently
        
        Args:
            queries: List of user queries
            
        Returns:
            List of QueryRefinementResponse objects
        """
        logger.info(f"🔍 Refining {len(queries)} queries concurrently")
        
        tasks = [self.refine_query(query) for query in queries]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        responses = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Query refinement failed for '{queries[i]}': {result}")
                responses.append(QueryRefinementResponse(
                    success=False,
                    error=str(result)
                ))
            else:
                responses.append(result)
        
        return responses

    async def cleanup(self):
        """Cleanup query refinement service resources"""
        try:
            # Gemini API doesn't maintain persistent connections
            # but we can clear the model reference and force garbage collection
            self.model = None
            self._initialized = False
            import gc
            gc.collect()
            logger.debug("✅ Query Refinement Service cleanup completed")
        except Exception as e:
            logger.warning(f"⚠️ Query Refinement Service cleanup warning: {e}")

    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.cleanup()
