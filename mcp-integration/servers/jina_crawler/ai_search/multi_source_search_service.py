"""
Multi-Source Search Service

Integrates multiple search engines to provide comprehensive search results:
1. Google PSE: 10 results
2. DuckDuckGo: 8 results  
3. Brave (2 pools): 2x3 = 6 results
Total: 24 results before reranking
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass

# Import search services
try:
    from .google_pse_service import GooglePSEService, GoogleSearchResult
    from .search_engine_service import SearchEngineService, SearchResult as DDGSearchResult
    from .brave_search_service import BraveSearchService, BraveSearchResult
    from .searxng_service import SearXNGService, SearXNGSearchResponse
    from .search_reranker import GeminiReranker
except ImportError:
    # Fallback for direct execution
    from google_pse_service import GooglePSEService, GoogleSearchResult
    from search_engine_service import SearchEngineService, SearchResult as DDGSearchResult
    from brave_search_service import BraveSearchService, BraveSearchResult
    from searxng_service import SearXNGService, SearXNGSearchResponse
    from search_reranker import GeminiReranker

logger = logging.getLogger(__name__)

@dataclass
class UnifiedSearchResult:
    """Unified search result from any source"""
    title: str
    url: str
    snippet: str
    source: str
    position: int = 0  # Position in original source results
    base_score: float = 0.0  # Unified base score
    position: int = 0  # For reranker compatibility

@dataclass
class MultiSourceSearchResponse:
    """Response from multi-source search"""
    query: str
    results: List[UnifiedSearchResult]
    total_results: int
    search_time: float
    success: bool
    source_stats: Dict[str, Dict[str, Any]]
    error: Optional[str] = None

class MultiSourceSearchService:
    """
    🌐 MULTI-SOURCE SEARCH ENGINE

    Integrates multiple search sources for comprehensive results:
    - Google PSE: 10 high-quality results
    - DuckDuckGo: 8 diverse results
    - Brave: 20 results (optimized single call with 4-key rotation)
    - SearXNG: 15 results from multiple engines

    Total: ~53 results → Reranker → Top 10 final results

    Features:
    - Parallel search execution across all sources
    - Optimized Brave search with unified API pool
    - Result deduplication and ranking
    - Source attribution and statistics
    - Unified result format
    - Vietnamese content optimization via SearXNG
    """
    
    def __init__(self,
                 google_api_key: str = None,
                 google_service_account_info: Dict[str, Any] = None,
                 google_cse_id: str = None,
                 gemini_api_key: str = None,
                 enable_reranking: bool = True):
        """
        Initialize multi-source search service

        Args:
            google_api_key: Google API key for PSE (optional)
            google_service_account_info: Google service account info for PSE (optional)
            google_cse_id: Google Custom Search Engine ID
            gemini_api_key: Gemini API key for reranking
            enable_reranking: Whether to enable result reranking
        """
        self.google_api_key = google_api_key
        self.google_service_account_info = google_service_account_info
        self.google_cse_id = google_cse_id
        self.gemini_api_key = gemini_api_key
        self.enable_reranking = enable_reranking

        # Initialize services
        self.google_service: Optional[GooglePSEService] = None
        self.ddg_service: Optional[SearchEngineService] = None
        self.brave_service: Optional[BraveSearchService] = None
        self.searxng_service: Optional[SearXNGService] = None
        self.reranker: Optional[GeminiReranker] = None

        self._initialized = False
    
    async def initialize(self) -> bool:
        """Initialize all search services"""
        try:
            logger.info("🚀 Initializing multi-source search service...")
            
            # Initialize Google PSE service
            if self.google_service_account_info or self.google_api_key:
                self.google_service = GooglePSEService(
                    service_account_info=self.google_service_account_info,
                    api_key=self.google_api_key,
                    search_engine_id=self.google_cse_id
                )
                await self.google_service.initialize()
            else:
                logger.warning("⚠️ No Google credentials provided, Google PSE disabled")
            
            # Initialize DuckDuckGo service
            self.ddg_service = SearchEngineService(max_results=8)
            await self.ddg_service.initialize()
            
            # Initialize Brave service
            self.brave_service = BraveSearchService()
            await self.brave_service.initialize()

            # Initialize SearXNG service
            self.searxng_service = SearXNGService(max_results=15)  # Increased for better balance
            await self.searxng_service.initialize()
            logger.info("✅ SearXNG service initialized")

            # Initialize reranker if enabled
            if self.enable_reranking and self.gemini_api_key:
                self.reranker = GeminiReranker(
                    api_key=self.gemini_api_key,
                    max_results=10,  # Max 10 results
                    min_results=3,   # At least 3 results
                    score_threshold=0.65  # Balanced threshold for quality
                )
                await self.reranker.initialize()
                logger.info("✅ Reranker initialized")

            self._initialized = True
            logger.info("✅ Multi-source search service initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize multi-source search service: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Cleanup all services"""
        logger.debug("🧹 Multi-source search service cleanup starting...")

        cleanup_tasks = []
        services = [
            ("Google PSE", self.google_service),
            ("DuckDuckGo", self.ddg_service),
            ("Brave", self.brave_service),
            ("SearXNG", self.searxng_service),
            ("Reranker", self.reranker)
        ]

        for service_name, service in services:
            if service:
                cleanup_tasks.append(self._safe_service_cleanup(service_name, service))

        # Wait for all cleanups to complete
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)

        logger.debug("✅ Multi-source search service cleaned up")

    async def _safe_service_cleanup(self, service_name: str, service):
        """Safely cleanup a service with error handling"""
        try:
            if hasattr(service, 'cleanup'):
                await service.cleanup()
                logger.debug(f"✅ {service_name} cleanup successful")
        except Exception as e:
            logger.debug(f"⚠️ {service_name} cleanup warning: {e}")

    async def _force_cleanup_sessions(self):
        """Force cleanup any remaining sessions in search services"""
        logger.debug("🔥 Force cleanup sessions in search services...")

        services = [self.google_service, self.brave_service, self.searxng_service]
        for service in services:
            if service and hasattr(service, 'session'):
                try:
                    if service.session and not service.session.closed:
                        await service.session.close()
                        await asyncio.sleep(0.1)
                        logger.debug(f"🔒 Force closed session in {service.__class__.__name__}")
                except Exception as e:
                    logger.debug(f"Force cleanup warning: {e}")
    
    async def search(self, 
                    query: str,
                    search_type: str = "web") -> MultiSourceSearchResponse:
        """
        Perform multi-source search
        
        Args:
            query: Search query
            search_type: Type of search ("web" or "news")
            
        Returns:
            MultiSourceSearchResponse with combined results
        """
        if not self._initialized:
            await self.initialize()
        
        start_time = time.time()
        source_stats = {}
        all_results = []
        
        try:
            logger.info(f"🔍 Multi-source search: '{query}'")
            
            # Create search tasks for parallel execution
            search_tasks = []
            
            # Google PSE search (10 results)
            if self.google_service:
                google_task = self._search_google(query, search_type)
                search_tasks.append(("google", google_task))
            
            # DuckDuckGo search (8 results)
            if self.ddg_service:
                ddg_task = self._search_duckduckgo(query, search_type)
                search_tasks.append(("duckduckgo", ddg_task))
            
            # Brave search (optimized single call with 4-key rotation = 20 results)
            if self.brave_service:
                brave_task = self._search_brave(query, search_type)
                search_tasks.append(("brave", brave_task))

            # SearXNG search (15 results from multiple engines)
            if self.searxng_service:
                searxng_task = self._search_searxng(query, search_type)
                search_tasks.append(("searxng", searxng_task))

            # Execute all searches in parallel
            logger.info(f"🚀 Executing {len(search_tasks)} parallel searches...")
            
            # Wait for all searches to complete with timeout
            search_results = await asyncio.wait_for(
                asyncio.gather(
                    *[task for _, task in search_tasks],
                    return_exceptions=True
                ),
                timeout=6.0  # 🚀 OPTIMIZATION: 6s max for all parallel searches (increased from 3s)
            )
            
            # Process results from each source
            for i, (source_name, result) in enumerate(zip([name for name, _ in search_tasks], search_results)):
                if isinstance(result, Exception):
                    logger.error(f"❌ {source_name} search failed: {result}")
                    source_stats[source_name] = {
                        "success": False,
                        "results": 0,
                        "error": str(result)
                    }
                else:
                    # Convert to unified format
                    unified_results = self._convert_to_unified(result, source_name, query)
                    all_results.extend(unified_results)
                    
                    source_stats[source_name] = {
                        "success": True,
                        "results": len(unified_results),
                        "search_time": getattr(result, 'search_time', 0)
                    }
            
            # Deduplicate results by URL
            deduplicated_results = self._deduplicate_results(all_results)

            # Add position numbers for reranker
            for i, result in enumerate(deduplicated_results):
                result.position = i + 1

            # Apply AI reranking if enabled (ORIGINAL DESIGN: metadata + AI)
            final_results = deduplicated_results
            if self.enable_reranking and self.reranker and len(deduplicated_results) > 3:
                logger.info(f"🎯 AI Reranking {len(deduplicated_results)} results with Gemma 3-27B...")
                try:
                    reranked = await self.reranker.rerank_results(query, deduplicated_results)
                    if reranked and len(reranked) > 0:
                        # Convert back to UnifiedSearchResult
                        final_results = []
                        for rank_result in reranked:
                            unified_result = rank_result.search_result
                            # Update with rerank info
                            if hasattr(unified_result, 'position'):
                                unified_result.position = rank_result.rank
                            final_results.append(unified_result)
                        logger.info(f"✅ AI Reranked to top {len(final_results)} results (Gemma 3-27B)")
                    else:
                        logger.warning(f"⚠️ AI Reranking returned empty results, using metadata fallback")
                        # Fallback: sort by metadata scores and apply threshold + max limit
                        sorted_results = sorted(deduplicated_results, key=lambda x: x.base_score, reverse=True)
                        final_results = [r for r in sorted_results if r.base_score >= 0.65][:10]
                except Exception as e:
                    logger.warning(f"⚠️ AI Reranking failed: {e}, using metadata fallback")
                    # Fallback: sort by metadata scores and apply threshold + max limit
                    sorted_results = sorted(deduplicated_results, key=lambda x: x.base_score, reverse=True)
                    final_results = [r for r in sorted_results if r.base_score >= 0.65][:10]
            else:
                logger.info(f"🎯 AI Reranking disabled, using metadata scoring fallback")
                # Sort by metadata scores and apply threshold + max limit
                sorted_results = sorted(deduplicated_results, key=lambda x: x.base_score, reverse=True)
                final_results = [r for r in sorted_results if r.base_score >= 0.65][:10]

            search_time = time.time() - start_time

            logger.info(f"✅ Multi-source search completed: {len(final_results)} results in {search_time:.2f}s")
            
            return MultiSourceSearchResponse(
                query=query,
                results=final_results,
                total_results=len(final_results),
                search_time=search_time,
                success=True,
                source_stats=source_stats
            )
            
        except Exception as e:
            search_time = time.time() - start_time
            logger.error(f"❌ Multi-source search failed: {e}")
            
            return MultiSourceSearchResponse(
                query=query,
                results=[],
                total_results=0,
                search_time=search_time,
                success=False,
                source_stats=source_stats,
                error=str(e)
            )
    
    async def _search_google(self, query: str, search_type: str) -> Any:
        """Search Google PSE"""
        return await self.google_service.search(query, search_type, count=10)
    
    async def _search_duckduckgo(self, query: str, search_type: str) -> Any:
        """Search DuckDuckGo"""
        return await self.ddg_service.search(query, search_type)
    
    async def _search_brave(self, query: str, search_type: str) -> Any:
        """Search Brave (optimized single call with 4-key rotation = 20 results)"""
        return await self.brave_service.search_parallel(query, search_type, searches_per_pool=1)

    async def _search_searxng(self, query: str, search_type: str) -> Any:
        """Search SearXNG (multiple engines, 15 results)"""
        return await self.searxng_service.search(query, search_type)

    def _convert_to_unified(self, result: Any, source_name: str, query: str = "") -> List[UnifiedSearchResult]:
        """
        Convert search results to unified format
        
        Args:
            result: Search result from any source
            source_name: Name of the source
            
        Returns:
            List of UnifiedSearchResult objects
        """
        unified_results = []
        
        try:
            if source_name == "google" and hasattr(result, 'results'):
                total_results = len(result.results)
                for i, res in enumerate(result.results):
                    unified_results.append(UnifiedSearchResult(
                        title=res.title,
                        url=res.url,
                        snippet=res.snippet,
                        source="google",
                        position=i,
                        base_score=self._calculate_intelligent_score(res, i, "google", total_results, query)
                    ))
            
            elif source_name == "duckduckgo" and hasattr(result, 'results'):
                total_results = len(result.results)
                for i, res in enumerate(result.results):
                    unified_results.append(UnifiedSearchResult(
                        title=res.title,
                        url=res.url,
                        snippet=res.snippet,
                        source="duckduckgo",
                        position=i,
                        base_score=self._calculate_intelligent_score(res, i, "duckduckgo", total_results, query)
                    ))
            
            elif source_name == "brave" and hasattr(result, 'results'):
                total_results = len(result.results)
                for i, res in enumerate(result.results):
                    unified_results.append(UnifiedSearchResult(
                        title=res.title,
                        url=res.url,
                        snippet=res.snippet,
                        source="brave",
                        position=i,
                        base_score=self._calculate_intelligent_score(res, i, "brave", total_results, query)
                    ))

            elif source_name == "searxng" and hasattr(result, 'results'):
                total_results = len(result.results)
                for i, res in enumerate(result.results):
                    unified_results.append(UnifiedSearchResult(
                        title=res.title,
                        url=res.url,
                        snippet=res.snippet,
                        source=f"searxng-{res.engine}",  # Include engine info
                        position=i,
                        base_score=self._calculate_intelligent_score(res, i, "searxng", total_results, query)
                    ))

        except Exception as e:
            logger.warning(f"⚠️ Failed to convert {source_name} results: {e}")
        
        return unified_results

    def _calculate_intelligent_score(self, result: Any, position: int, source: str, total_results: int, query: str = "") -> float:
        """
        🔧 REBALANCED: Calculate realistic intelligent score using metadata, snippet analysis, and position

        Args:
            result: Search result object with metadata
            position: Position in source results (0-based)
            source: Source name (google, duckduckgo, brave, searxng)
            total_results: Total results from this source
            query: Search query for relevance analysis

        Returns:
            Realistic score (0.0-1.0) with better distribution
        """
        # 1. REBALANCED Position score (40% weight) - More realistic range
        if total_results <= 1:
            position_score = 0.6  # Reduced from 0.9
        else:
            import math
            normalized_pos = position / (total_results - 1)
            position_score = 0.6 - (0.4 * normalized_pos)  # Range: 0.6 → 0.2

        # 2. REBALANCED Source authority score (20% weight) - More conservative
        source_multipliers = {
            "google": 0.8,      # Reduced from 1.0
            "brave": 0.75,      # Reduced from 0.98
            "duckduckgo": 0.7,  # Reduced from 0.95
            "searxng": 0.65     # Reduced from 0.92
        }
        base_source = source.split('-')[0] if '-' in source else source
        source_score = source_multipliers.get(base_source, 0.6)  # Reduced default

        # 3. REBALANCED Domain authority score (20% weight) - More realistic
        domain_score = self._calculate_domain_authority(result)

        # 4. Content relevance score (15% weight)
        content_score = self._calculate_content_relevance(result, query)

        # 5. Freshness score (5% weight)
        freshness_score = self._calculate_freshness_score(result)

        # REBALANCED weighted combination - More realistic scoring
        final_score = (
            domain_score * 0.35 +      # Domain Authority (still important)
            position_score * 0.30 +    # Position (rebalanced range)
            source_score * 0.20 +      # Source Authority (more conservative)
            content_score * 0.10 +     # Content Relevance
            freshness_score * 0.05     # Freshness
        )

        # REBALANCED: Better minimum threshold and realistic max
        return max(0.05, min(0.85, final_score))  # Realistic range: 0.05-0.85

    def _calculate_domain_authority(self, result: Any) -> float:
        """Calculate domain authority score based on metadata"""
        domain = getattr(result, 'domain', '')
        if not domain:
            # Extract from URL if domain not available
            url = getattr(result, 'url', '')
            if url:
                from urllib.parse import urlparse
                domain = urlparse(url).netloc

        # ENHANCED Vietnamese trusted domains with optimized scoring
        vn_gov_domains = ['sbv.gov.vn', 'chinhphu.vn', 'mof.gov.vn', 'most.gov.vn', 'baochinhphu.vn']
        vn_major_news = ['vnexpress.net', 'dantri.com.vn', 'thanhnien.vn', 'tuoitre.vn']
        vn_finance = ['cafef.vn', 'vietstock.vn', 'tinnhanhchungkhoan.vn', 'tapchitaichinh.vn', 'vneconomy.vn']
        vn_specialized = ['thitruongtaichinhtiente.vn', 'thesaigontimes.vn', 'vietnamplus.vn']
        vn_tech = ['genk.vn', 'znews.vn', 'vietnamnet.vn']

        if any(gov_domain in domain for gov_domain in vn_gov_domains):
            return 0.8   # 🔧 REBALANCED: Reduced from 1.0 - Government sources
        elif any(finance_domain in domain for finance_domain in vn_finance):
            return 0.7   # 🔧 REBALANCED: Reduced from 0.92 - Financial news
        elif any(news_domain in domain for news_domain in vn_major_news):
            return 0.65  # 🔧 REBALANCED: Reduced from 0.88 - Major Vietnamese news
        elif any(spec_domain in domain for spec_domain in vn_specialized):
            return 0.6   # 🔧 REBALANCED: Reduced from 0.85 - Specialized Vietnamese media
        elif any(tech_domain in domain for tech_domain in vn_tech):
            return 0.55  # 🔧 REBALANCED: Reduced from 0.8 - Tech news
        elif domain.endswith('.vn'):
            return 0.5   # 🔧 REBALANCED: Reduced from 0.75 - Other Vietnamese domains
        elif domain.endswith(('.edu', '.org')):
            return 0.55  # 🔧 REBALANCED: Reduced from 0.78 - Educational/organizational
        else:
            return 0.4   # 🔧 REBALANCED: Reduced from 0.6 - Other domains

    def _calculate_content_relevance(self, result: Any, query: str) -> float:
        """🔧 REBALANCED: Calculate content relevance based on snippet analysis"""
        if not query:
            return 0.5  # 🔧 REBALANCED: Reduced from 0.7 - Default if no query

        title = getattr(result, 'title', '').lower()
        snippet = getattr(result, 'snippet', '').lower()
        query_lower = query.lower()

        # Extract key terms from query
        query_terms = query_lower.split()
        vietnamese_terms = ['blockchain', 'ngân hàng', 'việt nam', '2025', 'fintech', 'công nghệ', 'chuyển đổi số']

        # Calculate term matching score
        title_matches = sum(1 for term in query_terms if term in title)
        snippet_matches = sum(1 for term in query_terms if term in snippet)
        vn_term_matches = sum(1 for term in vietnamese_terms if term in title + ' ' + snippet)

        # Weighted scoring
        title_score = min(1.0, title_matches / max(1, len(query_terms))) * 0.6
        snippet_score = min(1.0, snippet_matches / max(1, len(query_terms))) * 0.3
        vn_score = min(1.0, vn_term_matches / len(vietnamese_terms)) * 0.1

        return max(0.3, title_score + snippet_score + vn_score)

    def _calculate_freshness_score(self, result: Any) -> float:
        """🔧 REBALANCED: Calculate freshness score based on page age"""
        page_age = getattr(result, 'page_age', '')
        if not page_age:
            return 0.5  # 🔧 REBALANCED: Reduced from 0.7 - Default if no age info

        # 🔧 REBALANCED: More realistic freshness scoring
        if any(indicator in page_age.lower() for indicator in ['hour', 'giờ', 'minutes', 'phút']):
            return 0.8  # 🔧 REBALANCED: Reduced from 1.0 - Very fresh
        elif any(indicator in page_age.lower() for indicator in ['day', 'ngày', '1 day', '2 day']):
            return 0.7  # 🔧 REBALANCED: Reduced from 0.9 - Fresh
        elif any(indicator in page_age.lower() for indicator in ['week', 'tuần', '1 week']):
            return 0.6  # 🔧 REBALANCED: Reduced from 0.8 - Recent
        elif any(indicator in page_age.lower() for indicator in ['month', 'tháng']):
            return 0.4  # 🔧 REBALANCED: Reduced from 0.6 - Older
        else:
            return 0.3  # 🔧 REBALANCED: Reduced from 0.5 - Unknown/old

    def _deduplicate_results(self, results: List[UnifiedSearchResult]) -> List[UnifiedSearchResult]:
        """
        Remove duplicate results based on URL
        
        Args:
            results: List of search results
            
        Returns:
            Deduplicated list of results
        """
        seen_urls = set()
        deduplicated = []
        
        for result in results:
            if result.url not in seen_urls:
                seen_urls.add(result.url)
                deduplicated.append(result)
        
        logger.info(f"🔄 Deduplicated: {len(results)} → {len(deduplicated)} results")
        return deduplicated
    
    async def health_check(self) -> Dict[str, Any]:
        """Check health of all search services"""
        health_status = {
            "status": "unknown",
            "services": {}
        }
        
        try:
            # Check each service
            if self.google_service:
                health_status["services"]["google"] = await self.google_service.health_check()
            
            if self.ddg_service:
                health_status["services"]["duckduckgo"] = {"status": "available"}
            
            if self.brave_service:
                health_status["services"]["brave"] = await self.brave_service.health_check()

            if self.searxng_service:
                health_status["services"]["searxng"] = {"status": "available"}

            # Overall status
            all_healthy = all(
                service.get("status") in ["healthy", "available"] 
                for service in health_status["services"].values()
            )
            
            health_status["status"] = "healthy" if all_healthy else "partial"
            
        except Exception as e:
            health_status["status"] = "error"
            health_status["error"] = str(e)
        
        return health_status

# Factory function
async def create_multi_source_search_service(
    google_api_key: str,
    google_cse_id: str = None,
    gemini_api_key: str = None,
    enable_reranking: bool = True
) -> MultiSourceSearchService:
    """Create and initialize multi-source search service"""
    service = MultiSourceSearchService(
        google_api_key=google_api_key,
        google_cse_id=google_cse_id,
        gemini_api_key=gemini_api_key,
        enable_reranking=enable_reranking
    )
    await service.initialize()
    return service
