#!/usr/bin/env python3
"""
Debug Google Custom Search API response
"""

import asyncio
import aiohttp
import json

async def debug_google_api():
    """Debug Google API response"""
    api_key = "AIzaSyBrTm59kxOlERAmm-oxa-U0ZTXcZykujFA"
    
    # Try different CSE IDs
    cse_ids = [
        "e25b1a6b4e4d84c9c",  # Current one
        "017576662512468239146:omuauf_lfve",  # Previous one
        "000455696194071821846:1g2qeoclfwk",  # Another common one
    ]
    
    base_url = "https://www.googleapis.com/customsearch/v1"
    query = "artificial intelligence"
    
    async with aiohttp.ClientSession() as session:
        for i, cse_id in enumerate(cse_ids, 1):
            print(f"\n{'='*60}")
            print(f"Testing CSE ID {i}: {cse_id}")
            print(f"{'='*60}")
            
            params = {
                "key": api_key,
                "cx": cse_id,
                "q": query,
                "num": 3,
                "start": 1,
                "safe": "medium",
            }
            
            try:
                async with session.get(base_url, params=params) as response:
                    print(f"Status: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        # Print key info
                        print(f"Search info: {data.get('searchInformation', {})}")
                        
                        items = data.get('items', [])
                        print(f"Items found: {len(items)}")
                        
                        if items:
                            print("First result:")
                            first = items[0]
                            print(f"  Title: {first.get('title', 'No title')}")
                            print(f"  URL: {first.get('link', 'No URL')}")
                            print(f"  Snippet: {first.get('snippet', 'No snippet')[:100]}...")
                        else:
                            print("No items in response")
                            print(f"Full response keys: {list(data.keys())}")
                            
                    else:
                        error_text = await response.text()
                        print(f"Error: {error_text}")
                        
            except Exception as e:
                print(f"Exception: {e}")

if __name__ == "__main__":
    asyncio.run(debug_google_api())
