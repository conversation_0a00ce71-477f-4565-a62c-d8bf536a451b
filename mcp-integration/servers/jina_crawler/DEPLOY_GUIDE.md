# Hướng dẫn Deploy Tối ưu hóa Vietnamese News

## 🚀 Cách Deploy Nhanh

### Bước 1: Deploy tối ưu hóa
```bash
cd mcp-integration/servers/jina_crawler
python deploy_optimization.py
```

### Bước 2: Test thử ngay
```bash
# Test nhanh
python quick_test.py

# Hoặc test chi tiết
python test_dantri_vnexpress.py
```

### Bước 3: Restart ứng dụng của bạn
- Nếu đang chạy server, restart lại
- Nếu đang chạy trong Docker, restart container

## 📋 Những gì đã được tối ưu

### ✅ Files đã được cập nhật:
- `jini_crawler.py` - Crawler chính với size optimization
- `ai_search/batch_crawler_service.py` - Batch service với limits mới
- `utils/content_size_optimizer.py` - Module tối ưu kích thước (MỚI)
- `gemini_processor_optimized.py` - Gemini processor tối ưu (MỚI)

### ✅ Environment variables đã được set:
```
GEMINI_MAX_OUTPUT_TOKENS=8192
GEMINI_CONTEXT_LIMIT=200000
ENABLE_SIZE_OPTIMIZATION=true
MAX_CONTENT_PER_URL=15000
VIETNAMESE_NEWS_OPTIMIZATION=true
```

## 🧪 Cách sử dụng sau khi deploy

### Sử dụng trong code:
```python
from ai_search.batch_crawler_service import BatchCrawlerService

# Tự động sử dụng tối ưu hóa
service = BatchCrawlerService()
await service.initialize()

response = await service.crawl_urls(
    urls=["https://dantri.com.vn", "https://vnexpress.net"],
    query="tin tức kinh tế",
    use_batch_processing=True  # Sử dụng batch processing đã tối ưu
)
```

### Hoặc sử dụng crawler trực tiếp:
```python
from jini_crawler import JiniCrawler

crawler = JiniCrawler()
await crawler.initialize()

# Tự động áp dụng size optimization
result = await crawler.crawl_and_process("https://dantri.com.vn")
```

## 📊 Kết quả mong đợi

Sau khi deploy, khi crawl dantri.vn + vnexpress.net:
- **Trước**: >272k tokens (overflow ❌)
- **Sau**: ~43k tokens (OK ✅)
- **Giảm**: 85-95% kích thước
- **Chất lượng**: Vẫn giữ được thông tin chính

## 🔄 Rollback nếu cần

Nếu có vấn đề, rollback về version cũ:
```bash
python deploy_optimization.py rollback
```

## ⚠️ Lưu ý quan trọng

1. **Backup tự động**: Script đã tự động backup các file gốc
2. **Environment variables**: Đã được set trong file `.env`
3. **Restart required**: Cần restart ứng dụng để áp dụng thay đổi
4. **Test ngay**: Chạy test để đảm bảo hoạt động đúng

## 🎯 Troubleshooting

### Nếu vẫn bị overflow:
```python
# Giảm limits thêm nữa
MAX_CONTENT_PER_URL=10000
GEMINI_CONTEXT_LIMIT=150000
```

### Nếu chất lượng content thấp:
```python
# Tăng limits một chút
MAX_CONTENT_PER_URL=20000
```

### Nếu có lỗi import:
```bash
# Kiểm tra các file tối ưu có tồn tại không
ls -la utils/content_size_optimizer.py
ls -la gemini_processor_optimized.py
```

## 🎉 Hoàn thành!

Sau khi deploy thành công, bạn có thể:
- ✅ Crawl dantri.vn và vnexpress.net cùng lúc
- ✅ Không lo bị overflow context
- ✅ Vẫn giữ được chất lượng nội dung
- ✅ Xử lý nhanh hơn và tiết kiệm API cost