#!/usr/bin/env python3
"""
Deploy <PERSON><PERSON> as standalone MCPO server on port 8002
"""

import os
import subprocess
import time
import json

# --- Configuration ---
CONFIG_DIR = "mcpo_config_jina"
CONFIG_FILE = os.path.join(CONFIG_DIR, "config.json")
CONTAINER_NAME = "jina-crawler-mcpo-8003"
IMAGE_NAME = "ghcr.io/open-webui/mcpo:main"
PORT = 8003
NETWORK_NAME = "acca-network"
SERVER_DIR = "../.."  # Go up to mcp-integration/servers from jina_crawler directory
DOCKER_SERVER_MOUNT = "/app/servers"

# --- Only jina_crawler ---
JINA_CRAWLER_SERVER = "jina_crawler/server.py"

def run_command(command, check=True):
    """Runs a command in the shell and returns its output."""
    print(f"Executing: {' '.join(command)}")
    result = subprocess.run(command, capture_output=True, text=True)
    if check and result.returncode != 0:
        print(f"Error: {result.stderr}")
        raise RuntimeError(f"Command failed: {' '.join(command)}")
    return result

def stop_and_remove_container():
    """Stops and removes the existing container if it's running."""
    print(f"--- Stopping and removing old container: {CONTAINER_NAME} ---")
    run_command(["docker", "stop", CONTAINER_NAME], check=False)
    run_command(["docker", "rm", CONTAINER_NAME], check=False)
    print("--- Old container removed ---")

def create_config_file():
    """Creates the configuration file for jina_crawler only."""
    print(f"--- Creating config file for jina_crawler ---")
    os.makedirs(CONFIG_DIR, exist_ok=True)

    # Environment Variables for jina_crawler
    env_vars = {
        "GEMINI_API_KEY": os.environ.get("GEMINI_API_KEY", "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"),
        "GEMINI_MODEL_NAME": os.environ.get("GEMINI_MODEL_NAME", "gemini-2.5-flash-lite"),
        "PYTHONPATH": "/app/servers",
        "PYTHONUNBUFFERED": "1"
    }

    # Use the actual server.py file directly
    docker_path = "/app/servers/servers/jina_crawler/server.py"

    mcp_servers = {
        "jina_crawler": {
            "command": "python3.12",
            "args": [docker_path],
            "env": env_vars
        }
    }

    config_content = {"mcpServers": mcp_servers}

    with open(CONFIG_FILE, "w") as f:
        json.dump(config_content, f, indent=2)

    print(f"--- Config file created for jina_crawler ---")
    return ["jina_crawler"]

def create_jina_requirements():
    """Creates requirements file for jina_crawler."""
    print("--- Creating jina_crawler requirements file ---")
    
    req_file = os.path.join(SERVER_DIR, "jina_crawler_requirements.txt")
    
    if os.path.exists(req_file):
        print(f"Found requirements for jina_crawler: {req_file}")
    else:
        print("Warning: jina_crawler_requirements.txt not found")
    
    print(f"--- Requirements file ready ---")

def pull_latest_image():
    """Pulls the latest mcpo image from ghcr.io."""
    print(f"--- Pulling latest image: {IMAGE_NAME} ---")
    run_command(["docker", "pull", IMAGE_NAME])
    print("--- Image pulled successfully ---")
    
def start_jina_mcpo_container():
    """Starts the mcpo container with jina_crawler config."""
    print("--- Starting jina_crawler MCPO container ---")
    
    config_abs_path = os.path.abspath(CONFIG_FILE)
    servers_abs_path = os.path.abspath(SERVER_DIR)

    volumes = [
        "-v", f"{config_abs_path}:/app/config.json",
        "-v", f"{servers_abs_path}:{DOCKER_SERVER_MOUNT}"
    ]

    docker_command = [
        "docker", "run", "-d",
        "--name", CONTAINER_NAME,
        "-p", f"{PORT}:{PORT}",
        "--network", NETWORK_NAME,
        *volumes,
        IMAGE_NAME,
        "--config", "/app/config.json",
        "--port", str(PORT),
        "--host", "0.0.0.0",
    ]
    
    run_command(docker_command)
    print("--- Jina Crawler MCPO container started ---")
    
    # Wait a bit for container to be ready
    print("--- Waiting for container to be ready ---")
    time.sleep(5)
    
    # Install core MCP dependencies first
    print("--- Installing core MCP dependencies ---")
    core_deps = [
        "docker", "exec", CONTAINER_NAME,
        "pip", "install", "mcp", "aiohttp", "httpx", "pytz", "beautifulsoup4", "requests", "asyncio"
    ]
    run_command(core_deps)
    
    # Install jina_crawler specific dependencies
    print("--- Installing jina_crawler dependencies ---")
    jina_deps = [
        "docker", "exec", CONTAINER_NAME,
        "pip", "install",
        "aiohttp>=3.9.0", "asyncio-throttle>=1.0.2", "beautifulsoup4>=4.12.0",
        "tenacity>=8.2.0", "fake-useragent>=1.4.0", "tls-client>=0.2.0",
        "ddgs>=4.0.0", "python-dotenv>=1.0.0", "google-generativeai>=0.3.0",
        "pandas>=2.0.0", "numpy>=1.24.0", "fastapi>=0.104.0", "uvicorn>=0.24.0",
        "pydantic>=2.5.0", "structlog>=23.2.0", "PyPDF2>=3.0.0",
        "python-docx>=1.1.0", "Pillow>=10.0.0", "requests>=2.31.0",
        "lxml>=4.9.0", "html5lib>=1.1"
    ]
    run_command(jina_deps, check=False)
    
    print("--- Dependencies installed ---")

    # Restart container to reload with new dependencies
    print("--- Restarting container to apply dependencies ---")
    run_command(["docker", "restart", CONTAINER_NAME])
    print("--- Container restarted ---")

def wait_for_server():
    """Waits for jina_crawler server to become healthy."""
    print("--- Waiting for jina_crawler server to be ready ---")
    time.sleep(10)
    
    for i in range(30):
        print(f"Attempt {i+1}/30: Checking jina_crawler server status...")
        
        url = f"http://localhost:{PORT}/jina_crawler/docs"
        try:
            result = subprocess.run(["curl", "-s", "-f", "-L", url], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"   - jina_crawler is ready!")
                print("--- Jina Crawler MCPO server is ready! ---")
                return True
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
            pass
        
        time.sleep(3)

    print("\nError: Jina Crawler server did not become ready in time.")
    run_command(["docker", "logs", CONTAINER_NAME])
    raise SystemExit("Deployment failed.")

if __name__ == "__main__":
    stop_and_remove_container()
    tool_names = create_config_file()
    create_jina_requirements()
    pull_latest_image()
    start_jina_mcpo_container()
    wait_for_server()
    print(f"\n🚀🚀🚀 Jina Crawler MCPO server deployed successfully on port {PORT}! 🚀🚀🚀")
    print(f"Access jina_crawler tools at: http://localhost:{PORT}/jina_crawler/docs")
