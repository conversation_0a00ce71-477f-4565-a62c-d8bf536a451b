#!/usr/bin/env python3
"""
HTTP Wrapper for Jini Crawler MCP Server - Open WebUI Compatible (Fixed Version)
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

# Import MCP server components
from server import JinaCrawlerMCPServer

# Pydantic models for request validation
class CrawlUrlRequest(BaseModel):
    url: str = Field(..., description="URL to crawl")
    max_content_length: int = Field(10000, description="Maximum content length")

class AISearchRequest(BaseModel):
    query: str = Field(..., description="Search query")
    enable_query_refinement: bool = Field(True, description="Enable AI query refinement")
    search_type: str = Field("text", description="Type of search")
    max_sources: int = Field(10, description="Maximum number of sources")

class CrawlFullArticleRequest(BaseModel):
    url: str = Field(..., description="URL of article")
    max_content_length: int = Field(50000, description="Maximum content length")

class SearchGoogleRequest(BaseModel):
    query: str = Field(..., description="Google search query")
    max_results: int = Field(10, description="Maximum number of results")

class SmartCrawlRequest(BaseModel):
    url: str = Field(..., description="URL to crawl with smart processing")
    extract_type: str = Field("auto", description="Type of extraction")

class GetTrendingTopicsRequest(BaseModel):
    category: str = Field("general", description="Topic category")
    max_topics: int = Field(10, description="Maximum number of topics")

class HealthCheckRequest(BaseModel):
    pass  # No parameters required

class GetCrawlerStatsRequest(BaseModel):
    pass  # No parameters required

class AISearchStreamingRequest(BaseModel):
    query: str = Field(..., description="Search query for streaming")
    enable_query_refinement: bool = Field(True, description="Enable AI query refinement")

# Response model
class MCPToolResponse(BaseModel):
    success: bool
    data: Any = None
    error: Optional[str] = None

# Setup logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("jini-http-wrapper")

app = FastAPI(
    title="jina_crawler",
    description="jina_crawler MCP Server - Advanced web crawler with AI processing",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global MCP server instance
mcp_server: Optional[JinaCrawlerMCPServer] = None

@app.on_event("startup")
async def startup_event():
    """Initialize MCP server on startup"""
    global mcp_server
    mcp_server = JinaCrawlerMCPServer()
    await mcp_server.initialize()
    logger.info("✅ Jini Crawler MCP Server initialized")
    logger.info("🚀 Jini Crawler HTTP server started")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    global mcp_server
    if mcp_server:
        await mcp_server.cleanup()
    logger.info("✅ Jini Crawler HTTP server stopped")

# Utility functions for MCP tool execution
async def execute_tool(tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
    """Execute MCP tool"""
    try:
        result = await mcp_server.call_tool(tool_name, arguments)
        return {
            "success": True,
            "data": result,
            "error": None
        }
    except Exception as e:
        logger.error(f"Tool execution error: {e}")
        return {
            "success": False,
            "data": None,
            "error": str(e)
        }

@app.get("/")
async def root():
    """Root endpoint with server info - Open WebUI compatible"""
    return {
        "name": "jina_crawler",
        "description": "jina_crawler MCP Server - Advanced web crawler with AI processing",
        "version": "1.0.0", 
        "status": "healthy",
        "mcp_compatible": True,
        "tools_count": 9,
        "endpoints": {
            "openapi": "/openapi.json",
            "health": "/health",
            "docs": "/docs"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "server": "jini-crawler"}

# Main tool endpoints with proper Pydantic models
@app.post("/crawl_url", response_model=MCPToolResponse)
async def crawl_url_endpoint(request: CrawlUrlRequest):
    """Smart web crawler with AI summarization"""
    result = await execute_tool("crawl_url", request.dict())
    return MCPToolResponse(**result)

@app.post("/ai_search", response_model=MCPToolResponse)
async def ai_search_endpoint(request: AISearchRequest):
    """AI-powered comprehensive search engine"""
    result = await execute_tool("ai_search", request.dict())
    return MCPToolResponse(**result)

@app.post("/crawl_full_article", response_model=MCPToolResponse)
async def crawl_full_article_endpoint(request: CrawlFullArticleRequest):
    """Extract complete article content"""
    result = await execute_tool("crawl_url_full_article", request.dict())
    return MCPToolResponse(**result)

@app.post("/search_google", response_model=MCPToolResponse)
async def search_google_endpoint(request: SearchGoogleRequest):
    """Google search with processing"""
    result = await execute_tool("search_google", request.dict())
    return MCPToolResponse(**result)

@app.post("/smart_crawl", response_model=MCPToolResponse)
async def smart_crawl_endpoint(request: SmartCrawlRequest):
    """Smart content extraction"""
    result = await execute_tool("smart_crawl", request.dict())
    return MCPToolResponse(**result)

@app.post("/get_trending_topics", response_model=MCPToolResponse)
async def get_trending_topics_endpoint(request: GetTrendingTopicsRequest):
    """Get trending topics"""
    result = await execute_tool("get_trending_topics", request.dict())
    return MCPToolResponse(**result)

@app.post("/health_check", response_model=MCPToolResponse)
async def health_check_endpoint(request: HealthCheckRequest):
    """Health monitoring"""
    result = await execute_tool("health_check", {})
    return MCPToolResponse(**result)

@app.post("/get_crawler_stats", response_model=MCPToolResponse)
async def get_crawler_stats_endpoint(request: GetCrawlerStatsRequest):
    """Crawler statistics"""
    result = await execute_tool("get_crawler_stats", {})
    return MCPToolResponse(**result)

@app.post("/ai_search_streaming", response_model=MCPToolResponse)
async def ai_search_streaming_endpoint(request: AISearchStreamingRequest):
    """AI search with real-time updates"""
    result = await execute_tool("ai_search_streaming", request.dict())
    return MCPToolResponse(**result)

if __name__ == "__main__":
    print("🚀 Starting Jini Crawler MCP HTTP Interface (Fixed Version)")
    print("=" * 60)
    print("🛡️ Features:")
    print("- OpenAPI 3.1 compatible with Open WebUI")
    print("- 9 powerful crawling and AI search tools")
    print("- Proper Pydantic request/response models")
    print("- Auto-generated documentation")
    print("=" * 60)
    print("📡 Server will start on: http://0.0.0.0:8009")
    print("📋 API Documentation: http://0.0.0.0:8009/docs")
    print("🔗 OpenAPI Schema: http://0.0.0.0:8009/openapi.json")
    print("=" * 60)
    
    uvicorn.run(app, host="0.0.0.0", port=8009)