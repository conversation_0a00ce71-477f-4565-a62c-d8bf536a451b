# 📰 Full Article Crawling Feature

## Tổng quan

Tính năng **Full Article Crawling** đã đư<PERSON><PERSON> bổ sung vào <PERSON> Crawler MCP Server, cho phép cào toàn bộ nội dung bài viết thay vì chỉ bản tóm tắt.

## 🆚 So sánh các công cụ

| <PERSON><PERSON>ng cụ | Mô tả | Nội dung trả về | Độ dài tối đa |
|---------|-------|-----------------|---------------|
| `crawl_url` | Cào và xử lý tóm tắt | Nội dung được tóm tắt/xử lý | 10,000 ký tự |
| `crawl_full_article` | **MỚI** - Cào toàn bộ bài viết | Nội dung HOÀN CHỈNH của bài viết | 50,000 ký tự |
| `crawl_batch` | Cào nhiều URL song song | Nội dung tóm tắt cho nhiều URL | 10,000 ký tự/URL |

## 🚀 Cách sử dụng

### 1. Qua MCP Server

```json
{
  "method": "tools/call",
  "params": {
    "name": "crawl_full_article",
    "arguments": {
      "url": "https://example.com/article",
      "max_content_length": 50000
    }
  }
}
```

### 2. Qua Python API

```python
import asyncio
from jini_crawler import JiniCrawler

async def crawl_full_article_example():
    crawler = JiniCrawler()
    await crawler.initialize()
    
    # Cào toàn bộ bài viết
    result = await crawler.crawl_full_article(
        "https://example.com/article",
        max_content_length=50000
    )
    
    if result.success:
        print(f"Tiêu đề: {result.title}")
        print(f"Nội dung đầy đủ: {result.processed_content}")
        print(f"Độ dài: {len(result.processed_content)} ký tự")
    
    await crawler.cleanup()

asyncio.run(crawl_full_article_example())
```

## 🔧 Tham số

### `crawl_full_article`

- **url** (bắt buộc): URL của bài viết cần cào
- **max_content_length** (tùy chọn): Độ dài tối đa của nội dung gửi đến Gemini
  - Mặc định: 50,000 ký tự
  - Tối thiểu: 10,000 ký tự  
  - Tối đa: 100,000 ký tự

## 📊 Kết quả trả về

```json
{
  "success": true,
  "url": "https://example.com/article",
  "title": "Tiêu đề bài viết",
  "full_article_content": "Nội dung HOÀN CHỈNH của bài viết...",
  "processing_time": 1.23,
  "error": null,
  "metadata": {
    "original_length": 45000,
    "output_length": 12000,
    "model": "gemini-2.5-flash-lite",
    "task_type": "full_article",
    "content_type": "complete_article"
  },
  "timestamp": "2025-01-05T02:31:44.153Z",
  "crawler_type": "jina_full_article",
  "content_type": "complete_article"
}
```

## 🎯 Ưu điểm

1. **Nội dung đầy đủ**: Trích xuất toàn bộ bài viết, không bỏ sót thông tin
2. **Tối ưu cho tiếng Việt**: Hỗ trợ đặc biệt cho nội dung tiếng Việt
3. **Hiệu suất cao**: Sử dụng Gemini 2.5 Flash Lite cho xử lý nhanh
4. **Linh hoạt**: Có thể điều chỉnh độ dài nội dung tối đa

## 🔍 Ví dụ thực tế

### Test với bài viết Medium

```bash
cd mcp-integration/servers/jina_crawler
python3 test_full_article.py
```

### So sánh kết quả

| Phương pháp | Độ dài nội dung | Thời gian xử lý | Mô tả |
|-------------|-----------------|-----------------|-------|
| `crawl_url` | ~500-2000 ký tự | 0.5-1s | Tóm tắt, nội dung chính |
| `crawl_full_article` | ~2000-10000+ ký tự | 0.8-2s | Toàn bộ bài viết |

## 🛠️ Cài đặt và chạy

### 1. Khởi động MCP Server

```bash
cd mcp-integration/servers/jina_crawler
python3 server.py
```

### 2. Sử dụng qua Open WebUI

Server sẽ chạy trên port 8009 và có thể tích hợp với Open WebUI thông qua MCP protocol.

### 3. Kiểm tra health

```bash
# Test trực tiếp
python3 test_full_article.py

# Hoặc qua MCP
{
  "method": "tools/call",
  "params": {
    "name": "health_check",
    "arguments": {}
  }
}
```

## 🔧 Cấu hình Gemini

Đảm bảo có biến môi trường:

```bash
export GEMINI_API_KEY="your-api-key"
export GEMINI_MODEL_NAME="gemini-2.5-flash-lite"
```

## 📝 Lưu ý

1. **Paywall**: Một số trang có paywall sẽ chỉ trả về nội dung giới hạn
2. **Rate limiting**: Gemini API có giới hạn tần suất, server sẽ tự động retry
3. **Nội dung lớn**: Với bài viết rất dài, có thể cần tăng `max_content_length`
4. **Tiếng Việt**: Được tối ưu đặc biệt cho nội dung tiếng Việt

## 🎉 Kết luận

Tính năng **Full Article Crawling** giúp bạn:
- ✅ Lấy được toàn bộ nội dung bài viết
- ✅ Không bị giới hạn bởi tóm tắt
- ✅ Tối ưu cho nội dung tiếng Việt
- ✅ Tích hợp dễ dàng với hệ thống hiện có

**Sử dụng `crawl_full_article` khi bạn cần nội dung đầy đủ, `crawl_url` khi chỉ cần tóm tắt!**