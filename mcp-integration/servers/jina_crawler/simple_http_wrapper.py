#!/usr/bin/env python3
"""
Simple HTTP Wrapper for Jina Crawler - No Authentication Required
"""

import asyncio
import json
from typing import Dict, Any
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from jini_crawler import JiniCrawler

app = FastAPI(
    title="Jina Crawler Tools",
    description="Simple HTTP wrapper for Jina Crawler - No Auth Required",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global crawler instance
crawler = None

@app.on_event("startup")
async def startup_event():
    """Initialize crawler on startup"""
    global crawler
    crawler = JiniCrawler()
    await crawler.initialize()
    print("🚀 Simple Jina Crawler HTTP server started")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    global crawler
    if crawler:
        await crawler.cleanup()
    print("✅ Simple Jina Crawler HTTP server stopped")

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Simple Jina Crawler HTTP Server", "tools": 5, "auth": "none"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "server": "simple-jina-crawler"}

@app.get("/jina_crawler/openapi.json")
async def get_openapi():
    """Return OpenAPI spec for Open WebUI integration - NO AUTH REQUIRED"""
    return {
        "openapi": "3.0.0",
        "info": {
            "title": "Jina Crawler Tools",
            "description": "Simple Jina Crawler without authentication",
            "version": "1.0.0"
        },
        "paths": {
            "/tools/crawl_url": {
                "post": {
                    "summary": "Crawl and process a single URL",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "url": {"type": "string", "description": "URL to crawl"},
                            "max_content_length": {"type": "number", "default": 10000}
                        },
                        "required": ["url"]
                    }
                }
            },
            "/tools/crawl_batch": {
                "post": {
                    "summary": "Crawl multiple URLs in parallel",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "urls": {"type": "array", "items": {"type": "string"}},
                            "max_content_length": {"type": "number", "default": 10000}
                        },
                        "required": ["urls"]
                    }
                }
            },
            "/tools/health_check": {
                "post": {
                    "summary": "Check crawler health",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            },
            "/tools/get_stats": {
                "post": {
                    "summary": "Get crawler statistics",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            },
            "/tools/test_crawl": {
                "post": {
                    "summary": "Test crawl with sample URL",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            }
        },
        "specs": [
            {"type": "function", "name": "tool_crawl_url_post", "description": "Crawl and process a single URL"},
            {"type": "function", "name": "tool_crawl_batch_post", "description": "Crawl multiple URLs in parallel"},
            {"type": "function", "name": "tool_health_check_post", "description": "Check crawler health"},
            {"type": "function", "name": "tool_get_stats_post", "description": "Get crawler statistics"},
            {"type": "function", "name": "tool_test_crawl_post", "description": "Test crawl with sample URL"}
        ]
    }

@app.post("/tools/crawl_url")
async def crawl_url(payload: Dict[str, Any]):
    """Crawl a single URL"""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        url = payload.get("url")
        max_content_length = payload.get("max_content_length", 10000)
        
        if not url:
            raise HTTPException(status_code=400, detail="URL is required")
        
        result = await crawler.crawl_and_process(url, max_content_length)
        
        response = {
            "success": result.success,
            "url": result.url,
            "title": result.title,
            "processed_content": result.processed_content,
            "processing_time": result.processing_time,
            "error": result.error,
            "metadata": result.metadata,
            "crawler_type": "simple_jina_crawler"
        }
        
        return JSONResponse(content=response)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Crawl failed: {str(e)}")

@app.post("/tools/crawl_batch")
async def crawl_batch(payload: Dict[str, Any]):
    """Crawl multiple URLs"""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        urls = payload.get("urls", [])
        max_content_length = payload.get("max_content_length", 10000)
        
        if not urls:
            raise HTTPException(status_code=400, detail="URLs list is required")
        
        # Process URLs in parallel
        tasks = []
        for url in urls:
            task = crawler.crawl_and_process(url, max_content_length)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Format results
        formatted_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                formatted_results.append({
                    "success": False,
                    "url": urls[i],
                    "error": str(result)
                })
            else:
                formatted_results.append({
                    "success": result.success,
                    "url": result.url,
                    "title": result.title,
                    "processed_content": result.processed_content,
                    "processing_time": result.processing_time,
                    "error": result.error,
                    "metadata": result.metadata
                })
        
        response = {
            "batch_size": len(urls),
            "successful_crawls": sum(1 for r in formatted_results if r["success"]),
            "failed_crawls": sum(1 for r in formatted_results if not r["success"]),
            "results": formatted_results,
            "crawler_type": "simple_jina_batch"
        }
        
        return JSONResponse(content=response)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Batch crawl failed: {str(e)}")

@app.post("/tools/health_check")
async def health_check_tool():
    """Health check tool"""
    global crawler
    
    if not crawler:
        return JSONResponse(content={"status": "error", "message": "Crawler not initialized"})
    
    try:
        health = await crawler.health_check()
        return JSONResponse(content={
            "status": "healthy",
            "crawler_health": health,
            "server_type": "simple_jina_crawler"
        })
    except Exception as e:
        return JSONResponse(content={"status": "error", "error": str(e)})

@app.post("/tools/get_stats")
async def get_stats():
    """Get crawler statistics"""
    return JSONResponse(content={
        "server_type": "simple_jina_crawler",
        "features": [
            "No authentication required",
            "Async crawling with aiohttp",
            "BeautifulSoup HTML cleaning",
            "Gemini AI content processing",
            "Vietnamese content optimization",
            "Batch processing support"
        ],
        "tools_available": 5,
        "auth_required": False
    })

@app.post("/tools/test_crawl")
async def test_crawl():
    """Test crawl with a sample URL"""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        # Test with a simple URL
        test_url = "https://httpbin.org/html"
        result = await crawler.crawl_and_process(test_url, 5000)
        
        return JSONResponse(content={
            "test_successful": result.success,
            "test_url": test_url,
            "title": result.title,
            "content_length": len(result.processed_content or ""),
            "processing_time": result.processing_time,
            "error": result.error,
            "message": "Test crawl completed successfully" if result.success else "Test crawl failed"
        })
        
    except Exception as e:
        return JSONResponse(content={
            "test_successful": False,
            "error": str(e),
            "message": "Test crawl failed with exception"
        })

if __name__ == "__main__":
    import os
    port = int(os.environ.get("PORT", 8001))
    uvicorn.run(
        "simple_http_wrapper:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info"
    )