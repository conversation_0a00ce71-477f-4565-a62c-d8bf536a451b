# 🔧 Open WebUI Tool Refresh Guide

## ❌ Vấn đề: LLM không sử dụng 2 tools mới

Open WebUI có thể **cache** danh sách tools và không tự động phát hiện tools mới. Đ<PERSON>y là các cách để **force refresh**:

## 🚀 Giải pháp nhanh (Recommended)

### 1. **Restart Open WebUI Container**
```bash
# Nếu dùng Docker
docker restart openwebui

# Hoặc stop và start lại
docker stop openwebui
docker start openwebui
```

### 2. **Clear Browser Cache**
- Ctrl+Shift+R (hard refresh)
- Hoặc F12 → Application → Storage → Clear storage

### 3. **Re-add Function trong Open WebUI**
1. Vào **Admin Panel** → **Functions**
2. **Delete** function cũ (nếu có)
3. **Add** lại với URL: `http://localhost:8009/openapi.json`
4. **Save** và **Enable**

## 🔍 Kiểm tra Tools có sẵn

### Verify OpenAPI Schema:
```bash
curl -s http://localhost:8009/openapi.json | jq '.paths | keys'
```

**Kết quả mong đợi (11 endpoints):**
```json
[
  "/",
  "/crawl_batch",
  "/crawl_bypass_paywall",    ← 🔓 NEW
  "/crawl_full_article",      ← 📰 NEW  
  "/crawl_url",
  "/crawl_with_fallback",
  "/extract_news",
  "/get_stats",
  "/health",
  "/health_check",
  "/test_crawl"
]
```

### Test 2 Tools mới:
```bash
# Test Full Article
curl -X POST http://localhost:8009/crawl_full_article \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com/article"}'

# Test Paywall Bypass  
curl -X POST http://localhost:8009/crawl_bypass_paywall \
  -H "Content-Type: application/json" \
  -d '{"url": "https://medium.com/@example/article"}'
```

## 🎯 Debugging Steps

### 1. **Check Open WebUI Logs**
```bash
docker logs openwebui -f
```

### 2. **Verify Function Registration**
- Open WebUI → **Admin** → **Functions**
- Tìm function với URL `localhost:8009`
- Check **Status**: Should be **Active**
- Check **Tools**: Should show **9 tools**

### 3. **Manual Function Update**
```python
# Trong Open WebUI Python environment
import requests
response = requests.get("http://localhost:8009/openapi.json")
print(f"Tools found: {len(response.json()['paths'])}")
```

## 🔥 Advanced Solutions

### 1. **Force Schema Refresh Script**
```python
# refresh_openwebui_tools.py (đã có sẵn)
python3 refresh_openwebui_tools.py
```

### 2. **Manual Tool Registration**
Nếu auto-discovery không work, có thể manually register:

```python
# Trong Open WebUI admin
function_url = "http://localhost:8009/openapi.json"
# Import và enable manually
```

### 3. **Check Network Connectivity**
```bash
# Từ Open WebUI container
curl http://localhost:8009/health
curl http://localhost:8009/get_stats
```

## 📋 Troubleshooting Checklist

- [ ] Server running on port 8009? ✅
- [ ] OpenAPI schema có 11 endpoints? ✅  
- [ ] 2 tools mới có description rõ ràng? ✅
- [ ] Open WebUI container restart? ❓
- [ ] Browser cache cleared? ❓
- [ ] Function re-added trong Open WebUI? ❓
- [ ] Tools hiển thị trong UI? ❓
- [ ] LLM có thể call tools? ❓

## 🎯 Expected Behavior

Sau khi refresh thành công:

### **Tools Available:**
1. `crawl_url` - Standard crawling
2. `crawl_batch` - Batch processing  
3. `crawl_with_fallback` - Multiple methods
4. `extract_news` - News extraction
5. `health_check` - Health check
6. `get_stats` - Server stats
7. `test_crawl` - Test functionality
8. **`crawl_full_article`** - 📰 **Full content extraction**
9. **`crawl_bypass_paywall`** - 🔓 **Paywall bypass**

### **LLM Should Use New Tools When:**
- User asks for "full article" or "complete content"
- User mentions "paywall" or "blocked content"  
- User wants "unfiltered" or "raw content"
- User asks to "bypass restrictions"

## 💡 Pro Tips

1. **Restart Open WebUI** là cách nhanh nhất
2. **Clear browser cache** nếu UI không update
3. **Check logs** để debug connection issues
4. **Test tools manually** trước khi expect LLM sử dụng
5. **Verify descriptions** - LLM chọn tools dựa trên description

---

**🔥 Nếu vẫn không work, có thể là Open WebUI version cũ hoặc cần config đặc biệt!**