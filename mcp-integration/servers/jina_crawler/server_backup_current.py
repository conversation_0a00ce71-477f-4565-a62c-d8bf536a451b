#!/usr/bin/env python3
"""
Jina Crawler MCP Server
Provides web crawling and content processing using Jina-style crawler + Gemini AI
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)
from mcp.server.lowlevel.server import NotificationOptions

# Import our crawler components
from jini_crawler import JiniCrawler
from paywall_bypass_crawler import PaywallBypassCrawler

# Import AI Search Engine
from ai_search.ai_search_engine import AISearchEngine

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("jini-crawler-mcp-server")

# Initialize the MCP server
server = Server("jina_crawler")

# Global crawler instance
crawler = JiniCrawler()

class JinaCrawlerMCPServer:
    def __init__(self):
        self.crawler = JiniCrawler()
        self.paywall_crawler = PaywallBypassCrawler()
        self.ai_search_engine = AISearchEngine()
        self.initialized = False
    
    async def initialize(self):
        """Initialize the crawler"""
        if not self.initialized:
            await self.crawler.initialize()
            await self.paywall_crawler.initialize()
            await self.ai_search_engine.initialize()
            self.initialized = True
            logger.info("✅ Jini Crawler MCP Server initialized")
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.initialized:
            await self.crawler.cleanup()
            await self.paywall_crawler.cleanup()
            await self.ai_search_engine.cleanup()
            self.initialized = False
            logger.info("✅ Jina Crawler MCP Server cleanup completed")

# Global server instance
jina_server = JinaCrawlerMCPServer()

@server.list_resources()
async def list_resources() -> List[Resource]:
    """List available Jina Crawler resources"""
    return [
        Resource(
            uri="jina://crawler",
            name="Jina Web Crawler",
            description="Advanced web crawler with AI processing",
            mimeType="application/json",
        ),
        Resource(
            uri="jina://health",
            name="Health Status",
            description="Crawler health and performance metrics",
            mimeType="application/json",
        )
    ]

@server.read_resource()
async def read_resource(uri: str) -> str:
    """Read Jina Crawler resource"""
    if uri == "jina://crawler":
        return json.dumps({
            "description": "Use crawl_url, crawl_full_article, or crawl_batch tools to crawl websites",
            "features": [
                "Jina-style web crawling",
                "BeautifulSoup HTML cleaning",
                "Gemini AI content processing",
                "Full article extraction (complete content)",
                "Summarized content extraction",
                "Vietnamese content optimization",
                "Performance monitoring"
            ],
            "tools": {
                "crawl_url": "Extract summarized/processed content",
                "crawl_full_article": "Extract COMPLETE article content (not summarized)",
                "crawl_bypass_paywall": "🔓 BYPASS PAYWALLS - Extract content from behind paywalls",
                "crawl_batch": "Process multiple URLs in parallel"
            },
            "example": "crawl_full_article('https://dantri.com.vn/article-url')"
        }, indent=2)
    
    elif uri == "jina://health":
        try:
            await jina_server.initialize()
            health = await jina_server.crawler.health_check()
            return json.dumps(health, indent=2)
        except Exception as e:
            return json.dumps({"error": str(e)}, indent=2)
    
    raise ValueError(f"Unknown resource: {uri}")

@server.list_tools()
async def list_tools() -> List[Tool]:
    """List available Jina Crawler tools"""
    return [
        Tool(
            name="crawl_url",
            description="📄 SMART SUMMARIZER: Use for regular web crawling with AI-powered content processing and summarization. Returns clean, processed content optimized for reading. Best for general web content, news articles, and when you need intelligent content extraction. Use this as the DEFAULT choice for most crawling tasks.",
            inputSchema={
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "URL to crawl and process with AI summarization"
                    },
                    "max_content_length": {
                        "type": "integer",
                        "description": "Maximum content length to process (default: 10000)",
                        "default": 10000,
                        "minimum": 1000,
                        "maximum": 50000
                    }
                },
                "required": ["url"]
            }
        ),
        Tool(
            name="crawl_full_article",
            description="📰 COMPLETE ARTICLE EXTRACTOR: Use when you need the ENTIRE article text without any summarization or processing. Returns raw, complete content from public articles. Perfect for when user asks for 'full article', 'complete content', or 'entire text'. Does NOT bypass paywalls - use crawl_bypass_paywall for protected content.",
            inputSchema={
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "URL of public article to extract complete content from"
                    },
                    "max_content_length": {
                        "type": "integer",
                        "description": "Maximum content length to process (default: 50000 for full articles)",
                        "default": 50000,
                        "minimum": 10000,
                        "maximum": 100000
                    }
                },
                "required": ["url"]
            }
        ),
        Tool(
            name="crawl_batch",
            description="Crawl and process multiple URLs in parallel",
            inputSchema={
                "type": "object",
                "properties": {
                    "urls": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of URLs to crawl"
                    },
                    "max_content_length": {
                        "type": "integer",
                        "description": "Maximum content length per URL (default: 10000)",
                        "default": 10000,
                        "minimum": 1000,
                        "maximum": 50000
                    }
                },
                "required": ["urls"]
            }
        ),
        Tool(
            name="search_site",
            description="Search within a specific website using Jina crawler",
            inputSchema={
                "type": "object",
                "properties": {
                    "site_url": {
                        "type": "string",
                        "description": "Base URL of the site to search"
                    },
                    "query": {
                        "type": "string",
                        "description": "Search query"
                    },
                    "max_results": {
                        "type": "integer",
                        "description": "Maximum number of results (default: 10)",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 50
                    }
                },
                "required": ["site_url", "query"]
            }
        ),
        Tool(
            name="health_check",
            description="Check the health status of the crawler system",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),
        Tool(
            name="get_crawler_stats",
            description="Get performance statistics and metrics",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),
        Tool(
            name="crawl_bypass_paywall",
            description="🔓 PAYWALL BYPASS SPECIALIST: Use ONLY for Medium, NYTimes, WSJ, Bloomberg, and other subscription/paywall-protected articles. Uses 8 specialized bypass techniques (Google Cache, Archive.org, 12ft.io, etc.). DO NOT use for regular public websites - use crawl_url instead.",
            inputSchema={
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "URL of paywall-protected article (Medium, NYTimes, etc.)"
                    },
                    "max_content_length": {
                        "type": "integer",
                        "description": "Maximum content length to process (default: 50000)",
                        "default": 50000,
                        "minimum": 10000,
                        "maximum": 100000
                    }
                },
                "required": ["url"]
            }
        ),
        Tool(
            name="ai_search",
            description="🤖 AI SEARCH ENGINE: Advanced Perplexity-style AI search that refines your query, searches the web, crawls multiple sources, and synthesizes comprehensive 1500-3000 word answers with extensive citations. Leverages Gemini 2.5 Flash Lite's full 1M input + 65K output capacity for in-depth analysis. Perfect for research, academic questions, and complex topics requiring comprehensive analysis.",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Your search question or topic"
                    },
                    "enable_query_refinement": {
                        "type": "boolean",
                        "description": "Whether to refine the query using AI (default: true)",
                        "default": True
                    },
                    "search_type": {
                        "type": "string",
                        "description": "Type of search: 'text' for general web search, 'news' for news articles",
                        "enum": ["text", "news"],
                        "default": "text"
                    },
                    "max_sources": {
                        "type": "integer",
                        "description": "Maximum number of sources to analyze (default: 10, increased for comprehensive analysis)",
                        "default": 10,
                        "minimum": 5,
                        "maximum": 15
                    }
                },
                "required": ["query"]
            }
        ),
        Tool(
            name="ai_search_streaming",
            description="🚀 AI SEARCH STREAMING: Same as ai_search but provides real-time updates on search progress. Use when you want to show the user what's happening during the search process.",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Your search question or topic"
                    },
                    "enable_query_refinement": {
                        "type": "boolean",
                        "description": "Whether to refine the query using AI (default: true)",
                        "default": True
                    }
                },
                "required": ["query"]
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle tool calls"""
    
    try:
        # Initialize crawler if needed
        await jina_server.initialize()
        
        if name == "crawl_url":
            url = arguments["url"]
            max_content_length = arguments.get("max_content_length", 10000)
            
            logger.info(f"🕷️ Crawling URL: {url}")
            
            result = await jina_server.crawler.crawl_and_process(url, max_content_length)
            
            response = {
                "success": result.success,
                "url": result.url,
                "title": result.title,
                "processed_content": result.processed_content,
                "processing_time": result.processing_time,
                "error": result.error,
                "metadata": result.metadata,
                "timestamp": datetime.now().isoformat(),
                "crawler_type": "jina_style_with_gemini"
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2, ensure_ascii=False))]
        
        elif name == "crawl_full_article":
            url = arguments["url"]
            max_content_length = arguments.get("max_content_length", 50000)
            
            logger.info(f"📰 Crawling FULL article from: {url}")
            
            result = await jina_server.crawler.crawl_full_article(url, max_content_length)
            
            response = {
                "success": result.success,
                "url": result.url,
                "title": result.title,
                "full_article_content": result.processed_content,
                "processing_time": result.processing_time,
                "error": result.error,
                "metadata": result.metadata,
                "timestamp": datetime.now().isoformat(),
                "crawler_type": "jina_full_article",
                "content_type": "complete_article"
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2, ensure_ascii=False))]
        
        elif name == "crawl_batch":
            urls = arguments["urls"]
            max_content_length = arguments.get("max_content_length", 10000)
            
            logger.info(f"🚀 TRUE BATCH PROCESSING: {len(urls)} URLs with single Gemini call")
            
            # Step 1: Crawl all URLs to get raw content (no Gemini processing yet)
            batch_data = await jina_server.crawler.crawl_batch_raw(urls, max_content_length)
            
            # Step 2: Process all raw content with single Gemini 2.5 Flash batch call
            results = await jina_server.crawler.process_batch_with_gemini(batch_data, "batch crawl")
            
            logger.info(f"✅ OPTIMIZED BATCH: {len(results)} URLs processed with 1 Gemini 2.5 Flash call!")
            
            # Format results
            formatted_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    formatted_results.append({
                        "success": False,
                        "url": urls[i],
                        "error": str(result),
                        "timestamp": datetime.now().isoformat()
                    })
                else:
                    formatted_results.append({
                        "success": result.success,
                        "url": result.url,
                        "title": result.title,
                        "processed_content": result.processed_content,
                        "processing_time": result.processing_time,
                        "error": result.error,
                        "metadata": result.metadata,
                        "timestamp": datetime.now().isoformat()
                    })
            
            response = {
                "batch_size": len(urls),
                "successful_crawls": sum(1 for r in formatted_results if r["success"]),
                "failed_crawls": sum(1 for r in formatted_results if not r["success"]),
                "results": formatted_results,
                "crawler_type": "jina_style_batch"
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2, ensure_ascii=False))]
        
        elif name == "search_site":
            site_url = arguments["site_url"]
            query = arguments["query"]
            max_results = arguments.get("max_results", 10)
            
            logger.info(f"🔍 Searching site {site_url} for: {query}")
            
            # Import search function
            from jina_style_crawler import jina_search_site
            
            search_result = await jina_search_site(
                site_url=site_url,
                query=query,
                max_results=max_results
            )
            
            response = {
                "site_url": site_url,
                "query": query,
                "success": search_result.success,
                "total_found": search_result.total_found,
                "results": search_result.results,
                "error": search_result.error,
                "timestamp": datetime.now().isoformat(),
                "search_type": "site_search"
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2, ensure_ascii=False))]
        
        elif name == "health_check":
            logger.info("🏥 Performing health check")
            
            health = await jina_server.crawler.health_check()
            
            response = {
                "timestamp": datetime.now().isoformat(),
                "health_check": health,
                "server_status": "operational" if health.get("status") == "healthy" else "degraded"
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        elif name == "get_crawler_stats":
            logger.info("📊 Getting crawler statistics")
            
            # Get basic stats (you can extend this)
            stats = {
                "server_initialized": jina_server.initialized,
                "timestamp": datetime.now().isoformat(),
                "crawler_type": "jina_style_with_gemini",
                "features": [
                    "aiohttp-based crawling",
                    "BeautifulSoup HTML cleaning",
                    "Gemini 2.5 Flash Lite processing",
                    "Vietnamese content optimization",
                    "Async parallel processing"
                ]
            }
            
            return [TextContent(type="text", text=json.dumps(stats, indent=2))]
        
        elif name == "crawl_bypass_paywall":
            url = arguments["url"]
            max_content_length = arguments.get("max_content_length", 50000)
            
            logger.info(f"🔓 Attempting paywall bypass for: {url}")
            
            result = await jina_server.paywall_crawler.crawl_with_paywall_bypass(url, max_content_length)
            
            response = {
                "success": result.success,
                "url": result.url,
                "title": result.title,
                "full_article_content": result.processed_content,
                "processing_time": result.processing_time,
                "error": result.error,
                "metadata": result.metadata,
                "timestamp": datetime.now().isoformat(),
                "crawler_type": "paywall_bypass",
                "bypass_method": result.metadata.get("bypass_method", "unknown") if result.metadata else "unknown",
                "content_type": "paywall_bypassed_article"
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2, ensure_ascii=False))]

        elif name == "ai_search":
            query = arguments["query"]
            enable_query_refinement = arguments.get("enable_query_refinement", True)
            search_type = arguments.get("search_type", "text")
            max_sources = arguments.get("max_sources", 5)

            logger.info(f"🤖 AI Search for: {query}")

            # Update AI search engine max results if needed
            jina_server.ai_search_engine.max_search_results = max_sources

            try:
                result = await jina_server.ai_search_engine.search(
                    query=query,
                    enable_query_refinement=enable_query_refinement,
                    search_type=search_type
                )
            except Exception as e:
                logger.error(f"❌ AI Search failed: {e}")
                # Ensure cleanup even on error
                try:
                    await jina_server.ai_search_engine.cleanup()
                except:
                    pass
                raise

            if result.success and result.synthesized_answer:
                response = {
                    "success": True,
                    "query": result.query,
                    "refined_query": result.refined_query.refined_query if result.refined_query else None,
                    "answer": result.synthesized_answer.answer,
                    "citations": [
                        {
                            "url": citation.url,
                            "title": citation.title,
                            "snippet": citation.snippet,
                            "relevance_score": citation.relevance_score
                        }
                        for citation in result.synthesized_answer.citations
                    ],
                    "confidence": result.synthesized_answer.confidence,
                    "sources_used": result.synthesized_answer.sources_used,
                    "word_count": result.synthesized_answer.word_count,
                    "total_time": result.total_time,
                    "metadata": result.metadata,
                    "timestamp": datetime.now().isoformat(),
                    "search_type": "ai_search_perplexity_style"
                }
            else:
                response = {
                    "success": False,
                    "query": result.query,
                    "error": result.error,
                    "total_time": result.total_time,
                    "metadata": result.metadata,
                    "timestamp": datetime.now().isoformat(),
                    "search_type": "ai_search_perplexity_style"
                }

            return [TextContent(type="text", text=json.dumps(response, indent=2, ensure_ascii=False))]

        elif name == "ai_search_streaming":
            query = arguments["query"]
            enable_query_refinement = arguments.get("enable_query_refinement", True)

            logger.info(f"🚀 AI Search Streaming for: {query}")

            # For streaming, we'll collect updates and return them all at once
            # In a real streaming implementation, you'd use server-sent events or websockets
            updates = []

            async def collect_updates(update):
                updates.append(update)
                logger.info(f"Stream update: {update}")

            await jina_server.ai_search_engine.search_streaming(
                query=query,
                callback=collect_updates,
                enable_query_refinement=enable_query_refinement
            )

            response = {
                "success": True,
                "query": query,
                "streaming_updates": updates,
                "timestamp": datetime.now().isoformat(),
                "search_type": "ai_search_streaming"
            }

            return [TextContent(type="text", text=json.dumps(response, indent=2, ensure_ascii=False))]

        else:
            return [TextContent(type="text", text=f"Unknown tool: {name}")]

    except Exception as e:
        logger.error(f"Error in tool {name}: {str(e)}")
        error_response = {
            "error": str(e),
            "tool": name,
            "timestamp": datetime.now().isoformat()
        }
        return [TextContent(type="text", text=json.dumps(error_response, indent=2))]

    finally:
        # Force cleanup after each tool call to prevent resource leaks
        try:
            import gc
            # import asyncio  # Already imported globally

            # Small delay to allow operations to complete
            await asyncio.sleep(0.1)

            # Force garbage collection
            for _ in range(3):
                gc.collect()
                await asyncio.sleep(0.05)

        except Exception as cleanup_error:
            logger.warning(f"⚠️ Tool cleanup warning: {cleanup_error}")

async def health_check_server():
    """Simple HTTP health check server"""
    from aiohttp import web

    async def health(request):
        return web.json_response({"status": "healthy", "service": "jini-crawler"})

    app = web.Application()
    app.router.add_get('/health', health)

    runner = web.AppRunner(app)
    await runner.setup()
    site = web.TCPSite(runner, '0.0.0.0', 8009)
    await site.start()
    logger.info("🌐 Health check server started on http://0.0.0.0:8009/health")

async def main():
    """Main server function"""
    try:
        # Start health check server in background
        health_task = asyncio.create_task(health_check_server())

        # Run MCP server
        async with stdio_server() as (read_stream, write_stream):
            await server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="jini_crawler",
                    server_version="1.0.0",
                    capabilities=server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )
    finally:
        await jina_server.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
