# Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Smart Model Manager Configuration
GEMINI_PRIMARY_MODEL=gemini-2.5-flash-lite
GEMINI_FALLBACK_MODEL=gemini-2.0-flash
GEMINI_TOKEN_THRESHOLD=200000
GEMINI_SMART_SELECTION=true

# Legacy Gemini Configuration (for backward compatibility)
GEMINI_MODEL_NAME=gemini-2.5-flash-lite
GEMINI_API_URL=https://generativelanguage.googleapis.com/v1beta/models
GEMINI_TIMEOUT=30
GEMINI_MAX_RETRIES=3

# Rate Limit Management
# Primary model (gemini-2.5-flash-lite): 250,000 input tokens/minute
# Fallback model (gemini-2.0-flash): 1,000,000 input tokens/minute
# Token threshold: Switch to fallback if content > threshold tokens
