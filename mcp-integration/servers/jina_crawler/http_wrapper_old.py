#!/usr/bin/env python3
"""
HTTP Wrapper for Jina Crawler MCP Server
"""

import asyncio
import json
from typing import Dict, Any
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import J<PERSON>NResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from server_wrapper import JinaCrawlerServer

app = FastAPI(
    title="Jina Crawler",
    description="HTTP wrapper for Jina Crawler MCP Server",
    version="1.0.0",
    openapi_url=None  # Disable auto-generated OpenAPI
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Global MCP server instance
mcp_server = None

@app.on_event("startup")
async def startup_event():
    """Initialize MCP server on startup"""
    global mcp_server
    mcp_server = JinaCrawlerServer()
    print("🚀 Jina Crawler HTTP server started")

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Jina Crawler HTTP Server", "tools": 6}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "server": "jina-crawler"}

@app.get("/openapi.json")
async def get_openapi_standard():
    """Return OpenAPI spec for Open WebUI integration (standard endpoint)"""
    return await get_openapi_spec()

@app.get("/jina_crawler/openapi.json")
async def get_openapi():
    """Return OpenAPI spec for Open WebUI integration (custom endpoint)"""
    return await get_openapi_spec()

async def get_openapi_spec():
    """Return OpenAPI spec for Open WebUI integration"""
    return {
        "openapi": "3.0.0",
        "info": {
            "title": "Jina Crawler",
            "description": "Advanced web crawling and content processing with AI search capabilities",
            "version": "1.0.0"
        },
        "paths": {
            "/tools/crawl_url": {
                "post": {
                    "summary": "Crawl a URL using jina-crawler with optional ReaderLM processing",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "url": {"type": "string", "description": "URL to crawl"},
                            "method": {"type": "string", "enum": ["tls_bypass", "hybrid", "playwright"], "default": "tls_bypass"},
                            "process_content": {"type": "boolean", "default": True},
                            "task_type": {"type": "string", "enum": ["html_to_markdown"], "default": "html_to_markdown"},
                            "timeout": {"type": "number", "default": 30}
                        },
                        "required": ["url"]
                    }
                }
            },
            "/tools/health_check": {
                "post": {
                    "summary": "Check jina-crawler health and status",
                    "parameters": {
                        "type": "object",
                        "properties": {}
                    }
                }
            },
            "/tools/list_methods": {
                "post": {
                    "summary": "List available crawling methods and their capabilities",
                    "parameters": {
                        "type": "object",
                        "properties": {}
                    }
                }
            },
            "/tools/process_content": {
                "post": {
                    "summary": "Process HTML content using ReaderLM or Qwen3",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "content": {"type": "string", "description": "HTML content to process"},
                            "model": {"type": "string", "enum": ["readerlm", "qwen3"], "default": "readerlm"},
                            "max_length": {"type": "number", "default": 2000},
                            "temperature": {"type": "number", "default": 0.1}
                        },
                        "required": ["content"]
                    }
                }
            },
            "/tools/convert_html_to_markdown": {
                "post": {
                    "summary": "Convert HTML to Markdown using ReaderLM or Qwen3",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "html_content": {"type": "string", "description": "HTML content to convert to Markdown"},
                            "model": {"type": "string", "enum": ["readerlm", "qwen3"], "default": "readerlm"},
                            "max_length": {"type": "number", "default": 2000}
                        },
                        "required": ["html_content"]
                    }
                }
            },
            "/tools/ai_search": {
                "post": {
                    "summary": "Perform AI-powered search with content synthesis",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "Search query"},
                            "max_results": {"type": "number", "default": 10},
                            "search_engine": {"type": "string", "enum": ["duckduckgo", "brave"], "default": "duckduckgo"},
                            "synthesize": {"type": "boolean", "default": True}
                        },
                        "required": ["query"]
                    }
                }
            }
        },
        "specs": [
            {"type": "function", "name": "tool_crawl_url_post", "description": "Crawl a URL using jina-crawler with optional ReaderLM processing"},
            {"type": "function", "name": "tool_health_check_post", "description": "Check jina-crawler health and status"},
            {"type": "function", "name": "tool_list_methods_post", "description": "List available crawling methods and their capabilities"},
            {"type": "function", "name": "tool_process_content_post", "description": "Process HTML content using ReaderLM or Qwen3"},
            {"type": "function", "name": "tool_convert_html_to_markdown_post", "description": "Convert HTML to Markdown using ReaderLM or Qwen3"},
            {"type": "function", "name": "tool_ai_search_post", "description": "Perform AI-powered search with content synthesis"}
        ]
    }

@app.post("/tools/{tool_name}")
async def execute_tool(tool_name: str, payload: Dict[str, Any]):
    """Execute a specific tool"""
    global mcp_server
    
    if not mcp_server:
        raise HTTPException(status_code=500, detail="MCP server not initialized")
    
    try:
        # Route to appropriate tool handler
        if tool_name == "crawl_url":
            result = await mcp_server._crawl_url(payload)
        elif tool_name == "health_check":
            result = await mcp_server._health_check(payload)
        elif tool_name == "list_methods":
            result = await mcp_server._list_methods(payload)
        elif tool_name == "process_content":
            result = await mcp_server._process_content(payload)
        elif tool_name == "convert_html_to_markdown":
            result = await mcp_server._convert_html_to_markdown(payload)
        elif tool_name == "ai_search":
            result = await mcp_server._ai_search(payload)
        else:
            raise HTTPException(status_code=404, detail=f"Tool '{tool_name}' not found")
        
        return JSONResponse(content=result)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")

@app.post("/tools/call")
async def call_tool(payload: Dict[str, Any]):
    """Call a tool with name and arguments"""
    global mcp_server
    
    if not mcp_server:
        raise HTTPException(status_code=500, detail="MCP server not initialized")
    
    tool_name = payload.get("name")
    arguments = payload.get("arguments", {})
    
    if not tool_name:
        raise HTTPException(status_code=400, detail="Tool name is required")
    
    try:
        # Route to appropriate tool handler
        if tool_name == "crawl_url":
            result = await mcp_server._crawl_url(arguments)
        elif tool_name == "health_check":
            result = await mcp_server._health_check(arguments)
        elif tool_name == "list_methods":
            result = await mcp_server._list_methods(arguments)
        elif tool_name == "process_content":
            result = await mcp_server._process_content(arguments)
        elif tool_name == "convert_html_to_markdown":
            result = await mcp_server._convert_html_to_markdown(arguments)
        elif tool_name == "ai_search":
            result = await mcp_server._ai_search(arguments)
        else:
            raise HTTPException(status_code=404, detail=f"Tool '{tool_name}' not found")
        
        return JSONResponse(content={"result": result})
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")

@app.get("/tools/list")
async def list_tools():
    """List available tools"""
    return JSONResponse(content={
        "tools": [
            {"name": "crawl_url", "description": "Crawl a URL using jina-crawler with optional ReaderLM processing"},
            {"name": "health_check", "description": "Check jina-crawler health and status"},
            {"name": "list_methods", "description": "List available crawling methods and their capabilities"},
            {"name": "process_content", "description": "Process HTML content using ReaderLM or Qwen3"},
            {"name": "convert_html_to_markdown", "description": "Convert HTML to Markdown using ReaderLM or Qwen3"},
            {"name": "ai_search", "description": "Perform AI-powered search with content synthesis"}
        ]
    })

if __name__ == "__main__":
    import os
    port = int(os.environ.get("PORT", 8009))
    uvicorn.run(
        "http_wrapper:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info"
    )