#!/usr/bin/env python3
"""
🔧 VNEXPRESS SPECIALIST
Specialized handler for VnExpress.net with multiple bypass strategies
"""

import asyncio
import aiohttp
import logging
import time
import random
import re
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin, urlparse
import json

logger = logging.getLogger(__name__)

class VnExpressSpecialist:
    """Specialized handler for VnExpress.net"""
    
    def __init__(self):
        self.base_urls = {
            'main': 'https://vnexpress.net',
            'mobile': 'https://m.vnexpress.net',
            'rss': 'https://vnexpress.net/rss',
            'api': 'https://gw.vnexpress.net'
        }
        
        # Vietnamese user agents
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15',
            'VnExpress/5.0 (iPhone; iOS 17.2; Scale/3.00)',
            'VnExpress/4.8 (Android 14; Mobile)'
        ]
        
        # Vietnamese IP ranges
        self.vn_ip_ranges = [
            '**********/16',    # VNPT
            '*********/16',     # FPT
            '***********/16',   # Viettel
            '***********/16',   # CMC
            '***********/16'    # VDC
        ]
    
    def get_vietnamese_ip(self) -> str:
        """Generate Vietnamese IP address"""
        # Use VNPT range (most common)
        return f"14.161.{random.randint(1,255)}.{random.randint(1,255)}"
    
    def get_vietnamese_headers(self, url: str = "") -> Dict[str, str]:
        """Get Vietnamese-optimized headers"""
        headers = {
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'X-Forwarded-For': self.get_vietnamese_ip(),
            'CF-IPCountry': 'VN',
            'CF-RAY': f"{random.randint(100000000000000000, 999999999999999999)}-SIN",
            'CF-Visitor': '{"scheme":"https"}',
            'Referer': 'https://www.google.com.vn/'
        }
        
        # Add specific headers for mobile
        if 'iPhone' in headers['User-Agent'] or 'Mobile' in headers['User-Agent']:
            headers.update({
                'Sec-Ch-Ua-Mobile': '?1',
                'Sec-Ch-Ua-Platform': '"iOS"' if 'iPhone' in headers['User-Agent'] else '"Android"'
            })
        else:
            headers.update({
                'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="121", "Google Chrome";v="121"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"'
            })
        
        return headers
    
    async def fetch_with_method(self, session: aiohttp.ClientSession, url: str, method: str) -> Optional[str]:
        """Fetch using specific method"""
        try:
            if method == 'direct':
                return await self._fetch_direct(session, url)
            elif method == 'mobile':
                return await self._fetch_mobile(session, url)
            elif method == 'rss':
                return await self._fetch_rss(session, url)
            elif method == 'api':
                return await self._fetch_api(session, url)
            elif method == 'category_redirect':
                return await self._fetch_category_redirect(session, url)
            else:
                return await self._fetch_direct(session, url)
        except Exception as e:
            logger.warning(f"VnExpress method {method} failed: {e}")
            return None
    
    async def _fetch_direct(self, session: aiohttp.ClientSession, url: str) -> Optional[str]:
        """Direct fetch with Vietnamese headers"""
        headers = self.get_vietnamese_headers(url)
        
        # Add delay
        await asyncio.sleep(random.uniform(2, 5))
        
        async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=20)) as response:
            if response.status == 200:
                content = await response.text()
                logger.info(f"✅ VnExpress direct fetch: {len(content)} chars")
                return content
            else:
                logger.warning(f"⚠️ VnExpress direct HTTP {response.status}")
                return None
    
    async def _fetch_mobile(self, session: aiohttp.ClientSession, url: str) -> Optional[str]:
        """Fetch mobile version"""
        mobile_url = url.replace('vnexpress.net', 'm.vnexpress.net')
        headers = self.get_vietnamese_headers(mobile_url)
        headers['User-Agent'] = 'VnExpress/5.0 (iPhone; iOS 17.2; Scale/3.00)'
        
        await asyncio.sleep(random.uniform(1, 3))
        
        async with session.get(mobile_url, headers=headers, timeout=aiohttp.ClientTimeout(total=15)) as response:
            if response.status == 200:
                content = await response.text()
                logger.info(f"✅ VnExpress mobile fetch: {len(content)} chars")
                return content
            else:
                logger.warning(f"⚠️ VnExpress mobile HTTP {response.status}")
                return None
    
    async def _fetch_rss(self, session: aiohttp.ClientSession, url: str) -> Optional[str]:
        """Fetch RSS feed"""
        try:
            # Convert to RSS URL
            if '/cong-nghe' in url:
                rss_url = 'https://vnexpress.net/rss/cong-nghe.rss'
            elif '/kinh-doanh' in url:
                rss_url = 'https://vnexpress.net/rss/kinh-doanh.rss'
            else:
                rss_url = 'https://vnexpress.net/rss/tin-moi-nhat.rss'
            
            headers = self.get_vietnamese_headers(rss_url)
            headers['Accept'] = 'application/rss+xml, application/xml, text/xml'
            
            await asyncio.sleep(random.uniform(1, 2))
            
            async with session.get(rss_url, headers=headers, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    content = await response.text()
                    logger.info(f"✅ VnExpress RSS fetch: {len(content)} chars")
                    return content
                else:
                    logger.warning(f"⚠️ VnExpress RSS HTTP {response.status}")
                    return None
        except Exception as e:
            logger.warning(f"⚠️ VnExpress RSS error: {e}")
            return None
    
    async def _fetch_api(self, session: aiohttp.ClientSession, url: str) -> Optional[str]:
        """Try VnExpress API endpoints"""
        try:
            # Try to extract category from URL
            if '/cong-nghe' in url:
                api_url = 'https://gw.vnexpress.net/ar/get_rule_2?category_id=1002592&limit=20'
            else:
                api_url = 'https://gw.vnexpress.net/ar/get_rule_2?limit=20'
            
            headers = self.get_vietnamese_headers(api_url)
            headers.update({
                'Accept': 'application/json, text/plain, */*',
                'X-Requested-With': 'XMLHttpRequest',
                'Origin': 'https://vnexpress.net',
                'Referer': 'https://vnexpress.net/'
            })
            
            await asyncio.sleep(random.uniform(1, 2))
            
            async with session.get(api_url, headers=headers, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    content = await response.text()
                    logger.info(f"✅ VnExpress API fetch: {len(content)} chars")
                    return content
                else:
                    logger.warning(f"⚠️ VnExpress API HTTP {response.status}")
                    return None
        except Exception as e:
            logger.warning(f"⚠️ VnExpress API error: {e}")
            return None
    
    async def _fetch_category_redirect(self, session: aiohttp.ClientSession, url: str) -> Optional[str]:
        """Try category-specific URLs"""
        try:
            # Try different category URLs
            category_urls = [
                'https://vnexpress.net/cong-nghe',
                'https://vnexpress.net/so-hoa',
                'https://vnexpress.net/khoa-hoc'
            ]
            
            for cat_url in category_urls:
                headers = self.get_vietnamese_headers(cat_url)
                
                await asyncio.sleep(random.uniform(1, 2))
                
                async with session.get(cat_url, headers=headers, timeout=aiohttp.ClientTimeout(total=15)) as response:
                    if response.status == 200:
                        content = await response.text()
                        if len(content) > 5000:  # Good content
                            logger.info(f"✅ VnExpress category redirect: {len(content)} chars from {cat_url}")
                            return content
            
            return None
        except Exception as e:
            logger.warning(f"⚠️ VnExpress category redirect error: {e}")
            return None
    
    async def smart_fetch(self, url: str) -> Optional[str]:
        """Smart fetch with multiple fallback methods"""
        methods = ['direct', 'mobile', 'category_redirect', 'rss', 'api']
        
        logger.info(f"🎯 VnExpress specialist: trying {len(methods)} methods for {url}")
        
        # Create session with Vietnamese settings
        connector = aiohttp.TCPConnector(
            limit=5,
            limit_per_host=3,
            ttl_dns_cache=300,
            use_dns_cache=True,
            ssl=False
        )
        
        timeout = aiohttp.ClientTimeout(total=25, connect=10)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            for i, method in enumerate(methods, 1):
                try:
                    logger.info(f"🔄 VnExpress method {i}/{len(methods)}: {method}")
                    
                    content = await self.fetch_with_method(session, url, method)
                    if content and len(content) > 1000:
                        logger.info(f"✅ VnExpress success with method '{method}': {len(content)} chars")
                        return content
                    
                except Exception as e:
                    logger.warning(f"⚠️ VnExpress method '{method}' error: {e}")
                
                # Progressive delay
                if i < len(methods):
                    await asyncio.sleep(random.uniform(2, 4))
        
        logger.error(f"❌ All VnExpress methods failed for {url}")
        return None
    
    def extract_content(self, html: str) -> Dict[str, Any]:
        """Extract content from VnExpress HTML"""
        try:
            # Extract title
            title_patterns = [
                r'<h1[^>]*class="[^"]*title[^"]*"[^>]*>(.*?)</h1>',
                r'<title[^>]*>(.*?)</title>',
                r'<h1[^>]*>(.*?)</h1>'
            ]
            
            title = ""
            for pattern in title_patterns:
                title_match = re.search(pattern, html, re.DOTALL | re.IGNORECASE)
                if title_match:
                    title = re.sub(r'<[^>]+>', '', title_match.group(1)).strip()
                    if title and 'VnExpress' not in title:
                        break
            
            # Extract content
            content_patterns = [
                r'<div[^>]*class="[^"]*fck_detail[^"]*"[^>]*>(.*?)</div>',
                r'<div[^>]*class="[^"]*Normal[^"]*"[^>]*>(.*?)</div>',
                r'<article[^>]*>(.*?)</article>',
                r'<div[^>]*class="[^"]*content[^"]*"[^>]*>(.*?)</div>',
                r'<p[^>]*class="[^"]*description[^"]*"[^>]*>(.*?)</p>'
            ]
            
            content = ""
            for pattern in content_patterns:
                matches = re.findall(pattern, html, re.DOTALL | re.IGNORECASE)
                if matches:
                    content = ' '.join(matches)
                    break
            
            # If no content found, try to extract from RSS
            if not content and '<rss' in html.lower():
                content = self._extract_rss_content(html)
            
            # If still no content, try JSON API response
            if not content and html.strip().startswith('{'):
                content = self._extract_api_content(html)
            
            # Clean content
            content = re.sub(r'<[^>]+>', '', content)
            content = re.sub(r'\s+', ' ', content).strip()
            
            return {
                'title': title,
                'content': content,
                'source': 'vnexpress.net',
                'extracted': True
            }
        except Exception as e:
            logger.error(f"Error extracting VnExpress content: {e}")
            return {'title': '', 'content': '', 'source': 'vnexpress.net', 'extracted': False}
    
    def _extract_rss_content(self, rss_html: str) -> str:
        """Extract content from RSS feed"""
        try:
            # Extract RSS items
            items = re.findall(r'<item[^>]*>(.*?)</item>', rss_html, re.DOTALL | re.IGNORECASE)
            content_parts = []
            
            for item in items[:5]:  # Top 5 items
                title_match = re.search(r'<title[^>]*>(.*?)</title>', item, re.DOTALL | re.IGNORECASE)
                desc_match = re.search(r'<description[^>]*>(.*?)</description>', item, re.DOTALL | re.IGNORECASE)
                
                if title_match:
                    title = re.sub(r'<[^>]+>', '', title_match.group(1)).strip()
                    content_parts.append(f"**{title}**")
                
                if desc_match:
                    desc = re.sub(r'<[^>]+>', '', desc_match.group(1)).strip()
                    content_parts.append(desc)
                
                content_parts.append("")  # Separator
            
            return '\n'.join(content_parts)
        except Exception as e:
            logger.warning(f"Error extracting RSS content: {e}")
            return ""
    
    def _extract_api_content(self, json_str: str) -> str:
        """Extract content from API JSON response"""
        try:
            data = json.loads(json_str)
            content_parts = []
            
            if 'data' in data and isinstance(data['data'], list):
                for item in data['data'][:5]:  # Top 5 items
                    if 'title' in item:
                        content_parts.append(f"**{item['title']}**")
                    if 'lead' in item:
                        content_parts.append(item['lead'])
                    if 'description' in item:
                        content_parts.append(item['description'])
                    content_parts.append("")  # Separator
            
            return '\n'.join(content_parts)
        except Exception as e:
            logger.warning(f"Error extracting API content: {e}")
            return ""

# Test function
async def test_vnexpress_specialist():
    """Test VnExpress specialist"""
    specialist = VnExpressSpecialist()
    
    test_urls = [
        'https://vnexpress.net/cong-nghe',
        'https://vnexpress.net/so-hoa',
        'https://vnexpress.net/'
    ]
    
    for url in test_urls:
        print(f"\n🧪 Testing VnExpress specialist: {url}")
        content = await specialist.smart_fetch(url)
        if content:
            extracted = specialist.extract_content(content)
            print(f"✅ Success: {len(extracted['content'])} chars")
            print(f"Title: {extracted['title'][:100]}...")
            print(f"Content preview: {extracted['content'][:200]}...")
        else:
            print(f"❌ Failed: {url}")

if __name__ == "__main__":
    asyncio.run(test_vnexpress_specialist())
