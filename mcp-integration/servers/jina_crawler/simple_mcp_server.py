#!/usr/bin/env python3
"""
Simple MCP Server for testing MCPO integration
"""

import asyncio
import json
import sys
from typing import Any, Dict, List
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Tool,
    TextContent,
)

class SimpleMCPServer:
    """Simple MCP Server for testing"""
    
    def __init__(self):
        self.server = Server("simple-test-server")
        self._register_tools()
    
    def _register_tools(self):
        """Register MCP tools"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List all available tools"""
            return [
                Tool(
                    name="test_tool",
                    description="Simple test tool",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "message": {"type": "string", "description": "Test message"}
                        },
                        "required": ["message"]
                    }
                ),
                Tool(
                    name="health_check",
                    description="Health check tool",
                    inputSchema={
                        "type": "object",
                        "properties": {}
                    }
                )
            ]
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool execution"""
            
            try:
                if name == "test_tool":
                    message = arguments.get("message", "Hello from MCP!")
                    response = {
                        "success": True,
                        "message": f"Received: {message}",
                        "server": "simple-mcp-server"
                    }
                    
                elif name == "health_check":
                    response = {
                        "success": True,
                        "status": "healthy",
                        "server": "simple-mcp-server"
                    }
                    
                else:
                    response = {
                        "success": False,
                        "error": f"Unknown tool: {name}"
                    }
                
                return [TextContent(type="text", text=json.dumps(response, indent=2))]
                
            except Exception as e:
                error_response = {
                    "success": False,
                    "error": str(e),
                    "tool": name
                }
                return [TextContent(type="text", text=json.dumps(error_response, indent=2))]

async def main():
    """Main entry point for MCP server"""
    mcp_server = SimpleMCPServer()
    
    # Run the MCP server
    async with stdio_server() as (read_stream, write_stream):
        await mcp_server.server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="simple-test-server",
                server_version="1.0.0",
                capabilities=mcp_server.server.get_capabilities()
            )
        )

if __name__ == "__main__":
    asyncio.run(main())