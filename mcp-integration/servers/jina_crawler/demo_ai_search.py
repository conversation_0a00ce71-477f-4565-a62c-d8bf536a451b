#!/usr/bin/env python3
"""
Demo AI Search Engine - Clean and Simple
"""

import asyncio
import json
import sys
import os
import warnings

# Suppress warnings for demo
warnings.filterwarnings("ignore", category=ResourceWarning)

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

async def demo_ai_search():
    """Demo the AI search engine with a real query"""
    
    print("🤖 AI Search Engine Demo")
    print("=" * 60)
    print("Perplexity-style AI search with query refinement, web search, crawling, and synthesis")
    print()
    
    try:
        from server import call_tool
        
        # Demo query
        query = "What are the benefits of renewable energy?"
        
        print(f"🔍 Query: {query}")
        print("🚀 Processing...")
        print()
        
        # Call AI search
        arguments = {
            "query": query,
            "enable_query_refinement": True,
            "search_type": "text",
            "max_sources": 4
        }
        
        result = await call_tool("ai_search", arguments)
        
        if result and len(result) > 0:
            response_data = json.loads(result[0].text)
            
            if response_data.get("success"):
                # Display results
                print("📊 RESULTS")
                print("-" * 40)
                print(f"✅ Status: SUCCESS")
                print(f"⏱️ Processing time: {response_data.get('total_time', 0):.1f} seconds")
                print(f"📝 Content length: {response_data.get('word_count', 0)} words")
                print(f"📚 Sources analyzed: {response_data.get('sources_used', 0)}")
                print(f"🎯 Confidence: {response_data.get('confidence', 0):.2f}")
                
                # Query refinement
                if response_data.get("refined_query"):
                    print(f"\n🔧 QUERY REFINEMENT")
                    print("-" * 40)
                    print(f"Original: {response_data.get('query')}")
                    print(f"Refined:  {response_data.get('refined_query')}")
                
                # Answer
                answer = response_data.get("answer", "")
                if answer:
                    print(f"\n🤖 AI-SYNTHESIZED ANSWER")
                    print("-" * 40)
                    # Show first 800 characters
                    preview = answer[:800]
                    print(preview)
                    if len(answer) > 800:
                        print(f"\n... [Answer continues for {len(answer) - 800} more characters]")
                
                # Citations
                citations = response_data.get("citations", [])
                if citations:
                    print(f"\n📚 SOURCES & CITATIONS")
                    print("-" * 40)
                    for i, citation in enumerate(citations[:4], 1):
                        title = citation.get('title', 'No title')[:60]
                        url = citation.get('url', 'No URL')
                        relevance = citation.get('relevance_score', 0)
                        
                        print(f"[{i}] {title}...")
                        print(f"    🔗 {url}")
                        print(f"    📊 Relevance: {relevance:.2f}")
                        print()
                
                # Performance metrics
                metadata = response_data.get("metadata", {})
                if metadata:
                    print(f"📈 PERFORMANCE METRICS")
                    print("-" * 40)
                    print(f"Search results found: {metadata.get('search_results_count', 0)}")
                    print(f"URLs crawled: {metadata.get('crawled_urls_count', 0)}")
                    print(f"Successful crawls: {metadata.get('successful_crawls_count', 0)}")
                    print(f"Query was refined: {metadata.get('query_was_refined', False)}")
                
                print(f"\n🎉 Demo completed successfully!")
                return True
                
            else:
                print(f"❌ AI Search failed: {response_data.get('error', 'Unknown error')}")
                return False
        else:
            print("❌ No response from AI search engine")
            return False
            
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def demo_streaming():
    """Demo streaming AI search"""
    
    print(f"\n🚀 STREAMING AI SEARCH DEMO")
    print("=" * 60)
    
    try:
        from server import call_tool
        
        arguments = {
            "query": "climate change solutions",
            "enable_query_refinement": True
        }
        
        print(f"🔍 Query: {arguments['query']}")
        print("📡 Streaming updates...")
        print()
        
        result = await call_tool("ai_search_streaming", arguments)
        
        if result and len(result) > 0:
            response_data = json.loads(result[0].text)
            
            if response_data.get("success"):
                updates = response_data.get("streaming_updates", [])
                
                print("📊 STREAMING UPDATES")
                print("-" * 40)
                
                for i, update in enumerate(updates, 1):
                    step = update.get("step", "unknown")
                    message = update.get("message", "")
                    
                    # Format step names
                    step_icons = {
                        "started": "🚀",
                        "refining_query": "📝",
                        "query_refined": "🔧", 
                        "searching": "🌐",
                        "search_complete": "✅",
                        "crawling": "🕷️",
                        "crawling_complete": "✅",
                        "synthesizing": "🤖",
                        "complete": "🎉",
                        "error": "❌"
                    }
                    
                    icon = step_icons.get(step, "📍")
                    print(f"{i:2d}. {icon} {message}")
                
                print(f"\n✅ Streaming demo completed!")
                return True
            else:
                print(f"❌ Streaming failed")
                return False
        else:
            print("❌ No streaming response")
            return False
            
    except Exception as e:
        print(f"❌ Streaming demo failed: {e}")
        return False

async def main():
    """Main demo function"""
    
    print("🎯 AI Search Engine - Perplexity Style")
    print("Built with jina_crawler + Gemini 2.5 Flash Lite + DuckDuckGo")
    print("=" * 60)
    print()
    
    try:
        # Demo 1: Regular AI search
        success1 = await demo_ai_search()
        
        # Small delay
        await asyncio.sleep(2)
        
        # Demo 2: Streaming search
        success2 = await demo_streaming()
        
        print(f"\n🏁 DEMO SUMMARY")
        print("=" * 60)
        print(f"Regular AI Search: {'✅ SUCCESS' if success1 else '❌ FAILED'}")
        print(f"Streaming Search: {'✅ SUCCESS' if success2 else '❌ FAILED'}")
        
        if success1 and success2:
            print(f"\n🎉 All demos successful! AI Search Engine is ready for production.")
            print(f"🔗 Available at: http://cloudflare-resistant-jina-crawler:8009")
            print(f"📋 Tools: ai_search, ai_search_streaming")
        else:
            print(f"\n⚠️ Some demos failed - check logs above")
        
        return success1 and success2
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        print(f"\nDemo completed with {'success' if success else 'issues'}")
    except KeyboardInterrupt:
        print("\n⚠️ Demo interrupted by user")
    except Exception as e:
        print(f"\n💥 Demo error: {e}")
