"""
Advanced Connection Pool Manager for Jina Crawler
Optimizes HTTP connections, session reuse, and resource management
"""

import asyncio
import aiohttp
import time
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from contextlib import asynccontextmanager
import weakref

logger = logging.getLogger(__name__)

@dataclass
class ConnectionPoolConfig:
    """Configuration for connection pool"""
    max_connections: int = 100
    max_connections_per_host: int = 30
    ttl_dns_cache: int = 300
    timeout_total: int = 30
    timeout_connect: int = 10
    timeout_sock_read: int = 10
    keepalive_timeout: int = 30
    enable_cleanup_closed: bool = True
    limit_per_host: int = 10

class OptimizedConnectionPool:
    """
    Advanced connection pool with intelligent session management
    """
    
    def __init__(self, config: Optional[ConnectionPoolConfig] = None):
        self.config = config or ConnectionPoolConfig()
        self._sessions: Dict[str, aiohttp.ClientSession] = {}
        self._session_usage: Dict[str, int] = {}
        self._session_created: Dict[str, float] = {}
        self._cleanup_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
        
        # Track sessions for cleanup
        self._session_refs = weakref.WeakSet()
        
    async def initialize(self):
        """Initialize the connection pool"""
        logger.info("🚀 Initializing optimized connection pool...")
        
        # Start cleanup task
        self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
        
        logger.info("✅ Connection pool initialized")
    
    @asynccontextmanager
    async def get_session(self, session_key: str = "default"):
        """
        Get or create an optimized session with connection pooling
        
        Args:
            session_key: Unique key for session (allows multiple specialized sessions)
        """
        async with self._lock:
            session = await self._get_or_create_session(session_key)
            self._session_usage[session_key] = self._session_usage.get(session_key, 0) + 1
        
        try:
            yield session
        finally:
            # Session cleanup is handled by periodic cleanup
            pass
    
    async def _get_or_create_session(self, session_key: str) -> aiohttp.ClientSession:
        """Get existing session or create new optimized one"""
        
        # Check if session exists and is still valid
        if session_key in self._sessions:
            session = self._sessions[session_key]
            if not session.closed:
                return session
            else:
                # Clean up closed session
                del self._sessions[session_key]
                if session_key in self._session_usage:
                    del self._session_usage[session_key]
                if session_key in self._session_created:
                    del self._session_created[session_key]
        
        # Create new optimized session
        session = await self._create_optimized_session(session_key)
        self._sessions[session_key] = session
        self._session_created[session_key] = time.time()
        self._session_refs.add(session)
        
        logger.debug(f"✅ Created new optimized session: {session_key}")
        return session
    
    async def _create_optimized_session(self, session_key: str) -> aiohttp.ClientSession:
        """Create highly optimized aiohttp session"""
        
        # Advanced connector configuration
        connector = aiohttp.TCPConnector(
            limit=self.config.max_connections,
            limit_per_host=self.config.max_connections_per_host,
            ttl_dns_cache=self.config.ttl_dns_cache,
            use_dns_cache=True,
            enable_cleanup_closed=self.config.enable_cleanup_closed,
            keepalive_timeout=self.config.keepalive_timeout,
            # Advanced optimizations
            force_close=False,  # Keep connections alive
            ssl=False,  # Disable SSL verification for speed (use with caution)
        )
        
        # Optimized timeout configuration
        timeout = aiohttp.ClientTimeout(
            total=self.config.timeout_total,
            connect=self.config.timeout_connect,
            sock_read=self.config.timeout_sock_read
        )
        
        # Optimized headers
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=headers,
            # Advanced session optimizations
            cookie_jar=aiohttp.CookieJar(unsafe=True),  # Faster cookie handling
            read_timeout=None,  # Let timeout handle this
            conn_timeout=self.config.timeout_connect,
        )
        
        return session
    
    async def _periodic_cleanup(self):
        """Periodic cleanup of unused sessions"""
        while True:
            try:
                await asyncio.sleep(60)  # Cleanup every minute
                await self._cleanup_unused_sessions()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"⚠️ Session cleanup error: {e}")
    
    async def _cleanup_unused_sessions(self):
        """Clean up unused or old sessions"""
        current_time = time.time()
        sessions_to_remove = []
        
        async with self._lock:
            for session_key, created_time in self._session_created.items():
                # Remove sessions older than 10 minutes with low usage
                usage_count = self._session_usage.get(session_key, 0)
                age = current_time - created_time
                
                if (age > 600 and usage_count < 5) or age > 1800:  # 10 min low usage or 30 min max
                    sessions_to_remove.append(session_key)
            
            # Clean up identified sessions
            for session_key in sessions_to_remove:
                if session_key in self._sessions:
                    session = self._sessions[session_key]
                    if not session.closed:
                        await session.close()
                    
                    del self._sessions[session_key]
                    if session_key in self._session_usage:
                        del self._session_usage[session_key]
                    if session_key in self._session_created:
                        del self._session_created[session_key]
                    
                    logger.debug(f"🧹 Cleaned up unused session: {session_key}")
    
    async def get_session_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics"""
        async with self._lock:
            active_sessions = len([s for s in self._sessions.values() if not s.closed])
            total_usage = sum(self._session_usage.values())
            
            return {
                "active_sessions": active_sessions,
                "total_sessions_created": len(self._session_created),
                "total_usage_count": total_usage,
                "average_usage_per_session": total_usage / max(len(self._session_usage), 1),
                "session_keys": list(self._sessions.keys())
            }
    
    async def cleanup(self):
        """Cleanup all resources"""
        logger.info("🧹 Starting connection pool cleanup...")
        
        # Cancel cleanup task
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Close all sessions
        async with self._lock:
            for session in self._sessions.values():
                if not session.closed:
                    await session.close()
            
            self._sessions.clear()
            self._session_usage.clear()
            self._session_created.clear()
        
        logger.info("✅ Connection pool cleanup completed")

# Global connection pool instance
_connection_pool: Optional[OptimizedConnectionPool] = None

async def get_connection_pool() -> OptimizedConnectionPool:
    """Get or create global connection pool"""
    global _connection_pool
    
    if _connection_pool is None:
        _connection_pool = OptimizedConnectionPool()
        await _connection_pool.initialize()
    
    return _connection_pool

@asynccontextmanager
async def get_optimized_session(session_key: str = "default"):
    """Convenience function to get optimized session"""
    pool = await get_connection_pool()
    async with pool.get_session(session_key) as session:
        yield session

class SessionManager:
    """
    High-level session manager with specialized sessions for different use cases
    """
    
    def __init__(self):
        self.pool: Optional[OptimizedConnectionPool] = None
    
    async def initialize(self):
        """Initialize session manager"""
        self.pool = await get_connection_pool()
    
    @asynccontextmanager
    async def get_crawler_session(self):
        """Get session optimized for web crawling"""
        if not self.pool:
            await self.initialize()
        
        async with self.pool.get_session("crawler") as session:
            yield session
    
    @asynccontextmanager
    async def get_api_session(self):
        """Get session optimized for API calls"""
        if not self.pool:
            await self.initialize()
        
        async with self.pool.get_session("api") as session:
            yield session
    
    @asynccontextmanager
    async def get_media_session(self):
        """Get session optimized for media downloads"""
        if not self.pool:
            await self.initialize()
        
        async with self.pool.get_session("media") as session:
            yield session
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get session manager statistics"""
        if not self.pool:
            return {"status": "not_initialized"}
        
        return await self.pool.get_session_stats()
    
    async def cleanup(self):
        """Cleanup session manager"""
        if self.pool:
            await self.pool.cleanup()

# Global session manager
_session_manager: Optional[SessionManager] = None

async def get_session_manager() -> SessionManager:
    """Get or create global session manager"""
    global _session_manager
    
    if _session_manager is None:
        _session_manager = SessionManager()
        await _session_manager.initialize()
    
    return _session_manager

# Convenience functions
async def get_crawler_session():
    """Get optimized session for crawling"""
    manager = await get_session_manager()
    return manager.get_crawler_session()

async def get_api_session():
    """Get optimized session for API calls"""
    manager = await get_session_manager()
    return manager.get_api_session()

async def get_media_session():
    """Get optimized session for media downloads"""
    manager = await get_session_manager()
    return manager.get_media_session()