"""
Intelligent Caching System for Jina Crawler
Multi-layer caching with TTL, LRU eviction, and content-aware strategies
"""

import asyncio
import hashlib
import json
import time
import logging
from typing import Dict, Any, Optional, List, Union, Callable
from dataclasses import dataclass, asdict
from collections import OrderedDict
import weakref
import pickle
import gzip
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    key: str
    value: Any
    created_at: float
    last_accessed: float
    access_count: int
    ttl: Optional[float]
    size_bytes: int
    content_type: str
    
    def is_expired(self) -> bool:
        """Check if entry is expired"""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl
    
    def is_stale(self, max_age: float = 3600) -> bool:
        """Check if entry is stale (older than max_age)"""
        return time.time() - self.created_at > max_age

@dataclass
class CacheConfig:
    """Configuration for intelligent cache"""
    max_memory_mb: int = 256
    max_entries: int = 10000
    default_ttl: float = 3600  # 1 hour
    cleanup_interval: float = 300  # 5 minutes
    compression_threshold: int = 1024  # Compress entries > 1KB
    enable_persistence: bool = True
    persistence_path: str = "/tmp/jina_cache"
    
class IntelligentCache:
    """
    Multi-layer intelligent cache with advanced features:
    - Content-aware TTL
    - LRU eviction with access patterns
    - Automatic compression
    - Memory usage optimization
    - Persistence support
    """
    
    def __init__(self, config: Optional[CacheConfig] = None):
        self.config = config or CacheConfig()
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._memory_usage = 0
        self._lock = asyncio.Lock()
        self._cleanup_task: Optional[asyncio.Task] = None
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'compressions': 0,
            'decompressions': 0
        }
        
        # Content-specific TTL rules
        self._ttl_rules = {
            'search_results': 1800,  # 30 minutes
            'crawled_content': 3600,  # 1 hour
            'processed_content': 7200,  # 2 hours
            'api_response': 900,  # 15 minutes
            'media_content': 86400,  # 24 hours
        }
    
    async def initialize(self):
        """Initialize the cache system"""
        logger.info("🚀 Initializing intelligent cache system...")
        
        # Create persistence directory
        if self.config.enable_persistence:
            Path(self.config.persistence_path).mkdir(parents=True, exist_ok=True)
            await self._load_persistent_cache()
        
        # Start cleanup task
        self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
        
        logger.info(f"✅ Intelligent cache initialized (max: {self.config.max_memory_mb}MB, {self.config.max_entries} entries)")
    
    async def get(self, key: str, default: Any = None) -> Any:
        """Get value from cache with intelligent access tracking"""
        async with self._lock:
            cache_key = self._normalize_key(key)
            
            if cache_key not in self._cache:
                self._stats['misses'] += 1
                return default
            
            entry = self._cache[cache_key]
            
            # Check expiration
            if entry.is_expired():
                del self._cache[cache_key]
                self._memory_usage -= entry.size_bytes
                self._stats['misses'] += 1
                return default
            
            # Update access info
            entry.last_accessed = time.time()
            entry.access_count += 1
            
            # Move to end (LRU)
            self._cache.move_to_end(cache_key)
            
            self._stats['hits'] += 1
            
            # Decompress if needed
            value = entry.value
            if isinstance(value, bytes) and cache_key.endswith('_compressed'):
                try:
                    value = pickle.loads(gzip.decompress(value))
                    self._stats['decompressions'] += 1
                except Exception as e:
                    logger.warning(f"⚠️ Decompression failed for {cache_key}: {e}")
                    return default
            
            return value
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[float] = None,
        content_type: str = 'generic'
    ) -> bool:
        """Set value in cache with intelligent storage optimization"""
        async with self._lock:
            cache_key = self._normalize_key(key)
            
            # Determine TTL
            effective_ttl = ttl or self._get_content_ttl(content_type)
            
            # Serialize and potentially compress value
            serialized_value, is_compressed = await self._serialize_value(value, cache_key)
            size_bytes = len(serialized_value) if isinstance(serialized_value, (bytes, str)) else len(str(serialized_value))
            
            # Check if we need to make space
            await self._ensure_space(size_bytes)
            
            # Create cache entry
            entry = CacheEntry(
                key=cache_key,
                value=serialized_value,
                created_at=time.time(),
                last_accessed=time.time(),
                access_count=1,
                ttl=effective_ttl,
                size_bytes=size_bytes,
                content_type=content_type
            )
            
            # Remove existing entry if present
            if cache_key in self._cache:
                old_entry = self._cache[cache_key]
                self._memory_usage -= old_entry.size_bytes
            
            # Add new entry
            self._cache[cache_key] = entry
            self._memory_usage += size_bytes
            
            # Move to end (most recently used)
            self._cache.move_to_end(cache_key)
            
            if is_compressed:
                self._stats['compressions'] += 1
            
            return True
    
    async def delete(self, key: str) -> bool:
        """Delete entry from cache"""
        async with self._lock:
            cache_key = self._normalize_key(key)
            
            if cache_key in self._cache:
                entry = self._cache[cache_key]
                self._memory_usage -= entry.size_bytes
                del self._cache[cache_key]
                return True
            
            return False
    
    async def clear(self):
        """Clear all cache entries"""
        async with self._lock:
            self._cache.clear()
            self._memory_usage = 0
            logger.info("🧹 Cache cleared")
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        async with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_ratio = self._stats['hits'] / total_requests if total_requests > 0 else 0
            
            return {
                'entries': len(self._cache),
                'memory_usage_mb': self._memory_usage / (1024 * 1024),
                'memory_usage_percent': (self._memory_usage / (self.config.max_memory_mb * 1024 * 1024)) * 100,
                'hit_ratio': hit_ratio,
                'stats': self._stats.copy(),
                'avg_entry_size': self._memory_usage / len(self._cache) if self._cache else 0
            }
    
    def _normalize_key(self, key: str) -> str:
        """Normalize cache key"""
        if isinstance(key, str):
            return hashlib.md5(key.encode()).hexdigest()
        return hashlib.md5(str(key).encode()).hexdigest()
    
    def _get_content_ttl(self, content_type: str) -> float:
        """Get TTL based on content type"""
        return self._ttl_rules.get(content_type, self.config.default_ttl)
    
    async def _serialize_value(self, value: Any, cache_key: str) -> tuple[Any, bool]:
        """Serialize and optionally compress value"""
        try:
            # Try to serialize
            if isinstance(value, (dict, list)):
                serialized = json.dumps(value, ensure_ascii=False)
            else:
                serialized = value
            
            # Check if compression is beneficial
            if isinstance(serialized, str):
                serialized_bytes = serialized.encode('utf-8')
            else:
                serialized_bytes = pickle.dumps(serialized)
            
            if len(serialized_bytes) > self.config.compression_threshold:
                try:
                    compressed = gzip.compress(serialized_bytes)
                    if len(compressed) < len(serialized_bytes) * 0.8:  # At least 20% reduction
                        return compressed, True
                except Exception as e:
                    logger.debug(f"Compression failed for {cache_key}: {e}")
            
            return serialized, False
            
        except Exception as e:
            logger.warning(f"⚠️ Serialization failed for {cache_key}: {e}")
            return str(value), False
    
    async def _ensure_space(self, required_bytes: int):
        """Ensure enough space in cache"""
        max_memory_bytes = self.config.max_memory_mb * 1024 * 1024
        
        # Check memory limit
        while (self._memory_usage + required_bytes > max_memory_bytes or 
               len(self._cache) >= self.config.max_entries):
            
            if not self._cache:
                break
            
            # Remove least recently used entry
            lru_key, lru_entry = self._cache.popitem(last=False)
            self._memory_usage -= lru_entry.size_bytes
            self._stats['evictions'] += 1
    
    async def _periodic_cleanup(self):
        """Periodic cleanup of expired entries"""
        while True:
            try:
                await asyncio.sleep(self.config.cleanup_interval)
                await self._cleanup_expired()
                
                # Persist cache if enabled
                if self.config.enable_persistence:
                    await self._persist_cache()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"⚠️ Cache cleanup error: {e}")
    
    async def _cleanup_expired(self):
        """Clean up expired entries"""
        async with self._lock:
            expired_keys = []
            current_time = time.time()
            
            for key, entry in self._cache.items():
                if entry.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                entry = self._cache[key]
                self._memory_usage -= entry.size_bytes
                del self._cache[key]
            
            if expired_keys:
                logger.debug(f"🧹 Cleaned up {len(expired_keys)} expired cache entries")
    
    async def _persist_cache(self):
        """Persist cache to disk"""
        try:
            cache_file = Path(self.config.persistence_path) / "cache.pkl"
            
            # Only persist non-expired, frequently accessed entries
            persistent_cache = {}
            async with self._lock:
                for key, entry in self._cache.items():
                    if not entry.is_expired() and entry.access_count > 1:
                        persistent_cache[key] = asdict(entry)
            
            if persistent_cache:
                with open(cache_file, 'wb') as f:
                    pickle.dump(persistent_cache, f)
                
                logger.debug(f"💾 Persisted {len(persistent_cache)} cache entries")
                
        except Exception as e:
            logger.warning(f"⚠️ Cache persistence failed: {e}")
    
    async def _load_persistent_cache(self):
        """Load cache from disk"""
        try:
            cache_file = Path(self.config.persistence_path) / "cache.pkl"
            
            if cache_file.exists():
                with open(cache_file, 'rb') as f:
                    persistent_cache = pickle.load(f)
                
                loaded_count = 0
                for key, entry_dict in persistent_cache.items():
                    entry = CacheEntry(**entry_dict)
                    if not entry.is_expired():
                        self._cache[key] = entry
                        self._memory_usage += entry.size_bytes
                        loaded_count += 1
                
                logger.info(f"💾 Loaded {loaded_count} cache entries from disk")
                
        except Exception as e:
            logger.warning(f"⚠️ Cache loading failed: {e}")
    
    async def cleanup(self):
        """Cleanup cache resources"""
        logger.info("🧹 Starting cache cleanup...")
        
        # Cancel cleanup task
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Persist final state
        if self.config.enable_persistence:
            await self._persist_cache()
        
        # Clear cache
        await self.clear()
        
        logger.info("✅ Cache cleanup completed")

class ContentAwareCache(IntelligentCache):
    """
    Content-aware cache with specialized handling for different content types
    """
    
    def __init__(self, config: Optional[CacheConfig] = None):
        super().__init__(config)
        
        # Content-specific optimization rules
        self._content_rules = {
            'search_results': {
                'ttl': 1800,
                'compress': True,
                'priority': 'high'
            },
            'crawled_content': {
                'ttl': 3600,
                'compress': True,
                'priority': 'medium'
            },
            'processed_content': {
                'ttl': 7200,
                'compress': True,
                'priority': 'high'
            },
            'api_response': {
                'ttl': 900,
                'compress': False,
                'priority': 'low'
            }
        }
    
    async def set_search_results(self, query: str, results: List[Dict], ttl: Optional[float] = None):
        """Cache search results with query-specific optimization"""
        cache_key = f"search:{query}"
        return await self.set(cache_key, results, ttl, 'search_results')
    
    async def get_search_results(self, query: str) -> Optional[List[Dict]]:
        """Get cached search results"""
        cache_key = f"search:{query}"
        return await self.get(cache_key)
    
    async def set_crawled_content(self, url: str, content: str, ttl: Optional[float] = None):
        """Cache crawled content with URL-specific optimization"""
        cache_key = f"crawl:{url}"
        return await self.set(cache_key, content, ttl, 'crawled_content')
    
    async def get_crawled_content(self, url: str) -> Optional[str]:
        """Get cached crawled content"""
        cache_key = f"crawl:{url}"
        return await self.get(cache_key)
    
    async def set_processed_content(self, url: str, content: str, ttl: Optional[float] = None):
        """Cache processed content with processing-specific optimization"""
        cache_key = f"processed:{url}"
        return await self.set(cache_key, content, ttl, 'processed_content')
    
    async def get_processed_content(self, url: str) -> Optional[str]:
        """Get cached processed content"""
        cache_key = f"processed:{url}"
        return await self.get(cache_key)

# Global cache instances
_intelligent_cache: Optional[IntelligentCache] = None
_content_aware_cache: Optional[ContentAwareCache] = None

async def get_intelligent_cache() -> IntelligentCache:
    """Get or create global intelligent cache"""
    global _intelligent_cache
    
    if _intelligent_cache is None:
        _intelligent_cache = IntelligentCache()
        await _intelligent_cache.initialize()
    
    return _intelligent_cache

async def get_content_aware_cache() -> ContentAwareCache:
    """Get or create global content-aware cache"""
    global _content_aware_cache
    
    if _content_aware_cache is None:
        _content_aware_cache = ContentAwareCache()
        await _content_aware_cache.initialize()
    
    return _content_aware_cache

# Convenience functions
async def cache_get(key: str, default: Any = None) -> Any:
    """Get value from global cache"""
    cache = await get_intelligent_cache()
    return await cache.get(key, default)

async def cache_set(key: str, value: Any, ttl: Optional[float] = None, content_type: str = 'generic') -> bool:
    """Set value in global cache"""
    cache = await get_intelligent_cache()
    return await cache.set(key, value, ttl, content_type)

async def cache_delete(key: str) -> bool:
    """Delete value from global cache"""
    cache = await get_intelligent_cache()
    return await cache.delete(key)

async def cache_stats() -> Dict[str, Any]:
    """Get global cache statistics"""
    cache = await get_intelligent_cache()
    return await cache.get_stats()