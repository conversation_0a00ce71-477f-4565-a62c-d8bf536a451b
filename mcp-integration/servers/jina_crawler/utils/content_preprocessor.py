"""
Advanced Content Preprocessing Module
Reduces Gemini workload through intelligent content optimization and preprocessing
"""

import re
import logging
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from bs4 import BeautifulSoup, Comment
import hashlib
import time

logger = logging.getLogger(__name__)

@dataclass
class PreprocessingConfig:
    """Configuration for content preprocessing"""
    max_content_length: int = 50000
    min_meaningful_length: int = 100
    remove_boilerplate: bool = True
    extract_main_content: bool = True
    normalize_whitespace: bool = True
    remove_redundant_elements: bool = True
    extract_structured_data: bool = True
    enable_smart_truncation: bool = True

class ContentPreprocessor:
    """
    Advanced content preprocessor that optimizes content before Gemini processing
    Reduces token usage and improves processing quality
    """
    
    def __init__(self, config: Optional[PreprocessingConfig] = None):
        self.config = config or PreprocessingConfig()
        
        # Boilerplate patterns (common across websites)
        self.boilerplate_patterns = [
            r'cookie\s+policy',
            r'privacy\s+policy',
            r'terms\s+of\s+service',
            r'subscribe\s+to\s+newsletter',
            r'follow\s+us\s+on',
            r'share\s+this\s+article',
            r'related\s+articles?',
            r'you\s+might\s+also\s+like',
            r'advertisement',
            r'sponsored\s+content',
            r'click\s+here\s+to',
            r'read\s+more\s+about',
            r'copyright\s+\d{4}',
            r'all\s+rights\s+reserved'
        ]
        
        # Compile patterns for efficiency
        self.boilerplate_regex = re.compile(
            '|'.join(self.boilerplate_patterns), 
            re.IGNORECASE | re.MULTILINE
        )
        
        # Content quality indicators
        self.quality_indicators = {
            'high_quality': [
                r'\b(analysis|research|study|report|findings)\b',
                r'\b(according\s+to|based\s+on|research\s+shows)\b',
                r'\b(data|statistics|evidence|proof)\b',
                r'\b(expert|professor|scientist|researcher)\b'
            ],
            'low_quality': [
                r'\b(click\s+here|read\s+more|subscribe|follow)\b',
                r'\b(advertisement|sponsored|promoted)\b',
                r'\b(buy\s+now|order\s+today|limited\s+time)\b'
            ]
        }
        
        # Statistics
        self._stats = {
            'processed_count': 0,
            'total_reduction_ratio': 0.0,
            'avg_processing_time': 0.0,
            'boilerplate_removed': 0,
            'structured_data_extracted': 0
        }
    
    def preprocess_html(self, html_content: str, url: str = "") -> Dict[str, Any]:
        """
        Preprocess HTML content for optimal Gemini processing
        
        Args:
            html_content: Raw HTML content
            url: Source URL for context
            
        Returns:
            Dictionary with preprocessed content and metadata
        """
        start_time = time.time()
        original_length = len(html_content)
        
        try:
            # Parse HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Step 1: Remove unwanted elements
            cleaned_soup = self._remove_unwanted_elements(soup)
            
            # Step 2: Extract main content
            if self.config.extract_main_content:
                main_content = self._extract_main_content(cleaned_soup)
            else:
                main_content = cleaned_soup
            
            # Step 3: Extract structured data
            structured_data = {}
            if self.config.extract_structured_data:
                structured_data = self._extract_structured_data(main_content)
            
            # Step 4: Convert to optimized text
            optimized_text = self._html_to_optimized_text(main_content)
            
            # Step 5: Remove boilerplate content
            if self.config.remove_boilerplate:
                optimized_text = self._remove_boilerplate(optimized_text)
            
            # Step 6: Normalize and clean text
            if self.config.normalize_whitespace:
                optimized_text = self._normalize_whitespace(optimized_text)
            
            # Step 7: Smart truncation if needed
            if self.config.enable_smart_truncation and len(optimized_text) > self.config.max_content_length:
                optimized_text = self._smart_truncate(optimized_text, self.config.max_content_length)
            
            # Calculate metrics
            processing_time = time.time() - start_time
            final_length = len(optimized_text)
            reduction_ratio = 1 - (final_length / original_length) if original_length > 0 else 0
            
            # Update stats
            self._update_stats(processing_time, reduction_ratio)
            
            # Quality assessment
            quality_score = self._assess_content_quality(optimized_text)
            
            result = {
                'success': True,
                'original_content': html_content,
                'preprocessed_content': optimized_text,
                'structured_data': structured_data,
                'metadata': {
                    'original_length': original_length,
                    'final_length': final_length,
                    'reduction_ratio': reduction_ratio,
                    'processing_time': processing_time,
                    'quality_score': quality_score,
                    'url': url,
                    'preprocessing_applied': [
                        'unwanted_elements_removed',
                        'main_content_extracted' if self.config.extract_main_content else None,
                        'boilerplate_removed' if self.config.remove_boilerplate else None,
                        'whitespace_normalized' if self.config.normalize_whitespace else None,
                        'smart_truncated' if len(optimized_text) < final_length else None
                    ]
                }
            }
            
            logger.debug(f"✅ Preprocessed content: {original_length} → {final_length} chars ({reduction_ratio:.1%} reduction)")
            return result
            
        except Exception as e:
            logger.error(f"❌ Content preprocessing failed: {e}")
            return {
                'success': False,
                'original_content': html_content,
                'preprocessed_content': html_content[:self.config.max_content_length],
                'structured_data': {},
                'metadata': {
                    'error': str(e),
                    'original_length': original_length,
                    'final_length': min(len(html_content), self.config.max_content_length)
                }
            }
    
    def _remove_unwanted_elements(self, soup: BeautifulSoup) -> BeautifulSoup:
        """Remove unwanted HTML elements"""
        # Elements to remove completely
        unwanted_tags = [
            'script', 'style', 'noscript', 'iframe', 'embed', 'object',
            'form', 'input', 'button', 'select', 'textarea',
            'nav', 'header', 'footer', 'aside', 'menu',
            'advertisement', 'ads'
        ]
        
        for tag in unwanted_tags:
            for element in soup.find_all(tag):
                element.decompose()
        
        # Remove comments
        for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
            comment.extract()
        
        # Remove elements with unwanted classes/IDs
        unwanted_selectors = [
            '[class*="ad"]', '[class*="advertisement"]', '[class*="sponsor"]',
            '[class*="social"]', '[class*="share"]', '[class*="follow"]',
            '[class*="newsletter"]', '[class*="subscribe"]', '[class*="popup"]',
            '[class*="modal"]', '[class*="overlay"]', '[class*="sidebar"]',
            '[id*="ad"]', '[id*="advertisement"]', '[id*="sponsor"]'
        ]
        
        for selector in unwanted_selectors:
            for element in soup.select(selector):
                element.decompose()
        
        return soup
    
    def _extract_main_content(self, soup: BeautifulSoup) -> BeautifulSoup:
        """Extract main content using heuristics"""
        # Try common main content selectors
        main_selectors = [
            'main', 'article', '[role="main"]',
            '.main-content', '.content', '.post-content',
            '.entry-content', '.article-content', '.story-content',
            '#main-content', '#content', '#article'
        ]
        
        for selector in main_selectors:
            main_element = soup.select_one(selector)
            if main_element and len(main_element.get_text(strip=True)) > self.config.min_meaningful_length:
                logger.debug(f"📄 Found main content with selector: {selector}")
                return main_element
        
        # Fallback: find largest text block
        text_blocks = []
        for element in soup.find_all(['div', 'section', 'article']):
            text = element.get_text(strip=True)
            if len(text) > self.config.min_meaningful_length:
                text_blocks.append((element, len(text)))
        
        if text_blocks:
            # Return element with most text
            largest_element = max(text_blocks, key=lambda x: x[1])[0]
            logger.debug(f"📄 Using largest text block as main content")
            return largest_element
        
        # Final fallback: return body or entire soup
        body = soup.find('body')
        return body if body else soup
    
    def _extract_structured_data(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract structured data from HTML"""
        structured_data = {}
        
        try:
            # Extract title
            title_element = soup.find('h1') or soup.find('title')
            if title_element:
                structured_data['title'] = title_element.get_text(strip=True)
            
            # Extract headings hierarchy
            headings = []
            for level in range(1, 7):  # h1 to h6
                for heading in soup.find_all(f'h{level}'):
                    headings.append({
                        'level': level,
                        'text': heading.get_text(strip=True)
                    })
            structured_data['headings'] = headings
            
            # Extract lists
            lists = []
            for ul in soup.find_all('ul'):
                items = [li.get_text(strip=True) for li in ul.find_all('li')]
                if items:
                    lists.append({'type': 'unordered', 'items': items})
            
            for ol in soup.find_all('ol'):
                items = [li.get_text(strip=True) for li in ol.find_all('li')]
                if items:
                    lists.append({'type': 'ordered', 'items': items})
            
            structured_data['lists'] = lists
            
            # Extract quotes
            quotes = []
            for quote in soup.find_all('blockquote'):
                quote_text = quote.get_text(strip=True)
                if quote_text:
                    quotes.append(quote_text)
            structured_data['quotes'] = quotes
            
            # Extract meta information
            meta_info = {}
            for meta in soup.find_all('meta'):
                name = meta.get('name') or meta.get('property')
                content = meta.get('content')
                if name and content:
                    meta_info[name] = content
            structured_data['meta'] = meta_info
            
            if structured_data:
                self._stats['structured_data_extracted'] += 1
            
        except Exception as e:
            logger.debug(f"⚠️ Structured data extraction failed: {e}")
        
        return structured_data
    
    def _html_to_optimized_text(self, soup: BeautifulSoup) -> str:
        """Convert HTML to optimized text format"""
        # Extract text with structure preservation
        text_parts = []
        
        # Process headings with markdown-style formatting
        for heading in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
            level = int(heading.name[1])
            text = heading.get_text(strip=True)
            if text:
                text_parts.append(f"{'#' * level} {text}")
        
        # Process paragraphs
        for para in soup.find_all('p'):
            text = para.get_text(strip=True)
            if text and len(text) > 20:  # Skip very short paragraphs
                text_parts.append(text)
        
        # Process lists
        for ul in soup.find_all('ul'):
            for li in ul.find_all('li', recursive=False):
                text = li.get_text(strip=True)
                if text:
                    text_parts.append(f"• {text}")
        
        for ol in soup.find_all('ol'):
            for i, li in enumerate(ol.find_all('li', recursive=False), 1):
                text = li.get_text(strip=True)
                if text:
                    text_parts.append(f"{i}. {text}")
        
        # Process blockquotes
        for quote in soup.find_all('blockquote'):
            text = quote.get_text(strip=True)
            if text:
                text_parts.append(f"> {text}")
        
        # If no structured content found, extract all text
        if not text_parts:
            text_parts.append(soup.get_text(separator=' ', strip=True))
        
        return '\n\n'.join(text_parts)
    
    def _remove_boilerplate(self, text: str) -> str:
        """Remove boilerplate content using patterns"""
        lines = text.split('\n')
        filtered_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Check against boilerplate patterns
            if not self.boilerplate_regex.search(line):
                filtered_lines.append(line)
            else:
                self._stats['boilerplate_removed'] += 1
        
        return '\n'.join(filtered_lines)
    
    def _normalize_whitespace(self, text: str) -> str:
        """Normalize whitespace and clean text"""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove excessive newlines
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
        
        # Clean up common issues
        text = re.sub(r'\s+([.!?])', r'\1', text)  # Remove space before punctuation
        text = re.sub(r'([.!?])\s*([A-Z])', r'\1 \2', text)  # Ensure space after sentence end
        
        return text.strip()
    
    def _smart_truncate(self, text: str, max_length: int) -> str:
        """Intelligently truncate text while preserving meaning"""
        if len(text) <= max_length:
            return text
        
        # Try to find good breaking points
        truncated = text[:max_length]
        
        # Look for sentence endings
        sentence_endings = ['. ', '.\n', '! ', '!\n', '? ', '?\n']
        best_break = -1
        
        for ending in sentence_endings:
            pos = truncated.rfind(ending)
            if pos > max_length * 0.7:  # At least 70% of content
                best_break = max(best_break, pos + len(ending) - 1)
        
        if best_break > 0:
            return text[:best_break + 1]
        
        # Fallback: break at word boundary
        last_space = truncated.rfind(' ')
        if last_space > max_length * 0.8:  # At least 80% of content
            return text[:last_space] + "..."
        
        return truncated + "..."
    
    def _assess_content_quality(self, text: str) -> float:
        """Assess content quality score (0.0 to 1.0)"""
        if not text:
            return 0.0
        
        score = 0.5  # Base score
        
        # Check for high-quality indicators
        high_quality_matches = 0
        for pattern in self.quality_indicators['high_quality']:
            high_quality_matches += len(re.findall(pattern, text, re.IGNORECASE))
        
        # Check for low-quality indicators
        low_quality_matches = 0
        for pattern in self.quality_indicators['low_quality']:
            low_quality_matches += len(re.findall(pattern, text, re.IGNORECASE))
        
        # Adjust score based on indicators
        score += min(high_quality_matches * 0.1, 0.3)  # Max +0.3
        score -= min(low_quality_matches * 0.1, 0.3)   # Max -0.3
        
        # Length-based quality (longer content often more valuable)
        if len(text) > 1000:
            score += 0.1
        elif len(text) < 200:
            score -= 0.1
        
        # Sentence structure quality
        sentences = re.split(r'[.!?]+', text)
        avg_sentence_length = sum(len(s.split()) for s in sentences) / max(len(sentences), 1)
        
        if 10 <= avg_sentence_length <= 25:  # Good sentence length
            score += 0.1
        elif avg_sentence_length < 5 or avg_sentence_length > 40:  # Poor sentence length
            score -= 0.1
        
        return max(0.0, min(1.0, score))
    
    def _update_stats(self, processing_time: float, reduction_ratio: float):
        """Update preprocessing statistics"""
        self._stats['processed_count'] += 1
        
        # Update averages
        count = self._stats['processed_count']
        self._stats['avg_processing_time'] = (
            (self._stats['avg_processing_time'] * (count - 1) + processing_time) / count
        )
        self._stats['total_reduction_ratio'] = (
            (self._stats['total_reduction_ratio'] * (count - 1) + reduction_ratio) / count
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get preprocessing statistics"""
        return self._stats.copy()
    
    def preprocess_text(self, text: str) -> Dict[str, Any]:
        """Preprocess plain text content"""
        start_time = time.time()
        original_length = len(text)
        
        try:
            # Apply text-specific preprocessing
            processed_text = text
            
            # Remove boilerplate
            if self.config.remove_boilerplate:
                processed_text = self._remove_boilerplate(processed_text)
            
            # Normalize whitespace
            if self.config.normalize_whitespace:
                processed_text = self._normalize_whitespace(processed_text)
            
            # Smart truncation
            if self.config.enable_smart_truncation and len(processed_text) > self.config.max_content_length:
                processed_text = self._smart_truncate(processed_text, self.config.max_content_length)
            
            processing_time = time.time() - start_time
            final_length = len(processed_text)
            reduction_ratio = 1 - (final_length / original_length) if original_length > 0 else 0
            
            self._update_stats(processing_time, reduction_ratio)
            
            return {
                'success': True,
                'original_content': text,
                'preprocessed_content': processed_text,
                'metadata': {
                    'original_length': original_length,
                    'final_length': final_length,
                    'reduction_ratio': reduction_ratio,
                    'processing_time': processing_time,
                    'quality_score': self._assess_content_quality(processed_text)
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Text preprocessing failed: {e}")
            return {
                'success': False,
                'original_content': text,
                'preprocessed_content': text[:self.config.max_content_length],
                'metadata': {
                    'error': str(e),
                    'original_length': original_length
                }
            }

# Global preprocessor instance
_content_preprocessor: Optional[ContentPreprocessor] = None

def get_content_preprocessor(config: Optional[PreprocessingConfig] = None) -> ContentPreprocessor:
    """Get or create global content preprocessor"""
    global _content_preprocessor
    
    if _content_preprocessor is None:
        _content_preprocessor = ContentPreprocessor(config)
    
    return _content_preprocessor

# Convenience functions
def preprocess_html(html_content: str, url: str = "") -> Dict[str, Any]:
    """Preprocess HTML content"""
    preprocessor = get_content_preprocessor()
    return preprocessor.preprocess_html(html_content, url)

def preprocess_text(text: str) -> Dict[str, Any]:
    """Preprocess text content"""
    preprocessor = get_content_preprocessor()
    return preprocessor.preprocess_text(text)

def get_preprocessing_stats() -> Dict[str, Any]:
    """Get preprocessing statistics"""
    preprocessor = get_content_preprocessor()
    return preprocessor.get_stats()