"""
Advanced Gemini API Optimization Module
Implements intelligent batching, request optimization, and API efficiency strategies
"""

import asyncio
import logging
import time
import json
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from collections import deque
import hashlib

logger = logging.getLogger(__name__)

@dataclass
class BatchRequest:
    """Individual request in a batch"""
    id: str
    content: str
    task_type: str
    priority: int = 1
    created_at: float = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()

@dataclass
class BatchConfig:
    """Configuration for batch processing"""
    max_batch_size: int = 10
    max_wait_time: float = 2.0  # Maximum wait time before processing batch
    max_content_per_request: int = 40000  # Max content per individual request
    max_total_tokens: int = 800000  # Conservative limit for Gemini 2.5 Flash Lite
    enable_compression: bool = True
    priority_threshold: int = 3  # High priority requests get faster processing

class GeminiOptimizer:
    """
    Advanced Gemini API optimizer with intelligent batching and request management
    """
    
    def __init__(self, gemini_processor, config: Optional[BatchConfig] = None):
        self.gemini_processor = gemini_processor
        self.config = config or BatchConfig()
        
        # Batch management
        self._pending_requests: deque = deque()
        self._batch_lock = asyncio.Lock()
        self._batch_processor_task: Optional[asyncio.Task] = None
        self._processing_batches: Dict[str, List[BatchRequest]] = {}
        
        # Performance tracking
        self._stats = {
            'total_requests': 0,
            'batched_requests': 0,
            'individual_requests': 0,
            'batch_efficiency': 0.0,
            'avg_batch_size': 0.0,
            'total_processing_time': 0.0,
            'api_calls_saved': 0
        }
        
        # Request deduplication
        self._request_cache: Dict[str, Any] = {}
        self._cache_ttl = 300  # 5 minutes
        
    async def initialize(self):
        """Initialize the optimizer"""
        logger.info("🚀 Initializing Gemini API optimizer...")
        
        # Start batch processor
        self._batch_processor_task = asyncio.create_task(self._batch_processor())
        
        logger.info("✅ Gemini optimizer initialized")
    
    async def process_content_optimized(
        self,
        content: str,
        task_type: str = "html_to_markdown",
        priority: int = 1,
        force_individual: bool = False
    ) -> Dict[str, Any]:
        """
        Process content with intelligent optimization
        
        Args:
            content: Content to process
            task_type: Type of processing task
            priority: Request priority (1=low, 5=high)
            force_individual: Force individual processing (bypass batching)
        """
        start_time = time.time()
        
        # Check for cached result first
        cache_key = self._generate_cache_key(content, task_type)
        cached_result = self._get_cached_result(cache_key)
        if cached_result:
            logger.debug(f"💾 Cache hit for content processing")
            return cached_result
        
        # High priority or forced individual processing
        if priority >= self.config.priority_threshold or force_individual:
            result = await self._process_individual(content, task_type)
            self._stats['individual_requests'] += 1
        else:
            # Add to batch queue
            request = BatchRequest(
                id=cache_key,
                content=content,
                task_type=task_type,
                priority=priority
            )
            
            result = await self._add_to_batch(request)
            self._stats['batched_requests'] += 1
        
        # Cache successful results
        if result.get('success'):
            self._cache_result(cache_key, result)
        
        # Update stats
        self._stats['total_requests'] += 1
        self._stats['total_processing_time'] += time.time() - start_time
        
        return result
    
    async def process_batch_optimized(
        self,
        batch_data: List[Dict[str, str]],
        query: str = "",
        max_parallel_batches: int = 3
    ) -> List[Dict[str, Any]]:
        """
        Process multiple contents with advanced batching optimization
        
        Args:
            batch_data: List of content items to process
            query: Search query for context
            max_parallel_batches: Maximum parallel batch processing
        """
        if not batch_data:
            return []
        
        start_time = time.time()
        logger.info(f"🚀 Starting optimized batch processing for {len(batch_data)} items")
        
        # Check cache for existing results
        cached_results = {}
        uncached_items = []
        
        for item in batch_data:
            cache_key = self._generate_cache_key(item.get('raw_content', ''), 'batch_processing')
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                cached_results[item['url']] = cached_result
            else:
                uncached_items.append(item)
        
        logger.info(f"💾 Found {len(cached_results)} cached results, processing {len(uncached_items)} new items")
        
        # Process uncached items in optimized batches
        processed_results = []
        if uncached_items:
            # Split into optimal batch sizes
            optimal_batches = self._create_optimal_batches(uncached_items, query)
            
            # Process batches in parallel (limited concurrency)
            semaphore = asyncio.Semaphore(max_parallel_batches)
            
            async def process_single_batch(batch_items, batch_query):
                async with semaphore:
                    return await self.gemini_processor.process_batch(batch_items, batch_query)
            
            # Execute all batches in parallel
            batch_tasks = [
                process_single_batch(batch_items, query)
                for batch_items in optimal_batches
            ]
            
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # Flatten results and handle exceptions
            for result in batch_results:
                if isinstance(result, Exception):
                    logger.error(f"❌ Batch processing failed: {result}")
                    continue
                processed_results.extend(result)
        
        # Combine cached and processed results
        final_results = []
        for item in batch_data:
            url = item['url']
            if url in cached_results:
                # Use cached result
                cached_data = cached_results[url]
                final_results.append({
                    'url': url,
                    'title': cached_data.get('title', 'Cached Content'),
                    'processed_content': cached_data.get('processed_content', ''),
                    'key_points': cached_data.get('key_points', []),
                    'relevance_score': cached_data.get('relevance_score', 0.5)
                })
            else:
                # Find in processed results
                found = False
                for processed in processed_results:
                    if processed.get('url') == url:
                        final_results.append(processed)
                        
                        # Cache the result
                        cache_key = self._generate_cache_key(item.get('raw_content', ''), 'batch_processing')
                        self._cache_result(cache_key, processed)
                        found = True
                        break
                
                if not found:
                    # Fallback result
                    final_results.append({
                        'url': url,
                        'title': 'Processing Failed',
                        'processed_content': item.get('raw_content', '')[:1000],
                        'key_points': [],
                        'relevance_score': 0.1
                    })
        
        processing_time = time.time() - start_time
        efficiency_gain = len(cached_results) / len(batch_data) if batch_data else 0
        
        logger.info(f"✅ Optimized batch processing completed in {processing_time:.2f}s")
        logger.info(f"💰 Cache efficiency: {efficiency_gain:.1%} ({len(cached_results)}/{len(batch_data)} cached)")
        
        # Update stats
        self._stats['api_calls_saved'] += len(cached_results)
        
        return final_results
    
    def _create_optimal_batches(self, items: List[Dict], query: str) -> List[List[Dict]]:
        """Create optimally sized batches based on content length and token limits"""
        batches = []
        current_batch = []
        current_tokens = 0
        
        # Estimate tokens for query and overhead
        query_tokens = len(query.split()) * 1.3  # Rough token estimation
        overhead_tokens = 500  # Prompt overhead
        
        for item in items:
            content = item.get('raw_content', '')
            # Rough token estimation (1 token ≈ 0.75 words)
            content_tokens = len(content.split()) * 1.3
            
            # Check if adding this item would exceed limits
            if (current_tokens + content_tokens + query_tokens + overhead_tokens > self.config.max_total_tokens or
                len(current_batch) >= self.config.max_batch_size):
                
                if current_batch:
                    batches.append(current_batch)
                    current_batch = []
                    current_tokens = 0
            
            # Truncate content if too large
            if content_tokens > self.config.max_content_per_request:
                words = content.split()
                max_words = int(self.config.max_content_per_request / 1.3)
                content = ' '.join(words[:max_words])
                item['raw_content'] = content
                content_tokens = max_words * 1.3
            
            current_batch.append(item)
            current_tokens += content_tokens
        
        if current_batch:
            batches.append(current_batch)
        
        logger.debug(f"📦 Created {len(batches)} optimal batches from {len(items)} items")
        return batches
    
    async def _add_to_batch(self, request: BatchRequest) -> Dict[str, Any]:
        """Add request to batch queue and wait for result"""
        async with self._batch_lock:
            self._pending_requests.append(request)
        
        # Wait for processing with timeout
        timeout = self.config.max_wait_time * 3  # Allow extra time for processing
        start_wait = time.time()
        
        while time.time() - start_wait < timeout:
            # Check if request has been processed
            if request.id in self._request_cache:
                result = self._request_cache[request.id]
                del self._request_cache[request.id]  # Clean up
                return result
            
            await asyncio.sleep(0.1)  # Small delay
        
        # Timeout - process individually as fallback
        logger.warning(f"⚠️ Batch timeout for request {request.id}, processing individually")
        return await self._process_individual(request.content, request.task_type)
    
    async def _batch_processor(self):
        """Background task to process batched requests"""
        while True:
            try:
                await asyncio.sleep(0.5)  # Check every 500ms
                
                if not self._pending_requests:
                    continue
                
                # Collect batch
                batch_requests = []
                batch_start_time = time.time()
                
                async with self._batch_lock:
                    # Collect requests for batch
                    while (self._pending_requests and 
                           len(batch_requests) < self.config.max_batch_size):
                        
                        request = self._pending_requests.popleft()
                        
                        # Check if request is too old or high priority
                        age = time.time() - request.created_at
                        if (age > self.config.max_wait_time or 
                            request.priority >= self.config.priority_threshold):
                            batch_requests.append(request)
                        else:
                            # Put back if not ready
                            self._pending_requests.appendleft(request)
                            break
                
                if not batch_requests:
                    continue
                
                # Process batch
                await self._process_batch_requests(batch_requests)
                
                # Update stats
                if len(batch_requests) > 1:
                    self._stats['avg_batch_size'] = (
                        (self._stats['avg_batch_size'] * self._stats.get('batch_count', 0) + len(batch_requests)) /
                        (self._stats.get('batch_count', 0) + 1)
                    )
                    self._stats['batch_count'] = self._stats.get('batch_count', 0) + 1
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Batch processor error: {e}")
    
    async def _process_batch_requests(self, requests: List[BatchRequest]):
        """Process a batch of requests"""
        if len(requests) == 1:
            # Single request - process individually
            request = requests[0]
            result = await self._process_individual(request.content, request.task_type)
            self._request_cache[request.id] = result
        else:
            # Multiple requests - use batch processing
            batch_data = [
                {
                    'url': f"batch_request_{req.id}",
                    'raw_content': req.content
                }
                for req in requests
            ]
            
            try:
                batch_results = await self.gemini_processor.process_batch(batch_data, "")
                
                # Map results back to requests
                for i, request in enumerate(requests):
                    if i < len(batch_results):
                        result = batch_results[i]
                        self._request_cache[request.id] = {
                            'success': True,
                            'processed_content': result.get('processed_content', ''),
                            'title': result.get('title', ''),
                            'processing_time': 0.0,
                            'model': 'gemini-batch'
                        }
                    else:
                        # Fallback for missing results
                        self._request_cache[request.id] = {
                            'success': False,
                            'processed_content': '',
                            'error': 'Batch processing incomplete'
                        }
                        
            except Exception as e:
                logger.error(f"❌ Batch processing failed: {e}")
                # Fallback to individual processing
                for request in requests:
                    result = await self._process_individual(request.content, request.task_type)
                    self._request_cache[request.id] = result
    
    async def _process_individual(self, content: str, task_type: str) -> Dict[str, Any]:
        """Process content individually"""
        return await self.gemini_processor.process_content(content, task_type)
    
    def _generate_cache_key(self, content: str, task_type: str) -> str:
        """Generate cache key for content and task type"""
        content_hash = hashlib.md5(content.encode()).hexdigest()
        return f"{task_type}:{content_hash}"
    
    def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached result if available and not expired"""
        if cache_key in self._request_cache:
            cached_item = self._request_cache[cache_key]
            if isinstance(cached_item, dict) and 'cached_at' in cached_item:
                if time.time() - cached_item['cached_at'] < self._cache_ttl:
                    return cached_item['result']
                else:
                    del self._request_cache[cache_key]  # Expired
        return None
    
    def _cache_result(self, cache_key: str, result: Dict[str, Any]):
        """Cache processing result"""
        self._request_cache[cache_key] = {
            'result': result,
            'cached_at': time.time()
        }
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get optimizer statistics"""
        total_requests = self._stats['total_requests']
        if total_requests > 0:
            self._stats['batch_efficiency'] = self._stats['batched_requests'] / total_requests
        
        return {
            **self._stats,
            'pending_requests': len(self._pending_requests),
            'cache_size': len(self._request_cache),
            'avg_processing_time': (
                self._stats['total_processing_time'] / total_requests 
                if total_requests > 0 else 0
            )
        }
    
    async def cleanup(self):
        """Cleanup optimizer resources"""
        logger.info("🧹 Starting Gemini optimizer cleanup...")
        
        # Cancel batch processor
        if self._batch_processor_task:
            self._batch_processor_task.cancel()
            try:
                await self._batch_processor_task
            except asyncio.CancelledError:
                pass
        
        # Clear caches
        self._request_cache.clear()
        self._pending_requests.clear()
        
        logger.info("✅ Gemini optimizer cleanup completed")

# Global optimizer instance
_gemini_optimizer: Optional[GeminiOptimizer] = None

async def get_gemini_optimizer(gemini_processor) -> GeminiOptimizer:
    """Get or create global Gemini optimizer"""
    global _gemini_optimizer
    
    if _gemini_optimizer is None:
        _gemini_optimizer = GeminiOptimizer(gemini_processor)
        await _gemini_optimizer.initialize()
    
    return _gemini_optimizer

# Convenience functions
async def process_content_optimized(
    gemini_processor,
    content: str,
    task_type: str = "html_to_markdown",
    priority: int = 1
) -> Dict[str, Any]:
    """Process content with optimization"""
    optimizer = await get_gemini_optimizer(gemini_processor)
    return await optimizer.process_content_optimized(content, task_type, priority)

async def process_batch_optimized(
    gemini_processor,
    batch_data: List[Dict[str, str]],
    query: str = ""
) -> List[Dict[str, Any]]:
    """Process batch with optimization"""
    optimizer = await get_gemini_optimizer(gemini_processor)
    return await optimizer.process_batch_optimized(batch_data, query)