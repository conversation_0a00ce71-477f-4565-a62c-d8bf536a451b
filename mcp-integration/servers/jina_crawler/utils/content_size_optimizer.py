"""
Advanced Content Size Optimization Module
Specifically designed to handle large Vietnamese news sites like dantri.vn and vnexpress.net
Prevents context overflow while maintaining content quality
"""

import re
import logging
import time
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from bs4 import BeautifulSoup
import hashlib

logger = logging.getLogger(__name__)

@dataclass
class SizeOptimizationConfig:
    """Configuration for content size optimization"""
    max_total_tokens: int = 200000  # Conservative limit to stay under 272k
    max_content_per_url: int = 15000  # Reduced from 200k to 15k per URL
    max_output_tokens: int = 8192  # Reduced from 65k to 8k output tokens
    enable_smart_summarization: bool = True
    enable_content_deduplication: bool = True
    enable_relevance_filtering: bool = True
    priority_content_ratio: float = 0.7  # 70% for high-priority content
    vietnamese_news_optimization: bool = True

@dataclass
class ContentPriority:
    """Content priority classification"""
    title: int = 10
    main_article: int = 9
    quotes: int = 8
    key_points: int = 7
    images_with_captions: int = 6
    lists: int = 5
    secondary_content: int = 4
    navigation: int = 2
    ads: int = 1

class ContentSizeOptimizer:
    """
    Advanced content size optimizer specifically for Vietnamese news sites
    Prevents LLM context overflow while maintaining content quality
    """
    
    def __init__(self, config: Optional[SizeOptimizationConfig] = None):
        self.config = config or SizeOptimizationConfig()
        self.priority = ContentPriority()
        
        # Vietnamese news site patterns
        self.vietnamese_news_patterns = {
            'title_selectors': [
                'h1.title', 'h1.article-title', '.title-detail', 
                'h1[class*="title"]', '.post-title h1', '.entry-title'
            ],
            'content_selectors': [
                '.article-content', '.post-content', '.entry-content',
                '.detail-content', '.news-content', '[class*="article-body"]'
            ],
            'quote_patterns': [
                r'"([^"]{20,200})"',  # Quoted text
                r'["""]([^"""]{20,200})["""]',  # Vietnamese quotes
            ],
            'key_phrases': [
                r'theo\s+([^,\.]{10,50})',  # "theo..." (according to)
                r'cho\s+biết\s+([^,\.]{10,50})',  # "cho biết" (said that)
                r'nhấn\s+mạnh\s+([^,\.]{10,50})',  # "nhấn mạnh" (emphasized)
            ]
        }
        
        # Content deduplication cache
        self._content_hashes = set()
        self._stats = {
            'total_processed': 0,
            'size_reduction_ratio': 0.0,
            'duplicates_removed': 0,
            'avg_processing_time': 0.0
        }
    
    def optimize_batch_content(
        self, 
        batch_data: List[Dict[str, str]], 
        query: str = ""
    ) -> Tuple[List[Dict[str, str]], Dict[str, Any]]:
        """
        Optimize batch content to prevent context overflow
        
        Args:
            batch_data: List of dicts with 'url' and 'raw_content'
            query: Search query for relevance filtering
            
        Returns:
            Tuple of (optimized_batch_data, optimization_stats)
        """
        start_time = time.time()
        original_total_size = sum(len(item.get('raw_content', '')) for item in batch_data)
        
        try:
            logger.info(f"🔧 Optimizing batch content: {len(batch_data)} URLs, {original_total_size:,} chars")
            
            # Step 1: Individual content optimization
            optimized_items = []
            for item in batch_data:
                optimized_item = self._optimize_single_content(item, query)
                if optimized_item:  # Only include non-empty content
                    optimized_items.append(optimized_item)
            
            # Step 2: Global size management
            final_items = self._apply_global_size_limits(optimized_items, query)
            
            # Step 3: Content deduplication
            if self.config.enable_content_deduplication:
                final_items = self._remove_duplicate_content(final_items)
            
            # Calculate stats
            final_total_size = sum(len(item.get('raw_content', '')) for item in final_items)
            processing_time = time.time() - start_time
            reduction_ratio = 1 - (final_total_size / original_total_size) if original_total_size > 0 else 0
            
            # Update stats
            self._update_stats(processing_time, reduction_ratio)
            
            optimization_stats = {
                'original_urls': len(batch_data),
                'optimized_urls': len(final_items),
                'original_size': original_total_size,
                'final_size': final_total_size,
                'reduction_ratio': reduction_ratio,
                'processing_time': processing_time,
                'estimated_tokens': self._estimate_tokens(final_total_size),
                'within_limits': self._estimate_tokens(final_total_size) < self.config.max_total_tokens
            }
            
            logger.info(f"✅ Content optimization completed: {original_total_size:,} → {final_total_size:,} chars ({reduction_ratio:.1%} reduction)")
            logger.info(f"📊 Estimated tokens: {optimization_stats['estimated_tokens']:,} (limit: {self.config.max_total_tokens:,})")
            
            return final_items, optimization_stats
            
        except Exception as e:
            logger.error(f"❌ Content optimization failed: {e}")
            # Fallback: simple truncation
            fallback_items = []
            for item in batch_data[:5]:  # Limit to 5 URLs as fallback
                content = item.get('raw_content', '')[:self.config.max_content_per_url]
                fallback_items.append({
                    'url': item['url'],
                    'raw_content': content
                })
            
            return fallback_items, {
                'original_urls': len(batch_data),
                'optimized_urls': len(fallback_items),
                'error': str(e),
                'fallback_applied': True
            }
    
    def _optimize_single_content(self, item: Dict[str, str], query: str) -> Optional[Dict[str, str]]:
        """Optimize content for a single URL"""
        try:
            url = item.get('url', '')
            raw_content = item.get('raw_content', '')
            
            if not raw_content or len(raw_content) < 100:
                return None
            
            # Parse content
            soup = BeautifulSoup(raw_content, 'html.parser')
            
            # Extract prioritized content
            prioritized_content = self._extract_prioritized_content(soup, url, query)
            
            # Apply Vietnamese news optimization
            if self.config.vietnamese_news_optimization:
                prioritized_content = self._optimize_vietnamese_news_content(prioritized_content, query)
            
            # Smart truncation
            final_content = self._smart_truncate_content(prioritized_content, self.config.max_content_per_url)
            
            if len(final_content) < 200:  # Too small to be useful
                return None
            
            return {
                'url': url,
                'raw_content': final_content
            }
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to optimize content for {item.get('url', 'unknown')}: {e}")
            # Fallback: simple truncation
            return {
                'url': item.get('url', ''),
                'raw_content': item.get('raw_content', '')[:self.config.max_content_per_url]
            }
    
    def _extract_prioritized_content(self, soup: BeautifulSoup, url: str, query: str) -> str:
        """Extract content based on priority levels"""
        content_parts = []
        
        # Priority 1: Title (always include)
        title = self._extract_title(soup)
        if title:
            content_parts.append(f"# {title}\n")
        
        # Priority 2: Main article content
        main_content = self._extract_main_article(soup)
        if main_content:
            content_parts.append(main_content)
        
        # Priority 3: Important quotes and key points
        quotes = self._extract_quotes(soup)
        if quotes:
            content_parts.extend([f"> {quote}" for quote in quotes[:3]])  # Limit to 3 quotes
        
        # Priority 4: Lists and structured data
        lists = self._extract_lists(soup)
        if lists:
            content_parts.extend(lists[:2])  # Limit to 2 lists
        
        # Priority 5: Image captions (if relevant)
        if query:  # Only if we have a query to check relevance
            captions = self._extract_relevant_image_captions(soup, query)
            if captions:
                content_parts.extend(captions[:3])  # Limit to 3 captions
        
        return '\n\n'.join(content_parts)
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """Extract article title"""
        # Try Vietnamese news site specific selectors first
        for selector in self.vietnamese_news_patterns['title_selectors']:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.get_text(strip=True)
                if title and len(title) > 10:
                    return title
        
        # Fallback to standard selectors
        for tag in ['h1', 'title']:
            elem = soup.find(tag)
            if elem:
                title = elem.get_text(strip=True)
                if title and len(title) > 10:
                    return title
        
        return ""
    
    def _extract_main_article(self, soup: BeautifulSoup) -> str:
        """Extract main article content with smart truncation"""
        # Try Vietnamese news site specific selectors
        for selector in self.vietnamese_news_patterns['content_selectors']:
            content_elem = soup.select_one(selector)
            if content_elem:
                # Extract paragraphs
                paragraphs = []
                for p in content_elem.find_all('p'):
                    text = p.get_text(strip=True)
                    if text and len(text) > 30:  # Skip very short paragraphs
                        paragraphs.append(text)
                
                if paragraphs:
                    # Limit to most important paragraphs
                    important_paragraphs = self._select_important_paragraphs(paragraphs)
                    return '\n\n'.join(important_paragraphs)
        
        # Fallback: extract all paragraphs
        paragraphs = []
        for p in soup.find_all('p'):
            text = p.get_text(strip=True)
            if text and len(text) > 30:
                paragraphs.append(text)
        
        if paragraphs:
            important_paragraphs = self._select_important_paragraphs(paragraphs)
            return '\n\n'.join(important_paragraphs)
        
        return ""
    
    def _select_important_paragraphs(self, paragraphs: List[str]) -> List[str]:
        """Select most important paragraphs based on content analysis"""
        if len(paragraphs) <= 5:
            return paragraphs
        
        # Score paragraphs
        scored_paragraphs = []
        for i, para in enumerate(paragraphs):
            score = 0
            
            # First few paragraphs are usually important
            if i < 3:
                score += 3
            
            # Length-based scoring (medium length is often better)
            if 100 <= len(para) <= 300:
                score += 2
            elif 50 <= len(para) <= 500:
                score += 1
            
            # Vietnamese key phrase detection
            for pattern in self.vietnamese_news_patterns['key_phrases']:
                if re.search(pattern, para, re.IGNORECASE):
                    score += 2
            
            # Quote detection
            for pattern in self.vietnamese_news_patterns['quote_patterns']:
                if re.search(pattern, para):
                    score += 1
            
            scored_paragraphs.append((score, para))
        
        # Sort by score and take top paragraphs
        scored_paragraphs.sort(key=lambda x: x[0], reverse=True)
        return [para for score, para in scored_paragraphs[:8]]  # Limit to 8 paragraphs
    
    def _extract_quotes(self, soup: BeautifulSoup) -> List[str]:
        """Extract important quotes"""
        quotes = []
        
        # Extract from blockquotes
        for quote in soup.find_all('blockquote'):
            text = quote.get_text(strip=True)
            if text and 20 <= len(text) <= 200:
                quotes.append(text)
        
        # Extract quoted text from paragraphs
        for p in soup.find_all('p'):
            text = p.get_text()
            for pattern in self.vietnamese_news_patterns['quote_patterns']:
                matches = re.findall(pattern, text)
                for match in matches:
                    if 20 <= len(match) <= 200:
                        quotes.append(match)
        
        return quotes[:5]  # Limit to 5 quotes
    
    def _extract_lists(self, soup: BeautifulSoup) -> List[str]:
        """Extract structured lists"""
        lists = []
        
        # Unordered lists
        for ul in soup.find_all('ul'):
            items = []
            for li in ul.find_all('li'):
                text = li.get_text(strip=True)
                if text and len(text) < 200:  # Reasonable list item length
                    items.append(f"• {text}")
            
            if items and len(items) <= 10:  # Reasonable list size
                lists.append('\n'.join(items))
        
        # Ordered lists
        for ol in soup.find_all('ol'):
            items = []
            for i, li in enumerate(ol.find_all('li'), 1):
                text = li.get_text(strip=True)
                if text and len(text) < 200:
                    items.append(f"{i}. {text}")
            
            if items and len(items) <= 10:
                lists.append('\n'.join(items))
        
        return lists
    
    def _extract_relevant_image_captions(self, soup: BeautifulSoup, query: str) -> List[str]:
        """Extract image captions relevant to the query"""
        captions = []
        query_words = set(query.lower().split())
        
        for img in soup.find_all('img'):
            # Check alt text
            alt = img.get('alt', '')
            if alt and len(alt) > 10:
                alt_words = set(alt.lower().split())
                if query_words.intersection(alt_words):
                    captions.append(f"[Image: {alt}]")
            
            # Check caption in parent elements
            parent = img.parent
            if parent:
                caption_text = parent.get_text(strip=True)
                if caption_text and len(caption_text) < 200:
                    caption_words = set(caption_text.lower().split())
                    if query_words.intersection(caption_words):
                        captions.append(f"[Caption: {caption_text}]")
        
        return captions
    
    def _optimize_vietnamese_news_content(self, content: str, query: str) -> str:
        """Apply Vietnamese news specific optimizations"""
        if not content:
            return content
        
        lines = content.split('\n')
        optimized_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Skip common Vietnamese news boilerplate
            skip_patterns = [
                r'theo\s+vnexpress',
                r'theo\s+dantri',
                r'bản\s+quyền\s+thuộc',
                r'liên\s+hệ\s+quảng\s+cáo',
                r'đăng\s+ký\s+nhận\s+tin',
                r'chia\s+sẻ\s+bài\s+viết'
            ]
            
            should_skip = False
            for pattern in skip_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    should_skip = True
                    break
            
            if not should_skip:
                optimized_lines.append(line)
        
        return '\n'.join(optimized_lines)
    
    def _smart_truncate_content(self, content: str, max_length: int) -> str:
        """Smart content truncation preserving Vietnamese sentence structure"""
        if len(content) <= max_length:
            return content
        
        # Try to truncate at sentence boundaries
        truncated = content[:max_length]
        
        # Vietnamese sentence endings
        sentence_endings = ['. ', '.\n', '? ', '?\n', '! ', '!\n']
        
        best_cut = -1
        for ending in sentence_endings:
            pos = truncated.rfind(ending)
            if pos > max_length * 0.7:  # At least 70% of content
                best_cut = max(best_cut, pos + len(ending))
        
        if best_cut > 0:
            return content[:best_cut]
        
        # Fallback: cut at word boundary
        last_space = truncated.rfind(' ')
        if last_space > max_length * 0.8:
            return content[:last_space] + "..."
        
        return truncated + "..."
    
    def _apply_global_size_limits(self, items: List[Dict[str, str]], query: str) -> List[Dict[str, str]]:
        """Apply global size limits across all items"""
        total_size = sum(len(item.get('raw_content', '')) for item in items)
        
        if total_size <= self.config.max_total_tokens * 0.75:  # 75% of limit for safety
            return items
        
        logger.info(f"🔧 Applying global size limits: {total_size:,} chars > {self.config.max_total_tokens * 0.75:,}")
        
        # Sort items by relevance to query
        if query:
            scored_items = []
            query_words = set(query.lower().split())
            
            for item in items:
                content = item.get('raw_content', '').lower()
                content_words = set(content.split())
                relevance_score = len(query_words.intersection(content_words))
                scored_items.append((relevance_score, item))
            
            scored_items.sort(key=lambda x: x[0], reverse=True)
            items = [item for score, item in scored_items]
        
        # Keep items until we hit the limit
        final_items = []
        current_size = 0
        target_size = int(self.config.max_total_tokens * 0.75)
        
        for item in items:
            item_size = len(item.get('raw_content', ''))
            if current_size + item_size <= target_size:
                final_items.append(item)
                current_size += item_size
            else:
                # Try to fit a truncated version
                remaining_space = target_size - current_size
                if remaining_space > 1000:  # Only if we have reasonable space
                    truncated_content = item.get('raw_content', '')[:remaining_space]
                    final_items.append({
                        'url': item['url'],
                        'raw_content': truncated_content
                    })
                break
        
        logger.info(f"✅ Global size optimization: {len(items)} → {len(final_items)} URLs, {current_size:,} chars")
        return final_items
    
    def _remove_duplicate_content(self, items: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """Remove duplicate content based on content hashing"""
        unique_items = []
        seen_hashes = set()
        
        for item in items:
            content = item.get('raw_content', '')
            content_hash = hashlib.md5(content.encode()).hexdigest()
            
            if content_hash not in seen_hashes:
                seen_hashes.add(content_hash)
                unique_items.append(item)
            else:
                self._stats['duplicates_removed'] += 1
        
        if len(unique_items) < len(items):
            logger.info(f"🔄 Removed {len(items) - len(unique_items)} duplicate content items")
        
        return unique_items
    
    def _estimate_tokens(self, char_count: int) -> int:
        """Estimate token count from character count"""
        # Vietnamese text typically has ~3.5 chars per token
        return int(char_count / 3.5)
    
    def _update_stats(self, processing_time: float, reduction_ratio: float):
        """Update optimization statistics"""
        self._stats['total_processed'] += 1
        count = self._stats['total_processed']
        
        # Update averages
        self._stats['avg_processing_time'] = (
            (self._stats['avg_processing_time'] * (count - 1) + processing_time) / count
        )
        self._stats['size_reduction_ratio'] = (
            (self._stats['size_reduction_ratio'] * (count - 1) + reduction_ratio) / count
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get optimization statistics"""
        return self._stats.copy()

# Global optimizer instance
_content_size_optimizer: Optional[ContentSizeOptimizer] = None

def get_content_size_optimizer(config: Optional[SizeOptimizationConfig] = None) -> ContentSizeOptimizer:
    """Get or create global content size optimizer"""
    global _content_size_optimizer
    
    if _content_size_optimizer is None:
        _content_size_optimizer = ContentSizeOptimizer(config)
    
    return _content_size_optimizer

# Convenience functions
def optimize_batch_content(
    batch_data: List[Dict[str, str]], 
    query: str = ""
) -> Tuple[List[Dict[str, str]], Dict[str, Any]]:
    """Optimize batch content for size"""
    optimizer = get_content_size_optimizer()
    return optimizer.optimize_batch_content(batch_data, query)

def get_optimization_stats() -> Dict[str, Any]:
    """Get optimization statistics"""
    optimizer = get_content_size_optimizer()
    return optimizer.get_stats()