"""
Proxy-Enhanced DuckDuckGo Search
Wrapper for DuckDuckGo search with proxy rotation support
"""

import asyncio
import logging
import time
import random
from typing import List, Dict, Any, Optional
import aiohttp
import json
from urllib.parse import quote_plus
from .proxy_manager import ProxyManager, ProxyConfig

logger = logging.getLogger(__name__)

class ProxyDDGS:
    """
    DuckDuckGo search with proxy rotation support
    """
    
    def __init__(self, proxy_manager: Optional[ProxyManager] = None):
        self.proxy_manager = proxy_manager
        self.base_url = "https://duckduckgo.com"
        self.search_url = "https://html.duckduckgo.com/html/"
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def initialize(self):
        """Initialize the proxy DDGS client"""
        # Create session with appropriate settings
        connector = aiohttp.TCPConnector(
            limit=50,
            limit_per_host=10,
            ttl_dns_cache=300,
            use_dns_cache=True,
            ssl=False  # For proxy compatibility
        )
        
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=headers
        )
        
        logger.info("✅ Proxy DDGS client initialized")
    
    async def search_text(
        self,
        query: str,
        max_results: int = 15,
        region: str = "wt-wt",
        safesearch: str = "moderate"
    ) -> List[Dict[str, Any]]:
        """
        Search DuckDuckGo with proxy rotation
        """
        if not self.session:
            await self.initialize()
        
        # Get proxy if available
        proxy = None
        proxy_url = None
        if self.proxy_manager:
            proxy = self.proxy_manager.get_next_proxy()
            if proxy:
                proxy_url = proxy.to_url()
        
        start_time = time.time()
        
        try:
            # Prepare search parameters
            params = {
                'q': query,
                'kl': region,
                'safe': safesearch,
                's': '0',  # Start from first result
                'dc': str(max_results),
                'v': 'l',  # Use lite version
                'o': 'json'
            }
            
            # Add random delay to avoid rate limiting
            await asyncio.sleep(random.uniform(1.0, 2.5))
            
            # Perform search request
            async with self.session.get(
                self.search_url,
                params=params,
                proxy=proxy_url
            ) as response:
                
                response_time = time.time() - start_time
                
                if response.status == 200:
                    # Record successful request
                    if proxy and self.proxy_manager:
                        self.proxy_manager.record_request(proxy, True, response_time)
                    
                    # Parse HTML response
                    html_content = await response.text()
                    results = self._parse_search_results(html_content)
                    
                    logger.info(f"✅ Found {len(results)} results via {'proxy' if proxy else 'direct'} in {response_time:.2f}s")
                    return results[:max_results]
                
                elif response.status == 202:
                    # Rate limited
                    if proxy and self.proxy_manager:
                        self.proxy_manager.record_request(proxy, False, response_time)
                    
                    logger.warning(f"🚫 Rate limited (202) via {'proxy' if proxy else 'direct'}")
                    raise Exception(f"Rate limited: {response.status}")
                
                else:
                    # Other error
                    if proxy and self.proxy_manager:
                        self.proxy_manager.record_request(proxy, False, response_time)
                    
                    logger.warning(f"⚠️ Search failed with status {response.status}")
                    raise Exception(f"Search failed: {response.status}")
        
        except Exception as e:
            response_time = time.time() - start_time
            
            if proxy and self.proxy_manager:
                self.proxy_manager.record_request(proxy, False, response_time)
            
            logger.error(f"❌ Search error via {'proxy' if proxy else 'direct'}: {e}")
            raise
    
    def _parse_search_results(self, html_content: str) -> List[Dict[str, Any]]:
        """
        Parse DuckDuckGo HTML search results
        """
        results = []
        
        try:
            # Simple HTML parsing for DuckDuckGo results
            import re
            
            # Updated pattern for current DuckDuckGo HTML structure
            # Pattern 1: Standard web results
            title_pattern = r'<h2 class="result__title">\s*<a rel="nofollow" class="result__a" href="([^"]+)">([^<]+)</a>'
            snippet_pattern = r'<a class="result__snippet" href="[^"]*">([^<]+)</a>'
            
            # Find all titles and URLs
            title_matches = re.findall(title_pattern, html_content, re.DOTALL | re.IGNORECASE)
            
            # Find all snippets
            snippet_matches = re.findall(snippet_pattern, html_content, re.DOTALL | re.IGNORECASE)
            
            # Combine results (assuming same order)
            for i, (url, title) in enumerate(title_matches):
                # Clean up the extracted data
                title = re.sub(r'<[^>]+>', '', title).strip()
                url = url.strip()
                
                # Get corresponding snippet if available
                snippet = ""
                if i < len(snippet_matches):
                    snippet = re.sub(r'<[^>]+>', '', snippet_matches[i]).strip()
                
                if url and title and not url.startswith('https://duckduckgo.com/y.js'):  # Skip ads
                    results.append({
                        'title': title,
                        'href': url,
                        'body': snippet,
                        'rank': len(results) + 1
                    })
            
            # Fallback: try comprehensive pattern if no results
            if not results:
                # More comprehensive pattern
                comprehensive_pattern = r'<div class="result results_links results_links_deep web-result[^"]*"[^>]*>.*?<h2 class="result__title">\s*<a rel="nofollow" class="result__a" href="([^"]+)">([^<]+)</a>.*?<a class="result__snippet" href="[^"]*">([^<]+)</a>'
                comp_matches = re.findall(comprehensive_pattern, html_content, re.DOTALL | re.IGNORECASE)
                
                for i, (url, title, snippet) in enumerate(comp_matches):
                    title = re.sub(r'<[^>]+>', '', title).strip()
                    snippet = re.sub(r'<[^>]+>', '', snippet).strip()
                    url = url.strip()
                    
                    if url and title and not url.startswith('https://duckduckgo.com/y.js'):  # Skip ads
                        results.append({
                            'title': title,
                            'href': url,
                            'body': snippet,
                            'rank': i + 1
                        })
        
        except Exception as e:
            logger.warning(f"⚠️ HTML parsing error: {e}")
        
        return results
    
    async def search_news(
        self,
        query: str,
        max_results: int = 15,
        region: str = "wt-wt",
        safesearch: str = "moderate"
    ) -> List[Dict[str, Any]]:
        """
        Search DuckDuckGo News with proxy rotation
        """
        # For news, we'll use the regular search with news-specific parameters
        news_query = f"{query} site:news OR site:reuters.com OR site:bbc.com OR site:cnn.com"
        return await self.search_text(news_query, max_results, region, safesearch)
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.session:
            await self.session.close()
            self.session = None
        
        logger.info("✅ Proxy DDGS client cleanup completed")

# Enhanced search function with fallback
async def enhanced_ddgs_search(
    query: str,
    max_results: int = 15,
    search_type: str = "text",
    region: str = "wt-wt",
    safesearch: str = "moderate",
    proxy_manager: Optional[ProxyManager] = None,
    use_fallback: bool = True
) -> List[Dict[str, Any]]:
    """
    Enhanced DuckDuckGo search with proxy support and fallback
    """
    results = []
    
    # Try proxy-enhanced search first
    if proxy_manager:
        try:
            proxy_ddgs = ProxyDDGS(proxy_manager)
            await proxy_ddgs.initialize()
            
            if search_type == "news":
                results = await proxy_ddgs.search_news(query, max_results, region, safesearch)
            else:
                results = await proxy_ddgs.search_text(query, max_results, region, safesearch)
            
            await proxy_ddgs.cleanup()
            
            if results:
                logger.info(f"✅ Proxy search successful: {len(results)} results")
                return results
        
        except Exception as e:
            logger.warning(f"⚠️ Proxy search failed: {e}")
    
    # Fallback to original DuckDuckGo search
    if use_fallback and not results:
        try:
            logger.info("🔄 Falling back to direct DuckDuckGo search")
            
            from duckduckgo_search import DDGS
            
            # Removed delay - fail fast approach
            
            with DDGS() as ddgs:
                if search_type == "news":
                    results = list(ddgs.news(
                        keywords=query,
                        region=region,
                        safesearch=safesearch,
                        max_results=max_results
                    ))
                else:
                    results = list(ddgs.text(
                        keywords=query,
                        region=region,
                        safesearch=safesearch,
                        max_results=max_results
                    ))
            
            if results:
                logger.info(f"✅ Fallback search successful: {len(results)} results")
        
        except Exception as e:
            logger.error(f"❌ Fallback search also failed: {e}")
    
    return results