"""
Advanced Proxy Manager with Rotation
Manages proxy pools for rate limit avoidance and IP rotation
"""

import asyncio
import aiohttp
import logging
import time
import random
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from collections import deque
import json

logger = logging.getLogger(__name__)

@dataclass
class ProxyConfig:
    """Proxy configuration"""
    host: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    protocol: str = "http"  # http, https, socks4, socks5
    country: Optional[str] = None
    
    def to_url(self) -> str:
        """Convert to proxy URL"""
        if self.username and self.password:
            return f"{self.protocol}://{self.username}:{self.password}@{self.host}:{self.port}"
        return f"{self.protocol}://{self.host}:{self.port}"
    
    def to_dict(self) -> Dict[str, str]:
        """Convert to aiohttp proxy format"""
        return {
            "http": self.to_url(),
            "https": self.to_url()
        }

@dataclass
class ProxyStats:
    """Proxy usage statistics"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    last_used: float = 0
    avg_response_time: float = 0
    is_blocked: bool = False
    blocked_until: float = 0
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate"""
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests
    
    @property
    def is_available(self) -> bool:
        """Check if proxy is available (not blocked)"""
        if not self.is_blocked:
            return True
        return time.time() > self.blocked_until

class ProxyManager:
    """
    Advanced proxy manager with intelligent rotation and health monitoring
    """
    
    def __init__(self, config_file: Optional[str] = None):
        self.proxies: List[ProxyConfig] = []
        self.proxy_stats: Dict[str, ProxyStats] = {}
        self.current_proxy_index = 0
        self.rotation_strategy = "round_robin"  # round_robin, random, best_performance
        self.health_check_interval = 300  # 5 minutes
        self.block_duration = 1800  # 30 minutes
        
        # Load proxies from config
        if config_file:
            self._load_proxy_config(config_file)
        else:
            self._load_default_proxies()
        
        # Health monitoring
        self._health_check_task: Optional[asyncio.Task] = None
        
    def _load_proxy_config(self, config_file: str):
        """Load proxy configuration from file"""
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            for proxy_data in config.get('proxies', []):
                proxy = ProxyConfig(**proxy_data)
                self.proxies.append(proxy)
                self.proxy_stats[proxy.to_url()] = ProxyStats()
            
            logger.info(f"✅ Loaded {len(self.proxies)} proxies from {config_file}")
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to load proxy config: {e}")
            self._load_default_proxies()
    
    def _load_default_proxies(self):
        """Load default free proxy list (for testing)"""
        # Note: These are example free proxies - in production use paid proxy services
        default_proxies = [
            # Free proxy examples (may not work reliably)
            {"host": "proxy1.example.com", "port": 8080},
            {"host": "proxy2.example.com", "port": 3128},
            {"host": "proxy3.example.com", "port": 8000},
        ]
        
        for proxy_data in default_proxies:
            proxy = ProxyConfig(**proxy_data)
            self.proxies.append(proxy)
            self.proxy_stats[proxy.to_url()] = ProxyStats()
        
        logger.info(f"✅ Loaded {len(self.proxies)} default proxies")
    
    async def initialize(self):
        """Initialize proxy manager"""
        logger.info("🚀 Initializing proxy manager...")
        
        if not self.proxies:
            logger.warning("⚠️ No proxies configured - running without proxy rotation")
            return
        
        # Start health monitoring
        self._health_check_task = asyncio.create_task(self._periodic_health_check())
        
        # Initial health check
        await self._check_all_proxies_health()
        
        logger.info(f"✅ Proxy manager initialized with {len(self.proxies)} proxies")
    
    def get_next_proxy(self) -> Optional[ProxyConfig]:
        """Get next proxy based on rotation strategy"""
        if not self.proxies:
            return None
        
        available_proxies = [
            (i, proxy) for i, proxy in enumerate(self.proxies)
            if self.proxy_stats[proxy.to_url()].is_available
        ]
        
        if not available_proxies:
            logger.warning("⚠️ No available proxies - all are blocked")
            return None
        
        if self.rotation_strategy == "round_robin":
            return self._get_round_robin_proxy(available_proxies)
        elif self.rotation_strategy == "random":
            return self._get_random_proxy(available_proxies)
        elif self.rotation_strategy == "best_performance":
            return self._get_best_performance_proxy(available_proxies)
        else:
            return available_proxies[0][1]  # Fallback
    
    def _get_round_robin_proxy(self, available_proxies: List[tuple]) -> ProxyConfig:
        """Round robin proxy selection"""
        # Find next available proxy after current index
        for i, (index, proxy) in enumerate(available_proxies):
            if index >= self.current_proxy_index:
                self.current_proxy_index = (index + 1) % len(self.proxies)
                return proxy
        
        # Wrap around to beginning
        self.current_proxy_index = (available_proxies[0][0] + 1) % len(self.proxies)
        return available_proxies[0][1]
    
    def _get_random_proxy(self, available_proxies: List[tuple]) -> ProxyConfig:
        """Random proxy selection"""
        return random.choice(available_proxies)[1]
    
    def _get_best_performance_proxy(self, available_proxies: List[tuple]) -> ProxyConfig:
        """Select proxy with best performance"""
        best_proxy = None
        best_score = -1
        
        for _, proxy in available_proxies:
            stats = self.proxy_stats[proxy.to_url()]
            # Score based on success rate and response time
            score = stats.success_rate * (1 / max(stats.avg_response_time, 0.1))
            
            if score > best_score:
                best_score = score
                best_proxy = proxy
        
        return best_proxy or available_proxies[0][1]
    
    def record_request(self, proxy: ProxyConfig, success: bool, response_time: float):
        """Record proxy request statistics"""
        proxy_url = proxy.to_url()
        stats = self.proxy_stats[proxy_url]
        
        stats.total_requests += 1
        stats.last_used = time.time()
        
        if success:
            stats.successful_requests += 1
        else:
            stats.failed_requests += 1
            
            # Block proxy if too many failures
            if stats.success_rate < 0.3 and stats.total_requests > 5:
                stats.is_blocked = True
                stats.blocked_until = time.time() + self.block_duration
                logger.warning(f"🚫 Blocked proxy {proxy.host}:{proxy.port} due to low success rate")
        
        # Update average response time
        if stats.total_requests == 1:
            stats.avg_response_time = response_time
        else:
            stats.avg_response_time = (stats.avg_response_time * (stats.total_requests - 1) + response_time) / stats.total_requests
    
    async def test_proxy(self, proxy: ProxyConfig, timeout: float = 10) -> bool:
        """Test if proxy is working"""
        try:
            start_time = time.time()
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    "http://httpbin.org/ip",
                    proxy=proxy.to_url(),
                    timeout=aiohttp.ClientTimeout(total=timeout)
                ) as response:
                    if response.status == 200:
                        response_time = time.time() - start_time
                        self.record_request(proxy, True, response_time)
                        return True
                    else:
                        self.record_request(proxy, False, time.time() - start_time)
                        return False
                        
        except Exception as e:
            logger.debug(f"Proxy test failed for {proxy.host}:{proxy.port}: {e}")
            self.record_request(proxy, False, timeout)
            return False
    
    async def _check_all_proxies_health(self):
        """Check health of all proxies"""
        if not self.proxies:
            return
        
        logger.info("🔍 Checking proxy health...")
        
        tasks = [self.test_proxy(proxy) for proxy in self.proxies]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        healthy_count = sum(1 for result in results if result is True)
        logger.info(f"✅ Proxy health check: {healthy_count}/{len(self.proxies)} proxies healthy")
    
    async def _periodic_health_check(self):
        """Periodic health check for proxies"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._check_all_proxies_health()
                
                # Unblock proxies that have been blocked long enough
                current_time = time.time()
                for stats in self.proxy_stats.values():
                    if stats.is_blocked and current_time > stats.blocked_until:
                        stats.is_blocked = False
                        logger.info("✅ Unblocked proxy after cooldown period")
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"⚠️ Proxy health check error: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get proxy manager statistics"""
        if not self.proxies:
            return {"total_proxies": 0, "available_proxies": 0}
        
        available_count = sum(
            1 for stats in self.proxy_stats.values() 
            if stats.is_available
        )
        
        total_requests = sum(stats.total_requests for stats in self.proxy_stats.values())
        total_successful = sum(stats.successful_requests for stats in self.proxy_stats.values())
        
        return {
            "total_proxies": len(self.proxies),
            "available_proxies": available_count,
            "blocked_proxies": len(self.proxies) - available_count,
            "total_requests": total_requests,
            "success_rate": total_successful / total_requests if total_requests > 0 else 0,
            "rotation_strategy": self.rotation_strategy
        }
    
    async def cleanup(self):
        """Cleanup proxy manager"""
        logger.info("🧹 Cleaning up proxy manager...")
        
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        logger.info("✅ Proxy manager cleanup completed")

# Enhanced connection pool with proxy support
class ProxyAwareConnectionPool:
    """
    Connection pool with integrated proxy rotation
    """
    
    def __init__(self, proxy_manager: Optional[ProxyManager] = None):
        self.proxy_manager = proxy_manager
        self.sessions: Dict[str, aiohttp.ClientSession] = {}
        self._lock = asyncio.Lock()
    
    async def get_session_with_proxy(self) -> tuple[aiohttp.ClientSession, Optional[ProxyConfig]]:
        """Get session with rotated proxy"""
        proxy = None
        if self.proxy_manager:
            proxy = self.proxy_manager.get_next_proxy()
        
        session_key = proxy.to_url() if proxy else "no_proxy"
        
        async with self._lock:
            if session_key not in self.sessions:
                # Create session with proxy
                connector = aiohttp.TCPConnector(
                    limit=100,
                    limit_per_host=10,
                    ttl_dns_cache=300,
                    use_dns_cache=True
                )
                
                timeout = aiohttp.ClientTimeout(total=30)
                
                self.sessions[session_key] = aiohttp.ClientSession(
                    connector=connector,
                    timeout=timeout
                )
        
        return self.sessions[session_key], proxy
    
    async def cleanup(self):
        """Cleanup all sessions"""
        async with self._lock:
            for session in self.sessions.values():
                await session.close()
            self.sessions.clear()

# Global proxy manager instance
_proxy_manager: Optional[ProxyManager] = None

async def get_proxy_manager(config_file: Optional[str] = None) -> ProxyManager:
    """Get or create global proxy manager"""
    global _proxy_manager
    
    if _proxy_manager is None:
        _proxy_manager = ProxyManager(config_file)
        await _proxy_manager.initialize()
    
    return _proxy_manager

# Convenience functions
async def get_proxy_session() -> tuple[aiohttp.ClientSession, Optional[ProxyConfig]]:
    """Get session with proxy rotation"""
    proxy_manager = await get_proxy_manager()
    pool = ProxyAwareConnectionPool(proxy_manager)
    return await pool.get_session_with_proxy()