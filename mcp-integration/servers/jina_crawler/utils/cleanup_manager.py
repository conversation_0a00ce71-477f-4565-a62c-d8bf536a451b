"""
Cleanup Manager - Comprehensive resource cleanup utilities
"""

import asyncio
import gc
import logging
import weakref
from typing import List, Any, Optional
import aiohttp

logger = logging.getLogger(__name__)

class CleanupManager:
    """Comprehensive cleanup manager for all resources"""
    
    def __init__(self):
        self._sessions: List[weakref.ref] = []
        self._connectors: List[weakref.ref] = []
        self._tls_sessions: List[Any] = []
        self._cleanup_callbacks: List[callable] = []
        
    def register_session(self, session: aiohttp.ClientSession):
        """Register an aiohttp session for cleanup"""
        self._sessions.append(weakref.ref(session))
        
    def register_connector(self, connector: aiohttp.TCPConnector):
        """Register a TCP connector for cleanup"""
        self._connectors.append(weakref.ref(connector))
        
    def register_tls_session(self, tls_session: Any):
        """Register a TLS session for cleanup"""
        self._tls_sessions.append(tls_session)
        
    def register_cleanup_callback(self, callback: callable):
        """Register a cleanup callback"""
        self._cleanup_callbacks.append(callback)
        
    async def cleanup_all(self, force: bool = True, delay: float = 0.5, gc_cycles: int = 5):
        """Comprehensive cleanup of all registered resources"""
        logger.info("🧹 Starting comprehensive cleanup...")
        
        try:
            # 1. Execute custom cleanup callbacks
            await self._cleanup_callbacks_async()
            
            # 2. Cleanup TLS sessions
            await self._cleanup_tls_sessions()
            
            # 3. Cleanup aiohttp sessions
            await self._cleanup_aiohttp_sessions()
            
            # 4. Cleanup connectors
            await self._cleanup_connectors()
            
            # 5. Wait for pending operations
            if delay > 0:
                await asyncio.sleep(delay)
            
            # 6. Force garbage collection
            await self._force_garbage_collection(gc_cycles)
            
            # 7. Final cleanup check
            await self._final_cleanup_check()
            
            logger.info("✅ Comprehensive cleanup completed")
            
        except Exception as e:
            logger.error(f"❌ Cleanup error: {e}")
            
    async def _cleanup_callbacks_async(self):
        """Execute all cleanup callbacks"""
        for callback in self._cleanup_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback()
                else:
                    callback()
            except Exception as e:
                logger.warning(f"⚠️ Cleanup callback error: {e}")
        
        self._cleanup_callbacks.clear()
        
    async def _cleanup_tls_sessions(self):
        """Cleanup TLS sessions"""
        for tls_session in self._tls_sessions:
            try:
                if hasattr(tls_session, 'close'):
                    tls_session.close()
                elif hasattr(tls_session, 'session') and hasattr(tls_session.session, 'close'):
                    tls_session.session.close()
            except Exception as e:
                logger.warning(f"⚠️ TLS session cleanup warning: {e}")
        
        self._tls_sessions.clear()
        
    async def _cleanup_aiohttp_sessions(self):
        """Cleanup aiohttp sessions with enhanced tracking"""
        active_sessions = []

        for session_ref in self._sessions:
            session = session_ref()
            if session is not None:
                active_sessions.append(session)

        logger.debug(f"🧹 Cleaning up {len(active_sessions)} active sessions")

        # Close all active sessions with proper error handling
        for i, session in enumerate(active_sessions):
            try:
                if not session.closed:
                    logger.debug(f"🔒 Closing session {i+1}/{len(active_sessions)}")
                    await session.close()
                    # Wait longer for session to fully close
                    await asyncio.sleep(0.2)
                    logger.debug(f"✅ Session {i+1} closed successfully")
                else:
                    logger.debug(f"⏭️ Session {i+1} already closed")
            except Exception as e:
                logger.warning(f"⚠️ Session {i+1} cleanup warning: {e}")

        # Clear the session list
        self._sessions.clear()
        logger.debug(f"✅ All {len(active_sessions)} sessions cleaned up")
        
    async def _cleanup_connectors(self):
        """Cleanup TCP connectors"""
        active_connectors = []
        
        for connector_ref in self._connectors:
            connector = connector_ref()
            if connector is not None:
                active_connectors.append(connector)
        
        # Close all active connectors
        for connector in active_connectors:
            try:
                if not connector.closed:
                    await connector.close()
                    # Wait a bit for the connector to fully close
                    await asyncio.sleep(0.1)
            except Exception as e:
                logger.warning(f"⚠️ Connector cleanup warning: {e}")
        
        self._connectors.clear()
        
    async def _force_garbage_collection(self, cycles: int = 5):
        """Force garbage collection multiple times"""
        for i in range(cycles):
            gc.collect()
            await asyncio.sleep(0.1)
            
        # Additional cleanup for specific types
        gc.collect()
        
    async def _final_cleanup_check(self):
        """Final cleanup check and warnings"""
        # Check for any remaining unclosed resources
        try:
            # Force one more garbage collection
            gc.collect()
            await asyncio.sleep(0.2)
            
            # Log cleanup completion
            logger.debug("🔍 Final cleanup check completed")
            
        except Exception as e:
            logger.warning(f"⚠️ Final cleanup check warning: {e}")

# Global cleanup manager instance
_global_cleanup_manager: Optional[CleanupManager] = None

def get_cleanup_manager() -> CleanupManager:
    """Get the global cleanup manager instance"""
    global _global_cleanup_manager
    if _global_cleanup_manager is None:
        _global_cleanup_manager = CleanupManager()
    return _global_cleanup_manager

async def cleanup_all_resources(force: bool = True, delay: float = 0.5, gc_cycles: int = 5):
    """Cleanup all registered resources using the global manager with aggressive session cleanup"""
    manager = get_cleanup_manager()
    await manager.cleanup_all(force=force, delay=delay, gc_cycles=gc_cycles)

    # AGGRESSIVE: Force close any remaining aiohttp sessions
    await _force_close_all_aiohttp_sessions()


async def _force_close_all_aiohttp_sessions():
    """Force close any remaining aiohttp sessions in the process"""
    try:
        import gc
        import aiohttp

        # Find all aiohttp.ClientSession objects in memory
        sessions_found = 0
        sessions_closed = 0

        for obj in gc.get_objects():
            if isinstance(obj, aiohttp.ClientSession):
                sessions_found += 1
                try:
                    if not obj.closed:
                        await obj.close()
                        sessions_closed += 1
                        logger.debug(f"🔒 Force closed aiohttp session {sessions_closed}")
                        await asyncio.sleep(0.05)
                except Exception as e:
                    logger.debug(f"Force close session warning: {e}")

        if sessions_found > 0:
            logger.debug(f"🧹 Force cleanup: {sessions_closed}/{sessions_found} sessions closed")

    except Exception as e:
        logger.debug(f"Force session cleanup warning: {e}")

def register_session_for_cleanup(session: aiohttp.ClientSession):
    """Register a session for cleanup"""
    manager = get_cleanup_manager()
    manager.register_session(session)

def register_connector_for_cleanup(connector: aiohttp.TCPConnector):
    """Register a connector for cleanup"""
    manager = get_cleanup_manager()
    manager.register_connector(connector)

def register_tls_session_for_cleanup(tls_session: Any):
    """Register a TLS session for cleanup"""
    manager = get_cleanup_manager()
    manager.register_tls_session(tls_session)

def register_cleanup_callback(callback: callable):
    """Register a cleanup callback"""
    manager = get_cleanup_manager()
    manager.register_cleanup_callback(callback)

class CleanupContext:
    """Context manager for automatic cleanup"""
    
    def __init__(self, delay: float = 0.5, gc_cycles: int = 5):
        self.delay = delay
        self.gc_cycles = gc_cycles
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await cleanup_all_resources(delay=self.delay, gc_cycles=self.gc_cycles)

# Decorator for automatic cleanup
def with_cleanup(delay: float = 0.5, gc_cycles: int = 5):
    """Decorator to automatically cleanup resources after function execution"""
    def decorator(func):
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    await cleanup_all_resources(delay=delay, gc_cycles=gc_cycles)
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    # For sync functions, we can't await, so just do basic cleanup
                    gc.collect()
            return sync_wrapper
    return decorator
