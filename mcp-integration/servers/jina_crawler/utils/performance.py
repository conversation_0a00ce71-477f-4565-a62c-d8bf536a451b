"""
Performance optimization utilities for the ReaderLM Web Crawler.
"""

import asyncio
import time
import psutil
import gc
from typing import Dict, Any, Optional, Callable
from functools import wraps
from contextlib import asynccontextmanager

from .logging import get_logger

logger = get_logger(__name__)


class PerformanceMonitor:
    """Monitor system performance and resource usage."""
    
    def __init__(self):
        self.start_time = time.time()
        self.request_count = 0
        self.total_processing_time = 0.0
        self.peak_memory_usage = 0
        
    def record_request(self, processing_time: float):
        """Record a request's processing time."""
        self.request_count += 1
        self.total_processing_time += processing_time
        
        # Monitor memory usage
        current_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        if current_memory > self.peak_memory_usage:
            self.peak_memory_usage = current_memory
    
    def get_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        uptime = time.time() - self.start_time
        avg_processing_time = (
            self.total_processing_time / self.request_count 
            if self.request_count > 0 else 0
        )
        
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            "uptime_seconds": uptime,
            "total_requests": self.request_count,
            "requests_per_second": self.request_count / uptime if uptime > 0 else 0,
            "average_processing_time": avg_processing_time,
            "peak_memory_mb": self.peak_memory_usage,
            "current_memory_mb": memory_info.rss / 1024 / 1024,
            "memory_percent": process.memory_percent(),
            "cpu_percent": process.cpu_percent(),
            "open_files": len(process.open_files()),
            "connections": len(process.connections())
        }


# Global performance monitor
performance_monitor = PerformanceMonitor()


def performance_tracker(operation_name: str):
    """Decorator to track performance of async functions."""
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                processing_time = time.time() - start_time
                performance_monitor.record_request(processing_time)
                
                logger.debug(
                    f"Performance: {operation_name} completed",
                    extra={
                        "operation": operation_name,
                        "processing_time": processing_time,
                        "memory_mb": psutil.Process().memory_info().rss / 1024 / 1024
                    }
                )
                return result
            except Exception as e:
                processing_time = time.time() - start_time
                logger.error(
                    f"Performance: {operation_name} failed",
                    extra={
                        "operation": operation_name,
                        "processing_time": processing_time,
                        "error": str(e)
                    }
                )
                raise
        return wrapper
    return decorator


@asynccontextmanager
async def resource_limiter(max_concurrent: int = 10):
    """Context manager to limit concurrent resource usage."""
    semaphore = asyncio.Semaphore(max_concurrent)
    async with semaphore:
        yield


class MemoryManager:
    """Manage memory usage and garbage collection."""
    
    def __init__(self, memory_threshold_mb: int = 512):
        self.memory_threshold_mb = memory_threshold_mb
        self.last_gc_time = time.time()
        self.gc_interval = 60  # seconds
    
    def check_memory_usage(self) -> bool:
        """Check if memory usage exceeds threshold."""
        current_memory = psutil.Process().memory_info().rss / 1024 / 1024
        return current_memory > self.memory_threshold_mb
    
    async def cleanup_if_needed(self):
        """Perform cleanup if memory usage is high."""
        current_time = time.time()
        
        # Force garbage collection periodically or when memory is high
        if (current_time - self.last_gc_time > self.gc_interval or 
            self.check_memory_usage()):
            
            logger.info("Performing memory cleanup")
            
            # Force garbage collection
            collected = gc.collect()
            
            # Log memory stats
            memory_after = psutil.Process().memory_info().rss / 1024 / 1024
            logger.info(
                f"Memory cleanup completed: collected {collected} objects, "
                f"memory usage: {memory_after:.1f}MB"
            )
            
            self.last_gc_time = current_time


# Global memory manager
memory_manager = MemoryManager()


class RequestThrottler:
    """Throttle requests to prevent resource exhaustion."""
    
    def __init__(self, max_requests_per_second: int = 10):
        self.max_requests_per_second = max_requests_per_second
        self.request_times = []
    
    async def throttle(self):
        """Throttle requests based on rate limit."""
        current_time = time.time()
        
        # Remove old request times (older than 1 second)
        self.request_times = [
            t for t in self.request_times 
            if current_time - t < 1.0
        ]
        
        # Check if we need to wait
        if len(self.request_times) >= self.max_requests_per_second:
            sleep_time = 1.0 - (current_time - self.request_times[0])
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
        
        # Record this request
        self.request_times.append(current_time)


# Global request throttler
request_throttler = RequestThrottler()


class CacheOptimizer:
    """Optimize caching strategies for better performance."""
    
    def __init__(self, max_cache_size: int = 1000):
        self.max_cache_size = max_cache_size
        self.cache_hits = 0
        self.cache_misses = 0
    
    def record_hit(self):
        """Record a cache hit."""
        self.cache_hits += 1
    
    def record_miss(self):
        """Record a cache miss."""
        self.cache_misses += 1
    
    def get_hit_ratio(self) -> float:
        """Get cache hit ratio."""
        total = self.cache_hits + self.cache_misses
        return self.cache_hits / total if total > 0 else 0.0
    
    def should_cache(self, content_size: int) -> bool:
        """Determine if content should be cached based on size."""
        # Don't cache very large content (>1MB)
        return content_size < 1024 * 1024
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "hit_ratio": self.get_hit_ratio(),
            "max_cache_size": self.max_cache_size
        }


# Global cache optimizer
cache_optimizer = CacheOptimizer()


async def optimize_browser_settings() -> Dict[str, Any]:
    """Get optimized browser settings for resource efficiency."""
    return {
        "headless": True,
        "args": [
            "--no-sandbox",
            "--disable-setuid-sandbox",
            "--disable-dev-shm-usage",
            "--disable-accelerated-2d-canvas",
            "--no-first-run",
            "--no-zygote",
            "--disable-gpu",
            "--disable-extensions",
            "--disable-plugins",
            "--disable-background-timer-throttling",
            "--disable-renderer-backgrounding",
            "--disable-backgrounding-occluded-windows",
            "--disable-ipc-flooding-protection",
            "--disable-hang-monitor",
            "--disable-prompt-on-repost",
            "--disable-sync",
            "--disable-translate",
            "--disable-web-security",
            "--memory-pressure-off",
            "--max_old_space_size=4096"
        ]
    }


def get_system_resources() -> Dict[str, Any]:
    """Get current system resource usage."""
    process = psutil.Process()
    
    return {
        "cpu_percent": process.cpu_percent(),
        "memory_percent": process.memory_percent(),
        "memory_mb": process.memory_info().rss / 1024 / 1024,
        "open_files": len(process.open_files()),
        "connections": len(process.connections()),
        "threads": process.num_threads(),
        "system_cpu_percent": psutil.cpu_percent(),
        "system_memory_percent": psutil.virtual_memory().percent,
        "system_disk_percent": psutil.disk_usage('/').percent
    }


async def health_check_resources() -> Dict[str, Any]:
    """Perform resource health check."""
    resources = get_system_resources()
    performance_stats = performance_monitor.get_stats()
    cache_stats = cache_optimizer.get_cache_stats()
    
    # Determine health status
    status = "healthy"
    issues = []
    
    if resources["memory_percent"] > 80:
        status = "warning"
        issues.append("High memory usage")
    
    if resources["cpu_percent"] > 80:
        status = "warning"
        issues.append("High CPU usage")
    
    if resources["system_memory_percent"] > 90:
        status = "critical"
        issues.append("System memory critical")
    
    return {
        "status": status,
        "issues": issues,
        "resources": resources,
        "performance": performance_stats,
        "cache": cache_stats
    }