"""
Advanced Memory Optimization Module
Implements intelligent memory management, garbage collection, and resource optimization
"""

import gc
import asyncio
import logging
import psutil
import time
import weakref
from typing import Dict, Any, Optional, List, Set, Callable
from dataclasses import dataclass
from collections import defaultdict
import threading

logger = logging.getLogger(__name__)

@dataclass
class MemoryConfig:
    """Configuration for memory optimization"""
    max_memory_mb: int = 512
    gc_threshold_mb: int = 256
    gc_interval: float = 30.0  # seconds
    aggressive_gc_threshold: float = 0.8  # 80% of max memory
    enable_object_tracking: bool = True
    enable_weak_references: bool = True
    cleanup_interval: float = 60.0  # seconds

class MemoryOptimizer:
    """
    Advanced memory optimizer with intelligent garbage collection and resource management
    """
    
    def __init__(self, config: Optional[MemoryConfig] = None):
        self.config = config or MemoryConfig()
        
        # Memory tracking
        self._process = psutil.Process()
        self._peak_memory = 0
        self._gc_count = 0
        self._last_gc_time = 0
        
        # Object tracking
        self._tracked_objects: Set[weakref.ref] = set()
        self._object_counts = defaultdict(int)
        self._cleanup_callbacks: List[Callable] = []
        
        # Background tasks
        self._gc_task: Optional[asyncio.Task] = None
        self._cleanup_task: Optional[asyncio.Task] = None
        self._monitoring_task: Optional[asyncio.Task] = None
        
        # Statistics
        self._stats = {
            'gc_runs': 0,
            'objects_collected': 0,
            'memory_freed_mb': 0.0,
            'peak_memory_mb': 0.0,
            'avg_memory_mb': 0.0,
            'cleanup_runs': 0
        }
        
        # Thread safety
        self._lock = threading.Lock()
    
    async def initialize(self):
        """Initialize the memory optimizer"""
        logger.info("🚀 Initializing memory optimizer...")
        
        # Start background tasks
        self._gc_task = asyncio.create_task(self._periodic_gc())
        self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
        self._monitoring_task = asyncio.create_task(self._memory_monitoring())
        
        # Set initial memory baseline
        self._peak_memory = self.get_memory_usage()
        
        logger.info(f"✅ Memory optimizer initialized (max: {self.config.max_memory_mb}MB)")
    
    def get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        try:
            memory_info = self._process.memory_info()
            return memory_info.rss / 1024 / 1024  # Convert to MB
        except Exception as e:
            logger.warning(f"⚠️ Failed to get memory usage: {e}")
            return 0.0
    
    def get_memory_percent(self) -> float:
        """Get memory usage as percentage of configured maximum"""
        current_memory = self.get_memory_usage()
        return (current_memory / self.config.max_memory_mb) * 100
    
    async def force_gc(self, aggressive: bool = False) -> Dict[str, Any]:
        """
        Force garbage collection with optional aggressive mode
        
        Args:
            aggressive: Enable aggressive garbage collection
            
        Returns:
            Dictionary with GC results
        """
        start_time = time.time()
        memory_before = self.get_memory_usage()
        
        try:
            logger.debug(f"🧹 Starting {'aggressive ' if aggressive else ''}garbage collection...")
            
            # Standard garbage collection
            collected_objects = 0
            for generation in range(3):  # GC has 3 generations
                collected = gc.collect(generation)
                collected_objects += collected
                
                if aggressive:
                    # Additional cleanup for aggressive mode
                    await asyncio.sleep(0.01)  # Allow other tasks to run
            
            # Clean up tracked objects
            if self.config.enable_object_tracking:
                self._cleanup_tracked_objects()
            
            # Run cleanup callbacks
            await self._run_cleanup_callbacks()
            
            # Update statistics
            memory_after = self.get_memory_usage()
            memory_freed = memory_before - memory_after
            gc_time = time.time() - start_time
            
            with self._lock:
                self._stats['gc_runs'] += 1
                self._stats['objects_collected'] += collected_objects
                self._stats['memory_freed_mb'] += max(0, memory_freed)
                self._gc_count += 1
                self._last_gc_time = time.time()
            
            result = {
                'success': True,
                'objects_collected': collected_objects,
                'memory_before_mb': memory_before,
                'memory_after_mb': memory_after,
                'memory_freed_mb': max(0, memory_freed),
                'gc_time': gc_time,
                'aggressive': aggressive
            }
            
            logger.info(f"✅ GC completed: {collected_objects} objects, {memory_freed:.1f}MB freed in {gc_time:.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"❌ Garbage collection failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'memory_before_mb': memory_before,
                'gc_time': time.time() - start_time
            }
    
    def track_object(self, obj: Any, category: str = "general") -> bool:
        """
        Track an object for memory management
        
        Args:
            obj: Object to track
            category: Category for grouping
            
        Returns:
            True if tracking was successful
        """
        if not self.config.enable_object_tracking:
            return False
        
        try:
            # Create weak reference to avoid circular references
            weak_ref = weakref.ref(obj, self._object_cleanup_callback)
            
            with self._lock:
                self._tracked_objects.add(weak_ref)
                self._object_counts[category] += 1
            
            return True
            
        except Exception as e:
            logger.debug(f"⚠️ Failed to track object: {e}")
            return False
    
    def register_cleanup_callback(self, callback: Callable):
        """Register a cleanup callback function"""
        if callable(callback):
            self._cleanup_callbacks.append(callback)
    
    async def check_memory_pressure(self) -> Dict[str, Any]:
        """Check if system is under memory pressure"""
        current_memory = self.get_memory_usage()
        memory_percent = self.get_memory_percent()
        
        # System memory check
        system_memory = psutil.virtual_memory()
        system_pressure = system_memory.percent > 85
        
        # Process memory check
        process_pressure = memory_percent > (self.config.aggressive_gc_threshold * 100)
        
        pressure_level = "none"
        if system_pressure or process_pressure:
            if memory_percent > 90 or system_memory.percent > 95:
                pressure_level = "critical"
            elif memory_percent > 80 or system_memory.percent > 90:
                pressure_level = "high"
            else:
                pressure_level = "moderate"
        
        return {
            'pressure_level': pressure_level,
            'process_memory_mb': current_memory,
            'process_memory_percent': memory_percent,
            'system_memory_percent': system_memory.percent,
            'system_available_mb': system_memory.available / 1024 / 1024,
            'needs_gc': process_pressure,
            'needs_aggressive_gc': pressure_level in ['high', 'critical']
        }
    
    async def optimize_memory(self) -> Dict[str, Any]:
        """Perform comprehensive memory optimization"""
        start_time = time.time()
        
        try:
            logger.info("🔧 Starting memory optimization...")
            
            # Check memory pressure
            pressure_info = await self.check_memory_pressure()
            
            # Determine optimization strategy
            if pressure_info['pressure_level'] == 'critical':
                # Critical: Aggressive cleanup
                gc_result = await self.force_gc(aggressive=True)
                await self._emergency_cleanup()
            elif pressure_info['pressure_level'] in ['high', 'moderate']:
                # High/Moderate: Standard cleanup
                gc_result = await self.force_gc(aggressive=False)
            else:
                # Low pressure: Light cleanup
                gc_result = await self.force_gc(aggressive=False)
            
            # Update peak memory tracking
            current_memory = self.get_memory_usage()
            if current_memory > self._peak_memory:
                self._peak_memory = current_memory
                self._stats['peak_memory_mb'] = current_memory
            
            optimization_time = time.time() - start_time
            
            result = {
                'success': True,
                'optimization_time': optimization_time,
                'pressure_before': pressure_info,
                'gc_result': gc_result,
                'memory_after_mb': current_memory,
                'peak_memory_mb': self._peak_memory
            }
            
            logger.info(f"✅ Memory optimization completed in {optimization_time:.3f}s")
            return result
            
        except Exception as e:
            logger.error(f"❌ Memory optimization failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'optimization_time': time.time() - start_time
            }
    
    async def _periodic_gc(self):
        """Background task for periodic garbage collection"""
        while True:
            try:
                await asyncio.sleep(self.config.gc_interval)
                
                # Check if GC is needed
                memory_percent = self.get_memory_percent()
                time_since_last_gc = time.time() - self._last_gc_time
                
                should_gc = (
                    memory_percent > (self.config.aggressive_gc_threshold * 100) or
                    time_since_last_gc > (self.config.gc_interval * 2)
                )
                
                if should_gc:
                    aggressive = memory_percent > 90
                    await self.force_gc(aggressive=aggressive)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"⚠️ Periodic GC error: {e}")
    
    async def _periodic_cleanup(self):
        """Background task for periodic cleanup"""
        while True:
            try:
                await asyncio.sleep(self.config.cleanup_interval)
                
                # Clean up tracked objects
                if self.config.enable_object_tracking:
                    self._cleanup_tracked_objects()
                
                # Run cleanup callbacks
                await self._run_cleanup_callbacks()
                
                with self._lock:
                    self._stats['cleanup_runs'] += 1
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"⚠️ Periodic cleanup error: {e}")
    
    async def _memory_monitoring(self):
        """Background task for memory monitoring"""
        memory_samples = []
        
        while True:
            try:
                await asyncio.sleep(10)  # Monitor every 10 seconds
                
                current_memory = self.get_memory_usage()
                memory_samples.append(current_memory)
                
                # Keep only last 100 samples (about 16 minutes)
                if len(memory_samples) > 100:
                    memory_samples.pop(0)
                
                # Update average
                if memory_samples:
                    with self._lock:
                        self._stats['avg_memory_mb'] = sum(memory_samples) / len(memory_samples)
                
                # Check for memory leaks (steadily increasing memory)
                if len(memory_samples) >= 10:
                    recent_avg = sum(memory_samples[-5:]) / 5
                    older_avg = sum(memory_samples[-10:-5]) / 5
                    
                    if recent_avg > older_avg * 1.2:  # 20% increase
                        logger.warning(f"⚠️ Potential memory leak detected: {older_avg:.1f}MB → {recent_avg:.1f}MB")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"⚠️ Memory monitoring error: {e}")
    
    def _cleanup_tracked_objects(self):
        """Clean up dead weak references"""
        try:
            with self._lock:
                # Remove dead references
                dead_refs = {ref for ref in self._tracked_objects if ref() is None}
                self._tracked_objects -= dead_refs
                
                logger.debug(f"🧹 Cleaned up {len(dead_refs)} dead object references")
                
        except Exception as e:
            logger.warning(f"⚠️ Object cleanup failed: {e}")
    
    def _object_cleanup_callback(self, weak_ref):
        """Callback when tracked object is garbage collected"""
        try:
            with self._lock:
                self._tracked_objects.discard(weak_ref)
        except Exception:
            pass  # Ignore errors in cleanup callback
    
    async def _run_cleanup_callbacks(self):
        """Run all registered cleanup callbacks"""
        for callback in self._cleanup_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback()
                else:
                    callback()
            except Exception as e:
                logger.warning(f"⚠️ Cleanup callback failed: {e}")
    
    async def _emergency_cleanup(self):
        """Emergency cleanup for critical memory pressure"""
        logger.warning("🚨 Emergency memory cleanup activated")
        
        try:
            # Clear all tracked objects
            with self._lock:
                self._tracked_objects.clear()
                self._object_counts.clear()
            
            # Multiple aggressive GC passes
            for i in range(3):
                gc.collect()
                await asyncio.sleep(0.1)
            
            # Run all cleanup callbacks
            await self._run_cleanup_callbacks()
            
            logger.info("✅ Emergency cleanup completed")
            
        except Exception as e:
            logger.error(f"❌ Emergency cleanup failed: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get memory optimizer statistics"""
        current_memory = self.get_memory_usage()
        
        with self._lock:
            stats = self._stats.copy()
            stats.update({
                'current_memory_mb': current_memory,
                'peak_memory_mb': self._peak_memory,
                'memory_usage_percent': self.get_memory_percent(),
                'tracked_objects': len(self._tracked_objects),
                'object_counts': dict(self._object_counts),
                'gc_count': self._gc_count,
                'last_gc_time': self._last_gc_time
            })
        
        return stats
    
    async def cleanup(self):
        """Cleanup memory optimizer resources"""
        logger.info("🧹 Starting memory optimizer cleanup...")
        
        # Cancel background tasks
        for task in [self._gc_task, self._cleanup_task, self._monitoring_task]:
            if task:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        # Final cleanup
        await self._run_cleanup_callbacks()
        
        # Clear tracked objects
        with self._lock:
            self._tracked_objects.clear()
            self._object_counts.clear()
            self._cleanup_callbacks.clear()
        
        # Final garbage collection
        gc.collect()
        
        logger.info("✅ Memory optimizer cleanup completed")

# Global memory optimizer instance
_memory_optimizer: Optional[MemoryOptimizer] = None

async def get_memory_optimizer(config: Optional[MemoryConfig] = None) -> MemoryOptimizer:
    """Get or create global memory optimizer"""
    global _memory_optimizer
    
    if _memory_optimizer is None:
        _memory_optimizer = MemoryOptimizer(config)
        await _memory_optimizer.initialize()
    
    return _memory_optimizer

# Convenience functions
async def optimize_memory() -> Dict[str, Any]:
    """Optimize memory usage"""
    optimizer = await get_memory_optimizer()
    return await optimizer.optimize_memory()

async def force_gc(aggressive: bool = False) -> Dict[str, Any]:
    """Force garbage collection"""
    optimizer = await get_memory_optimizer()
    return await optimizer.force_gc(aggressive)

def track_object(obj: Any, category: str = "general") -> bool:
    """Track object for memory management"""
    try:
        # This is a sync function, so we need to handle async context
        import asyncio
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If we're in an async context, we can't await here
            # So we'll just return False for now
            return False
        else:
            optimizer = loop.run_until_complete(get_memory_optimizer())
            return optimizer.track_object(obj, category)
    except Exception:
        return False

async def get_memory_stats() -> Dict[str, Any]:
    """Get memory statistics"""
    optimizer = await get_memory_optimizer()
    return optimizer.get_stats()

def register_cleanup_callback(callback: Callable):
    """Register cleanup callback"""
    try:
        import asyncio
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # Create a task to register the callback
            asyncio.create_task(_register_callback_async(callback))
        else:
            optimizer = loop.run_until_complete(get_memory_optimizer())
            optimizer.register_cleanup_callback(callback)
    except Exception as e:
        logger.warning(f"⚠️ Failed to register cleanup callback: {e}")

async def _register_callback_async(callback: Callable):
    """Helper to register callback in async context"""
    optimizer = await get_memory_optimizer()
    optimizer.register_cleanup_callback(callback)