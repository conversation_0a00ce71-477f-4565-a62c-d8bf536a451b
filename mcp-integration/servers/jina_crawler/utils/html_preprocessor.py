"""
Advanced HTML Preprocessing for ReaderLM
Optimizes HTML content before processing to improve model performance
"""

import re
import logging
from typing import Dict, Any, List
import html as html_parser
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

class HTMLPreprocessor:
    """
    Advanced HTML preprocessing for optimal ReaderLM performance.
    
    Features:
    - Remove unnecessary elements (scripts, styles, comments)
    - Clean base64 images and SVGs
    - Extract main content
    - Optimize for Vietnamese content
    - Reduce input size for faster processing
    """
    
    def __init__(self):
        self.max_content_length = 2000  # Optimal for speed
        self.min_content_length = 100   # Minimum meaningful content
        
    def preprocess(self, html_content: str) -> Dict[str, Any]:
        """
        Comprehensive HTML preprocessing pipeline.
        
        Args:
            html_content: Raw HTML content
            
        Returns:
            Dict with processed content and metrics
        """
        if not html_content or not html_content.strip():
            return {
                'success': False,
                'error': 'Empty content',
                'original_length': 0,
                'processed_length': 0,
                'reduction_ratio': 0,
                'processed_content': ''
            }
        
        original_length = len(html_content)
        
        try:
            # Step 1: Remove unnecessary elements
            cleaned = self._remove_unnecessary_elements(html_content)
            
            # Step 2: Clean base64 and large images
            cleaned = self._clean_images(cleaned)
            
            # Step 3: Extract main content
            cleaned = self._extract_main_content(cleaned)
            
            # Step 4: Clean formatting
            cleaned = self._clean_formatting(cleaned)
            
            # Step 5: Optimize for Vietnamese
            cleaned = self._optimize_vietnamese_content(cleaned)
            
            # Step 6: Smart truncation
            cleaned = self._smart_truncate(cleaned)
            
            processed_length = len(cleaned)
            reduction_ratio = (original_length - processed_length) / original_length * 100
            
            return {
                'success': True,
                'original_length': original_length,
                'processed_length': processed_length,
                'reduction_ratio': reduction_ratio,
                'processed_content': cleaned,
                'elements_removed': {
                    'scripts': len(re.findall(r'<script[^>]*>.*?</script>', html_content, re.DOTALL)),
                    'styles': len(re.findall(r'<style[^>]*>.*?</style>', html_content, re.DOTALL)),
                    'comments': len(re.findall(r'<!--.*?-->', html_content, re.DOTALL)),
                    'base64_images': len(re.findall(r'data:image/[^;]+;base64,[^"\']+', html_content)),
                    'svgs': len(re.findall(r'<svg[^>]*>.*?</svg>', html_content, re.DOTALL))
                }
            }
            
        except Exception as e:
            logger.error(f"HTML preprocessing failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'original_length': original_length,
                'processed_length': 0,
                'reduction_ratio': 0,
                'processed_content': html_content[:self.max_content_length]
            }
    
    def _remove_unnecessary_elements(self, html: str) -> str:
        """Remove scripts, styles, comments, and other non-content elements."""
        # Remove scripts
        html = re.sub(r'<script[^>]*>.*?</script>', '', html, flags=re.DOTALL | re.IGNORECASE)
        
        # Remove styles
        html = re.sub(r'<style[^>]*>.*?</style>', '', html, flags=re.DOTALL | re.IGNORECASE)
        
        # Remove comments
        html = re.sub(r'<!--.*?-->', '', html, flags=re.DOTALL)
        
        # Remove noscript
        html = re.sub(r'<noscript[^>]*>.*?</noscript>', '', html, flags=re.DOTALL | re.IGNORECASE)
        
        # Remove meta tags
        html = re.sub(r'<meta[^>]*>', '', html, flags=re.IGNORECASE)
        
        # Remove link tags (stylesheets)
        html = re.sub(r'<link[^>]*>', '', html, flags=re.IGNORECASE)
        
        return html
    
    def _clean_images(self, html: str) -> str:
        """Remove base64 images and large SVGs to reduce content size."""
        # Remove base64 images
        html = re.sub(r'<img[^>]*data:image/[^;]+;base64,[^"\']*["\'][^>]*>', '', html, flags=re.IGNORECASE)
        
        # Remove large SVGs
        html = re.sub(r'<svg[^>]*>.*?</svg>', '', html, flags=re.DOTALL | re.IGNORECASE)
        
        # Keep regular images but clean attributes
        html = re.sub(r'<img([^>]*)(?:width|height|style|class)=[^>]*>', r'<img\1>', html, flags=re.IGNORECASE)
        
        return html
    
    def _extract_main_content(self, html: str) -> str:
        """Extract main content areas using common patterns."""
        # Try to find main content areas
        content_patterns = [
            r'<main[^>]*>(.*?)</main>',
            r'<article[^>]*>(.*?)</article>',
            r'<div[^>]*class=["\'][^"\']*(?:content|main|article|post)[^"\']*["\'][^>]*>(.*?)</div>',
            r'<section[^>]*class=["\'][^"\']*(?:content|main|article|post)[^"\']*["\'][^>]*>(.*?)</section>',
        ]
        
        for pattern in content_patterns:
            matches = re.findall(pattern, html, re.DOTALL | re.IGNORECASE)
            if matches:
                # Take the largest content block
                main_content = max(matches, key=len)
                if len(main_content) > len(html) * 0.3:  # Significant content
                    return main_content
        
        # Fallback: remove navigation and footer
        html = re.sub(r'<nav[^>]*>.*?</nav>', '', html, flags=re.DOTALL | re.IGNORECASE)
        html = re.sub(r'<footer[^>]*>.*?</footer>', '', html, flags=re.DOTALL | re.IGNORECASE)
        html = re.sub(r'<header[^>]*>.*?</header>', '', html, flags=re.DOTALL | re.IGNORECASE)
        
        return html
    
    def _clean_formatting(self, html: str) -> str:
        """Clean excessive formatting while preserving structure."""
        # Remove excessive attributes
        html = re.sub(r'<(\w+)(?:\s+[^>]*)?>', r'<\1>', html)
        
        # Remove empty tags
        html = re.sub(r'<(\w+)>\s*</\1>', '', html, flags=re.IGNORECASE)
        
        # Clean up whitespace
        html = re.sub(r'\s+', ' ', html)
        html = re.sub(r'>\s+<', '><', html)
        
        # Decode HTML entities
        html = html_parser.unescape(html)
        
        return html.strip()
    
    def _optimize_vietnamese_content(self, html: str) -> str:
        """Optimize content for Vietnamese processing."""
        # Vietnamese character pattern
        vietnamese_pattern = r'[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ]'
        
        if re.search(vietnamese_pattern, html):
            # Remove obvious English navigation
            html = re.sub(r'<[^>]*(?:menu|nav|sidebar)[^>]*>.*?</[^>]*>', '', html, flags=re.DOTALL | re.IGNORECASE)
        
        return html
    
    def _smart_truncate(self, content: str) -> str:
        """Intelligently truncate content while preserving meaning."""
        if len(content) <= self.max_content_length:
            return content
        
        # Find good breaking points
        truncated = content[:self.max_content_length]
        
        # Vietnamese sentence endings
        endings = ['. ', '? ', '! ', '.\n', '?\n', '!\n']
        
        best_break = -1
        for ending in endings:
            pos = truncated.rfind(ending)
            if pos > best_break and pos > self.max_content_length * 0.7:
                best_break = pos + len(ending)
        
        if best_break > 0:
            return content[:best_break]
        
        # Fallback to word boundary
        last_space = truncated.rfind(' ')
        if last_space > self.max_content_length * 0.8:
            return content[:last_space]
        
        return truncated + "..."

# Global instance
_preprocessor = None

def get_html_preprocessor() -> HTMLPreprocessor:
    """Get global preprocessor instance."""
    global _preprocessor
    
    if _preprocessor is None:
        _preprocessor = HTMLPreprocessor()
    return _preprocessor

def preprocess_html(html_content: str) -> str:
    """
    Convenience function for HTML preprocessing.
    
    Args:
        html_content: Raw HTML content
        
    Returns:
        Preprocessed HTML content
    """
    preprocessor = get_html_preprocessor()
    result = preprocessor.preprocess(html_content)
    
    return result['processed_content'] if result['success'] else html_content

if __name__ == "__main__":
    # Test the preprocessor
    test_html = """<html>
    <head>
        <title>Test Page</title>
        <style>body { color: red; }</style>
        <script>console.log('test');</script>
    </head>
    <body>
        <nav>Navigation</nav>
        <main>
            <h1>Tin tức công nghệ Việt Nam</h1>
            <p>Trí tuệ nhân tạo đang phát triển mạnh mẽ.</p>
            <img src="data:image/png;base64,ABC123" alt="test">
            <svg>...</svg>
        </main>
        <footer>Footer</footer>
    </body>
    </html>"""
    
    preprocessor = HTMLPreprocessor()
    result = preprocessor.preprocess(test_html)
    
    print("🧪 HTML Preprocessor Test")
    print("=" * 40)
    print(f"Original length: {result['original_length']}")
    print(f"Processed length: {result['processed_length']}")
    print(f"Reduction: {result['reduction_ratio']:.1f}%")
    print(f"Elements removed: {result['elements_removed']}")
    print("\nProcessed content:")
    print(result['processed_content'])