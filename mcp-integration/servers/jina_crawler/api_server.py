#!/usr/bin/env python3
"""
HTTP Wrapper for Jini Crawler MCP Server - Open WebUI Compatible (v3 - Final)
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

# Import MCP server components
from server import JinaCrawlerMCPServer, call_tool

# --- Pydantic Models for each Tool ---
# This creates a detailed OpenAPI schema that Open WebUI can understand easily.

class CrawlUrlRequest(BaseModel):
    url: str = Field(..., description="URL to crawl and process with AI summarization")
    max_content_length: int = Field(10000, description="Maximum content length to process (default: 10000)")

class AISearchRequest(BaseModel):
    query: str = Field(..., description="Your search question or topic")
    enable_query_refinement: bool = Field(True, description="Whether to refine the query using AI (default: true)")
    search_type: str = Field("text", description="Type of search: 'text' or 'news'", enum=["text", "news"])
    max_sources: int = Field(10, description="Maximum number of sources to analyze (default: 10)")

class CrawlFullArticleRequest(BaseModel):
    url: str = Field(..., description="URL of public article to extract complete content from")
    max_content_length: int = Field(50000, description="Maximum content length (default: 50000 for full articles)")

class CrawlBatchRequest(BaseModel):
    urls: List[str] = Field(..., description="List of URLs to crawl in parallel")
    max_content_length: int = Field(10000, description="Maximum content length per URL (default: 10000)")

class CrawlBypassPaywallRequest(BaseModel):
    url: str = Field(..., description="URL of paywall-protected article (e.g., Medium, NYTimes)")
    max_content_length: int = Field(50000, description="Maximum content length (default: 50000)")
    
class AISearchStreamingRequest(BaseModel):
    query: str = Field(..., description="Your search question or topic for streaming results")
    enable_query_refinement: bool = Field(True, description="Whether to refine the query using AI (default: true)")

# Models for tools without arguments
class HealthCheckRequest(BaseModel):
    pass

class GetCrawlerStatsRequest(BaseModel):
    pass
    
# --- Generic Response Model ---

class MCPToolResponse(BaseModel):
    success: bool
    data: Any = None
    error: Optional[str] = None

# --- FastAPI App Setup ---

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("jini-http-wrapper")

app = FastAPI(
    title="jina_crawler",
    description="Jina Crawler MCP Server - 8 powerful tools for web crawling, multi-source AI search, and content processing. Features Google PSE + DuckDuckGo + Brave parallel search. Playwright-free.",
    version="3.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"], allow_credentials=True, allow_methods=["*"], allow_headers=["*"],
)

# --- MCP Server Initialization ---

mcp_server: Optional[JinaCrawlerMCPServer] = None

@app.on_event("startup")
async def startup_event():
    global mcp_server
    mcp_server = JinaCrawlerMCPServer()
    await mcp_server.initialize()
    logger.info("✅ Jini Crawler MCP Server initialized")

@app.on_event("shutdown")
async def shutdown_event():
    global mcp_server
    if mcp_server:
        await mcp_server.cleanup()
    logger.info("✅ Jini Crawler MCP Server stopped")

# --- Tool Execution Logic ---

async def execute_tool(tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
    try:
        result = await call_tool(tool_name, arguments)
        content = result[0].text if result and len(result) > 0 else "No content"
        return {"success": True, "data": content, "error": None}
    except Exception as e:
        logger.error(f"Tool '{tool_name}' error: {e}")
        return {"success": False, "data": None, "error": str(e)}

# --- API Endpoints ---

@app.get("/")
async def root():
    return {
        "name": "jina_crawler", "version": "3.0.0", "status": "healthy", "tools_count": 8,
        "description": "Playwright-free crawler with 8 tools.",
        "endpoints": {"docs": "/docs", "openapi": "/openapi.json"}
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy", "server": "jini-crawler"}

@app.post("/crawl_url", response_model=MCPToolResponse, summary="📄 SMART SUMMARIZER: Crawl and summarize a web page")
async def crawl_url_endpoint(request: CrawlUrlRequest):
    return await execute_tool("crawl_url", request.dict())

@app.post("/ai_search", response_model=MCPToolResponse, summary="🌐 MULTI-SOURCE AI RESEARCH ENGINE: Elite-level AI search with 3 parallel sources (Google PSE + DuckDuckGo + Brave) and Gemini 2.5 Flash reasoning")
async def ai_search_endpoint(request: AISearchRequest):
    return await execute_tool("ai_search", request.dict())

@app.post("/crawl_full_article", response_model=MCPToolResponse, summary="📰 COMPLETE ARTICLE EXTRACTOR: Get the entire article text")
async def crawl_full_article_endpoint(request: CrawlFullArticleRequest):
    return await execute_tool("crawl_full_article", request.dict())

@app.post("/crawl_batch", response_model=MCPToolResponse, summary="🚀 BATCH CRAWLER: Process multiple URLs in parallel")
async def crawl_batch_endpoint(request: CrawlBatchRequest):
    return await execute_tool("crawl_batch", request.dict())

@app.post("/crawl_bypass_paywall", response_model=MCPToolResponse, summary="🔓 PAYWALL BYPASS SPECIALIST: Access content behind paywalls")
async def crawl_bypass_paywall_endpoint(request: CrawlBypassPaywallRequest):
    return await execute_tool("crawl_bypass_paywall", request.dict())

@app.post("/health_check", response_model=MCPToolResponse, summary="❤️ HEALTH CHECK: Check the crawler's health status")
async def health_check_endpoint(_: HealthCheckRequest):
    return await execute_tool("health_check", {})

@app.post("/get_crawler_stats", response_model=MCPToolResponse, summary="📊 GET STATS: Get crawler performance statistics")
async def get_crawler_stats_endpoint(_: GetCrawlerStatsRequest):
    return await execute_tool("get_crawler_stats", {})

@app.post("/ai_search_streaming", response_model=MCPToolResponse, summary="🚀 MULTI-SOURCE AI SEARCH STREAMING: Get real-time updates for multi-source AI search (Google PSE + DuckDuckGo + Brave)")
async def ai_search_streaming_endpoint(request: AISearchStreamingRequest):
    return await execute_tool("ai_search_streaming", request.dict())

if __name__ == "__main__":
    print("🚀 Starting Jini Crawler MCP HTTP Interface (v3 - Final)")
    print("=" * 60)
    print("✅ OpenAPI schema is now fully detailed and LLM-friendly.")
    print("✅ 8 powerful, Playwright-free tools available.")
    print("=" * 60)
    print("📡 Server starting on: http://0.0.0.0:8009")
    print("📋 API Docs: http://0.0.0.0:8009/docs")
    print("=" * 60)
    uvicorn.run(app, host="0.0.0.0", port=8009)
