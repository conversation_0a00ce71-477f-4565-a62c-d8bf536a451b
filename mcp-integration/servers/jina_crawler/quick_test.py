#!/usr/bin/env python3
"""
Quick test script - Test nhanh với dantri.vn và vnexpress.net
"""

import asyncio
import logging
import sys
import os

sys.path.append(os.path.dirname(__file__))

from jini_crawler import JiniCrawler

logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def quick_test():
    """Test nhanh 2 trang"""
    
    urls = [
        "https://dantri.com.vn",
        "https://vnexpress.net"
    ]
    
    logger.info("🧪 QUICK TEST: dantri.vn + vnexpress.net")
    logger.info("=" * 40)
    
    crawler = JiniCrawler()
    
    try:
        await crawler.initialize()
        
        # Test batch processing với 2 URLs
        batch_data = await crawler.crawl_batch_raw(urls, max_content_length=15000)
        
        if batch_data:
            total_size = sum(len(item.get('raw_content', '')) for item in batch_data)
            estimated_tokens = int(total_size / 3.5)  # Vietnamese chars to tokens
            
            logger.info(f"✅ Crawled {len(batch_data)} URLs")
            logger.info(f"📊 Total size: {total_size:,} chars")
            logger.info(f"🎯 Estimated tokens: {estimated_tokens:,}")
            
            if estimated_tokens < 200000:  # Safe limit
                logger.info("✅ WITHIN CONTEXT LIMITS!")
                
                # Process with Gemini
                results = await crawler.process_batch_with_gemini(batch_data, "tin tức Việt Nam")
                
                if results:
                    logger.info(f"🤖 Gemini processed {len(results)} results")
                    for result in results:
                        content_size = len(result.processed_content or '')
                        logger.info(f"   📄 {result.url}: {content_size:,} chars")
                    
                    total_output = sum(len(r.processed_content or '') for r in results)
                    logger.info(f"📊 Total output: {total_output:,} chars")
                    logger.info("🎉 SUCCESS - No context overflow!")
                else:
                    logger.warning("⚠️ Gemini processing failed")
            else:
                logger.warning(f"⚠️ May exceed context limit: {estimated_tokens:,} tokens")
        else:
            logger.error("❌ No content crawled")
            
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
    finally:
        await crawler.cleanup()

if __name__ == "__main__":
    asyncio.run(quick_test())