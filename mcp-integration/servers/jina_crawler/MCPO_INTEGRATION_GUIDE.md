# 🚀 Hướng dẫn tích hợp <PERSON>a Crawler MCPO Server

## 📋 Thông tin Container

### Container Details:
- **Container Name**: `jina-crawler-8009`
- **Image**: `jina_crawler-jina-crawler`
- **Port**: `8009` (Host) → `8009` (Container)
- **Network**: `acca-network`
- **Status**: ✅ Healthy & Running
- **Aliases**: `jina-crawler-8009`, `jina-crawler`

## 🌐 URL và Endpoints

### 🔗 **URLs để sử dụng:**

#### **Từ bên ngoài (External Access):**
```
http://localhost:8009
http://127.0.0.1:8009
http://[server-ip]:8009
```

#### **Từ containers khác trong cùng network `acca-network`:**
```
http://jina-crawler-8009:8009
http://jina-crawler:8009
```

### 📚 **C<PERSON>c Endpoints chính:**

| Endpoint | Method | Auth Required | Description |
|----------|--------|---------------|-------------|
| `/` | GET | ❌ | Server info |
| `/health` | GET | ❌ | Health check |
| `/docs` | GET | ❌ | Swagger UI |
| `/openapi.json` | GET | ❌ | OpenAPI schema |
| `/tools/list` | GET | ✅ | List all tools |
| `/tools/{tool_name}` | POST | ✅ | Execute tool |

## 🔑 Authentication

### **API Key**: `jina-crawler-secret-key-2025`

### **Header format**:
```bash
Authorization: Bearer jina-crawler-secret-key-2025
```

## 🛠️ Cách sử dụng

### 1. **Test Health (không cần auth):**
```bash
curl http://localhost:8009/health
```

### 2. **Xem documentation:**
```bash
# Mở browser
http://localhost:8009/docs
```

### 3. **List tools (cần auth):**
```bash
curl -H "Authorization: Bearer jina-crawler-secret-key-2025" \
     http://localhost:8009/tools/list
```

### 4. **Execute tool (cần auth):**
```bash
curl -H "Authorization: Bearer jina-crawler-secret-key-2025" \
     -H "Content-Type: application/json" \
     -X POST http://localhost:8009/tools/health_check \
     -d '{}'
```

### 5. **Crawl URL example:**
```bash
curl -H "Authorization: Bearer jina-crawler-secret-key-2025" \
     -H "Content-Type: application/json" \
     -X POST http://localhost:8009/tools/crawl_url \
     -d '{"url": "https://example.com", "max_content_length": 5000}'
```

## 🐳 Docker Network Integration

### **Network**: `acca-network`
- Container có thể giao tiếp với các containers khác trong cùng network
- Sử dụng container name làm hostname: `jina-crawler-8009` hoặc `jina-crawler`

### **Từ containers khác trong `acca-network`:**
```bash
# Từ container khác
curl http://jina-crawler:8009/health
curl http://jina-crawler-8009:8009/health
```

## 🔧 Cấu hình cho Open WebUI

### **Thêm vào Open WebUI Functions/Tools:**

```json
{
  "name": "Jina Crawler MCPO",
  "url": "http://jina-crawler:8009",
  "api_key": "jina-crawler-secret-key-2025",
  "description": "Advanced web crawling with AI processing",
  "endpoints": {
    "health": "/health",
    "docs": "/docs",
    "openapi": "/openapi.json",
    "tools": "/tools/list"
  }
}
```

### **Hoặc sử dụng external URL:**
```json
{
  "name": "Jina Crawler MCPO",
  "url": "http://localhost:8009",
  "api_key": "jina-crawler-secret-key-2025"
}
```

## 🛠️ 9 Tools có sẵn:

1. **`crawl_url`** - Smart summarizer với AI processing
2. **`crawl_full_article`** - Complete article extractor
3. **`crawl_batch`** - Batch crawl multiple URLs
4. **`search_site`** - Search within specific website
5. **`health_check`** - Check crawler health
6. **`get_crawler_stats`** - Get performance statistics
7. **`crawl_bypass_paywall`** - Bypass paywall (8 techniques)
8. **`ai_search`** - AI Search Engine với Brave support
9. **`ai_search_streaming`** - AI Search với streaming updates

## ✅ MCPO Compliance Features:

- ✅ **Bearer Authentication**: API key required
- ✅ **Auto-generated OpenAPI 3.1.0**: FastAPI tự động tạo
- ✅ **Interactive Documentation**: Swagger UI
- ✅ **Health Monitoring**: `/health` endpoint
- ✅ **CORS Support**: Cross-origin requests
- ✅ **Error Handling**: Proper HTTP status codes
- ✅ **Tool Discovery**: `/tools/list` endpoint

## 🔄 Container Management:

### **Start/Stop:**
```bash
cd mcp-integration/servers/jina_crawler
docker compose up -d    # Start
docker compose down     # Stop
```

### **View logs:**
```bash
docker logs jina-crawler-8009 -f
```

### **Restart:**
```bash
docker compose restart
```

---

**🎯 Server đã sẵn sàng sử dụng theo chuẩn MCPO!**