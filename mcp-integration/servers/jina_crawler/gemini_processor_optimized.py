"""
Size-Optimized Gemini 2.5 Flash API Processor
Specifically designed to prevent context overflow while maintaining quality
Optimized for Vietnamese news sites like dantri.vn and vnexpress.net
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List
import time
import asyncio
import aiohttp
from dataclasses import dataclass

# Import the content size optimizer
from utils.content_size_optimizer import get_content_size_optimizer, SizeOptimizationConfig

logger = logging.getLogger(__name__)

@dataclass
class OptimizedGeminiConfig:
    """Configuration for size-optimized Gemini API."""
    api_key: str
    model_name: str = "gemini-2.5-flash-lite"
    api_url: str = "https://generativelanguage.googleapis.com/v1beta/models"
    timeout: int = 30
    max_retries: int = 3
    # Size optimization settings
    max_output_tokens: int = 8192  # Reduced from 65k to 8k
    enable_size_optimization: bool = True
    target_context_limit: int = 200000  # Stay well under 272k limit

class OptimizedGeminiProcessor:
    """
    Size-optimized Gemini processor that prevents context overflow
    while maintaining content quality for Vietnamese news sites
    """
    
    def __init__(self, config: Optional[OptimizedGeminiConfig] = None):
        self.config = config or self._load_config()
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Initialize content size optimizer
        if self.config.enable_size_optimization:
            size_config = SizeOptimizationConfig(
                max_total_tokens=self.config.target_context_limit,
                max_output_tokens=self.config.max_output_tokens,
                vietnamese_news_optimization=True
            )
            self.size_optimizer = get_content_size_optimizer(size_config)
        else:
            self.size_optimizer = None
        
        # Size-optimized prompts (focused on key information extraction)
        self.prompts = {
            "batch_processing": """🎯 EFFICIENT CONTENT SYNTHESIS
Query: "{query}"

Extract and synthesize KEY INFORMATION from the following Vietnamese news sources. 
Focus on ESSENTIAL content only - main points, key facts, and important quotes.
Keep output CONCISE while preserving critical information.

REQUIRED OUTPUT FORMAT (JSON):
{{
  "batch_results": [
    {{
      "url": "source_url",
      "title": "concise_title",
      "processed_content": "key_points_in_markdown",
      "key_points": ["essential_point_1", "essential_point_2", "essential_point_3"],
      "relevance_score": 0.85
    }}
  ]
}}

CONTENT EXTRACTION RULES:
1. Extract ONLY the most important information
2. Summarize long articles to key points
3. Keep individual content under 1000 characters
4. Focus on facts, quotes, and main arguments
5. Remove redundant or boilerplate content

SOURCES:
{sources}

Return structured JSON with concise, high-quality content for each source.""",

            "html_to_markdown": """Convert this HTML to clean, CONCISE Markdown. Extract ONLY the main article content - title, key paragraphs, and important information. Remove ads, navigation, and boilerplate. Keep output under 2000 characters while preserving essential information.

HTML Content:
{content}

Concise Markdown (main content only):""",
            
            "summarize": """Summarize this Vietnamese content to KEY POINTS only. Focus on the most important information, main arguments, and essential facts. Keep under 1000 characters.

Content:
{content}

Key Points Summary:"""
        }
        
    def _load_config(self) -> OptimizedGeminiConfig:
        """Load optimized Gemini configuration"""
        api_key = os.getenv("GEMINI_API_KEY", "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM")
        if not api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
            
        return OptimizedGeminiConfig(
            api_key=api_key,
            model_name=os.getenv("GEMINI_MODEL_NAME", "gemini-2.5-flash-lite"),
            max_output_tokens=int(os.getenv("GEMINI_MAX_OUTPUT_TOKENS", "8192")),
            target_context_limit=int(os.getenv("GEMINI_CONTEXT_LIMIT", "200000"))
        )
    
    async def initialize(self) -> bool:
        """Initialize the optimized Gemini processor"""
        try:
            if not self.session:
                timeout = aiohttp.ClientTimeout(total=self.config.timeout)
                self.session = aiohttp.ClientSession(timeout=timeout)
            
            logger.info("✅ Optimized Gemini processor initialized")
            logger.info(f"📊 Context limit: {self.config.target_context_limit:,} tokens")
            logger.info(f"📊 Max output: {self.config.max_output_tokens:,} tokens")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize optimized Gemini processor: {e}")
            return False
    
    async def process_batch(self, batch_data: List[Dict[str, str]], query: str = "") -> List[Dict[str, Any]]:
        """
        Process batch with intelligent size optimization to prevent context overflow
        
        Args:
            batch_data: List of dicts with 'url' and 'raw_content' keys
            query: Search query for context
            
        Returns:
            List of processed results optimized for size
        """
        if not batch_data:
            return []

        try:
            start_time = time.time()
            original_count = len(batch_data)
            original_size = sum(len(item.get('raw_content', '')) for item in batch_data)
            
            logger.info(f"🚀 Starting size-optimized batch processing: {original_count} URLs, {original_size:,} chars")
            
            # Step 1: Apply content size optimization
            if self.size_optimizer:
                optimized_batch, optimization_stats = self.size_optimizer.optimize_batch_content(batch_data, query)
                
                logger.info(f"🔧 Content optimization: {optimization_stats['original_urls']} → {optimization_stats['optimized_urls']} URLs")
                logger.info(f"📊 Size reduction: {optimization_stats['original_size']:,} → {optimization_stats['final_size']:,} chars ({optimization_stats['reduction_ratio']:.1%})")
                
                if not optimization_stats.get('within_limits', True):
                    logger.warning(f"⚠️ Content still large after optimization: {optimization_stats['estimated_tokens']:,} tokens")
            else:
                optimized_batch = batch_data
                optimization_stats = {'final_size': original_size}
            
            if not optimized_batch:
                logger.warning("⚠️ No content remaining after optimization")
                return []
            
            # Step 2: Build size-optimized batch prompt
            batch_prompt = self._build_optimized_batch_prompt(optimized_batch, query)
            
            # Step 3: Process with Gemini using size-optimized settings
            try:
                import google.generativeai as genai
                genai.configure(api_key=self.config.api_key)
                model = genai.GenerativeModel(self.config.model_name)

                response = await asyncio.to_thread(
                    model.generate_content,
                    batch_prompt,
                    generation_config=genai.types.GenerationConfig(
                        max_output_tokens=self.config.max_output_tokens,  # Reduced output limit
                        temperature=0.0,  # Deterministic for consistency
                        candidate_count=1
                    )
                )

                processing_time = time.time() - start_time

                if response and response.text:
                    results = self._parse_batch_response(response.text, optimized_batch)
                    
                    logger.info(f"✅ Size-optimized batch processing completed in {processing_time:.2f}s")
                    logger.info(f"📊 Results: {len(results)} URLs processed")
                    logger.info(f"💰 Context efficiency: {optimization_stats.get('final_size', 0):,} chars used")
                    
                    return results
                else:
                    logger.warning("⚠️ Empty response from Gemini")
                    return self._create_fallback_batch_results(optimized_batch)

            except Exception as e:
                logger.error(f"❌ Gemini API call failed: {e}")
                return self._create_fallback_batch_results(optimized_batch)

        except Exception as e:
            logger.error(f"❌ Size-optimized batch processing failed: {e}")
            return self._create_fallback_batch_results(batch_data[:5])  # Fallback to first 5 URLs
    
    def _build_optimized_batch_prompt(self, batch_data: List[Dict[str, str]], query: str) -> str:
        """Build size-optimized batch prompt"""
        
        # Build sources section with size limits
        sources_parts = []
        for i, item in enumerate(batch_data, 1):
            url = item.get('url', f'unknown_url_{i}')
            content = item.get('raw_content', '')
            
            # Further truncate content if needed for prompt
            max_content_in_prompt = 8000  # Conservative limit per source in prompt
            if len(content) > max_content_in_prompt:
                content = content[:max_content_in_prompt] + "..."
            
            sources_parts.append(f"=== SOURCE {i}: {url} ===\n{content}\n")
        
        sources_text = '\n'.join(sources_parts)
        
        # Use size-optimized prompt template
        prompt = self.prompts["batch_processing"].format(
            query=query,
            sources=sources_text
        )
        
        # Log prompt size for monitoring
        prompt_size = len(prompt)
        estimated_tokens = prompt_size // 4  # Rough estimation
        logger.debug(f"📊 Batch prompt: {prompt_size:,} chars (~{estimated_tokens:,} tokens)")
        
        return prompt
    
    def _parse_batch_response(self, response_text: str, batch_data: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """Parse batch response with size awareness"""
        try:
            import json
            import re

            # Find JSON block in response
            json_match = re.search(r'\{.*"batch_results".*\}', response_text, re.DOTALL)

            if json_match:
                json_str = json_match.group(0)
                parsed_data = json.loads(json_str)

                if 'batch_results' in parsed_data:
                    results = []
                    batch_results = parsed_data['batch_results']

                    # Match results to original URLs
                    for i, original_item in enumerate(batch_data):
                        if i < len(batch_results):
                            result = batch_results[i]
                            
                            # Ensure content is within size limits
                            processed_content = result.get('processed_content', '')
                            if len(processed_content) > 2000:  # Limit individual result size
                                processed_content = processed_content[:2000] + "..."
                            
                            results.append({
                                'url': original_item['url'],
                                'title': result.get('title', 'No title')[:100],  # Limit title length
                                'processed_content': processed_content,
                                'key_points': result.get('key_points', [])[:5],  # Limit to 5 key points
                                'relevance_score': result.get('relevance_score', 0.5)
                            })
                        else:
                            # Fallback for missing results
                            results.append(self._create_fallback_result(original_item))

                    logger.info(f"✅ Successfully parsed {len(results)} size-optimized batch results")
                    return results

            # Fallback parsing
            logger.warning("⚠️ JSON parsing failed, trying fallback parsing")
            return self._parse_batch_response_fallback(response_text, batch_data)

        except Exception as e:
            logger.warning(f"⚠️ Batch response parsing failed: {e}")
            return self._create_fallback_batch_results(batch_data)
    
    def _parse_batch_response_fallback(self, response_text: str, batch_data: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """Fallback parsing with size limits"""
        results = []
        sections = response_text.split('=== SOURCE')

        for i, original_item in enumerate(batch_data):
            if i + 1 < len(sections):
                section_content = sections[i + 1]
                lines = section_content.split('\n')
                
                title = 'Processed Content'
                content_lines = []

                for line in lines[:20]:  # Limit to first 20 lines
                    line = line.strip()
                    if line and not line.startswith('==='):
                        if title == 'Processed Content':
                            title = line[:100]  # First meaningful line as title
                        content_lines.append(line)

                processed_content = '\n'.join(content_lines)
                if len(processed_content) > 1500:  # Size limit
                    processed_content = processed_content[:1500] + "..."

                results.append({
                    'url': original_item['url'],
                    'title': title,
                    'processed_content': processed_content,
                    'key_points': [],
                    'relevance_score': 0.6
                })
            else:
                results.append(self._create_fallback_result(original_item))

        return results
    
    def _create_fallback_result(self, original_item: Dict[str, str]) -> Dict[str, Any]:
        """Create size-limited fallback result"""
        raw_content = original_item.get('raw_content', '')
        
        # Extract basic title from content
        lines = raw_content.split('\n')
        title = 'Content Available'
        for line in lines[:5]:  # Check only first 5 lines
            line = line.strip()
            if line and 10 < len(line) < 100:
                title = line
                break

        # Limit fallback content size
        fallback_content = raw_content[:1000] if raw_content else 'No content available'

        return {
            'url': original_item['url'],
            'title': title,
            'processed_content': fallback_content,
            'key_points': ['Content extracted from source'],
            'relevance_score': 0.3
        }
    
    def _create_fallback_batch_results(self, batch_data: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """Create size-limited fallback results for entire batch"""
        logger.warning(f"⚠️ Creating size-limited fallback results for {len(batch_data)} URLs")
        return [self._create_fallback_result(item) for item in batch_data]
    
    async def process_content(
        self,
        content: str,
        task_type: str = "html_to_markdown",
        max_length: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Process individual content with size optimization
        """
        start_time = time.time()
        
        try:
            # Apply size optimization for individual content
            if self.size_optimizer and len(content) > 10000:
                # Use content preprocessor for large content
                from utils.content_preprocessor import get_content_preprocessor
                preprocessor = get_content_preprocessor()
                
                if task_type == "html_to_markdown":
                    result = preprocessor.preprocess_html(content)
                    if result.get('success'):
                        content = result['preprocessed_content']
                        logger.debug(f"📊 Content preprocessed: {result['metadata']['reduction_ratio']:.1%} reduction")
            
            # Prepare size-optimized prompt
            prompt = self._prepare_optimized_prompt(content, task_type, max_length)
            
            # Call Gemini API with size limits
            result = await self._call_gemini_api_optimized(prompt)
            
            processing_time = time.time() - start_time
            
            return {
                "success": True,
                "original_length": len(content),
                "processed_content": result,
                "output_length": len(result),
                "processing_time": processing_time,
                "model": self.config.model_name,
                "task_type": task_type,
                "size_optimized": True
            }
            
        except Exception as e:
            logger.error(f"Optimized Gemini processing error: {e}")
            return {
                "success": False,
                "original_length": len(content),
                "processed_content": "",
                "processing_time": 0,
                "model": self.config.model_name,
                "error": str(e),
                "output_length": 0
            }
    
    def _prepare_optimized_prompt(self, content: str, task_type: str, max_length: Optional[int]) -> str:
        """Prepare size-optimized prompt"""
        # Aggressive truncation for individual processing
        max_content_length = max_length or 5000  # Much smaller limit
        if len(content) > max_content_length:
            content = self._smart_truncate(content, max_content_length)
        
        prompt_template = self.prompts.get(task_type, self.prompts["html_to_markdown"])
        return prompt_template.format(content=content)
    
    def _smart_truncate(self, content: str, max_length: int) -> str:
        """Smart truncation optimized for Vietnamese content"""
        if len(content) <= max_length:
            return content
        
        truncated = content[:max_length]
        
        # Vietnamese sentence endings
        endings = ['. ', '.\n', '? ', '?\n', '! ', '!\n']
        
        best_cut = -1
        for ending in endings:
            pos = truncated.rfind(ending)
            if pos > max_length * 0.6:  # At least 60% of content
                best_cut = max(best_cut, pos + len(ending))
        
        if best_cut > 0:
            return content[:best_cut]
        
        # Fallback to word boundary
        last_space = truncated.rfind(' ')
        if last_space > max_length * 0.8:
            return content[:last_space] + "..."
        
        return truncated + "..."
    
    async def _call_gemini_api_optimized(self, prompt: str) -> str:
        """Call Gemini API with size-optimized settings"""
        if not self.session:
            await self.initialize()
        
        url = f"{self.config.api_url}/{self.config.model_name}:generateContent"
        params = {"key": self.config.api_key}
        
        headers = {"Content-Type": "application/json"}
        
        payload = {
            "contents": [{
                "parts": [{"text": prompt}]
            }],
            "generationConfig": {
                "temperature": 0.0,
                "maxOutputTokens": self.config.max_output_tokens,  # Size-limited output
                "topK": 1,
                "topP": 1.0
            }
        }
        
        for attempt in range(self.config.max_retries + 1):
            try:
                async with self.session.post(url, params=params, headers=headers, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        if "candidates" in result and len(result["candidates"]) > 0:
                            candidate = result["candidates"][0]
                            if "content" in candidate and "parts" in candidate["content"]:
                                parts = candidate["content"]["parts"]
                                if len(parts) > 0:
                                    return parts[0].get("text", "").strip()
                        return ""
                    
                    elif response.status == 429:
                        if attempt < self.config.max_retries:
                            wait_time = 2 ** attempt
                            logger.warning(f"Rate limited, waiting {wait_time}s before retry {attempt + 1}")
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            raise Exception(f"Rate limited after {self.config.max_retries} retries")
                    
                    else:
                        error_text = await response.text()
                        raise Exception(f"Gemini API error {response.status}: {error_text}")
                        
            except Exception as e:
                if attempt < self.config.max_retries:
                    wait_time = 2 ** attempt
                    logger.warning(f"Gemini API call failed, waiting {wait_time}s before retry {attempt + 1}: {e}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    raise
    
    async def cleanup(self) -> None:
        """Cleanup resources"""
        if self.session:
            await self.session.close()
            self.session = None
        logger.info("✅ Optimized Gemini processor cleanup completed")
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check with size optimization info"""
        try:
            test_content = "<h1>Test</h1><p>Kiểm tra hệ thống xử lý nội dung.</p>"
            test_result = await self.process_content(test_content, "html_to_markdown")
            
            return {
                "status": "healthy" if test_result.get("success") else "error",
                "model_name": self.config.model_name,
                "size_optimization_enabled": self.config.enable_size_optimization,
                "max_output_tokens": self.config.max_output_tokens,
                "target_context_limit": self.config.target_context_limit,
                "test_successful": test_result.get("success", False),
                "test_processing_time": test_result.get("processing_time", 0)
            }
            
        except Exception as e:
            logger.error(f"Optimized Gemini health check failed: {e}")
            return {
                "status": "error",
                "model_name": self.config.model_name,
                "error": str(e)
            }

# Global optimized processor instance
_optimized_gemini_processor: Optional[OptimizedGeminiProcessor] = None

async def get_optimized_gemini_processor() -> OptimizedGeminiProcessor:
    """Get or create global optimized Gemini processor"""
    global _optimized_gemini_processor
    
    if _optimized_gemini_processor is None:
        _optimized_gemini_processor = OptimizedGeminiProcessor()
        await _optimized_gemini_processor.initialize()
    
    return _optimized_gemini_processor