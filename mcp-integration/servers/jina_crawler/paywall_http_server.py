#!/usr/bin/env python3
"""
HTTP Server wrapper for Jina Crawler with Paywall Bypass
Runs on port 8009 with full article and paywall bypass capabilities
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Import our crawler components
from jini_crawler import JiniCrawler
from paywall_bypass_crawler import PaywallBypassCrawler

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("paywall-http-server")

# Initialize FastAPI app
app = FastAPI(
    title="Jina Crawler with Paywall Bypass",
    description="Advanced web crawler with full article extraction and paywall bypass capabilities",
    version="2.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global crawler instances
regular_crawler: Optional[JiniCrawler] = None
paywall_crawler: Optional[PaywallBypassCrawler] = None

# Request models
class CrawlRequest(BaseModel):
    url: str
    max_content_length: Optional[int] = 10000

class FullArticleRequest(BaseModel):
    url: str
    max_content_length: Optional[int] = 50000

class PaywallBypassRequest(BaseModel):
    url: str
    max_content_length: Optional[int] = 50000

@app.on_event("startup")
async def startup_event():
    """Initialize crawlers on startup"""
    global regular_crawler, paywall_crawler
    
    regular_crawler = JiniCrawler()
    paywall_crawler = PaywallBypassCrawler()
    
    await regular_crawler.initialize()
    await paywall_crawler.initialize()
    
    logger.info("🚀 Jina Crawler HTTP Server with Paywall Bypass started on port 8009")
    logger.info("✅ Regular crawler initialized")
    logger.info("🔓 Paywall bypass crawler initialized")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    global regular_crawler, paywall_crawler
    
    if regular_crawler:
        await regular_crawler.cleanup()
    if paywall_crawler:
        await paywall_crawler.cleanup()
    
    logger.info("✅ Crawlers cleanup completed")

@app.get("/")
async def root():
    """Root endpoint with server info"""
    return {
        "message": "Jina Crawler HTTP Server with Paywall Bypass",
        "version": "2.0.0",
        "port": 8009,
        "tools": {
            "crawl_url": "Extract summarized/processed content",
            "crawl_full_article": "Extract COMPLETE article content",
            "crawl_bypass_paywall": "🔓 BYPASS PAYWALLS - 8 techniques",
            "health_check": "Server health status"
        },
        "paywall_methods": [
            "fake_headers", "google_cache", "archive_org", "outline_com",
            "12ft_ladder", "amp_version", "print_version", "reader_mode"
        ],
        "features": [
            "Full article extraction",
            "Paywall bypass (8 techniques)",
            "Vietnamese content optimization",
            "Gemini 2.5 Flash processing",
            "Async parallel processing"
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        if not regular_crawler or not paywall_crawler:
            return JSONResponse(
                status_code=503,
                content={"status": "error", "message": "Crawlers not initialized"}
            )
        
        # Test regular crawler
        regular_health = await regular_crawler.health_check()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "regular_crawler": regular_health,
            "paywall_crawler": {
                "status": "healthy",
                "initialized": paywall_crawler._initialized
            },
            "server": "paywall_http_server",
            "port": 8009
        }
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "error": str(e)}
        )

@app.post("/crawl")
async def crawl_url(request: CrawlRequest):
    """Crawl and process a single URL (summarized)"""
    try:
        if not regular_crawler:
            raise HTTPException(status_code=503, detail="Regular crawler not initialized")
        
        logger.info(f"🕷️ Crawling URL: {request.url}")
        
        result = await regular_crawler.crawl_and_process(
            request.url, 
            request.max_content_length
        )
        
        response = {
            "success": result.success,
            "url": result.url,
            "title": result.title,
            "processed_content": result.processed_content,
            "processing_time": result.processing_time,
            "error": result.error,
            "metadata": result.metadata,
            "timestamp": datetime.now().isoformat(),
            "crawler_type": "regular",
            "content_type": "summarized"
        }
        
        return response
        
    except Exception as e:
        logger.error(f"Error crawling {request.url}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/crawl-full-article")
async def crawl_full_article(request: FullArticleRequest):
    """Crawl and extract COMPLETE article content"""
    try:
        if not regular_crawler:
            raise HTTPException(status_code=503, detail="Regular crawler not initialized")
        
        logger.info(f"📰 Crawling full article: {request.url}")
        
        result = await regular_crawler.crawl_full_article(
            request.url, 
            request.max_content_length
        )
        
        response = {
            "success": result.success,
            "url": result.url,
            "title": result.title,
            "full_article_content": result.processed_content,
            "processing_time": result.processing_time,
            "error": result.error,
            "metadata": result.metadata,
            "timestamp": datetime.now().isoformat(),
            "crawler_type": "full_article",
            "content_type": "complete_article"
        }
        
        return response
        
    except Exception as e:
        logger.error(f"Error crawling full article {request.url}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/crawl-bypass-paywall")
async def crawl_bypass_paywall(request: PaywallBypassRequest):
    """🔓 Crawl with paywall bypass using multiple techniques"""
    try:
        if not paywall_crawler:
            raise HTTPException(status_code=503, detail="Paywall crawler not initialized")
        
        logger.info(f"🔓 Bypassing paywall for: {request.url}")
        
        result = await paywall_crawler.crawl_with_paywall_bypass(
            request.url, 
            request.max_content_length
        )
        
        response = {
            "success": result.success,
            "url": result.url,
            "title": result.title,
            "full_article_content": result.processed_content,
            "processing_time": result.processing_time,
            "error": result.error,
            "metadata": result.metadata,
            "timestamp": datetime.now().isoformat(),
            "crawler_type": "paywall_bypass",
            "bypass_method": result.metadata.get("bypass_method", "unknown") if result.metadata else "unknown",
            "content_type": "paywall_bypassed_article"
        }
        
        return response
        
    except Exception as e:
        logger.error(f"Error bypassing paywall {request.url}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/tools")
async def list_tools():
    """List available tools"""
    return {
        "tools": [
            {
                "name": "crawl",
                "method": "POST",
                "endpoint": "/crawl",
                "description": "Crawl and process a single URL (summarized)",
                "parameters": {
                    "url": "string (required)",
                    "max_content_length": "integer (optional, default: 10000)"
                }
            },
            {
                "name": "crawl_full_article",
                "method": "POST", 
                "endpoint": "/crawl-full-article",
                "description": "Extract COMPLETE article content",
                "parameters": {
                    "url": "string (required)",
                    "max_content_length": "integer (optional, default: 50000)"
                }
            },
            {
                "name": "crawl_bypass_paywall",
                "method": "POST",
                "endpoint": "/crawl-bypass-paywall", 
                "description": "🔓 BYPASS PAYWALLS using 8 different techniques",
                "parameters": {
                    "url": "string (required)",
                    "max_content_length": "integer (optional, default: 50000)"
                },
                "bypass_methods": [
                    "fake_headers", "google_cache", "archive_org", "outline_com",
                    "12ft_ladder", "amp_version", "print_version", "reader_mode"
                ]
            }
        ]
    }

@app.get("/demo")
async def demo_endpoints():
    """Demo usage examples"""
    return {
        "demo_requests": {
            "regular_crawl": {
                "method": "POST",
                "url": "http://localhost:8009/crawl",
                "body": {
                    "url": "https://example.com/article",
                    "max_content_length": 10000
                }
            },
            "full_article": {
                "method": "POST", 
                "url": "http://localhost:8009/crawl-full-article",
                "body": {
                    "url": "https://example.com/article",
                    "max_content_length": 50000
                }
            },
            "paywall_bypass": {
                "method": "POST",
                "url": "http://localhost:8009/crawl-bypass-paywall",
                "body": {
                    "url": "https://medium.com/@author/article",
                    "max_content_length": 50000
                }
            }
        },
        "curl_examples": {
            "paywall_bypass": 'curl -X POST "http://localhost:8009/crawl-bypass-paywall" -H "Content-Type: application/json" -d \'{"url": "https://medium.com/@ignacio.de.gregorio.noblejas/what-china-gets-the-us-doesnt-f12059d0613d", "max_content_length": 50000}\''
        }
    }

if __name__ == "__main__":
    print("🚀 Starting Jina Crawler HTTP Server with Paywall Bypass on port 8009...")
    print("🔓 Features: Full Article + Paywall Bypass (8 techniques)")
    print("📚 Endpoints: /crawl, /crawl-full-article, /crawl-bypass-paywall")
    print("🏥 Health: /health")
    print("📖 Demo: /demo")
    
    uvicorn.run(
        "paywall_http_server:app",
        host="0.0.0.0",
        port=8009,
        reload=False,
        log_level="info"
    )