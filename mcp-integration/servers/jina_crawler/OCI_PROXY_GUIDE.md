# 🌐 OCI PROXY CONFIGURATION GUIDE

## 📋 TÓM TẮT

**Câu hỏi:** <PERSON><PERSON> cần mở port proxy trên OCI không?  
**Tr<PERSON> lời:** ❌ **KHÔNG CẦN** - Proxy là outbound connection

---

## 🔧 CÁCH PROXY HOẠT ĐỘNG TRÊN OCI

### 1. **Traffic Flow**
```
OCI Instance (Your App) 
    ↓ Outbound (Port 80/443/8080/3128)
Proxy Server (External)
    ↓ Forward Request  
Target Website (DuckDuckGo, Google, etc.)
    ↓ Response
Proxy Server
    ↓ Return Response
OCI Instance
```

### 2. **Security Groups Configuration**

#### ✅ **Cần có (thường đã có sẵn):**
```bash
# Egress Rules (Outbound)
Direction: Egress
Destination: 0.0.0.0/0
Protocol: TCP
Port Range: 80,443,8080,3128,1080-65535
Action: Allow
```

#### ❌ **KHÔNG cần:**
```bash
# Ingress Rules (Inbound) - KHÔNG CẦN cho proxy
Direction: Ingress  
Source: Proxy IP
Protocol: TCP
Port Range: Any
Action: Allow
```

---

## 🚫 TẠI SAO PROXY BỊ LỖI 502

### 1. **Proxy Server Issues**
- ❌ Proxy server offline/dead
- ❌ Credentials expired
- ❌ IP range blocked by provider
- ❌ Overloaded/rate limited

### 2. **Network Issues**  
- ❌ OCI → Proxy routing problems
- ❌ Proxy provider infrastructure issues
- ❌ Geographic restrictions

### 3. **Authentication Issues**
- ❌ Wrong username/password
- ❌ Account suspended
- ❌ IP whitelist restrictions

---

## 🛠️ GIẢI PHÁP THAY THẾ

### 1. **Free Proxy Services**
```python
# Sử dụng free proxy lists (không ổn định)
FREE_PROXIES = [
    "http://proxy1.free.com:8080",
    "http://proxy2.free.com:3128",
]
```

### 2. **Paid Proxy Services** (Khuyến nghị)
```python
# Reliable proxy services
PROXY_SERVICES = {
    "brightdata": "https://brightdata.com",
    "oxylabs": "https://oxylabs.io", 
    "smartproxy": "https://smartproxy.com",
    "proxy-cheap": "https://proxy-cheap.com"
}
```

### 3. **VPN Solutions**
```bash
# Setup VPN on OCI instance
sudo apt install openvpn
# Configure VPN connection
```

### 4. **Rotating User Agents** (Đơn giản nhất)
```python
# Thay đổi User-Agent để tránh detection
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
]
```

### 5. **Request Delays** (Hiệu quả nhất)
```python
# Tăng delay giữa các request
import random
import asyncio

async def search_with_delay():
    await asyncio.sleep(random.uniform(5, 15))  # 5-15 giây
    # Thực hiện search
```

---

## 🎯 KHUYẾN NGHỊ CHO AI_SEARCH

### 1. **Ngắn hạn (Immediate)**
```python
# Tăng delay trong search_engine_service.py
delay = random.uniform(10.0, 20.0)  # Tăng từ 1-3s lên 10-20s
await asyncio.sleep(delay)
```

### 2. **Trung hạn (1-2 tuần)**
```python
# Mua proxy service chất lượng
PROXY_CONFIG = {
    "provider": "brightdata",
    "endpoint": "brd-customer-xxx.zproxy.lum-superproxy.io:22225",
    "username": "brd-customer-xxx",
    "password": "your-password"
}
```

### 3. **Dài hạn (1 tháng)**
```python
# Implement multiple search engines
SEARCH_ENGINES = [
    "duckduckgo",
    "bing_api",
    "google_custom_search",
    "serp_api"
]
```

---

## 🔍 TEST NETWORK CONNECTIVITY

### 1. **Test Outbound Connectivity**
```bash
# Test từ OCI instance
curl -I http://httpbin.org/ip
curl -I https://duckduckgo.com

# Test với proxy
curl -x ******************************:port http://httpbin.org/ip
```

### 2. **Check OCI Security Groups**
```bash
# List security groups
oci network security-list list --compartment-id <compartment-id>

# Check egress rules
oci network security-list get --security-list-id <security-list-id>
```

### 3. **Network Troubleshooting**
```bash
# Check DNS resolution
nslookup duckduckgo.com
dig duckduckgo.com

# Check routing
traceroute duckduckgo.com
mtr duckduckgo.com
```

---

## 📊 KẾT LUẬN

### ✅ **AI_Search Status:**
- **Architecture**: Excellent ✅
- **Code Quality**: High ✅  
- **Error Handling**: Robust ✅
- **External Dependencies**: Issues ⚠️

### 🎯 **Next Steps:**
1. **Immediate**: Increase request delays
2. **Short-term**: Find working proxy service
3. **Long-term**: Implement multiple search engines

### 💡 **Key Takeaway:**
Proxy issues are **external problems**, not code problems. The AI_Search engine is well-built and ready to work once network issues are resolved.