version: '3.8'

services:
  jina-crawler:
    build: .
    container_name: jina-crawler-mcp-proxy-8002
    ports:
      - "8002:8002"
    environment:
      - PORT=8002
      - GEMINI_API_KEY=${GEMINI_API_KEY:-AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM}
      - GEMINI_MODEL_NAME=gemini-2.5-flash-lite
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - acca-network

networks:
  acca-network:
    external: true
