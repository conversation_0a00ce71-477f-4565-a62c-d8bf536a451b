#!/usr/bin/env python3
"""
HTTP Wrapper for Jini Crawler MCP Server - Open WebUI Compatible
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Import MCP server components
from server import JinaCrawlerMCPServer

# Setup logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("jini-http-wrapper")

app = FastAPI(
    title="Jini Crawler Tools",
    description="HTTP wrapper for Jini Crawler MCP Server - Open WebUI Compatible",
    version="1.0.0",
    openapi_url=None  # Disable auto-generated OpenAPI
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global MCP server instance
mcp_server = None

@app.on_event("startup")
async def startup_event():
    """Initialize MCP server on startup"""
    global mcp_server
    mcp_server = JinaCrawlerMCPServer()
    await mcp_server.initialize()
    logger.info("🚀 Jini Crawler HTTP server started")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    global mcp_server
    if mcp_server:
        await mcp_server.cleanup()
    logger.info("✅ Jini Crawler HTTP server stopped")

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Jini Crawler HTTP Server", "tools": 9}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "server": "jini-crawler"}

@app.get("/")
async def root():
    """Root endpoint with server info"""
    return {
        "name": "Jini Crawler Tools",
        "description": "Advanced web crawler with AI processing - 9 powerful tools",
        "version": "1.0.0",
        "status": "healthy",
        "tools_count": 9,
        "endpoints": {
            "openapi": "/openapi.json",
            "health": "/health",
            "tools": "/tools/list"
        }
    }

@app.get("/openapi.json")
async def get_openapi_standard():
    """Return OpenAPI spec for Open WebUI integration (standard endpoint)"""
    try:
        spec = await get_openapi_spec()
        logger.info(f"OpenAPI spec generated with {len(spec.get('paths', {}))} paths")
        return spec
    except Exception as e:
        logger.error(f"Error generating OpenAPI spec: {e}")
        # Return minimal spec as fallback
        return {
            "openapi": "3.1.0",
            "info": {"title": "jina_crawler", "version": "1.0.0"},
            "paths": {"/health_check": {"post": {"summary": "Health check"}}}
        }

@app.get("/jini_crawler/openapi.json")
async def get_openapi():
    """Return OpenAPI spec for Open WebUI integration (custom endpoint)"""
    return await get_openapi_spec()

async def get_openapi_spec():
    """Return OpenAPI spec for Open WebUI integration"""

    try:
        # Define paths separately for better debugging
        paths = {
        "/crawl_url": {
            "post": {
                "summary": "Smart web crawler with AI summarization",
                "description": "Crawl a URL and return AI-summarized content",
                "operationId": "tool_crawl_url_post",
                "requestBody": {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/crawl_url_form_model"
                            }
                        }
                    }
                },
                "responses": {
                    "200": {
                        "description": "Successful Response",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "title": "Response Tool Crawl Url Post"
                                }
                            }
                        }
                    },
                    "422": {
                        "description": "Validation Error",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/HTTPValidationError"
                                }
                            }
                        }
                    }
                }
            }
        },
        "/ai_search": {
            "post": {
                "summary": "AI-powered comprehensive search engine",
                "description": "Perform intelligent web search with AI processing",
                "operationId": "tool_ai_search_post",
                "requestBody": {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/ai_search_form_model"
                            }
                        }
                    }
                },
                "responses": {
                    "200": {
                        "description": "Successful Response",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "title": "Response Tool AI Search Post"
                                }
                            }
                        }
                    },
                    "422": {
                        "description": "Validation Error",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/HTTPValidationError"
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    # Add more paths
    paths.update({
        "/crawl_full_article": {
            "post": {
                "summary": "Extract complete article without summarization",
                "description": "Extract full article content without AI summarization",
                "operationId": "tool_crawl_full_article_post",
                "requestBody": {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/crawl_full_article_form_model"
                            }
                        }
                    }
                },
                "responses": {
                    "200": {
                        "description": "Successful Response",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "title": "Response Tool Crawl Full Article Post"
                                }
                            }
                        }
                    },
                    "422": {
                        "description": "Validation Error",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/HTTPValidationError"
                                }
                            }
                        }
                    }
                }
            }
        },
        "/crawl_batch": {
            "post": {
                "summary": "Crawl multiple URLs in parallel",
                "description": "Crawl multiple URLs simultaneously for efficiency",
                "operationId": "tool_crawl_batch_post",
                "requestBody": {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/crawl_batch_form_model"
                            }
                        }
                    }
                },
                "responses": {
                    "200": {
                        "description": "Successful Response",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "title": "Response Tool Crawl Batch Post"
                                }
                            }
                        }
                    },
                    "422": {
                        "description": "Validation Error",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/HTTPValidationError"
                                }
                            }
                        }
                    }
                }
            }
        },
        "/search_site": {
            "post": {
                "summary": "Search within a specific website",
                "description": "Search for content within a specific website domain",
                "operationId": "tool_search_site_post",
                "requestBody": {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/search_site_form_model"
                            }
                        }
                    }
                },
                "responses": {
                    "200": {
                        "description": "Successful Response",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "title": "Response Tool Search Site Post"
                                }
                            }
                        }
                    },
                    "422": {
                        "description": "Validation Error",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/HTTPValidationError"
                                }
                            }
                        }
                    }
                }
            }
        },
        "/crawl_bypass_paywall": {
            "post": {
                "summary": "Bypass paywall for protected articles",
                "description": "Access content behind paywalls using advanced techniques",
                "operationId": "tool_crawl_bypass_paywall_post",
                "requestBody": {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/crawl_bypass_paywall_form_model"
                            }
                        }
                    }
                },
                "responses": {
                    "200": {
                        "description": "Successful Response",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "title": "Response Tool Crawl Bypass Paywall Post"
                                }
                            }
                        }
                    },
                    "422": {
                        "description": "Validation Error",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/HTTPValidationError"
                                }
                            }
                        }
                    }
                }
            }
        },
        "/health_check": {
            "post": {
                "summary": "Check crawler health status",
                "description": "Get current health and status of the crawler service",
                "operationId": "tool_health_check_post",
                "requestBody": {
                    "required": False,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/health_check_form_model"
                            }
                        }
                    }
                },
                "responses": {
                    "200": {
                        "description": "Successful Response",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "title": "Response Tool Health Check Post"
                                }
                            }
                        }
                    },
                    "422": {
                        "description": "Validation Error",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/HTTPValidationError"
                                }
                            }
                        }
                    }
                }
            }
        },
        "/get_crawler_stats": {
            "post": {
                "summary": "Get performance statistics",
                "description": "Retrieve detailed performance metrics and statistics",
                "operationId": "tool_get_crawler_stats_post",
                "requestBody": {
                    "required": False,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/get_crawler_stats_form_model"
                            }
                        }
                    }
                },
                "responses": {
                    "200": {
                        "description": "Successful Response",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "title": "Response Tool Get Crawler Stats Post"
                                }
                            }
                        }
                    },
                    "422": {
                        "description": "Validation Error",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/HTTPValidationError"
                                }
                            }
                        }
                    }
                }
            }
        },
        "/ai_search_streaming": {
            "post": {
                "summary": "AI search with real-time updates",
                "description": "Perform AI search with streaming real-time results",
                "operationId": "tool_ai_search_streaming_post",
                "requestBody": {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/ai_search_streaming_form_model"
                            }
                        }
                    }
                },
                "responses": {
                    "200": {
                        "description": "Successful Response",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "title": "Response Tool AI Search Streaming Post"
                                }
                            }
                        }
                    },
                    "422": {
                        "description": "Validation Error",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/HTTPValidationError"
                                }
                            }
                        }
                    }
                }
            }
        }
    })

    # Define components
    components = {
        "schemas": {
            "schemas": {
                "crawl_url_form_model": {
                    "properties": {
                        "url": {
                            "type": "string",
                            "title": "Url",
                            "description": "URL to crawl"
                        },
                        "max_content_length": {
                            "type": "integer",
                            "title": "Max Content Length",
                            "description": "Maximum content length",
                            "default": 10000
                        }
                    },
                    "type": "object",
                    "required": ["url"],
                    "title": "crawl_url_form_model"
                },
                "ai_search_form_model": {
                    "properties": {
                        "query": {
                            "type": "string",
                            "title": "Query",
                            "description": "Search query"
                        },
                        "enable_query_refinement": {
                            "type": "boolean",
                            "title": "Enable Query Refinement",
                            "description": "Enable AI query refinement",
                            "default": True
                        },
                        "search_type": {
                            "type": "string",
                            "title": "Search Type",
                            "description": "Type of search",
                            "default": "text"
                        },
                        "max_sources": {
                            "type": "integer",
                            "title": "Max Sources",
                            "description": "Maximum number of sources",
                            "default": 10
                        }
                    },
                    "type": "object",
                    "required": ["query"],
                    "title": "ai_search_form_model"
                },
                "crawl_full_article_form_model": {
                    "properties": {
                        "url": {
                            "type": "string",
                            "title": "Url",
                            "description": "URL of article"
                        },
                        "max_content_length": {
                            "type": "integer",
                            "title": "Max Content Length",
                            "description": "Maximum content length",
                            "default": 50000
                        }
                    },
                    "type": "object",
                    "required": ["url"],
                    "title": "crawl_full_article_form_model"
                },
                "crawl_batch_form_model": {
                    "properties": {
                        "urls": {
                            "type": "array",
                            "items": {"type": "string"},
                            "title": "URLs",
                            "description": "List of URLs to crawl"
                        },
                        "max_content_length": {
                            "type": "integer",
                            "title": "Max Content Length",
                            "description": "Maximum content length per URL",
                            "default": 10000
                        }
                    },
                    "type": "object",
                    "required": ["urls"],
                    "title": "crawl_batch_form_model"
                },
                "search_site_form_model": {
                    "properties": {
                        "site_url": {
                            "type": "string",
                            "title": "Site URL",
                            "description": "Base URL of site"
                        },
                        "query": {
                            "type": "string",
                            "title": "Query",
                            "description": "Search query"
                        },
                        "max_results": {
                            "type": "integer",
                            "title": "Max Results",
                            "description": "Maximum number of results",
                            "default": 10
                        }
                    },
                    "type": "object",
                    "required": ["site_url", "query"],
                    "title": "search_site_form_model"
                },
                "crawl_bypass_paywall_form_model": {
                    "properties": {
                        "url": {
                            "type": "string",
                            "title": "Url",
                            "description": "Paywall-protected URL"
                        },
                        "max_content_length": {
                            "type": "integer",
                            "title": "Max Content Length",
                            "description": "Maximum content length",
                            "default": 50000
                        }
                    },
                    "type": "object",
                    "required": ["url"],
                    "title": "crawl_bypass_paywall_form_model"
                },
                "health_check_form_model": {
                    "properties": {},
                    "type": "object",
                    "title": "health_check_form_model"
                },
                "get_crawler_stats_form_model": {
                    "properties": {},
                    "type": "object",
                    "title": "get_crawler_stats_form_model"
                },
                "ai_search_streaming_form_model": {
                    "properties": {
                        "query": {
                            "type": "string",
                            "title": "Query",
                            "description": "Search query"
                        },
                        "enable_query_refinement": {
                            "type": "boolean",
                            "title": "Enable Query Refinement",
                            "description": "Enable AI query refinement",
                            "default": True
                        }
                    },
                    "type": "object",
                    "required": ["query"],
                    "title": "ai_search_streaming_form_model"
                },
                "HTTPValidationError": {
                    "properties": {
                        "detail": {
                            "items": {
                                "$ref": "#/components/schemas/ValidationError"
                            },
                            "type": "array",
                            "title": "Detail"
                        }
                    },
                    "type": "object",
                    "title": "HTTPValidationError"
                },
                "ValidationError": {
                    "properties": {
                        "loc": {
                            "items": {
                                "anyOf": [
                                    {"type": "string"},
                                    {"type": "integer"}
                                ]
                            },
                            "type": "array",
                            "title": "Location"
                        },
                        "msg": {
                            "type": "string",
                            "title": "Message"
                        },
                        "type": {
                            "type": "string",
                            "title": "Error Type"
                        }
                    },
                    "type": "object",
                    "required": ["loc", "msg", "type"],
                    "title": "ValidationError"
                }
            }
        }
    }

        spec = {
            "openapi": "3.1.0",
            "info": {
                "title": "jina_crawler",
                "description": "jina_crawler MCP Server",
                "version": "1.0.0"
            },
            "servers": [
                {
                    "url": "/jina_crawler"
                }
            ],
            "paths": paths,
            "components": components
        }

        logger.info(f"Generated OpenAPI spec with {len(paths)} paths: {list(paths.keys())}")
        return spec

    except Exception as e:
        logger.error(f"Error in get_openapi_spec: {e}")
        # Return minimal spec as fallback
        return {
            "openapi": "3.1.0",
            "info": {"title": "jina_crawler", "version": "1.0.0"},
            "paths": {"/health_check": {"post": {"summary": "Health check"}}}
        }
                    "summary": "AI-powered comprehensive search engine",
                    "description": "Perform intelligent web search with AI processing",
                    "operationId": "tool_ai_search_post",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/ai_search_form_model"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successful Response",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "title": "Response Tool AI Search Post"
                                    }
                                }
                            }
                        },
                        "422": {
                            "description": "Validation Error",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/HTTPValidationError"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/crawl_full_article": {
                "post": {
                    "summary": "Extract complete article without summarization",
                    "description": "Extract full article content without AI summarization",
                    "operationId": "tool_crawl_full_article_post",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/crawl_full_article_form_model"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successful Response",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "title": "Response Tool Crawl Full Article Post"
                                    }
                                }
                            }
                        },
                        "422": {
                            "description": "Validation Error",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/HTTPValidationError"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/crawl_batch": {
                "post": {
                    "summary": "Crawl multiple URLs in parallel",
                    "description": "Crawl multiple URLs simultaneously for efficiency",
                    "operationId": "tool_crawl_batch_post",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/crawl_batch_form_model"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successful Response",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "title": "Response Tool Crawl Batch Post"
                                    }
                                }
                            }
                        },
                        "422": {
                            "description": "Validation Error",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/HTTPValidationError"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/search_site": {
                "post": {
                    "summary": "Search within a specific website",
                    "description": "Search for content within a specific website domain",
                    "operationId": "tool_search_site_post",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/search_site_form_model"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successful Response",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "title": "Response Tool Search Site Post"
                                    }
                                }
                            }
                        },
                        "422": {
                            "description": "Validation Error",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/HTTPValidationError"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/crawl_bypass_paywall": {
                "post": {
                    "summary": "Bypass paywall for protected articles",
                    "description": "Access content behind paywalls using advanced techniques",
                    "operationId": "tool_crawl_bypass_paywall_post",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/crawl_bypass_paywall_form_model"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successful Response",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "title": "Response Tool Crawl Bypass Paywall Post"
                                    }
                                }
                            }
                        },
                        "422": {
                            "description": "Validation Error",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/HTTPValidationError"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/health_check": {
                "post": {
                    "summary": "Check crawler health status",
                    "description": "Get current health and status of the crawler service",
                    "operationId": "tool_health_check_post",
                    "requestBody": {
                        "required": False,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/health_check_form_model"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successful Response",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "title": "Response Tool Health Check Post"
                                    }
                                }
                            }
                        },
                        "422": {
                            "description": "Validation Error",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/HTTPValidationError"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/get_crawler_stats": {
                "post": {
                    "summary": "Get performance statistics",
                    "description": "Retrieve detailed performance metrics and statistics",
                    "operationId": "tool_get_crawler_stats_post",
                    "requestBody": {
                        "required": False,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/get_crawler_stats_form_model"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successful Response",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "title": "Response Tool Get Crawler Stats Post"
                                    }
                                }
                            }
                        },
                        "422": {
                            "description": "Validation Error",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/HTTPValidationError"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/ai_search_streaming": {
                "post": {
                    "summary": "AI search with real-time updates",
                    "description": "Perform AI search with streaming real-time results",
                    "operationId": "tool_ai_search_streaming_post",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/ai_search_streaming_form_model"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successful Response",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "title": "Response Tool AI Search Streaming Post"
                                    }
                                }
                            }
                        },
                        "422": {
                            "description": "Validation Error",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/HTTPValidationError"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        "components": {
            "schemas": {
                "crawl_url_form_model": {
                    "properties": {
                        "url": {
                            "type": "string",
                            "title": "Url",
                            "description": "URL to crawl"
                        },
                        "max_content_length": {
                            "type": "integer",
                            "title": "Max Content Length",
                            "description": "Maximum content length",
                            "default": 10000
                        }
                    },
                    "type": "object",
                    "required": ["url"],
                    "title": "crawl_url_form_model"
                },
                "ai_search_form_model": {
                    "properties": {
                        "query": {
                            "type": "string",
                            "title": "Query",
                            "description": "Search query"
                        },
                        "enable_query_refinement": {
                            "type": "boolean",
                            "title": "Enable Query Refinement",
                            "description": "Enable AI query refinement",
                            "default": True
                        },
                        "search_type": {
                            "type": "string",
                            "title": "Search Type",
                            "description": "Type of search",
                            "default": "text"
                        },
                        "max_sources": {
                            "type": "integer",
                            "title": "Max Sources",
                            "description": "Maximum number of sources",
                            "default": 10
                        }
                    },
                    "type": "object",
                    "required": ["query"],
                    "title": "ai_search_form_model"
                },
                "crawl_full_article_form_model": {
                    "properties": {
                        "url": {
                            "type": "string",
                            "title": "Url",
                            "description": "URL of article"
                        },
                        "max_content_length": {
                            "type": "integer",
                            "title": "Max Content Length",
                            "description": "Maximum content length",
                            "default": 50000
                        }
                    },
                    "type": "object",
                    "required": ["url"],
                    "title": "crawl_full_article_form_model"
                },
                "crawl_batch_form_model": {
                    "properties": {
                        "urls": {
                            "type": "array",
                            "items": {"type": "string"},
                            "title": "URLs",
                            "description": "List of URLs to crawl"
                        },
                        "max_content_length": {
                            "type": "integer",
                            "title": "Max Content Length",
                            "description": "Maximum content length per URL",
                            "default": 10000
                        }
                    },
                    "type": "object",
                    "required": ["urls"],
                    "title": "crawl_batch_form_model"
                },
                "search_site_form_model": {
                    "properties": {
                        "site_url": {
                            "type": "string",
                            "title": "Site URL",
                            "description": "Base URL of site"
                        },
                        "query": {
                            "type": "string",
                            "title": "Query",
                            "description": "Search query"
                        },
                        "max_results": {
                            "type": "integer",
                            "title": "Max Results",
                            "description": "Maximum number of results",
                            "default": 10
                        }
                    },
                    "type": "object",
                    "required": ["site_url", "query"],
                    "title": "search_site_form_model"
                },
                "crawl_bypass_paywall_form_model": {
                    "properties": {
                        "url": {
                            "type": "string",
                            "title": "Url",
                            "description": "Paywall-protected URL"
                        },
                        "max_content_length": {
                            "type": "integer",
                            "title": "Max Content Length",
                            "description": "Maximum content length",
                            "default": 50000
                        }
                    },
                    "type": "object",
                    "required": ["url"],
                    "title": "crawl_bypass_paywall_form_model"
                },
                "get_crawler_stats_form_model": {
                    "properties": {},
                    "type": "object",
                    "title": "get_crawler_stats_form_model"
                },
                "ai_search_streaming_form_model": {
                    "properties": {
                        "query": {
                            "type": "string",
                            "title": "Query",
                            "description": "Search query"
                        },
                        "enable_query_refinement": {
                            "type": "boolean",
                            "title": "Enable Query Refinement",
                            "description": "Enable AI query refinement",
                            "default": True
                        }
                    },
                    "type": "object",
                    "required": ["query"],
                    "title": "ai_search_streaming_form_model"
                },
                "health_check_form_model": {
                    "properties": {},
                    "type": "object",
                    "title": "health_check_form_model"
                },
                "HTTPValidationError": {
                    "properties": {
                        "detail": {
                            "items": {
                                "$ref": "#/components/schemas/ValidationError"
                            },
                            "type": "array",
                            "title": "Detail"
                        }
                    },
                    "type": "object",
                    "title": "HTTPValidationError"
                },
                "ValidationError": {
                    "properties": {
                        "loc": {
                            "items": {
                                "anyOf": [
                                    {"type": "string"},
                                    {"type": "integer"}
                                ]
                            },
                            "type": "array",
                            "title": "Location"
                        },
                        "msg": {
                            "type": "string",
                            "title": "Message"
                        },
                        "type": {
                            "type": "string",
                            "title": "Error Type"
                        }
                    },
                    "type": "object",
                    "required": ["loc", "msg", "type"],
                    "title": "ValidationError"
                }
            }
        }
    }

# Individual endpoint handlers for MCPO compatibility
@app.post("/crawl_url")
async def crawl_url_endpoint(payload: Dict[str, Any]):
    """Crawl URL endpoint"""
    return await handle_crawl_url(payload)

@app.post("/ai_search")
async def ai_search_endpoint(payload: Dict[str, Any]):
    """AI Search endpoint"""
    return await handle_ai_search(payload)

@app.post("/crawl_full_article")
async def crawl_full_article_endpoint(payload: Dict[str, Any]):
    """Crawl full article endpoint"""
    return await handle_crawl_full_article(payload)

@app.post("/crawl_batch")
async def crawl_batch_endpoint(payload: Dict[str, Any]):
    """Crawl batch endpoint"""
    return await handle_crawl_batch(payload)

@app.post("/search_site")
async def search_site_endpoint(payload: Dict[str, Any]):
    """Search site endpoint"""
    return await handle_search_site(payload)

@app.post("/crawl_bypass_paywall")
async def crawl_bypass_paywall_endpoint(payload: Dict[str, Any]):
    """Crawl bypass paywall endpoint"""
    return await handle_crawl_bypass_paywall(payload)

@app.post("/health_check")
async def health_check_endpoint(payload: Dict[str, Any] = None):
    """Health check endpoint"""
    if payload is None:
        payload = {}
    return await handle_health_check(payload)

@app.post("/get_crawler_stats")
async def get_crawler_stats_endpoint(payload: Dict[str, Any] = None):
    """Get crawler stats endpoint"""
    if payload is None:
        payload = {}
    return await handle_get_crawler_stats(payload)

@app.post("/ai_search_streaming")
async def ai_search_streaming_endpoint(payload: Dict[str, Any]):
    """AI search streaming endpoint"""
    return await handle_ai_search_streaming(payload)

@app.post("/tools/{tool_name}")
async def execute_tool(tool_name: str, payload: Dict[str, Any]):
    """Execute a specific tool"""
    global mcp_server

    if not mcp_server:
        raise HTTPException(status_code=500, detail="MCP server not initialized")

    try:
        # Route to appropriate tool handler based on tool name
        if tool_name == "crawl_url":
            result = await handle_crawl_url(payload)
        elif tool_name == "crawl_full_article":
            result = await handle_crawl_full_article(payload)
        elif tool_name == "crawl_batch":
            result = await handle_crawl_batch(payload)
        elif tool_name == "ai_search":
            result = await handle_ai_search(payload)
        elif tool_name == "search_site":
            result = await handle_search_site(payload)
        elif tool_name == "crawl_bypass_paywall":
            result = await handle_crawl_bypass_paywall(payload)
        elif tool_name == "health_check":
            result = await handle_health_check(payload)
        elif tool_name == "get_crawler_stats":
            result = await handle_get_crawler_stats(payload)
        elif tool_name == "ai_search_streaming":
            result = await handle_ai_search_streaming(payload)
        else:
            raise HTTPException(status_code=404, detail=f"Tool '{tool_name}' not found")

        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Tool execution failed: {e}")
        raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")

# Tool handler functions
async def handle_crawl_url(payload: Dict[str, Any]):
    """Handle crawl_url tool"""
    url = payload.get("url")
    max_content_length = payload.get("max_content_length", 10000)

    if not url:
        raise HTTPException(status_code=400, detail="URL is required")

    result = await mcp_server.crawler.crawl_and_process(url, max_content_length)
    return {
        "success": result.success,
        "url": result.url,
        "title": result.title,
        "processed_content": result.processed_content,
        "processing_time": result.processing_time,
        "error": result.error,
        "metadata": result.metadata
    }

async def handle_crawl_full_article(payload: Dict[str, Any]):
    """Handle crawl_full_article tool"""
    url = payload.get("url")
    max_content_length = payload.get("max_content_length", 50000)

    if not url:
        raise HTTPException(status_code=400, detail="URL is required")

    result = await mcp_server.crawler.crawl_full_article(url, max_content_length)
    return {
        "success": result.success,
        "url": result.url,
        "title": result.title,
        "full_content": result.full_content,
        "processing_time": result.processing_time,
        "error": result.error,
        "metadata": result.metadata
    }

async def handle_crawl_batch(payload: Dict[str, Any]):
    """Handle crawl_batch tool"""
    urls = payload.get("urls", [])
    max_content_length = payload.get("max_content_length", 10000)

    if not urls:
        raise HTTPException(status_code=400, detail="URLs list is required")

    # Process URLs in parallel
    tasks = []
    for url in urls:
        task = mcp_server.crawler.crawl_and_process(url, max_content_length)
        tasks.append(task)

    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Format results
    formatted_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            formatted_results.append({
                "success": False,
                "url": urls[i],
                "error": str(result)
            })
        else:
            formatted_results.append({
                "success": result.success,
                "url": result.url,
                "title": result.title,
                "processed_content": result.processed_content,
                "processing_time": result.processing_time,
                "error": result.error,
                "metadata": result.metadata
            })

    return {
        "batch_size": len(urls),
        "successful_crawls": sum(1 for r in formatted_results if r["success"]),
        "failed_crawls": sum(1 for r in formatted_results if not r["success"]),
        "results": formatted_results
    }

async def handle_ai_search(payload: Dict[str, Any]):
    """Handle ai_search tool"""
    query = payload.get("query")
    enable_query_refinement = payload.get("enable_query_refinement", True)
    search_type = payload.get("search_type", "text")
    max_sources = payload.get("max_sources", 10)

    if not query:
        raise HTTPException(status_code=400, detail="Query is required")

    result = await mcp_server.ai_search_engine.search(
        query=query,
        enable_query_refinement=enable_query_refinement,
        search_type=search_type,
        max_sources=max_sources
    )
    return result

async def handle_search_site(payload: Dict[str, Any]):
    """Handle search_site tool"""
    site_url = payload.get("site_url")
    query = payload.get("query")
    max_results = payload.get("max_results", 10)

    if not site_url or not query:
        raise HTTPException(status_code=400, detail="site_url and query are required")

    # Use site-specific search logic
    search_query = f"site:{site_url} {query}"
    result = await mcp_server.ai_search_engine.search(
        query=search_query,
        enable_query_refinement=False,
        search_type="text",
        max_sources=max_results
    )
    return result

async def handle_crawl_bypass_paywall(payload: Dict[str, Any]):
    """Handle crawl_bypass_paywall tool"""
    url = payload.get("url")
    max_content_length = payload.get("max_content_length", 50000)

    if not url:
        raise HTTPException(status_code=400, detail="URL is required")

    result = await mcp_server.paywall_crawler.bypass_and_crawl(url, max_content_length)
    return {
        "success": result.success,
        "url": result.url,
        "title": result.title,
        "content": result.content,
        "bypass_method": result.bypass_method,
        "processing_time": result.processing_time,
        "error": result.error
    }

async def handle_health_check(payload: Dict[str, Any]):
    """Handle health_check tool"""
    try:
        health = await mcp_server.crawler.health_check()
        return {
            "status": "healthy",
            "crawler_health": health,
            "server_type": "jini_crawler"
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }

async def handle_get_crawler_stats(payload: Dict[str, Any]):
    """Handle get_crawler_stats tool"""
    return {
        "server_type": "jini_crawler",
        "features": [
            "Jina-style web crawling",
            "BeautifulSoup HTML cleaning",
            "Gemini AI content processing",
            "Full article extraction",
            "Paywall bypass capabilities",
            "AI-powered search engine",
            "Batch processing support",
            "Vietnamese content optimization",
            "Real-time streaming search"
        ],
        "tools_available": 9,
        "auth_required": False
    }

async def handle_ai_search_streaming(payload: Dict[str, Any]):
    """Handle ai_search_streaming tool"""
    query = payload.get("query")
    enable_query_refinement = payload.get("enable_query_refinement", True)

    if not query:
        raise HTTPException(status_code=400, detail="Query is required")

    # For now, use the same logic as ai_search but with streaming indication
    result = await mcp_server.ai_search_engine.search(
        query=query,
        enable_query_refinement=enable_query_refinement,
        search_type="text",
        max_sources=10
    )

    # Add streaming metadata
    result["streaming"] = True
    result["real_time_updates"] = True
    return result

@app.post("/tools/call")
async def call_tool(payload: Dict[str, Any]):
    """Call a tool with name and arguments"""
    global mcp_server

    if not mcp_server:
        raise HTTPException(status_code=500, detail="MCP server not initialized")

    tool_name = payload.get("name")
    arguments = payload.get("arguments", {})

    if not tool_name:
        raise HTTPException(status_code=400, detail="Tool name is required")

    try:
        # Route to appropriate tool handler
        if tool_name == "crawl_url":
            result = await handle_crawl_url(arguments)
        elif tool_name == "crawl_full_article":
            result = await handle_crawl_full_article(arguments)
        elif tool_name == "crawl_batch":
            result = await handle_crawl_batch(arguments)
        elif tool_name == "ai_search":
            result = await handle_ai_search(arguments)
        elif tool_name == "search_site":
            result = await handle_search_site(arguments)
        elif tool_name == "crawl_bypass_paywall":
            result = await handle_crawl_bypass_paywall(arguments)
        elif tool_name == "health_check":
            result = await handle_health_check(arguments)
        elif tool_name == "get_crawler_stats":
            result = await handle_get_crawler_stats(arguments)
        elif tool_name == "ai_search_streaming":
            result = await handle_ai_search_streaming(arguments)
        else:
            raise HTTPException(status_code=404, detail=f"Tool '{tool_name}' not found")

        return JSONResponse(content={"result": result})

    except Exception as e:
        logger.error(f"Tool execution failed: {e}")
        raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")

@app.post("/tools/list")
async def list_tools():
    """List available tools"""
    return JSONResponse(content={
        "tools": [
            {"name": "crawl_url", "description": "Smart web crawler with AI summarization"},
            {"name": "crawl_full_article", "description": "Extract complete article without summarization"},
            {"name": "crawl_batch", "description": "Crawl multiple URLs in parallel"},
            {"name": "ai_search", "description": "AI-powered comprehensive search engine"},
            {"name": "search_site", "description": "Search within a specific website"},
            {"name": "crawl_bypass_paywall", "description": "Bypass paywall for protected articles"},
            {"name": "health_check", "description": "Check crawler health status"},
            {"name": "get_crawler_stats", "description": "Get performance statistics"},
            {"name": "ai_search_streaming", "description": "AI search with real-time updates"}
        ]
    })

if __name__ == "__main__":
    import os
    port = int(os.environ.get("PORT", 8009))
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info"
    )
