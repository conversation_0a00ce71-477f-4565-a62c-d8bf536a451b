
@app.post("/ai_web_search", operation_id="ai_web_search")
async def ai_web_search(request: AIWebSearchRequest):
    """🧠 AI WEB SEARCH: Use this for complex questions that require web search and synthesis. Provides a Perplexity-like experience by searching, crawling, and generating a comprehensive answer. Use for questions like 'What are the latest AI trends?' or 'Summarize the news about climate change'."""
    global ai_search_engine
    if not ai_search_engine:
        raise HTTPException(status_code=500, detail="AI Search Engine not initialized")
        
    try:
        result = await ai_search_engine.search(
            query=request.query,
            enable_query_refinement=request.enable_query_refinement,
            search_type=request.search_type
        )
        
        # Convert result to dict for JSON response
        response_data = ai_search_engine.to_dict(result)
        
        return JSONResponse(content=response_data)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI search failed: {str(e)}")

# This endpoint is more complex due to streaming
from fastapi.responses import StreamingResponse

@app.post("/ai_web_search_streaming", operation_id="ai_web_search_streaming")
async def ai_web_search_streaming(request: AIWebSearchRequest):
    """⚡️ AI WEB SEARCH (STREAMING): Use for real-time, interactive AI search. Provides a stream of updates as the search progresses, from refining the query to synthesizing the final answer. Ideal for user-facing applications where immediate feedback is important."""
    global ai_search_engine
    if not ai_search_engine:
        raise HTTPException(status_code=500, detail="AI Search Engine not initialized")

    # Create a queue to pass messages from the callback to the streaming response
    queue = asyncio.Queue()

    async def stream_callback(data: dict):
        """Callback to put data into the queue"""
        await queue.put(json.dumps(data))

    async def stream_generator():
        """Generator that yields data from the queue"""
        # Start the search process in the background
        search_task = asyncio.create_task(
            ai_search_engine.search_streaming(
                query=request.query,
                callback=stream_callback,
                enable_query_refinement=request.enable_query_refinement
            )
        )
        
        # Add a finalizer to the queue to signal the end
        search_task.add_done_callback(lambda t: asyncio.create_task(queue.put(None)))

        while True:
            item = await queue.get()
            if item is None:
                break
            yield f"data: {item}\n\n"
            
        try:
            await search_task
        except Exception as e:
            # If the task failed, we can log it here
            print(f"Streaming search task failed: {e}")


    return StreamingResponse(stream_generator(), media_type="text/event-stream")


if __name__ == "__main__":
    import os
    port = int(os.environ.get("PORT", 8009))
    uvicorn.run(
        "cloudflare_resistant_http_wrapper:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info"
    )
