#!/usr/bin/env python3
"""
🔧 Force Open WebUI Tool Refresh Script
Giúp Open WebUI phát hiện và sử dụng 2 tools mới: crawl_full_article và crawl_bypass_paywall
"""

import requests
import json
import time
import sys
from typing import Dict, Any

def check_server_health() -> bool:
    """Kiểm tra server có hoạt động không"""
    try:
        response = requests.get("http://localhost:8009/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def get_server_tools() -> Dict[str, Any]:
    """Lấy danh sách tools từ OpenAPI schema"""
    try:
        response = requests.get("http://localhost:8009/openapi.json", timeout=10)
        if response.status_code == 200:
            schema = response.json()
            return schema.get('paths', {})
        return {}
    except Exception as e:
        print(f"❌ Lỗi khi lấy OpenAPI schema: {e}")
        return {}

def test_new_tools() -> Dict[str, bool]:
    """Test 2 tools mới"""
    results = {}
    
    # Test crawl_full_article
    try:
        response = requests.post(
            "http://localhost:8009/crawl_full_article",
            json={"url": "https://httpbin.org/html"},
            timeout=30
        )
        results['crawl_full_article'] = response.status_code == 200
    except:
        results['crawl_full_article'] = False
    
    # Test crawl_bypass_paywall  
    try:
        response = requests.post(
            "http://localhost:8009/crawl_bypass_paywall",
            json={"url": "https://httpbin.org/html"},
            timeout=30
        )
        results['crawl_bypass_paywall'] = response.status_code == 200
    except:
        results['crawl_bypass_paywall'] = False
        
    return results

def print_tool_analysis(tools: Dict[str, Any]):
    """Phân tích và hiển thị thông tin tools"""
    print("\n📋 PHÂN TÍCH TOOLS:")
    print("=" * 50)
    
    # Đếm tools
    total_endpoints = len(tools)
    main_tools = [k for k in tools.keys() if k not in ['/', '/health']]
    
    print(f"🔢 Tổng endpoints: {total_endpoints}")
    print(f"🛠️  Main tools: {len(main_tools)}")
    
    # Kiểm tra 2 tools mới
    new_tools = ['/crawl_full_article', '/crawl_bypass_paywall']
    print(f"\n🔥 TOOLS MỚI:")
    
    for tool in new_tools:
        if tool in tools:
            tool_info = tools[tool].get('post', {})
            summary = tool_info.get('summary', 'No summary')
            description = tool_info.get('description', 'No description')
            print(f"  ✅ {tool}")
            print(f"     📝 {summary}")
            print(f"     📄 {description}")
        else:
            print(f"  ❌ {tool} - MISSING!")
    
    # Hiển thị tất cả tools
    print(f"\n📋 TẤT CẢ ENDPOINTS:")
    for i, endpoint in enumerate(sorted(tools.keys()), 1):
        icon = "🔥" if endpoint in new_tools else "⚡" if endpoint not in ['/', '/health'] else "ℹ️"
        print(f"  {i:2d}. {icon} {endpoint}")

def generate_openwebui_instructions():
    """Tạo hướng dẫn cho Open WebUI"""
    instructions = """
🎯 HƯỚNG DẪN REFRESH OPEN WEBUI:

1. 🔄 RESTART OPEN WEBUI:
   docker restart openwebui

2. 🧹 CLEAR BROWSER CACHE:
   - Ctrl+Shift+R (hard refresh)
   - F12 → Application → Clear Storage

3. 🔧 RE-ADD FUNCTION:
   - Admin Panel → Functions
   - Delete old function (if exists)
   - Add new: http://localhost:8009/openapi.json
   - Save & Enable

4. ✅ VERIFY:
   - Check function shows "9 tools"
   - Test với prompt: "crawl full article from [URL]"
   - Test với prompt: "bypass paywall for [URL]"

5. 🎯 TRIGGER PHRASES cho LLM:
   - "full article" → sẽ dùng crawl_full_article
   - "bypass paywall" → sẽ dùng crawl_bypass_paywall
   - "complete content" → sẽ dùng crawl_full_article
   - "unfiltered content" → sẽ dùng crawl_bypass_paywall
"""
    return instructions

def main():
    print("🚀 FORCE OPEN WEBUI TOOL REFRESH")
    print("=" * 50)
    
    # 1. Kiểm tra server
    print("1️⃣ Kiểm tra server...")
    if not check_server_health():
        print("❌ Server không hoạt động trên port 8009!")
        print("   Chạy: python3 cloudflare_resistant_http_wrapper.py")
        sys.exit(1)
    print("✅ Server hoạt động tốt")
    
    # 2. Lấy danh sách tools
    print("\n2️⃣ Lấy danh sách tools...")
    tools = get_server_tools()
    if not tools:
        print("❌ Không thể lấy OpenAPI schema!")
        sys.exit(1)
    
    # 3. Phân tích tools
    print_tool_analysis(tools)
    
    # 4. Test 2 tools mới
    print("\n3️⃣ Test 2 tools mới...")
    test_results = test_new_tools()
    
    for tool, success in test_results.items():
        status = "✅ HOẠT ĐỘNG" if success else "❌ LỖI"
        print(f"  {tool}: {status}")
    
    # 5. Tạo hướng dẫn
    print("\n4️⃣ Hướng dẫn refresh Open WebUI:")
    print(generate_openwebui_instructions())
    
    # 6. Kết luận
    print("\n🎯 KẾT LUẬN:")
    if all(test_results.values()):
        print("✅ 2 tools mới hoạt động hoàn hảo!")
        print("🔧 Vấn đề là ở Open WebUI cache - cần restart/refresh")
        print("📝 Follow hướng dẫn trên để fix")
    else:
        print("❌ Có tools bị lỗi - cần debug server trước")
    
    print(f"\n📊 SUMMARY:")
    print(f"   📋 Total endpoints: {len(tools)}")
    print(f"   🛠️  Main tools: {len([k for k in tools.keys() if k not in ['/', '/health']])}")
    print(f"   🔥 New tools: 2 (crawl_full_article, crawl_bypass_paywall)")
    print(f"   ✅ Working: {sum(test_results.values())}/2")

if __name__ == "__main__":
    main()