#!/usr/bin/env python3
"""
MCPO-compliant HTTP Wrapper for Jina Crawler MCP Server
Follows the MCPO standard from https://github.com/open-webui/mcpo
"""

import asyncio
import json
from typing import Dict, Any
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Import the restored server components
from jini_crawler import JiniCrawler
from paywall_bypass_crawler import PaywallBypassCrawler
from ai_search.ai_search_engine import AISearchEngine

# MCPO Authentication
API_KEY = "jina-crawler-secret-key-2025"
security = HTTPBearer()

def verify_api_key(credentials: HTTPAuthorizationCredentials = Security(security)):
    """Verify API key for MCPO authentication"""
    if credentials.credentials != API_KEY:
        raise HTTPException(
            status_code=401,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return credentials.credentials

app = FastAPI(
    title="Jina Crawler MCPO Server",
    description="MCPO-compliant proxy for Jina Crawler MCP Server with authentication and auto-generated OpenAPI",
    version="1.0.0",
    docs_url="/docs",
    openapi_url="/openapi.json"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

class RestoredJinaCrawlerServer:
    """Restored Jina Crawler Server with all 10 tools"""
    
    def __init__(self):
        self.crawler = JiniCrawler()
        self.paywall_crawler = PaywallBypassCrawler()
        self.ai_search_engine = AISearchEngine()
        self.initialized = False
    
    async def initialize(self):
        """Initialize the crawler"""
        if not self.initialized:
            await self.crawler.initialize()
            await self.paywall_crawler.initialize()
            await self.ai_search_engine.initialize()
            self.initialized = True
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.initialized:
            await self.crawler.cleanup()
            await self.paywall_crawler.cleanup()
            await self.ai_search_engine.cleanup()
            self.initialized = False

# Global server instance
restored_server = RestoredJinaCrawlerServer()

@app.on_event("startup")
async def startup_event():
    """Initialize server on startup"""
    print("🚀 Restored Jina Crawler HTTP server started with 10 tools")

@app.get("/")
async def root():
    """Root endpoint - MCPO compliant"""
    return {
        "message": "Jina Crawler MCPO Server",
        "tools": 9,
        "version": "1.0.0",
        "mcpo_compliant": True,
        "authentication": "Bearer token required",
        "docs": "/docs",
        "openapi": "/openapi.json"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint - no authentication required"""
    return {
        "status": "healthy",
        "server": "jina-crawler-mcpo",
        "version": "1.0.0",
        "mcpo_compliant": True
    }

@app.get("/openapi.json")
async def get_openapi_standard():
    """Return OpenAPI spec for Open WebUI integration (standard endpoint)"""
    return await get_openapi_spec()

@app.get("/jina_crawler/openapi.json")
async def get_openapi():
    """Return OpenAPI spec for Open WebUI integration (custom endpoint)"""
    return await get_openapi_spec()

async def get_openapi_spec():
    """Return OpenAPI spec for Open WebUI integration"""
    def json_request_body(schema: dict):
        return {
            "required": True,
            "content": {
                "application/json": {
                    "schema": schema
                }
            }
        }

    def success_json_response():
        return {
            "description": "Successful Response",
            "content": {
                "application/json": {
                    "schema": {"type": "object"}
                }
            }
        }

    return {
        "openapi": "3.0.0",
        "info": {
            "title": "Restored Jina Crawler",
            "description": "Advanced web crawling with 10 tools including paywall bypass and AI search",
            "version": "1.0.0"
        },
        "components": {
            "securitySchemes": {
                "HTTPBearer": {
                    "type": "http",
                    "scheme": "bearer"
                }
            }
        },
        "security": [{"HTTPBearer": []}],
        "paths": {
            "/tools/crawl_url": {
                "post": {
                    "operationId": "jina_crawler_crawl_url",
                    "summary": "Smart summarizer with AI-powered content processing",
                    "requestBody": json_request_body({
                        "type": "object",
                        "properties": {
                            "url": {"type": "string", "description": "URL to crawl"},
                            "max_content_length": {"type": "number", "default": 10000}
                        },
                        "required": ["url"]
                    }),
                    "responses": {"200": success_json_response()}
                }
            },
            "/tools/crawl_full_article": {
                "post": {
                    "operationId": "jina_crawler_crawl_full_article",
                    "summary": "Complete article extractor without summarization",
                    "requestBody": json_request_body({
                        "type": "object",
                        "properties": {
                            "url": {"type": "string", "description": "URL to extract complete content"},
                            "max_content_length": {"type": "number", "default": 50000}
                        },
                        "required": ["url"]
                    }),
                    "responses": {"200": success_json_response()}
                }
            },
            "/tools/crawl_batch": {
                "post": {
                    "operationId": "jina_crawler_crawl_batch",
                    "summary": "Crawl multiple URLs in parallel",
                    "requestBody": json_request_body({
                        "type": "object",
                        "properties": {
                            "urls": {"type": "array", "items": {"type": "string"}},
                            "max_content_length": {"type": "number", "default": 10000}
                        },
                        "required": ["urls"]
                    }),
                    "responses": {"200": success_json_response()}
                }
            },
            "/tools/search_site": {
                "post": {
                    "operationId": "jina_crawler_search_site",
                    "summary": "Search within a specific website",
                    "requestBody": json_request_body({
                        "type": "object",
                        "properties": {
                            "site_url": {"type": "string", "description": "Base URL of site"},
                            "query": {"type": "string", "description": "Search query"},
                            "max_results": {"type": "number", "default": 10}
                        },
                        "required": ["site_url", "query"]
                    }),
                    "responses": {"200": success_json_response()}
                }
            },
            "/tools/health_check": {
                "post": {
                    "operationId": "jina_crawler_health_check",
                    "summary": "Check crawler health status",
                    "requestBody": json_request_body({"type": "object", "properties": {}}),
                    "responses": {"200": success_json_response()}
                }
            },
            "/tools/get_crawler_stats": {
                "post": {
                    "operationId": "jina_crawler_get_crawler_stats",
                    "summary": "Get performance statistics",
                    "requestBody": json_request_body({"type": "object", "properties": {}}),
                    "responses": {"200": success_json_response()}
                }
            },
            "/tools/crawl_bypass_paywall": {
                "post": {
                    "operationId": "jina_crawler_crawl_bypass_paywall",
                    "summary": "🔓 Bypass paywall and extract content",
                    "requestBody": json_request_body({
                        "type": "object",
                        "properties": {
                            "url": {"type": "string", "description": "Paywall-protected URL"},
                            "max_content_length": {"type": "number", "default": 50000}
                        },
                        "required": ["url"]
                    }),
                    "responses": {"200": success_json_response()}
                }
            },
            "/tools/ai_search": {
                "post": {
                    "operationId": "jina_crawler_ai_search",
                    "summary": "🤖 AI Search Engine with Brave support",
                    "requestBody": json_request_body({
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "Search query"},
                            "enable_query_refinement": {"type": "boolean", "default": True},
                            "search_type": {"type": "string", "enum": ["text", "news"], "default": "text"},
                            "search_engine": {"type": "string", "enum": ["duckduckgo", "brave"], "default": "duckduckgo"},
                            "max_sources": {"type": "number", "default": 10}
                        },
                        "required": ["query"]
                    }),
                    "responses": {"200": success_json_response()}
                }
            },
            "/tools/ai_search_streaming": {
                "post": {
                    "operationId": "jina_crawler_ai_search_streaming",
                    "summary": "🚀 AI Search with real-time updates",
                    "requestBody": json_request_body({
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "Search query"},
                            "enable_query_refinement": {"type": "boolean", "default": True}
                        },
                        "required": ["query"]
                    }),
                    "responses": {"200": success_json_response()}
                }
            }
        }
    }

@app.post("/tools/{tool_name}")
async def execute_tool(
    tool_name: str,
    payload: Dict[str, Any],
    api_key: str = Depends(verify_api_key)
):
    """Execute a specific tool - MCPO compliant with authentication"""
    global restored_server
    
    try:
        # Initialize server if needed
        await restored_server.initialize()
        
        if tool_name == "crawl_url":
            url = payload["url"]
            max_content_length = payload.get("max_content_length", 10000)
            
            result = await restored_server.crawler.crawl_and_process(url, max_content_length)
            
            return {
                "success": result.success,
                "url": result.url,
                "title": result.title,
                "processed_content": result.processed_content,
                "processing_time": result.processing_time,
                "error": result.error,
                "metadata": result.metadata,
                "crawler_type": "jina_style_with_gemini"
            }
        
        elif tool_name == "crawl_full_article":
            url = payload["url"]
            max_content_length = payload.get("max_content_length", 50000)
            
            result = await restored_server.crawler.crawl_full_article(url, max_content_length)
            
            return {
                "success": result.success,
                "url": result.url,
                "title": result.title,
                "full_article_content": result.processed_content,
                "processing_time": result.processing_time,
                "error": result.error,
                "metadata": result.metadata,
                "crawler_type": "jina_full_article",
                "content_type": "complete_article"
            }
        
        elif tool_name == "crawl_batch":
            urls = payload["urls"]
            max_content_length = payload.get("max_content_length", 10000)
            
            # Process URLs in parallel
            tasks = []
            for url in urls:
                task = restored_server.crawler.crawl_and_process(url, max_content_length)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Format results
            formatted_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    formatted_results.append({
                        "success": False,
                        "url": urls[i],
                        "error": str(result)
                    })
                else:
                    formatted_results.append({
                        "success": result.success,
                        "url": result.url,
                        "title": result.title,
                        "processed_content": result.processed_content,
                        "processing_time": result.processing_time,
                        "error": result.error,
                        "metadata": result.metadata
                    })
            
            return {
                "batch_size": len(urls),
                "successful_crawls": sum(1 for r in formatted_results if r["success"]),
                "failed_crawls": sum(1 for r in formatted_results if not r["success"]),
                "results": formatted_results,
                "crawler_type": "jina_style_batch"
            }
        
        elif tool_name == "search_site":
            site_url = payload["site_url"]
            query = payload["query"]
            max_results = payload.get("max_results", 10)
            
            # Use jini_crawler for site search instead of jina_style_crawler
            try:
                # Simple implementation using jini_crawler
                search_url = f"{site_url}/search?q={query}"
                result = await restored_server.crawler.crawl_and_process(search_url, 5000)
                
                return {
                    "site_url": site_url,
                    "query": query,
                    "success": result.success,
                    "total_found": 1 if result.success else 0,
                    "results": [{"title": result.title, "url": result.url, "snippet": result.processed_content[:200]}] if result.success else [],
                    "error": result.error,
                    "search_type": "site_search_via_jini_crawler"
                }
            except Exception as e:
                return {
                    "site_url": site_url,
                    "query": query,
                    "success": False,
                    "total_found": 0,
                    "results": [],
                    "error": str(e),
                    "search_type": "site_search_via_jini_crawler"
                }
        
        elif tool_name == "health_check":
            health = await restored_server.crawler.health_check()
            
            return {
                "health_check": health,
                "server_status": "operational" if health.get("status") == "healthy" else "degraded"
            }
        
        elif tool_name == "get_crawler_stats":
            return {
                "server_initialized": restored_server.initialized,
                "crawler_type": "jina_style_with_gemini",
                "features": [
                    "aiohttp-based crawling",
                    "BeautifulSoup HTML cleaning", 
                    "Gemini 2.5 Flash Lite processing",
                    "Vietnamese content optimization",
                    "Async parallel processing",
                    "Brave Search fallback",
                    "Paywall bypass (8 techniques)",
                    "AI Search streaming"
                ]
            }
        
        elif tool_name == "crawl_bypass_paywall":
            url = payload["url"]
            max_content_length = payload.get("max_content_length", 50000)
            
            result = await restored_server.paywall_crawler.crawl_with_paywall_bypass(url, max_content_length)
            
            return {
                "success": result.success,
                "url": result.url,
                "title": result.title,
                "full_article_content": result.processed_content,
                "processing_time": result.processing_time,
                "error": result.error,
                "metadata": result.metadata,
                "crawler_type": "paywall_bypass",
                "bypass_method": result.metadata.get("bypass_method", "unknown") if result.metadata else "unknown",
                "content_type": "paywall_bypassed_article"
            }
        
        elif tool_name == "ai_search":
            query = payload["query"]
            enable_query_refinement = payload.get("enable_query_refinement", True)
            search_type = payload.get("search_type", "text")
            max_sources = payload.get("max_sources", 10)

            # Update AI search engine max results if needed
            restored_server.ai_search_engine.max_search_results = max_sources

            try:
                result = await restored_server.ai_search_engine.search(
                    query=query,
                    enable_query_refinement=enable_query_refinement,
                    search_type=search_type
                )
            except Exception as e:
                # Ensure cleanup even on error
                try:
                    await restored_server.ai_search_engine.cleanup()
                except:
                    pass
                raise

            if result.success and result.synthesized_answer:
                return {
                    "success": True,
                    "query": result.query,
                    "refined_query": result.refined_query.refined_query if result.refined_query else None,
                    "answer": result.synthesized_answer.answer,
                    "citations": [
                        {
                            "url": citation.url,
                            "title": citation.title,
                            "snippet": citation.snippet,
                            "relevance_score": citation.relevance_score
                        }
                        for citation in result.synthesized_answer.citations
                    ],
                    "confidence": result.synthesized_answer.confidence,
                    "sources_used": result.synthesized_answer.sources_used,
                    "word_count": result.synthesized_answer.word_count,
                    "total_time": result.total_time,
                    "metadata": result.metadata,
                    "search_type": "ai_search_perplexity_style"
                }
            else:
                return {
                    "success": False,
                    "query": result.query,
                    "error": result.error,
                    "total_time": result.total_time,
                    "metadata": result.metadata,
                    "search_type": "ai_search_perplexity_style"
                }
        
        elif tool_name == "ai_search_streaming":
            query = payload["query"]
            enable_query_refinement = payload.get("enable_query_refinement", True)

            # For streaming, we'll collect updates and return them all at once
            updates = []

            async def collect_updates(update):
                updates.append(update)

            await restored_server.ai_search_engine.search_streaming(
                query=query,
                callback=collect_updates,
                enable_query_refinement=enable_query_refinement
            )

            return {
                "success": True,
                "query": query,
                "streaming_updates": updates,
                "search_type": "ai_search_streaming"
            }
        
        else:
            raise HTTPException(status_code=404, detail=f"Tool '{tool_name}' not found")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Tool execution failed: {str(e)}")

@app.get("/tools/list")
async def list_tools(api_key: str = Depends(verify_api_key)):
    """List all available tools - MCPO compliant with authentication"""
    return {
        "tools": [
            {"name": "crawl_url", "description": "📄 Smart summarizer with AI processing"},
            {"name": "crawl_full_article", "description": "📰 Complete article extractor"},
            {"name": "crawl_batch", "description": "🔄 Batch crawl multiple URLs"},
            {"name": "search_site", "description": "🔍 Search within specific website"},
            {"name": "health_check", "description": "🏥 Check crawler health"},
            {"name": "get_crawler_stats", "description": "📊 Get performance statistics"},
            {"name": "crawl_bypass_paywall", "description": "🔓 Bypass paywall (8 techniques)"},
            {"name": "ai_search", "description": "🤖 AI Search Engine with Brave support"},
            {"name": "ai_search_streaming", "description": "🚀 AI Search with streaming updates"}
        ],
        "total_tools": 9,
        "server": "jina-crawler-mcpo",
        "version": "1.0.0",
        "mcpo_compliant": True
    }

if __name__ == "__main__":
    import os
    port = int(os.environ.get("PORT", 8002))
    print(f"🚀 Starting Jina Crawler MCPO Server on port {port}")
    print(f"🔑 API Key: {API_KEY}")
    print(f"📚 Docs: http://localhost:{port}/docs")
    print(f"🔍 OpenAPI: http://localhost:{port}/openapi.json")
    uvicorn.run(
        "http_wrapper:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info"
    )