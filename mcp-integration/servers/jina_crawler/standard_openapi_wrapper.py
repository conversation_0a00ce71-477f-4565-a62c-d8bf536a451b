#!/usr/bin/env python3
"""
Standard OpenAPI Wrapper for Simple Jina Crawler
Compatible with Open WebUI
"""

import asyncio
import json
from typing import Dict, Any
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from jini_crawler import JiniCrawler

app = FastAPI(
    title="Jina Crawler Tools",
    description="Standard OpenAPI wrapper for Jina Crawler - Compatible with Open WebUI",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global crawler instance
crawler = None

@app.on_event("startup")
async def startup_event():
    """Initialize crawler on startup"""
    global crawler
    crawler = JiniCrawler()
    await crawler.initialize()
    print("🚀 Standard Jina Crawler OpenAPI server started")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    global crawler
    if crawler:
        await crawler.cleanup()
    print("✅ Standard Jina Crawler OpenAPI server stopped")

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Standard Jina Crawler OpenAPI Server", "tools": 5}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "server": "standard-jina-crawler"}

@app.get("/openapi.json")
async def get_openapi():
    """Return STANDARD OpenAPI spec for Open WebUI integration"""
    return {
        "openapi": "3.0.0",
        "info": {
            "title": "Jina Crawler Tools",
            "description": "Standard OpenAPI wrapper for Jina Crawler - Compatible with Open WebUI",
            "version": "1.0.0"
        },
        "paths": {
            "/crawl_url": {
                "post": {
                    "summary": "Crawl and process a single URL",
                    "description": "Crawl a URL and process it with Gemini AI to extract clean content",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "url": {
                                            "type": "string",
                                            "description": "URL to crawl and process",
                                            "example": "https://dantri.com.vn"
                                        },
                                        "max_content_length": {
                                            "type": "integer",
                                            "description": "Maximum content length to process",
                                            "default": 10000,
                                            "minimum": 1000,
                                            "maximum": 50000
                                        }
                                    },
                                    "required": ["url"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successful Response",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "success": {"type": "boolean"},
                                            "url": {"type": "string"},
                                            "title": {"type": "string"},
                                            "processed_content": {"type": "string"},
                                            "processing_time": {"type": "number"},
                                            "error": {"type": "string"},
                                            "metadata": {"type": "object"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/crawl_batch": {
                "post": {
                    "summary": "Crawl multiple URLs in parallel",
                    "description": "Crawl multiple URLs simultaneously and process them with AI",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "urls": {
                                            "type": "array",
                                            "items": {"type": "string"},
                                            "description": "List of URLs to crawl",
                                            "example": ["https://dantri.com.vn", "https://vnexpress.net"]
                                        },
                                        "max_content_length": {
                                            "type": "integer",
                                            "description": "Maximum content length per URL",
                                            "default": 10000,
                                            "minimum": 1000,
                                            "maximum": 50000
                                        }
                                    },
                                    "required": ["urls"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successful Response",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "batch_size": {"type": "integer"},
                                            "successful_crawls": {"type": "integer"},
                                            "failed_crawls": {"type": "integer"},
                                            "results": {"type": "array"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/health_check": {
                "post": {
                    "summary": "Check crawler health",
                    "description": "Check the health status of the crawler system",
                    "requestBody": {
                        "required": False,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {}
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successful Response",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "status": {"type": "string"},
                                            "crawler_health": {"type": "object"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/get_stats": {
                "post": {
                    "summary": "Get crawler statistics",
                    "description": "Get performance statistics and features",
                    "requestBody": {
                        "required": False,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {}
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successful Response",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "server_type": {"type": "string"},
                                            "features": {"type": "array", "items": {"type": "string"}},
                                            "tools_available": {"type": "integer"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/test_crawl": {
                "post": {
                    "summary": "Test crawl with sample URL",
                    "description": "Test the crawler with a sample URL to verify functionality",
                    "requestBody": {
                        "required": False,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {}
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successful Response",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "test_successful": {"type": "boolean"},
                                            "test_url": {"type": "string"},
                                            "title": {"type": "string"},
                                            "content_length": {"type": "integer"},
                                            "processing_time": {"type": "number"},
                                            "error": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

@app.post("/crawl_url")
async def crawl_url(payload: Dict[str, Any]):
    """Crawl a single URL"""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        url = payload.get("url")
        max_content_length = payload.get("max_content_length", 10000)
        
        if not url:
            raise HTTPException(status_code=400, detail="URL is required")
        
        result = await crawler.crawl_and_process(url, max_content_length)
        
        response = {
            "success": result.success,
            "url": result.url,
            "title": result.title,
            "processed_content": result.processed_content,
            "processing_time": result.processing_time,
            "error": result.error,
            "metadata": result.metadata,
            "crawler_type": "standard_jina_crawler"
        }
        
        return JSONResponse(content=response)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Crawl failed: {str(e)}")

@app.post("/crawl_batch")
async def crawl_batch(payload: Dict[str, Any]):
    """Crawl multiple URLs"""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        urls = payload.get("urls", [])
        max_content_length = payload.get("max_content_length", 10000)
        
        if not urls:
            raise HTTPException(status_code=400, detail="URLs list is required")
        
        # Process URLs in parallel
        tasks = []
        for url in urls:
            task = crawler.crawl_and_process(url, max_content_length)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Format results
        formatted_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                formatted_results.append({
                    "success": False,
                    "url": urls[i],
                    "error": str(result)
                })
            else:
                formatted_results.append({
                    "success": result.success,
                    "url": result.url,
                    "title": result.title,
                    "processed_content": result.processed_content,
                    "processing_time": result.processing_time,
                    "error": result.error,
                    "metadata": result.metadata
                })
        
        response = {
            "batch_size": len(urls),
            "successful_crawls": sum(1 for r in formatted_results if r["success"]),
            "failed_crawls": sum(1 for r in formatted_results if not r["success"]),
            "results": formatted_results,
            "crawler_type": "standard_jina_batch"
        }
        
        return JSONResponse(content=response)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Batch crawl failed: {str(e)}")

@app.post("/health_check")
async def health_check_tool():
    """Health check tool"""
    global crawler
    
    if not crawler:
        return JSONResponse(content={"status": "error", "message": "Crawler not initialized"})
    
    try:
        health = await crawler.health_check()
        return JSONResponse(content={
            "status": "healthy",
            "crawler_health": health,
            "server_type": "standard_jina_crawler"
        })
    except Exception as e:
        return JSONResponse(content={"status": "error", "error": str(e)})

@app.post("/get_stats")
async def get_stats():
    """Get crawler statistics"""
    return JSONResponse(content={
        "server_type": "standard_jina_crawler",
        "features": [
            "Standard OpenAPI 3.0 compliance",
            "Compatible with Open WebUI",
            "No authentication required",
            "Async crawling with aiohttp",
            "BeautifulSoup HTML cleaning",
            "Gemini AI content processing",
            "Vietnamese content optimization",
            "Batch processing support"
        ],
        "tools_available": 5,
        "auth_required": False
    })

@app.post("/test_crawl")
async def test_crawl():
    """Test crawl with a sample URL"""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=500, detail="Crawler not initialized")
    
    try:
        # Test with a simple URL
        test_url = "https://httpbin.org/html"
        result = await crawler.crawl_and_process(test_url, 5000)
        
        return JSONResponse(content={
            "test_successful": result.success,
            "test_url": test_url,
            "title": result.title,
            "content_length": len(result.processed_content or ""),
            "processing_time": result.processing_time,
            "error": result.error,
            "message": "Test crawl completed successfully" if result.success else "Test crawl failed"
        })
        
    except Exception as e:
        return JSONResponse(content={
            "test_successful": False,
            "error": str(e),
            "message": "Test crawl failed with exception"
        })

if __name__ == "__main__":
    import os
    port = int(os.environ.get("PORT", 8002))
    uvicorn.run(
        "standard_openapi_wrapper:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        log_level="info"
    )