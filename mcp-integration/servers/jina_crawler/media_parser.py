#!/usr/bin/env python3
"""
Async Media Parser for Jina Crawler
Supports PDF, DOCX, and Image parsing with embedded markdown for Open WebUI
"""

import asyncio
import aiohttp
import aiofiles
import base64
import io
import logging
import mimetypes
import tempfile
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import urljoin, urlparse
from pathlib import Path

# Media parsing imports
try:
    import PyPDF2
    from docx import Document
    from PIL import Image
except ImportError as e:
    logging.warning(f"Media parsing dependencies not installed: {e}")
    PyPDF2 = None
    Document = None
    Image = None

logger = logging.getLogger(__name__)

class AsyncMediaParser:
    """Async media parser for PDFs, DOCX, and images"""
    
    def __init__(self, session: Optional[aiohttp.ClientSession] = None):
        self.session = session
        self.supported_image_formats = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'}
        self.supported_doc_formats = {'.pdf', '.docx'}
        
    async def parse_media_from_html(self, html_content: str, base_url: str) -> Dict[str, Any]:
        """
        Parse media content from HTML and return embedded markdown
        
        Args:
            html_content: HTML content to parse
            base_url: Base URL for resolving relative links
            
        Returns:
            Dict with parsed media content and embedded markdown
        """
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            media_content = {
                'images': [],
                'documents': [],
                'embedded_markdown': '',
                'media_count': 0
            }
            
            # Parse images
            images = await self._parse_images_from_soup(soup, base_url)
            media_content['images'] = images
            
            # Parse document links (PDFs, DOCX)
            documents = await self._parse_documents_from_soup(soup, base_url)
            media_content['documents'] = documents
            
            # Generate embedded markdown for Open WebUI
            embedded_md = await self._generate_embedded_markdown(images, documents)
            media_content['embedded_markdown'] = embedded_md
            media_content['media_count'] = len(images) + len(documents)
            
            logger.info(f"📸 Parsed {len(images)} images and {len(documents)} documents")
            return media_content
            
        except Exception as e:
            logger.error(f"❌ Media parsing failed: {e}")
            return {
                'images': [],
                'documents': [],
                'embedded_markdown': '',
                'media_count': 0,
                'error': str(e)
            }
    
    async def _parse_images_from_soup(self, soup, base_url: str) -> List[Dict[str, Any]]:
        """Parse images from BeautifulSoup object"""
        images = []
        img_tags = soup.find_all('img')
        
        # Limit to first 10 images to avoid overwhelming
        for img in img_tags[:10]:
            try:
                src = img.get('src')
                if not src:
                    continue
                
                # Resolve relative URLs
                full_url = urljoin(base_url, src)
                
                # Get image info
                alt_text = img.get('alt', '')
                title = img.get('title', '')
                
                # Try to get image as base64 for embedding
                image_data = await self._fetch_image_as_base64(full_url)
                
                image_info = {
                    'url': full_url,
                    'alt': alt_text,
                    'title': title,
                    'base64': image_data.get('base64') if image_data else None,
                    'mime_type': image_data.get('mime_type') if image_data else None,
                    'size': image_data.get('size') if image_data else None
                }
                
                images.append(image_info)
                
            except Exception as e:
                logger.debug(f"⚠️ Failed to parse image {img.get('src', 'unknown')}: {e}")
                continue
        
        return images
    
    async def _parse_documents_from_soup(self, soup, base_url: str) -> List[Dict[str, Any]]:
        """Parse document links from BeautifulSoup object"""
        documents = []
        
        # Find PDF and DOCX links
        links = soup.find_all('a', href=True)
        
        for link in links[:5]:  # Limit to first 5 documents
            try:
                href = link.get('href')
                if not href:
                    continue
                
                # Resolve relative URLs
                full_url = urljoin(base_url, href)
                
                # Check if it's a supported document format
                parsed_url = urlparse(full_url)
                path = Path(parsed_url.path)
                extension = path.suffix.lower()
                
                if extension in self.supported_doc_formats:
                    # Try to parse document content
                    doc_content = await self._parse_document_content(full_url, extension)
                    
                    doc_info = {
                        'url': full_url,
                        'type': extension[1:],  # Remove dot
                        'title': link.get_text(strip=True) or path.name,
                        'content': doc_content.get('content') if doc_content else None,
                        'pages': doc_content.get('pages') if doc_content else None,
                        'error': doc_content.get('error') if doc_content else None
                    }
                    
                    documents.append(doc_info)
                    
            except Exception as e:
                logger.debug(f"⚠️ Failed to parse document {link.get('href', 'unknown')}: {e}")
                continue
        
        return documents
    
    async def _fetch_image_as_base64(self, image_url: str) -> Optional[Dict[str, Any]]:
        """Fetch image and convert to base64 for embedding"""
        try:
            if not self.session:
                async with aiohttp.ClientSession() as session:
                    return await self._download_image_data(session, image_url)
            else:
                return await self._download_image_data(self.session, image_url)
                
        except Exception as e:
            logger.debug(f"⚠️ Failed to fetch image {image_url}: {e}")
            return None
    
    async def _download_image_data(self, session: aiohttp.ClientSession, image_url: str) -> Dict[str, Any]:
        """Download image data and convert to base64"""
        async with session.get(image_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
            if response.status == 200:
                content = await response.read()
                
                # Limit image size to 2MB for embedding
                if len(content) > 2 * 1024 * 1024:
                    logger.debug(f"⚠️ Image too large: {len(content)} bytes")
                    return None
                
                # Get MIME type
                content_type = response.headers.get('content-type', '')
                if not content_type.startswith('image/'):
                    # Try to guess from URL
                    mime_type, _ = mimetypes.guess_type(image_url)
                    content_type = mime_type or 'image/jpeg'
                
                # Convert to base64
                base64_data = base64.b64encode(content).decode('utf-8')
                
                return {
                    'base64': base64_data,
                    'mime_type': content_type,
                    'size': len(content)
                }
            else:
                logger.debug(f"⚠️ Failed to download image: HTTP {response.status}")
                return None
    
    async def _parse_document_content(self, doc_url: str, extension: str) -> Optional[Dict[str, Any]]:
        """Parse document content (PDF or DOCX)"""
        try:
            if not self.session:
                async with aiohttp.ClientSession() as session:
                    return await self._download_and_parse_document(session, doc_url, extension)
            else:
                return await self._download_and_parse_document(self.session, doc_url, extension)
                
        except Exception as e:
            logger.debug(f"⚠️ Failed to parse document {doc_url}: {e}")
            return {'error': str(e)}
    
    async def _download_and_parse_document(self, session: aiohttp.ClientSession, doc_url: str, extension: str) -> Dict[str, Any]:
        """Download and parse document content"""
        async with session.get(doc_url, timeout=aiohttp.ClientTimeout(total=30)) as response:
            if response.status == 200:
                content = await response.read()
                
                # Limit document size to 10MB
                if len(content) > 10 * 1024 * 1024:
                    return {'error': 'Document too large (>10MB)'}
                
                # Parse based on extension
                if extension == '.pdf':
                    return await self._parse_pdf_content(content)
                elif extension == '.docx':
                    return await self._parse_docx_content(content)
                else:
                    return {'error': f'Unsupported format: {extension}'}
            else:
                return {'error': f'HTTP {response.status}'}
    
    async def _parse_pdf_content(self, pdf_content: bytes) -> Dict[str, Any]:
        """Parse PDF content asynchronously"""
        if not PyPDF2:
            return {'error': 'PyPDF2 not installed'}
        
        try:
            # Run PDF parsing in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self._extract_pdf_text, pdf_content)
            return result
        except Exception as e:
            return {'error': f'PDF parsing failed: {str(e)}'}
    
    def _extract_pdf_text(self, pdf_content: bytes) -> Dict[str, Any]:
        """Extract text from PDF (runs in thread pool)"""
        try:
            pdf_file = io.BytesIO(pdf_content)
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            
            text_content = []
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    text = page.extract_text()
                    if text.strip():
                        text_content.append(f"--- Page {page_num + 1} ---\n{text}")
                except Exception as e:
                    text_content.append(f"--- Page {page_num + 1} ---\n[Error extracting text: {e}]")
            
            return {
                'content': '\n\n'.join(text_content),
                'pages': len(pdf_reader.pages)
            }
        except Exception as e:
            return {'error': f'PDF extraction failed: {str(e)}'}
    
    async def _parse_docx_content(self, docx_content: bytes) -> Dict[str, Any]:
        """Parse DOCX content asynchronously"""
        if not Document:
            return {'error': 'python-docx not installed'}
        
        try:
            # Run DOCX parsing in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self._extract_docx_text, docx_content)
            return result
        except Exception as e:
            return {'error': f'DOCX parsing failed: {str(e)}'}
    
    def _extract_docx_text(self, docx_content: bytes) -> Dict[str, Any]:
        """Extract text from DOCX (runs in thread pool)"""
        try:
            docx_file = io.BytesIO(docx_content)
            doc = Document(docx_file)
            
            paragraphs = []
            for para in doc.paragraphs:
                text = para.text.strip()
                if text:
                    paragraphs.append(text)
            
            return {
                'content': '\n\n'.join(paragraphs),
                'pages': len(doc.paragraphs)
            }
        except Exception as e:
            return {'error': f'DOCX extraction failed: {str(e)}'}
    
    async def _generate_embedded_markdown(self, images: List[Dict], documents: List[Dict]) -> str:
        """Generate embedded markdown for Open WebUI"""
        markdown_parts = []
        
        # Add images as embedded markdown
        if images:
            markdown_parts.append("## 📸 Images Found:")
            for i, img in enumerate(images, 1):
                if img.get('base64') and img.get('mime_type'):
                    # Embedded image for Open WebUI
                    embedded_img = f"![{img.get('alt', f'Image {i}')}](data:{img['mime_type']};base64,{img['base64']})"
                    markdown_parts.append(embedded_img)
                    
                    # Add caption if available
                    if img.get('title') or img.get('alt'):
                        caption = img.get('title') or img.get('alt')
                        markdown_parts.append(f"*{caption}*")
                else:
                    # Fallback to URL link
                    alt_text = img.get('alt', f'Image {i}')
                    markdown_parts.append(f"![{alt_text}]({img['url']})")
                
                markdown_parts.append("")  # Empty line
        
        # Add documents content
        if documents:
            markdown_parts.append("## 📄 Documents Found:")
            for doc in documents:
                title = doc.get('title', 'Document')
                doc_type = doc.get('type', 'unknown').upper()
                
                markdown_parts.append(f"### {title} ({doc_type})")
                
                if doc.get('content'):
                    # Limit content length for display
                    content = doc['content']
                    if len(content) > 2000:
                        content = content[:2000] + "...\n\n*[Content truncated]*"
                    
                    markdown_parts.append("```")
                    markdown_parts.append(content)
                    markdown_parts.append("```")
                elif doc.get('error'):
                    markdown_parts.append(f"*Error: {doc['error']}*")
                else:
                    markdown_parts.append(f"*[Document available at: {doc['url']}]*")
                
                markdown_parts.append("")  # Empty line
        
        return '\n'.join(markdown_parts)
