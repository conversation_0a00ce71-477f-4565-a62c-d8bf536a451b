#!/usr/bin/env python3
"""
Advanced Paywall Bypass Crawler
Implements various techniques to bypass common paywall implementations
"""

import asyncio
import logging
import time
import os
import aiohttp
import json
import re
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from urllib.parse import urljoin, urlparse, quote
from bs4 import BeautifulSoup
from jini_crawler import <PERSON><PERSON><PERSON><PERSON><PERSON>, JiniCrawlResult, PageSnapshot

logger = logging.getLogger(__name__)

@dataclass
class PaywallBypassConfig:
    """Configuration for paywall bypass techniques"""
    use_google_cache: bool = True
    use_archive_org: bool = True
    use_outline_com: bool = True
    use_12ft_ladder: bool = True
    use_fake_referrer: bool = True
    use_reader_mode: bool = True
    use_amp_version: bool = True
    use_print_version: bool = True
    timeout: int = 30
    max_retries: int = 3

class PaywallBypassCrawler(JiniCrawler):
    """
    Advanced crawler with paywall bypass capabilities
    """
    
    def __init__(self, bypass_config: Optional[PaywallBypassConfig] = None):
        super().__init__()
        self.bypass_config = bypass_config or PaywallBypassConfig()
        
        # Common paywall bypass user agents
        self.bypass_user_agents = [
            # Google Bot
            "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)",
            # Facebook Bot
            "facebookexternalhit/1.1 (+http://www.facebook.com/externalhit_uatext.php)",
            # Twitter Bot
            "Twitterbot/1.0",
            # Archive.org Bot
            "Mozilla/5.0 (compatible; archive.org_bot +http://www.archive.org/details/archive.org_bot)",
            # Generic crawler
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        ]
        
        # Common referrers that bypass paywalls
        self.bypass_referrers = [
            "https://www.google.com/",
            "https://www.facebook.com/",
            "https://twitter.com/",
            "https://www.reddit.com/",
            "https://news.ycombinator.com/",
            "https://www.linkedin.com/"
        ]
    
    async def crawl_with_paywall_bypass(self, url: str, max_content_length: int = 50000) -> JiniCrawlResult:
        """
        Crawl URL with multiple paywall bypass techniques
        """
        if not self._initialized:
            await self.initialize()
        
        logger.info(f"🔓 Attempting paywall bypass for: {url}")
        
        # Try different bypass methods in order of effectiveness
        bypass_methods = [
            ("direct_with_fake_headers", self._crawl_with_fake_headers),
            ("google_cache", self._crawl_google_cache),
            ("archive_org", self._crawl_archive_org),
            ("outline_com", self._crawl_outline_com),
            ("12ft_ladder", self._crawl_12ft_ladder),
            ("amp_version", self._crawl_amp_version),
            ("print_version", self._crawl_print_version),
            ("reader_mode", self._crawl_reader_mode)
        ]
        
        for method_name, method_func in bypass_methods:
            try:
                logger.info(f"🔍 Trying {method_name}...")
                result = await method_func(url, max_content_length)
                
                if result.success and result.processed_content and len(result.processed_content.strip()) > 200:
                    logger.info(f"✅ Success with {method_name}!")
                    result.metadata = result.metadata or {}
                    result.metadata["bypass_method"] = method_name
                    return result
                else:
                    logger.warning(f"⚠️ {method_name} returned limited content")
                    
            except Exception as e:
                logger.warning(f"❌ {method_name} failed: {e}")
                continue
        
        # If all bypass methods fail, try regular crawl
        logger.info("🔄 All bypass methods failed, trying regular crawl...")
        return await self.crawl_full_article(url, max_content_length)
    
    async def _crawl_with_fake_headers(self, url: str, max_content_length: int) -> JiniCrawlResult:
        """Crawl with fake headers and referrers"""
        if not self.bypass_config.use_fake_referrer:
            raise Exception("Fake referrer bypass disabled")
        
        # Try different combinations of user agents and referrers
        for user_agent in self.bypass_user_agents[:3]:  # Try top 3
            for referrer in self.bypass_referrers[:2]:  # Try top 2
                try:
                    # Create custom session with fake headers
                    headers = {
                        'User-Agent': user_agent,
                        'Referer': referrer,
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.5',
                        'Accept-Encoding': 'gzip, deflate',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                    }
                    
                    timeout = aiohttp.ClientTimeout(total=self.bypass_config.timeout)
                    async with aiohttp.ClientSession(timeout=timeout, headers=headers) as session:
                        async with session.get(url) as response:
                            if response.status == 200:
                                html_content = await response.text()
                                
                                # Process with BeautifulSoup
                                soup = BeautifulSoup(html_content, 'html.parser')
                                
                                # Check if we got past paywall (look for common paywall indicators)
                                if self._has_paywall_content(soup):
                                    continue  # This method didn't work
                                
                                # Extract content
                                cleaned_content = self._extract_main_content(soup)
                                
                                if len(cleaned_content.strip()) > 200:
                                    # Process with Gemini
                                    gemini_result = await self.gemini_processor.process_content(
                                        cleaned_content[:max_content_length], "full_article"
                                    )
                                    
                                    if gemini_result.get("success"):
                                        return JiniCrawlResult(
                                            success=True,
                                            url=url,
                                            title=soup.title.string.strip() if soup.title else "",
                                            original_content=html_content,
                                            cleaned_content=cleaned_content,
                                            processed_content=gemini_result.get("processed_content", ""),
                                            processing_time=gemini_result.get("processing_time", 0.0),
                                            metadata={
                                                "bypass_method": "fake_headers",
                                                "user_agent": user_agent,
                                                "referrer": referrer,
                                                "original_length": len(html_content),
                                                "output_length": len(gemini_result.get("processed_content", "")),
                                                "model": gemini_result.get("model", "unknown")
                                            }
                                        )
                except Exception as e:
                    continue
        
        raise Exception("Fake headers bypass failed")
    
    async def _crawl_google_cache(self, url: str, max_content_length: int) -> JiniCrawlResult:
        """Crawl using Google Cache"""
        if not self.bypass_config.use_google_cache:
            raise Exception("Google cache bypass disabled")
        
        cache_url = f"https://webcache.googleusercontent.com/search?q=cache:{quote(url)}"
        return await self._crawl_bypass_service(cache_url, url, "google_cache", max_content_length)
    
    async def _crawl_archive_org(self, url: str, max_content_length: int) -> JiniCrawlResult:
        """Crawl using Archive.org"""
        if not self.bypass_config.use_archive_org:
            raise Exception("Archive.org bypass disabled")
        
        # First, get the latest snapshot
        api_url = f"https://archive.org/wayback/available?url={quote(url)}"
        
        async with self.session.get(api_url) as response:
            if response.status == 200:
                data = await response.json()
                if data.get("archived_snapshots", {}).get("closest", {}).get("url"):
                    archive_url = data["archived_snapshots"]["closest"]["url"]
                    return await self._crawl_bypass_service(archive_url, url, "archive_org", max_content_length)
        
        raise Exception("Archive.org bypass failed")
    
    async def _crawl_outline_com(self, url: str, max_content_length: int) -> JiniCrawlResult:
        """Crawl using Outline.com"""
        if not self.bypass_config.use_outline_com:
            raise Exception("Outline.com bypass disabled")
        
        outline_url = f"https://outline.com/{quote(url)}"
        return await self._crawl_bypass_service(outline_url, url, "outline_com", max_content_length)
    
    async def _crawl_12ft_ladder(self, url: str, max_content_length: int) -> JiniCrawlResult:
        """Crawl using 12ft.io"""
        if not self.bypass_config.use_12ft_ladder:
            raise Exception("12ft.io bypass disabled")
        
        ladder_url = f"https://12ft.io/{quote(url)}"
        return await self._crawl_bypass_service(ladder_url, url, "12ft_ladder", max_content_length)
    
    async def _crawl_amp_version(self, url: str, max_content_length: int) -> JiniCrawlResult:
        """Try to find and crawl AMP version"""
        if not self.bypass_config.use_amp_version:
            raise Exception("AMP version bypass disabled")
        
        # Common AMP URL patterns
        amp_patterns = [
            url.replace("://", "://amp."),
            url + "/amp",
            url + "?amp=1",
            url.replace("www.", "amp."),
        ]
        
        for amp_url in amp_patterns:
            try:
                result = await self._crawl_bypass_service(amp_url, url, "amp_version", max_content_length)
                if result.success:
                    return result
            except:
                continue
        
        raise Exception("AMP version bypass failed")
    
    async def _crawl_print_version(self, url: str, max_content_length: int) -> JiniCrawlResult:
        """Try to find print version"""
        if not self.bypass_config.use_print_version:
            raise Exception("Print version bypass disabled")
        
        # Common print URL patterns
        print_patterns = [
            url + "?print=1",
            url + "/print",
            url + "?view=print",
            url.replace("://", "://print."),
        ]
        
        for print_url in print_patterns:
            try:
                result = await self._crawl_bypass_service(print_url, url, "print_version", max_content_length)
                if result.success:
                    return result
            except:
                continue
        
        raise Exception("Print version bypass failed")
    
    async def _crawl_reader_mode(self, url: str, max_content_length: int) -> JiniCrawlResult:
        """Simulate reader mode extraction"""
        if not self.bypass_config.use_reader_mode:
            raise Exception("Reader mode bypass disabled")
        
        # This would require more sophisticated content extraction
        # For now, we'll use a more aggressive content extraction approach
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    html_content = await response.text()
                    soup = BeautifulSoup(html_content, 'html.parser')
                    
                    # More aggressive content extraction for reader mode
                    content = self._extract_reader_mode_content(soup)
                    
                    if len(content.strip()) > 200:
                        gemini_result = await self.gemini_processor.process_content(
                            content[:max_content_length], "full_article"
                        )
                        
                        if gemini_result.get("success"):
                            return JiniCrawlResult(
                                success=True,
                                url=url,
                                title=soup.title.string.strip() if soup.title else "",
                                original_content=html_content,
                                cleaned_content=content,
                                processed_content=gemini_result.get("processed_content", ""),
                                processing_time=gemini_result.get("processing_time", 0.0),
                                metadata={
                                    "bypass_method": "reader_mode",
                                    "extraction_method": "aggressive"
                                }
                            )
        except Exception as e:
            pass
        
        raise Exception("Reader mode bypass failed")
    
    async def _crawl_bypass_service(self, service_url: str, original_url: str, method_name: str, max_content_length: int) -> JiniCrawlResult:
        """Generic method to crawl through bypass services"""
        try:
            async with self.session.get(service_url) as response:
                if response.status == 200:
                    html_content = await response.text()
                    soup = BeautifulSoup(html_content, 'html.parser')
                    
                    # Extract content
                    content = self._extract_main_content(soup)
                    
                    if len(content.strip()) > 200:
                        # Process with Gemini
                        gemini_result = await self.gemini_processor.process_content(
                            content[:max_content_length], "full_article"
                        )
                        
                        if gemini_result.get("success"):
                            return JiniCrawlResult(
                                success=True,
                                url=original_url,
                                title=soup.title.string.strip() if soup.title else "",
                                original_content=html_content,
                                cleaned_content=content,
                                processed_content=gemini_result.get("processed_content", ""),
                                processing_time=gemini_result.get("processing_time", 0.0),
                                metadata={
                                    "bypass_method": method_name,
                                    "service_url": service_url,
                                    "original_length": len(html_content),
                                    "output_length": len(gemini_result.get("processed_content", "")),
                                    "model": gemini_result.get("model", "unknown")
                                }
                            )
        except Exception as e:
            pass
        
        raise Exception(f"{method_name} service failed")
    
    def _has_paywall_content(self, soup: BeautifulSoup) -> bool:
        """Check if the page contains paywall indicators"""
        paywall_indicators = [
            "paywall", "subscribe", "premium", "member-only", "subscription",
            "sign up", "register", "login", "continue reading", "read more",
            "unlock", "access", "trial", "free article"
        ]
        
        text_content = soup.get_text().lower()
        
        # Check for paywall keywords
        for indicator in paywall_indicators:
            if indicator in text_content:
                return True
        
        # Check for common paywall CSS classes/IDs
        paywall_selectors = [
            ".paywall", "#paywall", ".subscription", ".premium-content",
            ".member-only", ".subscriber-only", "[data-paywall]"
        ]
        
        for selector in paywall_selectors:
            if soup.select(selector):
                return True
        
        return False
    
    def _extract_main_content(self, soup: BeautifulSoup) -> str:
        """Extract main content from soup"""
        # Remove unwanted elements
        for element in soup(['script', 'style', 'nav', 'header', 'footer', 'aside',
                           'iframe', 'noscript', 'form', 'button', 'advertisement']):
            element.decompose()
        
        # Remove paywall elements
        paywall_selectors = [
            ".paywall", "#paywall", ".subscription", ".premium-content",
            ".member-only", ".subscriber-only", "[data-paywall]",
            ".subscribe-banner", ".subscription-banner"
        ]
        
        for selector in paywall_selectors:
            for element in soup.select(selector):
                element.decompose()
        
        # Try to find main content
        main_content = None
        for selector in ['main', 'article', '.content', '.main-content', '.post-content', 
                        '.entry-content', '.article-content', '.story-content']:
            if selector.startswith('.'):
                element = soup.select_one(selector)
            else:
                element = soup.find(selector)
            
            if element and len(element.get_text(strip=True)) > 200:
                main_content = element
                break
        
        if not main_content:
            main_content = soup.find('body') or soup
        
        return str(main_content)
    
    def _extract_reader_mode_content(self, soup: BeautifulSoup) -> str:
        """More aggressive content extraction for reader mode"""
        # Remove all unwanted elements
        unwanted_tags = [
            'script', 'style', 'nav', 'header', 'footer', 'aside', 'iframe', 
            'noscript', 'form', 'button', 'advertisement', 'ads', 'sidebar',
            'menu', 'navigation', 'social', 'share', 'comment', 'related'
        ]
        
        for tag in unwanted_tags:
            for element in soup.find_all(tag):
                element.decompose()
        
        # Remove elements with unwanted classes/IDs
        unwanted_patterns = [
            'ad', 'advertisement', 'social', 'share', 'comment', 'sidebar',
            'menu', 'nav', 'header', 'footer', 'paywall', 'subscription',
            'premium', 'member', 'login', 'signup', 'register'
        ]
        
        for element in soup.find_all():
            if element.get('class') or element.get('id'):
                classes = ' '.join(element.get('class', []))
                element_id = element.get('id', '')
                combined = (classes + ' ' + element_id).lower()
                
                for pattern in unwanted_patterns:
                    if pattern in combined:
                        element.decompose()
                        break
        
        # Extract all paragraphs and headings
        content_elements = soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li', 'blockquote'])
        
        # Filter out short paragraphs (likely not main content)
        meaningful_content = []
        for element in content_elements:
            text = element.get_text(strip=True)
            if len(text) > 50:  # Only include substantial text
                meaningful_content.append(str(element))
        
        return '\n'.join(meaningful_content) if meaningful_content else str(soup)

# Convenience function
async def crawl_with_paywall_bypass(url: str, max_content_length: int = 50000) -> JiniCrawlResult:
    """Quick function to crawl with paywall bypass"""
    crawler = PaywallBypassCrawler()
    try:
        await crawler.initialize()
        return await crawler.crawl_with_paywall_bypass(url, max_content_length)
    finally:
        await crawler.cleanup()

# Test function
async def test_paywall_bypass():
    """Test paywall bypass functionality"""
    print("🔓 Testing Paywall Bypass Crawler")
    print("=" * 50)
    
    # Test URLs with known paywalls
    test_urls = [
        "https://medium.com/@ignacio.de.gregorio.noblejas/what-china-gets-the-us-doesnt-f12059d0613d",
        "https://www.nytimes.com/",  # Has paywall
        "https://www.wsj.com/",     # Has paywall
    ]
    
    crawler = PaywallBypassCrawler()
    await crawler.initialize()
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n🔍 Test {i}: {url}")
        print("-" * 40)
        
        try:
            result = await crawler.crawl_with_paywall_bypass(url)
            
            if result.success:
                print(f"✅ Success with method: {result.metadata.get('bypass_method', 'unknown')}")
                print(f"📰 Title: {result.title}")
                print(f"📊 Content length: {len(result.processed_content or '')} chars")
                print(f"⏱️  Processing time: {result.processing_time:.2f}s")
                if result.processed_content:
                    preview = result.processed_content[:200] + "..." if len(result.processed_content) > 200 else result.processed_content
                    print(f"📄 Preview: {preview}")
            else:
                print(f"❌ Failed: {result.error}")
                
        except Exception as e:
            print(f"💥 Exception: {e}")
    
    await crawler.cleanup()
    print("\n✅ Paywall bypass test completed!")

if __name__ == "__main__":
    asyncio.run(test_paywall_bypass())