#!/usr/bin/env python3
"""
HTTP MCP Server Wrapper for Jina Crawler
Exposes MCP tools via HTTP for Open WebUI integration
"""

import asyncio
import json
import logging
import aiohttp
from typing import Any, Dict, List, Optional
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
)
from mcp.server.lowlevel.server import NotificationOptions

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("jina-crawler-http-mcp")

# Initialize the MCP server
server = Server("jina_crawler_http")

class JinaCrawlerHTTPMCP:
    def __init__(self):
        self.base_url = "http://jina-crawler-8009:8009"
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def call_http_endpoint(self, endpoint: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Gọi HTTP endpoint và trả về kết quả"""
        try:
            url = f"{self.base_url}/{endpoint}"
            
            if data:
                async with self.session.post(url, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {"success": True, "data": result}
                    else:
                        error_text = await response.text()
                        return {"success": False, "error": f"HTTP {response.status}: {error_text}"}
            else:
                async with self.session.get(url) as response:
                    if response.status == 200:
                        result = await response.json()
                        return {"success": True, "data": result}
                    else:
                        error_text = await response.text()
                        return {"success": False, "error": f"HTTP {response.status}: {error_text}"}
        
        except Exception as e:
            logger.error(f"Error calling HTTP endpoint {endpoint}: {e}")
            return {"success": False, "error": str(e)}

# Global instance
http_mcp = JinaCrawlerHTTPMCP()

@server.list_resources()
async def list_resources() -> List[Resource]:
    """List available Jina Crawler resources"""
    return [
        Resource(
            uri="jina://crawler",
            name="Jina Web Crawler",
            description="Advanced web crawler with AI processing - HTTP Wrapper",
            mimeType="application/json",
        ),
        Resource(
            uri="jina://health",
            name="Health Status",
            description="Crawler health and performance metrics",
            mimeType="application/json",
        )
    ]

@server.read_resource()
async def read_resource(uri: str) -> str:
    """Read Jina Crawler resource"""
    async with http_mcp:
        if uri == "jina://crawler":
            result = await http_mcp.call_http_endpoint("")
            if result["success"]:
                return json.dumps({
                    "description": "HTTP Wrapper for Jina Crawler - 8 powerful tools available",
                    "features": [
                        "Jina-style web crawling via HTTP",
                        "BeautifulSoup HTML cleaning",
                        "Gemini AI content processing",
                        "Full article extraction",
                        "Paywall bypass capabilities",
                        "AI search engine",
                        "Vietnamese content optimization",
                        "Performance monitoring"
                    ],
                    "tools": {
                        "crawl_url": "Extract summarized/processed content",
                        "crawl_full_article": "Extract COMPLETE article content",
                        "crawl_bypass_paywall": "Bypass paywalls and extract content",
                        "ai_search": "Advanced AI search engine",
                        "ai_search_streaming": "AI search with real-time updates",
                        "health_check": "Check crawler health",
                        "get_crawler_stats": "Get performance statistics"
                    },
                    "note": "This is an HTTP wrapper for the containerized Jina Crawler service"
                }, indent=2)
            else:
                return json.dumps({"error": result["error"]}, indent=2)
        
        elif uri == "jina://health":
            result = await http_mcp.call_http_endpoint("health")
            if result["success"]:
                return json.dumps(result["data"], indent=2)
            else:
                return json.dumps({"error": result["error"]}, indent=2)
        
        raise ValueError(f"Unknown resource: {uri}")

@server.list_tools()
async def list_tools() -> List[Tool]:
    """List available Jina Crawler tools"""
    return [
        Tool(
            name="crawl_url",
            description="📄 SMART SUMMARIZER: Use for regular web crawling with AI-powered content processing and summarization. Returns clean, processed content optimized for reading. Best for general web content, news articles, and when you need intelligent content extraction.",
            inputSchema={
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "URL to crawl and process with AI summarization"
                    },
                    "max_content_length": {
                        "type": "integer",
                        "description": "Maximum content length to process (default: 10000)",
                        "default": 10000,
                        "minimum": 1000,
                        "maximum": 50000
                    }
                },
                "required": ["url"]
            }
        ),
        Tool(
            name="crawl_full_article",
            description="📰 COMPLETE ARTICLE EXTRACTOR: Use when you need the ENTIRE article text without any summarization or processing. Returns raw, complete content from public articles. Perfect for when user asks for 'full article', 'complete content', or 'entire text'.",
            inputSchema={
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "URL of public article to extract complete content from"
                    },
                    "max_content_length": {
                        "type": "integer",
                        "description": "Maximum content length to process (default: 50000 for full articles)",
                        "default": 50000,
                        "minimum": 10000,
                        "maximum": 100000
                    }
                },
                "required": ["url"]
            }
        ),
        Tool(
            name="crawl_bypass_paywall",
            description="🔓 PAYWALL BYPASS SPECIALIST: Use ONLY for Medium, NYTimes, WSJ, Bloomberg, and other subscription/paywall-protected articles. Uses 8 specialized bypass techniques. DO NOT use for regular public websites - use crawl_url instead.",
            inputSchema={
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "URL of paywall-protected article (Medium, NYTimes, etc.)"
                    },
                    "max_content_length": {
                        "type": "integer",
                        "description": "Maximum content length to process (default: 50000)",
                        "default": 50000,
                        "minimum": 10000,
                        "maximum": 100000
                    }
                },
                "required": ["url"]
            }
        ),
        Tool(
            name="ai_search",
            description="🤖 AI SEARCH ENGINE: Advanced Perplexity-style AI search that refines your query, searches the web, crawls multiple sources, and synthesizes comprehensive 1500-3000 word answers with extensive citations. Leverages Gemini 2.5 Flash Lite's full 1M input + 65K output capacity for in-depth analysis. Perfect for research, academic questions, and complex topics requiring comprehensive analysis.",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Your search question or topic"
                    },
                    "enable_query_refinement": {
                        "type": "boolean",
                        "description": "Whether to refine the query using AI (default: true)",
                        "default": True
                    },
                    "search_type": {
                        "type": "string",
                        "description": "Type of search: 'text' for general web search, 'news' for news articles",
                        "enum": ["text", "news"],
                        "default": "text"
                    },
                    "max_sources": {
                        "type": "integer",
                        "description": "Maximum number of sources to analyze (default: 10, increased for comprehensive analysis)",
                        "default": 10,
                        "minimum": 5,
                        "maximum": 15
                    }
                },
                "required": ["query"]
            }
        ),
        Tool(
            name="ai_search_streaming",
            description="🚀 AI SEARCH STREAMING: Same as ai_search but provides real-time updates on search progress. Use when you want to show the user what's happening during the search process.",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Your search question or topic"
                    },
                    "enable_query_refinement": {
                        "type": "boolean",
                        "description": "Whether to refine the query using AI (default: true)",
                        "default": True
                    }
                },
                "required": ["query"]
            }
        ),
        Tool(
            name="crawl_batch",
            description="🚀 BATCH CRAWLER: Process multiple URLs in parallel for high-efficiency crawling. Perfect for bulk content extraction, processing lists of articles, or when you need to crawl many pages from the same site. Returns individual results for each URL along with batch summary statistics.",
            inputSchema={
                "type": "object",
                "properties": {
                    "urls": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of URLs to crawl in parallel"
                    },
                    "max_content_length": {
                        "type": "integer",
                        "description": "Maximum content length per URL (default: 10000)",
                        "default": 10000,
                        "minimum": 1000,
                        "maximum": 50000
                    }
                },
                "required": ["urls"]
            }
        ),
        Tool(
            name="health_check",
            description="❤️ HEALTH CHECK: Check the health status of the crawler system",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),
        Tool(
            name="get_crawler_stats",
            description="📊 GET STATS: Get performance statistics and metrics",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle tool calls by forwarding to HTTP server"""
    
    try:
        async with http_mcp:
            if name == "crawl_url":
                result = await http_mcp.call_http_endpoint("crawl_url", arguments)
            elif name == "crawl_full_article":
                result = await http_mcp.call_http_endpoint("crawl_full_article", arguments)
            elif name == "crawl_bypass_paywall":
                result = await http_mcp.call_http_endpoint("crawl_bypass_paywall", arguments)
            elif name == "ai_search":
                result = await http_mcp.call_http_endpoint("ai_search", arguments)
            elif name == "ai_search_streaming":
                result = await http_mcp.call_http_endpoint("ai_search_streaming", arguments)
            elif name == "crawl_batch":
                result = await http_mcp.call_http_endpoint("crawl_batch", arguments)
            elif name == "health_check":
                result = await http_mcp.call_http_endpoint("health_check", {})
            elif name == "get_crawler_stats":
                result = await http_mcp.call_http_endpoint("get_crawler_stats", {})
            else:
                result = {"success": False, "error": f"Unknown tool: {name}"}
            
            if result["success"]:
                # Extract the actual content from the HTTP response
                data = result["data"]
                if isinstance(data, dict) and "data" in data:
                    # The HTTP wrapper returns {"success": bool, "data": actual_content}
                    # If actual_content is already a string, don't double-dump it
                    actual_content = data["data"]
                    if isinstance(actual_content, str):
                        content = actual_content
                    else:
                        content = json.dumps(actual_content, indent=2, ensure_ascii=False)
                else:
                    content = json.dumps(data, indent=2, ensure_ascii=False)
                return [TextContent(type="text", text=content)]
            else:
                return [TextContent(type="text", text=json.dumps({"error": result["error"]}, indent=2))]
    
    except Exception as e:
        logger.error(f"Error in tool {name}: {str(e)}")
        import time
        error_response = {
            "error": str(e),
            "tool": name,
            "timestamp": time.time()
        }
        return [TextContent(type="text", text=json.dumps(error_response, indent=2))]

async def main():
    """Main server function"""
    async with http_mcp:
        async with stdio_server() as (read_stream, write_stream):
            await server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="jina_crawler_http",
                    server_version="1.0.0",
                    capabilities=server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )

if __name__ == "__main__":
    asyncio.run(main())