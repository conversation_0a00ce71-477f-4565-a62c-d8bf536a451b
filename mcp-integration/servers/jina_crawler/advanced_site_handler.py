#!/usr/bin/env python3
"""
🔧 ADVANCED SITE HANDLER
Specialized handlers for problematic sites like VnExpress, GSMArena
"""

import asyncio
import aiohttp
import logging
import time
import random
from typing import Dict, Optional, Any
from urllib.parse import urljoin, urlparse
import json
import re

# Import VnExpress specialist
try:
    from vnexpress_specialist import VnExpressSpecialist
    VNEXPRESS_SPECIALIST_AVAILABLE = True
except ImportError:
    VNEXPRESS_SPECIALIST_AVAILABLE = False
    logger.warning("⚠️ VnExpress specialist not available")

logger = logging.getLogger(__name__)

class AdvancedSiteHandler:
    """Advanced handler for problematic sites"""
    
    def __init__(self):
        # Initialize VnExpress specialist
        self.vnexpress_specialist = VnExpressSpecialist() if VNEXPRESS_SPECIALIST_AVAILABLE else None

        self.site_configs = {
            'vnexpress.net': {
                'user_agents': [
                    # 🔧 ENHANCED: More realistic Vietnamese user agents
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15',
                    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1'
                ],
                'headers': {
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                    'Accept-Language': 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
                    'Accept-Encoding': 'gzip, deflate, br, zstd',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                    'Sec-Ch-Ua-Mobile': '?0',
                    'Sec-Ch-Ua-Platform': '"Windows"',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1',
                    'Upgrade-Insecure-Requests': '1',
                    'X-Forwarded-For': '**********',  # Vietnamese IP range
                    'X-Real-IP': '**********'
                },
                'delay_range': (3, 8),  # Longer delays
                'retry_count': 5,  # More retries
                'methods': ['tls_bypass', 'mobile_app', 'rss_feed', 'amp', 'direct']
            },
            'dantri.com.vn': {
                'user_agents': [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1'
                ],
                'headers': {
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                },
                'delay_range': (1, 3),
                'retry_count': 2,
                'methods': ['direct', 'mobile']
            },
            'thanhnien.vn': {
                'user_agents': [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1'
                ],
                'headers': {
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                },
                'delay_range': (1, 3),
                'retry_count': 2,
                'methods': ['direct', 'mobile']
            },
            'vietnamnet.vn': {
                'user_agents': [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1'
                ],
                'headers': {
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                },
                'delay_range': (1, 3),
                'retry_count': 2,
                'methods': ['direct', 'mobile']
            },
            'gsmarena.com': {
                'user_agents': [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
                ],
                'headers': {
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1'
                },
                'delay_range': (1, 3),
                'retry_count': 2,
                'methods': ['direct', 'mobile']
            }
        }
    
    def get_domain(self, url: str) -> str:
        """Extract domain from URL"""
        try:
            return urlparse(url).netloc.lower()
        except:
            return ""
    
    def get_site_config(self, url: str) -> Dict[str, Any]:
        """Get configuration for specific site"""
        domain = self.get_domain(url)
        
        # Check for exact matches
        for site_domain, config in self.site_configs.items():
            if site_domain in domain:
                return config
        
        # Default config for unknown sites
        return {
            'user_agents': [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ],
            'headers': {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive'
            },
            'delay_range': (1, 2),
            'retry_count': 2,
            'methods': ['direct']
        }
    
    async def fetch_with_method(self, session: aiohttp.ClientSession, url: str, method: str, config: Dict[str, Any]) -> Optional[str]:
        """Fetch content using specific method"""
        try:
            if method == 'tls_bypass':
                return await self._fetch_tls_bypass(url, config)
            elif method == 'mobile_app':
                return await self._fetch_mobile_app(session, url, config)
            elif method == 'rss_feed':
                return await self._fetch_rss_feed(session, url, config)
            elif method == 'direct':
                return await self._fetch_direct(session, url, config)
            elif method == 'mobile':
                return await self._fetch_mobile(session, url, config)
            elif method == 'amp':
                return await self._fetch_amp(session, url, config)
            else:
                return await self._fetch_direct(session, url, config)
        except Exception as e:
            logger.warning(f"Method {method} failed for {url}: {e}")
            return None
    
    async def _fetch_direct(self, session: aiohttp.ClientSession, url: str, config: Dict[str, Any]) -> Optional[str]:
        """Direct fetch with site-specific headers"""
        headers = config['headers'].copy()
        headers['User-Agent'] = random.choice(config['user_agents'])
        
        # Add random delay
        delay = random.uniform(*config['delay_range'])
        await asyncio.sleep(delay)
        
        async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=15)) as response:
            if response.status == 200:
                content = await response.text()
                logger.info(f"✅ Direct fetch successful for {url}")
                return content
            else:
                logger.warning(f"⚠️ Direct fetch HTTP {response.status} for {url}")
                return None
    
    async def _fetch_mobile(self, session: aiohttp.ClientSession, url: str, config: Dict[str, Any]) -> Optional[str]:
        """Fetch mobile version"""
        headers = config['headers'].copy()
        headers['User-Agent'] = 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
        
        # Try mobile subdomain
        mobile_url = url.replace('www.', 'm.').replace('://', '://m.') if 'm.' not in url else url
        
        delay = random.uniform(*config['delay_range'])
        await asyncio.sleep(delay)
        
        async with session.get(mobile_url, headers=headers, timeout=aiohttp.ClientTimeout(total=15)) as response:
            if response.status == 200:
                content = await response.text()
                logger.info(f"✅ Mobile fetch successful for {mobile_url}")
                return content
            else:
                logger.warning(f"⚠️ Mobile fetch HTTP {response.status} for {mobile_url}")
                return None
    
    async def _fetch_amp(self, session: aiohttp.ClientSession, url: str, config: Dict[str, Any]) -> Optional[str]:
        """Fetch AMP version (for VnExpress)"""
        headers = config['headers'].copy()
        headers['User-Agent'] = random.choice(config['user_agents'])
        
        # Try AMP URL
        amp_url = url.replace('.html', '.amp.html') if '.html' in url else url + '.amp.html'
        
        delay = random.uniform(*config['delay_range'])
        await asyncio.sleep(delay)
        
        async with session.get(amp_url, headers=headers, timeout=aiohttp.ClientTimeout(total=15)) as response:
            if response.status == 200:
                content = await response.text()
                logger.info(f"✅ AMP fetch successful for {amp_url}")
                return content
            else:
                logger.warning(f"⚠️ AMP fetch HTTP {response.status} for {amp_url}")
                return None

    async def _fetch_tls_bypass(self, url: str, config: Dict[str, Any]) -> Optional[str]:
        """Fetch using TLS client bypass for VnExpress"""
        try:
            import tls_client

            # Create TLS session with Vietnamese fingerprint
            session = tls_client.Session(
                client_identifier="chrome_120",
                random_tls_extension_order=True
            )

            headers = config['headers'].copy()
            headers['User-Agent'] = random.choice(config['user_agents'])

            # Vietnamese-specific headers
            headers.update({
                'X-Forwarded-For': f"14.161.{random.randint(1,255)}.{random.randint(1,255)}",
                'CF-IPCountry': 'VN',
                'CF-RAY': f"{random.randint(100000000000000000, 999999999999999999)}-SIN",
                'CF-Visitor': '{"scheme":"https"}',
                'Referer': 'https://www.google.com.vn/'
            })

            delay = random.uniform(*config['delay_range'])
            await asyncio.sleep(delay)

            response = session.get(url, headers=headers, timeout=20)

            if response.status_code == 200:
                content = response.text
                logger.info(f"✅ TLS bypass successful for {url}")
                return content
            else:
                logger.warning(f"⚠️ TLS bypass HTTP {response.status_code} for {url}")
                return None

        except ImportError:
            logger.warning("⚠️ TLS client not available")
            return None
        except Exception as e:
            logger.warning(f"⚠️ TLS bypass error: {e}")
            return None

    async def _fetch_mobile_app(self, session: aiohttp.ClientSession, url: str, config: Dict[str, Any]) -> Optional[str]:
        """Fetch using mobile app user agent"""
        headers = config['headers'].copy()
        headers['User-Agent'] = 'VnExpress/5.0 (iPhone; iOS 17.2; Scale/3.00)'
        headers['X-Requested-With'] = 'com.vnexpress.ios'
        headers['Accept'] = 'application/json, text/plain, */*'

        delay = random.uniform(*config['delay_range'])
        await asyncio.sleep(delay)

        async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=15)) as response:
            if response.status == 200:
                content = await response.text()
                logger.info(f"✅ Mobile app fetch successful for {url}")
                return content
            else:
                logger.warning(f"⚠️ Mobile app fetch HTTP {response.status} for {url}")
                return None

    async def _fetch_rss_feed(self, session: aiohttp.ClientSession, url: str, config: Dict[str, Any]) -> Optional[str]:
        """Try to fetch RSS feed version"""
        try:
            # Convert article URL to RSS feed
            if 'vnexpress.net' in url:
                rss_url = 'https://vnexpress.net/rss/cong-nghe.rss'
            else:
                return None

            headers = config['headers'].copy()
            headers['User-Agent'] = random.choice(config['user_agents'])
            headers['Accept'] = 'application/rss+xml, application/xml, text/xml'

            delay = random.uniform(*config['delay_range'])
            await asyncio.sleep(delay)

            async with session.get(rss_url, headers=headers, timeout=aiohttp.ClientTimeout(total=15)) as response:
                if response.status == 200:
                    content = await response.text()
                    logger.info(f"✅ RSS feed fetch successful for {rss_url}")
                    return content
                else:
                    logger.warning(f"⚠️ RSS feed HTTP {response.status} for {rss_url}")
                    return None
        except Exception as e:
            logger.warning(f"⚠️ RSS feed error: {e}")
            return None
    
    async def smart_fetch(self, url: str) -> Optional[str]:
        """Smart fetch with multiple fallback methods"""
        domain = self.get_domain(url)

        # 🔧 ENHANCED: Use VnExpress specialist for VnExpress
        if 'vnexpress.net' in domain and self.vnexpress_specialist:
            logger.info(f"🎯 Using VnExpress specialist for {domain}")
            try:
                content = await self.vnexpress_specialist.smart_fetch(url)
                if content:
                    logger.info(f"✅ VnExpress specialist success: {len(content)} chars")
                    return content
                else:
                    logger.warning(f"⚠️ VnExpress specialist failed, trying fallback methods")
            except Exception as e:
                logger.warning(f"⚠️ VnExpress specialist error: {e}, trying fallback methods")

        # Fallback to original methods
        config = self.get_site_config(url)
        logger.info(f"🎯 Smart fetching {domain} with {len(config['methods'])} methods")
        
        # Create session with specific settings
        connector = aiohttp.TCPConnector(
            limit=10,
            limit_per_host=5,
            ttl_dns_cache=300,
            use_dns_cache=True,
            ssl=False  # Disable SSL verification for problematic sites
        )
        
        timeout = aiohttp.ClientTimeout(total=20, connect=10)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # Try each method
            for method in config['methods']:
                for attempt in range(config['retry_count']):
                    try:
                        logger.info(f"🔄 Trying method '{method}' (attempt {attempt + 1}/{config['retry_count']})")
                        
                        content = await self.fetch_with_method(session, url, method, config)
                        if content and len(content) > 1000:  # Minimum content length
                            logger.info(f"✅ Successfully fetched {len(content)} chars using method '{method}'")
                            return content
                        
                    except asyncio.TimeoutError:
                        logger.warning(f"⏰ Timeout for method '{method}' attempt {attempt + 1}")
                    except Exception as e:
                        logger.warning(f"❌ Error with method '{method}' attempt {attempt + 1}: {e}")
                    
                    # Wait before retry
                    if attempt < config['retry_count'] - 1:
                        await asyncio.sleep(random.uniform(1, 3))
        
        logger.error(f"❌ All methods failed for {url}")
        return None
    
    def extract_content(self, html: str, url: str) -> Dict[str, Any]:
        """Extract content from HTML based on site"""
        domain = self.get_domain(url)

        if 'vnexpress.net' in domain:
            # Use VnExpress specialist for extraction if available
            if self.vnexpress_specialist:
                return self.vnexpress_specialist.extract_content(html)
            else:
                return self._extract_vnexpress(html)
        elif 'dantri.com.vn' in domain:
            return self._extract_dantri(html)
        elif 'thanhnien.vn' in domain:
            return self._extract_thanhnien(html)
        elif 'vietnamnet.vn' in domain:
            return self._extract_vietnamnet(html)
        elif 'gsmarena.com' in domain:
            return self._extract_gsmarena(html)
        else:
            return self._extract_generic(html)
    
    def _extract_vnexpress(self, html: str) -> Dict[str, Any]:
        """Extract content from VnExpress"""
        try:
            # Extract title
            title_match = re.search(r'<h1[^>]*class="[^"]*title[^"]*"[^>]*>(.*?)</h1>', html, re.DOTALL | re.IGNORECASE)
            title = re.sub(r'<[^>]+>', '', title_match.group(1)).strip() if title_match else ""
            
            # Extract article content
            content_patterns = [
                r'<div[^>]*class="[^"]*fck_detail[^"]*"[^>]*>(.*?)</div>',
                r'<div[^>]*class="[^"]*Normal[^"]*"[^>]*>(.*?)</div>',
                r'<p[^>]*class="[^"]*description[^"]*"[^>]*>(.*?)</p>'
            ]
            
            content = ""
            for pattern in content_patterns:
                matches = re.findall(pattern, html, re.DOTALL | re.IGNORECASE)
                if matches:
                    content = ' '.join(matches)
                    break
            
            # Clean content
            content = re.sub(r'<[^>]+>', '', content)
            content = re.sub(r'\s+', ' ', content).strip()
            
            return {
                'title': title,
                'content': content,
                'source': 'vnexpress.net',
                'extracted': True
            }
        except Exception as e:
            logger.error(f"Error extracting VnExpress content: {e}")
            return {'title': '', 'content': '', 'source': 'vnexpress.net', 'extracted': False}

    def _extract_dantri(self, html: str) -> Dict[str, Any]:
        """Extract content from DanTri"""
        try:
            # Extract title
            title_patterns = [
                r'<h1[^>]*class="[^"]*title[^"]*"[^>]*>(.*?)</h1>',
                r'<h1[^>]*>(.*?)</h1>'
            ]

            title = ""
            for pattern in title_patterns:
                title_match = re.search(pattern, html, re.DOTALL | re.IGNORECASE)
                if title_match:
                    title = re.sub(r'<[^>]+>', '', title_match.group(1)).strip()
                    break

            # Extract content
            content_patterns = [
                r'<div[^>]*class="[^"]*singular-content[^"]*"[^>]*>(.*?)</div>',
                r'<div[^>]*class="[^"]*detail-content[^"]*"[^>]*>(.*?)</div>',
                r'<div[^>]*class="[^"]*content[^"]*"[^>]*>(.*?)</div>'
            ]

            content = ""
            for pattern in content_patterns:
                matches = re.findall(pattern, html, re.DOTALL | re.IGNORECASE)
                if matches:
                    content = ' '.join(matches)
                    break

            # Clean content
            content = re.sub(r'<[^>]+>', '', content)
            content = re.sub(r'\s+', ' ', content).strip()

            return {
                'title': title,
                'content': content,
                'source': 'dantri.com.vn',
                'extracted': True
            }
        except Exception as e:
            logger.error(f"Error extracting DanTri content: {e}")
            return {'title': '', 'content': '', 'source': 'dantri.com.vn', 'extracted': False}

    def _extract_thanhnien(self, html: str) -> Dict[str, Any]:
        """Extract content from ThanhNien"""
        try:
            # Extract title
            title_match = re.search(r'<h1[^>]*class="[^"]*details__headline[^"]*"[^>]*>(.*?)</h1>', html, re.DOTALL | re.IGNORECASE)
            if not title_match:
                title_match = re.search(r'<h1[^>]*>(.*?)</h1>', html, re.DOTALL | re.IGNORECASE)
            title = re.sub(r'<[^>]+>', '', title_match.group(1)).strip() if title_match else ""

            # Extract content
            content_patterns = [
                r'<div[^>]*class="[^"]*details__content[^"]*"[^>]*>(.*?)</div>',
                r'<div[^>]*class="[^"]*content[^"]*"[^>]*>(.*?)</div>'
            ]

            content = ""
            for pattern in content_patterns:
                matches = re.findall(pattern, html, re.DOTALL | re.IGNORECASE)
                if matches:
                    content = ' '.join(matches)
                    break

            # Clean content
            content = re.sub(r'<[^>]+>', '', content)
            content = re.sub(r'\s+', ' ', content).strip()

            return {
                'title': title,
                'content': content,
                'source': 'thanhnien.vn',
                'extracted': True
            }
        except Exception as e:
            logger.error(f"Error extracting ThanhNien content: {e}")
            return {'title': '', 'content': '', 'source': 'thanhnien.vn', 'extracted': False}

    def _extract_vietnamnet(self, html: str) -> Dict[str, Any]:
        """Extract content from VietnamNet"""
        try:
            # Extract title
            title_match = re.search(r'<h1[^>]*class="[^"]*title[^"]*"[^>]*>(.*?)</h1>', html, re.DOTALL | re.IGNORECASE)
            if not title_match:
                title_match = re.search(r'<h1[^>]*>(.*?)</h1>', html, re.DOTALL | re.IGNORECASE)
            title = re.sub(r'<[^>]+>', '', title_match.group(1)).strip() if title_match else ""

            # Extract content
            content_patterns = [
                r'<div[^>]*class="[^"]*ArticleContent[^"]*"[^>]*>(.*?)</div>',
                r'<div[^>]*class="[^"]*maincontent[^"]*"[^>]*>(.*?)</div>',
                r'<div[^>]*class="[^"]*content[^"]*"[^>]*>(.*?)</div>'
            ]

            content = ""
            for pattern in content_patterns:
                matches = re.findall(pattern, html, re.DOTALL | re.IGNORECASE)
                if matches:
                    content = ' '.join(matches)
                    break

            # Clean content
            content = re.sub(r'<[^>]+>', '', content)
            content = re.sub(r'\s+', ' ', content).strip()

            return {
                'title': title,
                'content': content,
                'source': 'vietnamnet.vn',
                'extracted': True
            }
        except Exception as e:
            logger.error(f"Error extracting VietnamNet content: {e}")
            return {'title': '', 'content': '', 'source': 'vietnamnet.vn', 'extracted': False}
    
    def _extract_gsmarena(self, html: str) -> Dict[str, Any]:
        """Extract content from GSMArena"""
        try:
            # Extract title
            title_match = re.search(r'<h1[^>]*>(.*?)</h1>', html, re.DOTALL | re.IGNORECASE)
            title = re.sub(r'<[^>]+>', '', title_match.group(1)).strip() if title_match else ""
            
            # Extract specs and content
            content_patterns = [
                r'<div[^>]*class="[^"]*specs-brief[^"]*"[^>]*>(.*?)</div>',
                r'<div[^>]*class="[^"]*article-info[^"]*"[^>]*>(.*?)</div>',
                r'<table[^>]*>(.*?)</table>'
            ]
            
            content = ""
            for pattern in content_patterns:
                matches = re.findall(pattern, html, re.DOTALL | re.IGNORECASE)
                if matches:
                    content += ' '.join(matches) + ' '
            
            # Clean content
            content = re.sub(r'<[^>]+>', '', content)
            content = re.sub(r'\s+', ' ', content).strip()
            
            return {
                'title': title,
                'content': content,
                'source': 'gsmarena.com',
                'extracted': True
            }
        except Exception as e:
            logger.error(f"Error extracting GSMArena content: {e}")
            return {'title': '', 'content': '', 'source': 'gsmarena.com', 'extracted': False}
    
    def _extract_generic(self, html: str) -> Dict[str, Any]:
        """Generic content extraction"""
        try:
            # Extract title
            title_match = re.search(r'<title[^>]*>(.*?)</title>', html, re.DOTALL | re.IGNORECASE)
            title = re.sub(r'<[^>]+>', '', title_match.group(1)).strip() if title_match else ""
            
            # Extract main content
            content_patterns = [
                r'<article[^>]*>(.*?)</article>',
                r'<main[^>]*>(.*?)</main>',
                r'<div[^>]*class="[^"]*content[^"]*"[^>]*>(.*?)</div>'
            ]
            
            content = ""
            for pattern in content_patterns:
                matches = re.findall(pattern, html, re.DOTALL | re.IGNORECASE)
                if matches:
                    content = ' '.join(matches)
                    break
            
            # Clean content
            content = re.sub(r'<[^>]+>', '', content)
            content = re.sub(r'\s+', ' ', content).strip()
            
            return {
                'title': title,
                'content': content,
                'source': 'generic',
                'extracted': True
            }
        except Exception as e:
            logger.error(f"Error extracting generic content: {e}")
            return {'title': '', 'content': '', 'source': 'generic', 'extracted': False}

# Test function
async def test_advanced_handler():
    """Test the advanced site handler"""
    handler = AdvancedSiteHandler()
    
    test_urls = [
        'https://vnexpress.net/cong-nghe',
        'https://www.gsmarena.com/samsung_galaxy_s24-13312.php'
    ]
    
    for url in test_urls:
        print(f"\n🧪 Testing {url}")
        content = await handler.smart_fetch(url)
        if content:
            extracted = handler.extract_content(content, url)
            print(f"✅ Success: {len(extracted['content'])} chars extracted")
            print(f"Title: {extracted['title'][:100]}...")
        else:
            print(f"❌ Failed to fetch {url}")

if __name__ == "__main__":
    asyncio.run(test_advanced_handler())
