# 🚀 Jina Crawler MCP Server - Setup Guide

## ✅ **HOÀN THIỆN - MCP Server chuẩn**

### 🏗️ **Architecture**
- **MCP Protocol**: Chuẩn MCP với stdio communication
- **Core Engine**: Jina-style crawler + Gemini 2.5 Flash Lite
- **Vietnamese Optimized**: Tối ưu đặc biệt cho nội dung tiếng Việt
- **Full Article Support**: T<PERSON>h năng mới - cào toàn bộ bài viết (không tóm tắt)

### 🔧 **Environment Variables**
File `.env` đã được cấu hình:
```bash
GEMINI_API_KEY=AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM
GEMINI_MODEL_NAME=gemini-2.5-flash-lite
GEMINI_API_URL=https://generativelanguage.googleapis.com/v1beta/models
GEMINI_TIMEOUT=30
GEMINI_MAX_RETRIES=3
```

### 🚀 **Cách hoạt động**

#### Pipeline xử lý:
1. **Playwright Crawler** - Advanced web crawling với snapshot
2. **BeautifulSoup** - Làm sạch HTML và extract content
3. **Gemini 2.5 Flash Lite** - AI processing HTML → Markdown
4. **Vietnamese Optimization** - Smart truncation và prompts tiếng Việt

#### Tính năng đặc biệt:
- ✅ **MCP Protocol chuẩn** - Tương thích với Open WebUI
- ✅ **Tối ưu tiếng Việt** - Prompts và xử lý văn bản tiếng Việt
- ✅ **Siêu nhanh** - Sub-second processing với Gemini Flash Lite
- ✅ **Smart truncation** - Tôn trọng cấu trúc câu tiếng Việt
- ✅ **Retry logic** - Xử lý lỗi thông minh với exponential backoff
- ✅ **Parallel processing** - Batch crawling hiệu suất cao

### 🛠️ **MCP Tools Available**

1. **`crawl_url`** - Crawl và xử lý 1 URL với AI
   ```json
   {
     "url": "https://dantri.com.vn",
     "max_content_length": 10000
   }
   ```

2. **`crawl_batch`** - Crawl nhiều URL đồng thời
   ```json
   {
     "urls": ["https://dantri.com.vn", "https://vnexpress.net"],
     "max_content_length": 10000
   }
   ```

3. **`search_site`** - Tìm kiếm trong website
   ```json
   {
     "site_url": "https://dantri.com.vn",
     "query": "công nghệ AI",
     "max_results": 10
   }
   ```

4. **`health_check`** - Kiểm tra sức khỏe hệ thống
5. **`get_crawler_stats`** - Thống kê hiệu suất

### 📊 **Kết quả thực tế**
Test với dantri.com.vn:
```json
{
  "success": true,
  "url": "https://dantri.com.vn",
  "title": "Tin tức Việt Nam và quốc tế nóng, nhanh, cập nhật 24h | Báo Dân trí",
  "processed_content": "# Tin tức Việt Nam và quốc tế nóng, nhanh, cập nhật 24h...",
  "processing_time": 0.90,
  "metadata": {
    "model": "gemini-2.5-flash-lite",
    "task_type": "html_to_markdown",
    "original_length": 8000,
    "output_length": 430
  }
}
```

### 🚀 **Cách chạy**

#### 1. MCP Server (Production)
```bash
cd mem0-owui/mcp-integration/servers/jina_crawler
python3 server.py
```

#### 2. Direct Test
```bash
python3 test_direct.py
```

#### 3. Test với dantri.vn
```bash
python3 test_dantri.py
```

### ✅ **Test Results**
- **✅ Crawler Engine**: Hoạt động hoàn hảo
- **✅ Gemini API**: Processing time 0.90s
- **✅ Vietnamese Content**: Xử lý chính xác tiếng Việt
- **✅ MCP Protocol**: Tương thích chuẩn MCP
- **✅ dantri.com.vn**: Crawl thành công

### 🔗 **Integration Status**
- **✅ MCP Server**: Chuẩn MCP protocol với stdio
- **✅ Configuration**: Environment variables configured
- **✅ Dependencies**: All required packages in requirements.txt
- **✅ Vietnamese Support**: Optimized cho nội dung tiếng Việt
- **✅ Production Ready**: Sẵn sàng deploy

### 📁 **File Structure**
```
jina_crawler/
├── server.py              # MCP Server chính
├── jini_crawler.py        # Core crawler logic
├── jina_style_crawler.py  # Playwright crawler
├── gemini_processor.py    # Gemini AI processor
├── requirements.txt       # Dependencies
├── .env                   # Environment config
├── SETUP_GUIDE.md        # Documentation
├── test_direct.py        # Direct test
├── test_dantri.py        # dantri.vn test
└── utils/                # Utility modules
```

**🎉 Jina Crawler MCP Server đã hoàn thiện và sẵn sàng sử dụng!**