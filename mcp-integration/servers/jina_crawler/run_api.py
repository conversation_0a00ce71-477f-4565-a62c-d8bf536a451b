#!/usr/bin/env python3
"""
Script to run the Jini Crawler API server
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger("jini-api-runner")

def main():
    """Main function to run the API server"""
    try:
        import uvicorn
        from api_server import app
        
        logger.info("Starting Jini Crawler API Server...")
        logger.info("API will be available at: http://0.0.0.0:8010")
        logger.info("OpenAPI docs at: http://0.0.0.0:8010/docs")
        logger.info("Health check at: http://0.0.0.0:8010/health")
        
        # Run the server
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8010,
            reload=False,
            log_level="info",
            access_log=True
        )
        
    except ImportError as e:
        logger.error(f"Missing dependencies: {e}")
        logger.error("Please install: pip install fastapi uvicorn")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
