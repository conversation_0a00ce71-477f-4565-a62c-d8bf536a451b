#!/usr/bin/env python3
"""
Test script for Brave Search MCP Server
Demonstrates all available search capabilities
"""

import asyncio
import json
import sys
import os

# Add the server directory to Python path
sys.path.append(os.path.dirname(__file__))

from server import BraveSearchClient

async def test_brave_search():
    """Test all Brave Search functionalities"""
    
    # Initialize client with API key
    client = BraveSearchClient()
    
    print("🔍 BRAVE SEARCH MCP SERVER TEST")
    print("=" * 50)
    
    try:
        # Test 1: Web Search
        print("\n1. Testing Web Search...")
        web_results = await client.web_search(
            query="Python programming tutorial",
            count=3,
            country="US"
        )
        print(f"✅ Web search successful - Found {len(web_results.get('web', {}).get('results', []))} results")
        
        # Test 2: News Search (with delay to avoid rate limit)
        print("\n2. Testing News Search...")
        await asyncio.sleep(2)  # Rate limit protection
        news_results = await client.news_search(
            query="artificial intelligence",
            count=2,
            country="US"
        )
        print(f"✅ News search successful - Found {len(news_results.get('results', []))} results")
        
        # Test 3: Image Search
        print("\n3. Testing Image Search...")
        await asyncio.sleep(2)  # Rate limit protection
        image_results = await client.image_search(
            query="nature landscape",
            count=2,
            country="US"
        )
        print(f"✅ Image search successful - Found {len(image_results.get('results', []))} results")
        
        print("\n🎉 ALL TESTS PASSED!")
        print("\nAvailable Brave Search Tools:")
        print("- brave_web_search: Search the web")
        print("- brave_news_search: Search for news articles")
        print("- brave_image_search: Search for images")
        print("- brave_video_search: Search for videos")
        print("- brave_summarize_url: Get webpage summaries")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        if "rate limit" in str(e).lower():
            print("💡 This is normal during testing - the API key is working!")
        elif "API key" in str(e):
            print("💡 Please check your Brave Search API key configuration")
    
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(test_brave_search())