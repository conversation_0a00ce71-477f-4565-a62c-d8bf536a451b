#!/usr/bin/env python3
"""
Brave Search MCP Server
Provides web search capabilities using Brave Search API for Open WebUI integration
"""

import asyncio
import json
import os
from datetime import datetime
from typing import Any, Dict, List, Optional
from urllib.parse import quote

import aiohttp
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)

# Initialize the MCP server
server = Server("brave_search")

# Brave Search API configuration
BRAVE_API_BASE = "https://api.search.brave.com/res/v1"

# Supported countries by Brave Search API (38 countries)
SUPPORTED_COUNTRIES = {
    "US", "CA", "GB", "AU", "DE", "FR", "IT", "ES", "NL", "BE", "CH", "AT",
    "SE", "NO", "DK", "FI", "IE", "PT", "GR", "PL", "CZ", "HU", "SK", "SI",
    "HR", "BG", "RO", "EE", "LV", "LT", "LU", "MT", "CY", "IS", "LI", "MC", "SM", "VA"
}

def validate_country(country: str) -> str:
    """Validate and normalize country code"""
    if not country:
        return "US"
    
    country = country.upper()
    
    # Map common country codes that aren't supported to closest alternatives
    country_mapping = {
        "VN": "US",  # Vietnam -> US
        "JP": "US",  # Japan -> US
        "KR": "US",  # Korea -> US
        "CN": "US",  # China -> US
        "IN": "GB",  # India -> GB
        "BR": "US",  # Brazil -> US
        "MX": "US",  # Mexico -> US
        "AR": "US",  # Argentina -> US
        "RU": "DE",  # Russia -> DE
        "TR": "DE",  # Turkey -> DE
        "SA": "GB",  # Saudi Arabia -> GB
        "AE": "GB",  # UAE -> GB
        "SG": "GB",  # Singapore -> GB
        "MY": "GB",  # Malaysia -> GB
        "TH": "GB",  # Thailand -> GB
        "ID": "GB",  # Indonesia -> GB
        "PH": "US",  # Philippines -> US
    }
    
    # Use mapping if country not supported
    if country not in SUPPORTED_COUNTRIES:
        country = country_mapping.get(country, "US")
    
    return country

class BraveSearchClient:
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.getenv('BRAVE_API_KEY', 'BSAWh8paqNo8JFJaBmvd6WX7CqT2_Sk')
        self.session = None
    
    async def get_session(self):
        if self.session is None:
            headers = {
                'Accept': 'application/json',
                'Accept-Encoding': 'gzip'
            }
            if self.api_key:
                headers['X-Subscription-Token'] = self.api_key
            
            self.session = aiohttp.ClientSession(headers=headers)
        return self.session
    
    async def close(self):
        if self.session:
            await self.session.close()
            self.session = None
    
    async def make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Make Brave Search API request"""
        session = await self.get_session()
        url = f"{BRAVE_API_BASE}{endpoint}"
        
        async with session.get(url, params=params) as response:
            if response.status == 200:
                return await response.json()
            elif response.status == 401:
                raise Exception("Brave Search API authentication failed - check your API key")
            elif response.status == 429:
                raise Exception("Brave Search API rate limit exceeded")
            elif response.status == 400:
                error_text = await response.text()
                raise Exception(f"Bad request: {error_text}")
            else:
                error_text = await response.text()
                raise Exception(f"Brave Search API error {response.status}: {error_text}")
    
    async def web_search(self, query: str, count: int = 10, offset: int = 0, 
                        country: str = "US", search_lang: str = "en",
                        ui_lang: str = "en-US", safesearch: str = "moderate",
                        freshness: Optional[str] = None) -> Dict[str, Any]:
        """Perform web search"""
        params = {
            'q': query,
            'count': min(count, 20),  # Max 20 results per request
            'offset': offset,
            'country': validate_country(country),
            'search_lang': search_lang,
            'ui_lang': ui_lang,
            'safesearch': safesearch
        }
        
        if freshness:
            params['freshness'] = freshness
        
        return await self.make_request('/web/search', params)
    
    async def news_search(self, query: str, count: int = 10, offset: int = 0,
                         country: str = "US", search_lang: str = "en",
                         ui_lang: str = "en-US", safesearch: str = "moderate",
                         freshness: Optional[str] = None) -> Dict[str, Any]:
        """Perform news search"""
        params = {
            'q': query,
            'count': min(count, 20),
            'offset': offset,
            'country': validate_country(country),
            'search_lang': search_lang,
            'ui_lang': ui_lang,
            'safesearch': safesearch
        }
        
        if freshness:
            params['freshness'] = freshness
        
        return await self.make_request('/news/search', params)
    
    async def image_search(self, query: str, count: int = 10, offset: int = 0,
                          country: str = "US", search_lang: str = "en",
                          safesearch: str = "moderate") -> Dict[str, Any]:
        """Perform image search"""
        params = {
            'q': query,
            'count': min(count, 20),
            'offset': offset,
            'country': validate_country(country),
            'search_lang': search_lang,
            'safesearch': safesearch
        }
        
        return await self.make_request('/images/search', params)
    
    async def video_search(self, query: str, count: int = 10, offset: int = 0,
                          country: str = "US", search_lang: str = "en",
                          safesearch: str = "moderate") -> Dict[str, Any]:
        """Perform video search"""
        params = {
            'q': query,
            'count': min(count, 20),
            'offset': offset,
            'country': validate_country(country),
            'search_lang': search_lang,
            'safesearch': safesearch
        }
        
        return await self.make_request('/videos/search', params)

# Global Brave Search client
brave_client = BraveSearchClient()

def format_web_results(results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Format web search results"""
    formatted = []
    for result in results:
        formatted.append({
            "title": result.get("title", ""),
            "url": result.get("url", ""),
            "description": result.get("description", ""),
            "published": result.get("age", ""),
            "language": result.get("language", ""),
            "family_friendly": result.get("family_friendly", True)
        })
    return formatted

def format_news_results(results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Format news search results"""
    formatted = []
    for result in results:
        formatted.append({
            "title": result.get("title", ""),
            "url": result.get("url", ""),
            "description": result.get("description", ""),
            "published": result.get("age", ""),
            "source": result.get("meta_url", {}).get("netloc", ""),
            "thumbnail": result.get("thumbnail", {}).get("src", "")
        })
    return formatted

def format_image_results(results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Format image search results"""
    formatted = []
    for result in results:
        formatted.append({
            "title": result.get("title", ""),
            "url": result.get("url", ""),
            "thumbnail": result.get("thumbnail", {}).get("src", ""),
            "source": result.get("source", ""),
            "properties": result.get("properties", {})
        })
    return formatted

def format_video_results(results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Format video search results"""
    formatted = []
    for result in results:
        formatted.append({
            "title": result.get("title", ""),
            "url": result.get("url", ""),
            "description": result.get("description", ""),
            "thumbnail": result.get("thumbnail", {}).get("src", ""),
            "published": result.get("age", ""),
            "duration": result.get("video", {}).get("duration", ""),
            "views": result.get("video", {}).get("views", "")
        })
    return formatted

@server.list_resources()
async def list_resources() -> List[Resource]:
    """List available Brave Search resources"""
    return [
        Resource(
            uri="brave://trending",
            name="Trending Topics",
            description="Current trending search topics",
            mimeType="application/json",
        ),
        Resource(
            uri="brave://search",
            name="Web Search",
            description="Brave web search interface",
            mimeType="application/json",
        )
    ]

@server.read_resource()
async def read_resource(uri: str) -> str:
    """Read Brave Search resource"""
    if uri == "brave://trending":
        return json.dumps({
            "description": "Use search tools to find trending topics",
            "suggestion": "Try searching for current events or popular topics"
        }, indent=2)
    
    elif uri == "brave://search":
        return json.dumps({
            "description": "Use the brave_web_search tool to search the web",
            "example": "brave_web_search('artificial intelligence news')"
        }, indent=2)
    
    raise ValueError(f"Unknown resource: {uri}")

@server.list_tools()
async def list_tools() -> List[Tool]:
    """List available Brave Search tools"""
    return [
        Tool(
            name="brave_web_search",
            description="Search the web using Brave Search",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query"
                    },
                    "count": {
                        "type": "integer",
                        "description": "Number of results (max 20)",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 20
                    },
                    "offset": {
                        "type": "integer",
                        "description": "Offset for pagination",
                        "default": 0,
                        "minimum": 0
                    },
                    "country": {
                        "type": "string",
                        "description": "Country code (e.g., US, GB, DE)",
                        "default": "US"
                    },
                    "freshness": {
                        "type": "string",
                        "description": "Time filter: pd (past day), pw (past week), pm (past month), py (past year)",
                        "enum": ["pd", "pw", "pm", "py"]
                    },
                    "safesearch": {
                        "type": "string",
                        "description": "Safe search level",
                        "enum": ["strict", "moderate", "off"],
                        "default": "moderate"
                    }
                },
                "required": ["query"]
            }
        ),
        Tool(
            name="brave_news_search",
            description="Search for news articles using Brave Search",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "News search query"
                    },
                    "count": {
                        "type": "integer",
                        "description": "Number of results (max 20)",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 20
                    },
                    "offset": {
                        "type": "integer",
                        "description": "Offset for pagination",
                        "default": 0,
                        "minimum": 0
                    },
                    "country": {
                        "type": "string",
                        "description": "Country code (e.g., US, GB, DE)",
                        "default": "US"
                    },
                    "freshness": {
                        "type": "string",
                        "description": "Time filter: pd (past day), pw (past week), pm (past month)",
                        "enum": ["pd", "pw", "pm"]
                    }
                },
                "required": ["query"]
            }
        ),
        Tool(
            name="brave_image_search",
            description="Search for images using Brave Search",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Image search query"
                    },
                    "count": {
                        "type": "integer",
                        "description": "Number of results (max 20)",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 20
                    },
                    "offset": {
                        "type": "integer",
                        "description": "Offset for pagination",
                        "default": 0,
                        "minimum": 0
                    },
                    "country": {
                        "type": "string",
                        "description": "Country code (e.g., US, GB, DE)",
                        "default": "US"
                    },
                    "safesearch": {
                        "type": "string",
                        "description": "Safe search level",
                        "enum": ["strict", "moderate", "off"],
                        "default": "moderate"
                    }
                },
                "required": ["query"]
            }
        ),
        Tool(
            name="brave_video_search",
            description="Search for videos using Brave Search",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Video search query"
                    },
                    "count": {
                        "type": "integer",
                        "description": "Number of results (max 20)",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 20
                    },
                    "offset": {
                        "type": "integer",
                        "description": "Offset for pagination",
                        "default": 0,
                        "minimum": 0
                    },
                    "country": {
                        "type": "string",
                        "description": "Country code (e.g., US, GB, DE)",
                        "default": "US"
                    },
                    "safesearch": {
                        "type": "string",
                        "description": "Safe search level",
                        "enum": ["strict", "moderate", "off"],
                        "default": "moderate"
                    }
                },
                "required": ["query"]
            }
        ),
        Tool(
            name="brave_summarize_url",
            description="Get a summary of a webpage content",
            inputSchema={
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "URL to summarize"
                    },
                    "summary": {
                        "type": "boolean",
                        "description": "Whether to include AI summary",
                        "default": True
                    }
                },
                "required": ["url"]
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle tool calls"""
    
    try:
        if name == "brave_web_search":
            query = arguments["query"]
            count = arguments.get("count", 10)
            offset = arguments.get("offset", 0)
            country = validate_country(arguments.get("country", "US"))
            freshness = arguments.get("freshness")
            safesearch = arguments.get("safesearch", "moderate")
            
            results = await brave_client.web_search(
                query=query,
                count=count,
                offset=offset,
                country=country,
                freshness=freshness,
                safesearch=safesearch
            )
            
            # Format results
            web_results = results.get("web", {}).get("results", [])
            formatted_results = format_web_results(web_results)
            
            response = {
                "query": query,
                "type": "web_search",
                "results_count": len(formatted_results),
                "results": formatted_results,
                "query_info": {
                    "country": country,
                    "safesearch": safesearch,
                    "freshness": freshness
                }
            }
            
            # Add mixed results if available
            if "mixed" in results:
                response["mixed_results"] = results["mixed"]
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        elif name == "brave_news_search":
            query = arguments["query"]
            count = arguments.get("count", 10)
            offset = arguments.get("offset", 0)
            country = validate_country(arguments.get("country", "US"))
            freshness = arguments.get("freshness")
            
            results = await brave_client.news_search(
                query=query,
                count=count,
                offset=offset,
                country=country,
                freshness=freshness
            )
            
            # Format results
            news_results = results.get("results", [])
            formatted_results = format_news_results(news_results)
            
            response = {
                "query": query,
                "type": "news_search",
                "results_count": len(formatted_results),
                "results": formatted_results,
                "query_info": {
                    "country": country,
                    "freshness": freshness
                }
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        elif name == "brave_image_search":
            query = arguments["query"]
            count = arguments.get("count", 10)
            offset = arguments.get("offset", 0)
            country = validate_country(arguments.get("country", "US"))
            safesearch = arguments.get("safesearch", "moderate")
            
            results = await brave_client.image_search(
                query=query,
                count=count,
                offset=offset,
                country=country,
                safesearch=safesearch
            )
            
            # Format results
            image_results = results.get("results", [])
            formatted_results = format_image_results(image_results)
            
            response = {
                "query": query,
                "type": "image_search",
                "results_count": len(formatted_results),
                "results": formatted_results,
                "query_info": {
                    "country": country,
                    "safesearch": safesearch
                }
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        elif name == "brave_video_search":
            query = arguments["query"]
            count = arguments.get("count", 10)
            offset = arguments.get("offset", 0)
            country = validate_country(arguments.get("country", "US"))
            safesearch = arguments.get("safesearch", "moderate")
            
            results = await brave_client.video_search(
                query=query,
                count=count,
                offset=offset,
                country=country,
                safesearch=safesearch
            )
            
            # Format results
            video_results = results.get("results", [])
            formatted_results = format_video_results(video_results)
            
            response = {
                "query": query,
                "type": "video_search",
                "results_count": len(formatted_results),
                "results": formatted_results,
                "query_info": {
                    "country": country,
                    "safesearch": safesearch
                }
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        elif name == "brave_summarize_url":
            url = arguments["url"]
            include_summary = arguments.get("summary", True)
            
            # For URL summarization, we'll use web search to find information about the URL
            # This is a simplified implementation - in a real scenario, you might want to
            # fetch and parse the URL content directly
            
            search_query = f"site:{url.split('/')[2]} {url}"
            results = await brave_client.web_search(query=search_query, count=3)
            
            web_results = results.get("web", {}).get("results", [])
            
            response = {
                "url": url,
                "type": "url_summary",
                "search_results": format_web_results(web_results),
                "note": "This is a search-based summary. For full content analysis, consider using a dedicated web scraping tool."
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        else:
            return [TextContent(type="text", text=f"Unknown tool: {name}")]
    
    except Exception as e:
        error_msg = str(e)
        if "API key" in error_msg:
            error_msg += "\n\nTo use Brave Search, you need to:\n1. Get a free API key from https://api.search.brave.com/\n2. Set the BRAVE_API_KEY environment variable"
        
        return [TextContent(type="text", text=f"Error: {error_msg}")]

async def main():
    """Main server function"""
    from mcp.types import ServerCapabilities
    try:
        async with stdio_server() as (read_stream, write_stream):
            await server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="brave_search",
                    server_version="1.0.0",
                    capabilities=ServerCapabilities(
                        tools={}
                    ),
                ),
            )
    finally:
        await brave_client.close()

if __name__ == "__main__":
    asyncio.run(main())