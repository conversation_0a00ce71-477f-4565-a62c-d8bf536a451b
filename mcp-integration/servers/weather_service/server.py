#!/usr/bin/env python3
"""
MCP Server for Weather Information Service
Provides tools for weather data, forecasts, and weather-related utilities
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional
import httpx
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)
from mcp.server.lowlevel.server import NotificationOptions

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("weather-service-mcp-server")

class WeatherServiceMCPServer:
    def __init__(self):
        self.server = Server("weather-service")
        self.http_client = httpx.AsyncClient(timeout=30.0)
        
        # Weather API endpoints (using free services)
        self.openweather_base = "https://api.openweathermap.org/data/2.5"
        self.wttr_base = "https://wttr.in"
        
        self.setup_handlers()

    def setup_handlers(self):
        """Setup MCP server handlers"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available weather tools"""
            return [
                Tool(
                    name="get_current_weather",
                    description="Get current weather conditions for a location",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "City name, coordinates (lat,lon), or location query"
                            },
                            "units": {
                                "type": "string",
                                "enum": ["metric", "imperial", "kelvin"],
                                "description": "Temperature units",
                                "default": "metric"
                            },
                            "language": {
                                "type": "string",
                                "description": "Language for weather descriptions (en, vi, etc.)",
                                "default": "en"
                            }
                        },
                        "required": ["location"]
                    }
                ),
                Tool(
                    name="get_weather_forecast",
                    description="Get weather forecast for a location",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "City name, coordinates (lat,lon), or location query"
                            },
                            "days": {
                                "type": "integer",
                                "description": "Number of forecast days (1-7)",
                                "default": 3,
                                "minimum": 1,
                                "maximum": 7
                            },
                            "units": {
                                "type": "string",
                                "enum": ["metric", "imperial", "kelvin"],
                                "description": "Temperature units",
                                "default": "metric"
                            }
                        },
                        "required": ["location"]
                    }
                ),
                Tool(
                    name="get_weather_alerts",
                    description="Get weather alerts and warnings for a location",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "City name, coordinates (lat,lon), or location query"
                            },
                            "severity": {
                                "type": "string",
                                "enum": ["all", "minor", "moderate", "severe", "extreme"],
                                "description": "Minimum alert severity level",
                                "default": "all"
                            }
                        },
                        "required": ["location"]
                    }
                ),
                Tool(
                    name="compare_weather_locations",
                    description="Compare weather conditions between multiple locations",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "locations": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "List of locations to compare",
                                "minItems": 2,
                                "maxItems": 5
                            },
                            "units": {
                                "type": "string",
                                "enum": ["metric", "imperial", "kelvin"],
                                "description": "Temperature units",
                                "default": "metric"
                            }
                        },
                        "required": ["locations"]
                    }
                ),
                Tool(
                    name="get_weather_history",
                    description="Get historical weather data for a location",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "City name, coordinates (lat,lon), or location query"
                            },
                            "date": {
                                "type": "string",
                                "description": "Date for historical data (YYYY-MM-DD format)"
                            },
                            "units": {
                                "type": "string",
                                "enum": ["metric", "imperial", "kelvin"],
                                "description": "Temperature units",
                                "default": "metric"
                            }
                        },
                        "required": ["location", "date"]
                    }
                ),
                Tool(
                    name="get_air_quality",
                    description="Get air quality information for a location",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "City name, coordinates (lat,lon), or location query"
                            }
                        },
                        "required": ["location"]
                    }
                ),
                Tool(
                    name="get_uv_index",
                    description="Get UV index information for a location",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "City name, coordinates (lat,lon), or location query"
                            }
                        },
                        "required": ["location"]
                    }
                ),
                Tool(
                    name="get_weather_map_url",
                    description="Get weather map URLs for visualization",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "City name, coordinates (lat,lon), or location query"
                            },
                            "map_type": {
                                "type": "string",
                                "enum": ["temperature", "precipitation", "pressure", "wind", "clouds"],
                                "description": "Type of weather map",
                                "default": "temperature"
                            },
                            "zoom": {
                                "type": "integer",
                                "description": "Map zoom level (1-10)",
                                "default": 5,
                                "minimum": 1,
                                "maximum": 10
                            }
                        },
                        "required": ["location"]
                    }
                ),
                Tool(
                    name="weather_travel_advice",
                    description="Get weather-based travel advice for a location",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "Destination location"
                            },
                            "travel_date": {
                                "type": "string",
                                "description": "Travel date (YYYY-MM-DD format, optional)"
                            },
                            "activity_type": {
                                "type": "string",
                                "enum": ["outdoor", "indoor", "beach", "hiking", "business", "general"],
                                "description": "Type of activities planned",
                                "default": "general"
                            }
                        },
                        "required": ["location"]
                    }
                )
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls"""
            try:
                if name == "get_current_weather":
                    return await self._get_current_weather(arguments)
                elif name == "get_weather_forecast":
                    return await self._get_weather_forecast(arguments)
                elif name == "get_weather_alerts":
                    return await self._get_weather_alerts(arguments)
                elif name == "compare_weather_locations":
                    return await self._compare_weather_locations(arguments)
                elif name == "get_weather_history":
                    return await self._get_weather_history(arguments)
                elif name == "get_air_quality":
                    return await self._get_air_quality(arguments)
                elif name == "get_uv_index":
                    return await self._get_uv_index(arguments)
                elif name == "get_weather_map_url":
                    return await self._get_weather_map_url(arguments)
                elif name == "weather_travel_advice":
                    return await self._weather_travel_advice(arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
            except Exception as e:
                logger.error(f"Error in tool {name}: {str(e)}")
                return [TextContent(type="text", text=f"Error: {str(e)}")]

    async def _get_current_weather(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get current weather conditions"""
        try:
            location = arguments["location"]
            units = arguments.get("units", "metric")
            language = arguments.get("language", "en")
            
            # Use wttr.in for free weather data
            url = f"{self.wttr_base}/{location}?format=j1"
            
            response = await self.http_client.get(url)
            response.raise_for_status()
            data = response.json()
            
            current = data.get("current_condition", [{}])[0]
            location_info = data.get("nearest_area", [{}])[0]
            
            # Convert temperature based on units
            temp_c = float(current.get("temp_C", 0))
            if units == "imperial":
                temperature = round(temp_c * 9/5 + 32, 1)
                temp_unit = "°F"
            elif units == "kelvin":
                temperature = round(temp_c + 273.15, 1)
                temp_unit = "K"
            else:
                temperature = temp_c
                temp_unit = "°C"
            
            # Convert wind speed
            wind_kmh = float(current.get("windspeedKmph", 0))
            if units == "imperial":
                wind_speed = round(wind_kmh * 0.621371, 1)
                wind_unit = "mph"
            else:
                wind_speed = wind_kmh
                wind_unit = "km/h"
            
            result = {
                "location": {
                    "name": location_info.get("areaName", [{}])[0].get("value", location),
                    "country": location_info.get("country", [{}])[0].get("value", ""),
                    "region": location_info.get("region", [{}])[0].get("value", ""),
                    "coordinates": {
                        "latitude": location_info.get("latitude", ""),
                        "longitude": location_info.get("longitude", "")
                    }
                },
                "current_weather": {
                    "temperature": f"{temperature}{temp_unit}",
                    "feels_like": f"{round(float(current.get('FeelsLikeC', temp_c)) * (9/5) + 32 if units == 'imperial' else float(current.get('FeelsLikeC', temp_c)), 1)}{temp_unit}",
                    "description": current.get("weatherDesc", [{}])[0].get("value", ""),
                    "humidity": f"{current.get('humidity', 0)}%",
                    "pressure": f"{current.get('pressure', 0)} mb",
                    "visibility": f"{current.get('visibility', 0)} km",
                    "uv_index": current.get("uvIndex", 0),
                    "wind": {
                        "speed": f"{wind_speed} {wind_unit}",
                        "direction": current.get("winddir16Point", ""),
                        "degree": f"{current.get('winddirDegree', 0)}°"
                    },
                    "precipitation": f"{current.get('precipMM', 0)} mm",
                    "cloud_cover": f"{current.get('cloudcover', 0)}%"
                },
                "observation_time": current.get("observation_time", ""),
                "units": units
            }
            
            return [TextContent(
                type="text",
                text=f"Current weather for {location}:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error getting current weather: {str(e)}")
            return [TextContent(type="text", text=f"Error getting current weather: {str(e)}")]

    async def _get_weather_forecast(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get weather forecast"""
        try:
            location = arguments["location"]
            days = min(arguments.get("days", 3), 7)
            units = arguments.get("units", "metric")
            
            # Use wttr.in for forecast data
            url = f"{self.wttr_base}/{location}?format=j1"
            
            response = await self.http_client.get(url)
            response.raise_for_status()
            data = response.json()
            
            location_info = data.get("nearest_area", [{}])[0]
            weather_data = data.get("weather", [])
            
            forecast_days = []
            for day_data in weather_data[:days]:
                date = day_data.get("date", "")
                
                # Get temperature values
                max_temp_c = float(day_data.get("maxtempC", 0))
                min_temp_c = float(day_data.get("mintempC", 0))
                
                if units == "imperial":
                    max_temp = round(max_temp_c * 9/5 + 32, 1)
                    min_temp = round(min_temp_c * 9/5 + 32, 1)
                    temp_unit = "°F"
                elif units == "kelvin":
                    max_temp = round(max_temp_c + 273.15, 1)
                    min_temp = round(min_temp_c + 273.15, 1)
                    temp_unit = "K"
                else:
                    max_temp = max_temp_c
                    min_temp = min_temp_c
                    temp_unit = "°C"
                
                # Get hourly data for more details
                hourly = day_data.get("hourly", [])
                if hourly:
                    midday_data = hourly[len(hourly)//2]  # Get midday data
                    description = midday_data.get("weatherDesc", [{}])[0].get("value", "")
                    humidity = midday_data.get("humidity", 0)
                    wind_speed = midday_data.get("windspeedKmph", 0)
                    precipitation = midday_data.get("precipMM", 0)
                else:
                    description = ""
                    humidity = 0
                    wind_speed = 0
                    precipitation = 0
                
                forecast_days.append({
                    "date": date,
                    "temperature": {
                        "max": f"{max_temp}{temp_unit}",
                        "min": f"{min_temp}{temp_unit}"
                    },
                    "description": description,
                    "humidity": f"{humidity}%",
                    "wind_speed": f"{wind_speed} km/h",
                    "precipitation": f"{precipitation} mm",
                    "uv_index": day_data.get("uvIndex", 0)
                })
            
            result = {
                "location": {
                    "name": location_info.get("areaName", [{}])[0].get("value", location),
                    "country": location_info.get("country", [{}])[0].get("value", "")
                },
                "forecast_days": days,
                "forecast": forecast_days,
                "units": units
            }
            
            return [TextContent(
                type="text",
                text=f"Weather forecast for {location} ({days} days):\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error getting weather forecast: {str(e)}")
            return [TextContent(type="text", text=f"Error getting weather forecast: {str(e)}")]

    async def _get_weather_alerts(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get weather alerts (simplified implementation)"""
        try:
            location = arguments["location"]
            severity = arguments.get("severity", "all")
            
            # Note: This is a simplified implementation
            # In a real scenario, you'd use a proper weather alerts API
            
            result = {
                "location": location,
                "alerts": [],
                "message": "Weather alerts feature requires a premium weather API service. This is a placeholder implementation.",
                "recommendation": "For real-time weather alerts, consider integrating with services like OpenWeatherMap One Call API, WeatherAPI, or national weather services."
            }
            
            return [TextContent(
                type="text",
                text=f"Weather alerts for {location}:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error getting weather alerts: {str(e)}")
            return [TextContent(type="text", text=f"Error getting weather alerts: {str(e)}")]

    async def _compare_weather_locations(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Compare weather between multiple locations"""
        try:
            locations = arguments["locations"]
            units = arguments.get("units", "metric")
            
            comparison_data = []
            
            for location in locations:
                try:
                    # Get current weather for each location
                    weather_result = await self._get_current_weather({
                        "location": location,
                        "units": units
                    })
                    
                    # Parse the result to extract key data
                    if weather_result and "Error" not in weather_result[0].text:
                        # Extract temperature from the result
                        result_text = weather_result[0].text
                        if "current_weather" in result_text:
                            # Simple parsing - in production, you'd want more robust parsing
                            comparison_data.append({
                                "location": location,
                                "status": "success",
                                "summary": f"Weather data retrieved for {location}"
                            })
                        else:
                            comparison_data.append({
                                "location": location,
                                "status": "error",
                                "message": "Could not parse weather data"
                            })
                    else:
                        comparison_data.append({
                            "location": location,
                            "status": "error",
                            "message": "Failed to get weather data"
                        })
                        
                except Exception as e:
                    comparison_data.append({
                        "location": location,
                        "status": "error",
                        "message": str(e)
                    })
            
            result = {
                "comparison_type": "weather_conditions",
                "locations_count": len(locations),
                "units": units,
                "locations": comparison_data,
                "note": "For detailed comparison, call get_current_weather for each location individually"
            }
            
            return [TextContent(
                type="text",
                text=f"Weather comparison for {len(locations)} locations:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error comparing weather locations: {str(e)}")
            return [TextContent(type="text", text=f"Error comparing weather locations: {str(e)}")]

    async def _get_weather_history(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get historical weather data"""
        try:
            location = arguments["location"]
            date = arguments["date"]
            units = arguments.get("units", "metric")
            
            result = {
                "location": location,
                "date": date,
                "units": units,
                "historical_data": None,
                "message": "Historical weather data requires a premium API service.",
                "recommendation": "For historical weather data, consider using services like OpenWeatherMap History API, WeatherAPI History, or Visual Crossing Weather API."
            }
            
            return [TextContent(
                type="text",
                text=f"Historical weather data for {location} on {date}:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error getting weather history: {str(e)}")
            return [TextContent(type="text", text=f"Error getting weather history: {str(e)}")]

    async def _get_air_quality(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get air quality information"""
        try:
            location = arguments["location"]
            
            result = {
                "location": location,
                "air_quality": None,
                "message": "Air quality data requires specialized API services.",
                "recommendation": "For air quality data, consider using services like OpenWeatherMap Air Pollution API, IQAir API, or government environmental agencies."
            }
            
            return [TextContent(
                type="text",
                text=f"Air quality information for {location}:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error getting air quality: {str(e)}")
            return [TextContent(type="text", text=f"Error getting air quality: {str(e)}")]

    async def _get_uv_index(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get UV index information"""
        try:
            location = arguments["location"]
            
            # Try to get UV index from current weather
            weather_result = await self._get_current_weather({
                "location": location,
                "units": "metric"
            })
            
            if weather_result and "Error" not in weather_result[0].text:
                result = {
                    "location": location,
                    "uv_data": "UV index included in current weather data",
                    "recommendation": "Check the current weather data for UV index information"
                }
            else:
                result = {
                    "location": location,
                    "uv_data": None,
                    "message": "UV index data requires specialized API services.",
                    "recommendation": "For detailed UV index data, consider using services like OpenWeatherMap UV Index API or EPA UV Index API."
                }
            
            return [TextContent(
                type="text",
                text=f"UV index information for {location}:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error getting UV index: {str(e)}")
            return [TextContent(type="text", text=f"Error getting UV index: {str(e)}")]

    async def _get_weather_map_url(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get weather map URLs"""
        try:
            location = arguments["location"]
            map_type = arguments.get("map_type", "temperature")
            zoom = arguments.get("zoom", 5)
            
            # Generate weather map URLs (using free services)
            maps = {
                "wttr_in_map": f"{self.wttr_base}/{location}.png",
                "windy_map": f"https://embed.windy.com/embed2.html?lat=0&lon=0&detailLat=0&detailLon=0&width=650&height=450&zoom={zoom}&level=surface&overlay={map_type}&product=ecmwf&menu=&message=&marker=&calendar=now&pressure=&type=map&location=coordinates&detail=&metricWind=default&metricTemp=default&radarRange=-1",
                "openweather_map": f"https://openweathermap.org/weathermap?basemap=map&cities=true&layer={map_type}&lat=0&lon=0&zoom={zoom}"
            }
            
            result = {
                "location": location,
                "map_type": map_type,
                "zoom_level": zoom,
                "weather_maps": maps,
                "note": "Map URLs are generic. For precise location mapping, coordinates would be needed."
            }
            
            return [TextContent(
                type="text",
                text=f"Weather map URLs for {location}:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error getting weather map URLs: {str(e)}")
            return [TextContent(type="text", text=f"Error getting weather map URLs: {str(e)}")]

    async def _weather_travel_advice(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get weather-based travel advice"""
        try:
            location = arguments["location"]
            travel_date = arguments.get("travel_date")
            activity_type = arguments.get("activity_type", "general")
            
            # Get current weather or forecast
            if travel_date:
                # For future dates, get forecast
                weather_result = await self._get_weather_forecast({
                    "location": location,
                    "days": 7,
                    "units": "metric"
                })
            else:
                # For current conditions
                weather_result = await self._get_current_weather({
                    "location": location,
                    "units": "metric"
                })
            
            # Generate travel advice based on activity type
            advice_templates = {
                "outdoor": [
                    "Check temperature for appropriate clothing",
                    "Monitor precipitation for outdoor activities",
                    "Consider wind conditions for outdoor sports",
                    "Check UV index for sun protection"
                ],
                "beach": [
                    "Check temperature and water conditions",
                    "Monitor UV index for sun safety",
                    "Check wind conditions for water activities",
                    "Avoid beach during storms or high winds"
                ],
                "hiking": [
                    "Check temperature range for layered clothing",
                    "Monitor precipitation and trail conditions",
                    "Check visibility for mountain hiking",
                    "Consider altitude and weather changes"
                ],
                "business": [
                    "Check temperature for appropriate attire",
                    "Monitor precipitation for travel planning",
                    "Consider weather delays for transportation",
                    "Pack weather-appropriate accessories"
                ],
                "general": [
                    "Check current weather conditions",
                    "Monitor forecast for travel dates",
                    "Pack appropriate clothing",
                    "Plan for weather-related delays"
                ]
            }
            
            result = {
                "location": location,
                "travel_date": travel_date or "current",
                "activity_type": activity_type,
                "weather_status": "Retrieved" if weather_result and "Error" not in weather_result[0].text else "Error retrieving weather",
                "travel_advice": advice_templates.get(activity_type, advice_templates["general"]),
                "recommendations": [
                    "Check weather forecast closer to travel date",
                    "Pack layers for temperature changes",
                    "Bring weather protection (umbrella, sunscreen)",
                    "Monitor local weather alerts",
                    "Have backup indoor activities planned"
                ]
            }
            
            return [TextContent(
                type="text",
                text=f"Weather travel advice for {location}:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            logger.error(f"Error getting travel advice: {str(e)}")
            return [TextContent(type="text", text=f"Error getting travel advice: {str(e)}")]

    async def run(self):
        """Run the MCP server"""
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="weather-service",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )

async def main():
    """Main entry point"""
    server = WeatherServiceMCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())