#!/usr/bin/env python3
"""
File System MCP Server
Provides file system operations for Open WebUI integration
"""

import asyncio
import json
import os
import shutil
import stat
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)

# Initialize the MCP server
server = Server("filesystem")

# Safe directory - restrict operations to this directory and subdirectories
SAFE_ROOT = Path("/home/<USER>/AccA/AccA")

def is_safe_path(path: Path) -> bool:
    """Check if path is within safe directory"""
    try:
        resolved_path = path.resolve()
        return str(resolved_path).startswith(str(SAFE_ROOT.resolve()))
    except (OSError, ValueError):
        return False

def get_safe_path(path_str: str) -> Path:
    """Get safe path within allowed directory"""
    if path_str.startswith('/'):
        # Absolute path - make it relative to safe root
        path = SAFE_ROOT / path_str.lstrip('/')
    else:
        # Relative path
        path = SAFE_ROOT / path_str
    
    if not is_safe_path(path):
        raise ValueError(f"Path {path_str} is outside safe directory")
    
    return path

def format_file_size(size: int) -> str:
    """Format file size in human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size < 1024.0:
            return f"{size:.1f} {unit}"
        size /= 1024.0
    return f"{size:.1f} TB"

def get_file_info(path: Path) -> Dict[str, Any]:
    """Get detailed file information"""
    try:
        stat_info = path.stat()
        return {
            "name": path.name,
            "path": str(path.relative_to(SAFE_ROOT)),
            "type": "directory" if path.is_dir() else "file",
            "size": stat_info.st_size,
            "size_formatted": format_file_size(stat_info.st_size),
            "modified": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
            "created": datetime.fromtimestamp(stat_info.st_ctime).isoformat(),
            "permissions": stat.filemode(stat_info.st_mode),
            "is_readable": os.access(path, os.R_OK),
            "is_writable": os.access(path, os.W_OK),
            "is_executable": os.access(path, os.X_OK),
        }
    except (OSError, ValueError) as e:
        return {"error": str(e)}

@server.list_resources()
async def list_resources() -> List[Resource]:
    """List available file system resources"""
    return [
        Resource(
            uri="filesystem://root",
            name="File System Root",
            description="Root directory for file operations",
            mimeType="application/json",
        )
    ]

@server.read_resource()
async def read_resource(uri: str) -> str:
    """Read file system resource"""
    if uri == "filesystem://root":
        root_info = get_file_info(SAFE_ROOT)
        return json.dumps(root_info, indent=2)
    
    raise ValueError(f"Unknown resource: {uri}")

@server.list_tools()
async def list_tools() -> List[Tool]:
    """List available file system tools"""
    return [
        Tool(
            name="list_directory",
            description="List contents of a directory",
            inputSchema={
                "type": "object",
                "properties": {
                    "path": {
                        "type": "string",
                        "description": "Directory path to list (relative to safe root)",
                        "default": "."
                    },
                    "show_hidden": {
                        "type": "boolean",
                        "description": "Show hidden files and directories",
                        "default": False
                    },
                    "recursive": {
                        "type": "boolean",
                        "description": "List recursively",
                        "default": False
                    }
                },
                "required": []
            }
        ),
        Tool(
            name="read_file",
            description="Read contents of a text file",
            inputSchema={
                "type": "object",
                "properties": {
                    "path": {
                        "type": "string",
                        "description": "File path to read"
                    },
                    "encoding": {
                        "type": "string",
                        "description": "File encoding",
                        "default": "utf-8"
                    }
                },
                "required": ["path"]
            }
        ),
        Tool(
            name="write_file",
            description="Write content to a file",
            inputSchema={
                "type": "object",
                "properties": {
                    "path": {
                        "type": "string",
                        "description": "File path to write"
                    },
                    "content": {
                        "type": "string",
                        "description": "Content to write"
                    },
                    "encoding": {
                        "type": "string",
                        "description": "File encoding",
                        "default": "utf-8"
                    },
                    "create_dirs": {
                        "type": "boolean",
                        "description": "Create parent directories if they don't exist",
                        "default": True
                    }
                },
                "required": ["path", "content"]
            }
        ),
        Tool(
            name="create_directory",
            description="Create a new directory",
            inputSchema={
                "type": "object",
                "properties": {
                    "path": {
                        "type": "string",
                        "description": "Directory path to create"
                    },
                    "parents": {
                        "type": "boolean",
                        "description": "Create parent directories if they don't exist",
                        "default": True
                    }
                },
                "required": ["path"]
            }
        ),
        Tool(
            name="delete_file",
            description="Delete a file or directory",
            inputSchema={
                "type": "object",
                "properties": {
                    "path": {
                        "type": "string",
                        "description": "Path to delete"
                    },
                    "recursive": {
                        "type": "boolean",
                        "description": "Delete directories recursively",
                        "default": False
                    }
                },
                "required": ["path"]
            }
        ),
        Tool(
            name="copy_file",
            description="Copy a file or directory",
            inputSchema={
                "type": "object",
                "properties": {
                    "source": {
                        "type": "string",
                        "description": "Source path"
                    },
                    "destination": {
                        "type": "string",
                        "description": "Destination path"
                    },
                    "overwrite": {
                        "type": "boolean",
                        "description": "Overwrite destination if it exists",
                        "default": False
                    }
                },
                "required": ["source", "destination"]
            }
        ),
        Tool(
            name="move_file",
            description="Move/rename a file or directory",
            inputSchema={
                "type": "object",
                "properties": {
                    "source": {
                        "type": "string",
                        "description": "Source path"
                    },
                    "destination": {
                        "type": "string",
                        "description": "Destination path"
                    },
                    "overwrite": {
                        "type": "boolean",
                        "description": "Overwrite destination if it exists",
                        "default": False
                    }
                },
                "required": ["source", "destination"]
            }
        ),
        Tool(
            name="get_file_info",
            description="Get detailed information about a file or directory",
            inputSchema={
                "type": "object",
                "properties": {
                    "path": {
                        "type": "string",
                        "description": "Path to get information about"
                    }
                },
                "required": ["path"]
            }
        ),
        Tool(
            name="search_files",
            description="Search for files by name pattern",
            inputSchema={
                "type": "object",
                "properties": {
                    "pattern": {
                        "type": "string",
                        "description": "File name pattern (supports wildcards)"
                    },
                    "path": {
                        "type": "string",
                        "description": "Directory to search in",
                        "default": "."
                    },
                    "recursive": {
                        "type": "boolean",
                        "description": "Search recursively",
                        "default": True
                    }
                },
                "required": ["pattern"]
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle tool calls"""
    
    try:
        if name == "list_directory":
            path_str = arguments.get("path", ".")
            show_hidden = arguments.get("show_hidden", False)
            recursive = arguments.get("recursive", False)
            
            path = get_safe_path(path_str)
            
            if not path.exists():
                return [TextContent(type="text", text=f"Directory {path_str} does not exist")]
            
            if not path.is_dir():
                return [TextContent(type="text", text=f"{path_str} is not a directory")]
            
            items = []
            
            def scan_directory(dir_path: Path, level: int = 0):
                try:
                    for item in sorted(dir_path.iterdir()):
                        if not show_hidden and item.name.startswith('.'):
                            continue
                        
                        info = get_file_info(item)
                        info["level"] = level
                        items.append(info)
                        
                        if recursive and item.is_dir() and level < 3:  # Limit recursion depth
                            scan_directory(item, level + 1)
                except PermissionError:
                    pass
            
            scan_directory(path)
            
            result = {
                "directory": str(path.relative_to(SAFE_ROOT)),
                "total_items": len(items),
                "items": items
            }
            
            return [TextContent(type="text", text=json.dumps(result, indent=2))]
        
        elif name == "read_file":
            path_str = arguments["path"]
            encoding = arguments.get("encoding", "utf-8")
            
            path = get_safe_path(path_str)
            
            if not path.exists():
                return [TextContent(type="text", text=f"File {path_str} does not exist")]
            
            if not path.is_file():
                return [TextContent(type="text", text=f"{path_str} is not a file")]
            
            try:
                content = path.read_text(encoding=encoding)
                result = {
                    "path": str(path.relative_to(SAFE_ROOT)),
                    "size": len(content),
                    "encoding": encoding,
                    "content": content
                }
                return [TextContent(type="text", text=json.dumps(result, indent=2))]
            except UnicodeDecodeError:
                return [TextContent(type="text", text=f"Cannot read {path_str}: not a text file or wrong encoding")]
        
        elif name == "write_file":
            path_str = arguments["path"]
            content = arguments["content"]
            encoding = arguments.get("encoding", "utf-8")
            create_dirs = arguments.get("create_dirs", True)
            
            path = get_safe_path(path_str)
            
            if create_dirs:
                path.parent.mkdir(parents=True, exist_ok=True)
            
            path.write_text(content, encoding=encoding)
            
            result = {
                "path": str(path.relative_to(SAFE_ROOT)),
                "size": len(content),
                "encoding": encoding,
                "status": "written"
            }
            
            return [TextContent(type="text", text=json.dumps(result, indent=2))]
        
        elif name == "create_directory":
            path_str = arguments["path"]
            parents = arguments.get("parents", True)
            
            path = get_safe_path(path_str)
            
            path.mkdir(parents=parents, exist_ok=True)
            
            result = {
                "path": str(path.relative_to(SAFE_ROOT)),
                "status": "created"
            }
            
            return [TextContent(type="text", text=json.dumps(result, indent=2))]
        
        elif name == "delete_file":
            path_str = arguments["path"]
            recursive = arguments.get("recursive", False)
            
            path = get_safe_path(path_str)
            
            if not path.exists():
                return [TextContent(type="text", text=f"Path {path_str} does not exist")]
            
            if path.is_dir():
                if recursive:
                    shutil.rmtree(path)
                else:
                    path.rmdir()
            else:
                path.unlink()
            
            result = {
                "path": str(path.relative_to(SAFE_ROOT)),
                "status": "deleted"
            }
            
            return [TextContent(type="text", text=json.dumps(result, indent=2))]
        
        elif name == "copy_file":
            source_str = arguments["source"]
            dest_str = arguments["destination"]
            overwrite = arguments.get("overwrite", False)
            
            source = get_safe_path(source_str)
            dest = get_safe_path(dest_str)
            
            if not source.exists():
                return [TextContent(type="text", text=f"Source {source_str} does not exist")]
            
            if dest.exists() and not overwrite:
                return [TextContent(type="text", text=f"Destination {dest_str} exists and overwrite is False")]
            
            if source.is_dir():
                shutil.copytree(source, dest, dirs_exist_ok=overwrite)
            else:
                shutil.copy2(source, dest)
            
            result = {
                "source": str(source.relative_to(SAFE_ROOT)),
                "destination": str(dest.relative_to(SAFE_ROOT)),
                "status": "copied"
            }
            
            return [TextContent(type="text", text=json.dumps(result, indent=2))]
        
        elif name == "move_file":
            source_str = arguments["source"]
            dest_str = arguments["destination"]
            overwrite = arguments.get("overwrite", False)
            
            source = get_safe_path(source_str)
            dest = get_safe_path(dest_str)
            
            if not source.exists():
                return [TextContent(type="text", text=f"Source {source_str} does not exist")]
            
            if dest.exists() and not overwrite:
                return [TextContent(type="text", text=f"Destination {dest_str} exists and overwrite is False")]
            
            shutil.move(str(source), str(dest))
            
            result = {
                "source": source_str,
                "destination": str(dest.relative_to(SAFE_ROOT)),
                "status": "moved"
            }
            
            return [TextContent(type="text", text=json.dumps(result, indent=2))]
        
        elif name == "get_file_info":
            path_str = arguments["path"]
            path = get_safe_path(path_str)
            
            if not path.exists():
                return [TextContent(type="text", text=f"Path {path_str} does not exist")]
            
            info = get_file_info(path)
            return [TextContent(type="text", text=json.dumps(info, indent=2))]
        
        elif name == "search_files":
            pattern = arguments["pattern"]
            path_str = arguments.get("path", ".")
            recursive = arguments.get("recursive", True)
            
            path = get_safe_path(path_str)
            
            if not path.exists() or not path.is_dir():
                return [TextContent(type="text", text=f"Directory {path_str} does not exist")]
            
            import fnmatch
            matches = []
            
            def search_dir(dir_path: Path):
                try:
                    for item in dir_path.iterdir():
                        if fnmatch.fnmatch(item.name, pattern):
                            info = get_file_info(item)
                            matches.append(info)
                        
                        if recursive and item.is_dir():
                            search_dir(item)
                except PermissionError:
                    pass
            
            search_dir(path)
            
            result = {
                "pattern": pattern,
                "search_path": str(path.relative_to(SAFE_ROOT)),
                "matches_found": len(matches),
                "matches": matches
            }
            
            return [TextContent(type="text", text=json.dumps(result, indent=2))]
        
        else:
            return [TextContent(type="text", text=f"Unknown tool: {name}")]
    
    except Exception as e:
        return [TextContent(type="text", text=f"Error: {str(e)}")]

async def main():
    """Main server function"""
    from mcp.types import ServerCapabilities
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="filesystem",
                server_version="1.0.0",
                capabilities=ServerCapabilities(
                    tools={}
                ),
            ),
        )

if __name__ == "__main__":
    asyncio.run(main())