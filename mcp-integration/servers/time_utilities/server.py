#!/usr/bin/env python3
"""
MCP Server for Time and Date Utilities
Provides tools for time/date operations, timezone conversions, and scheduling
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional
import pytz
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)
from mcp.server.lowlevel.server import NotificationOptions

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("time-utilities-mcp-server")

class TimeUtilitiesMCPServer:
    def __init__(self):
        self.server = Server("time-utilities")
        self.setup_handlers()

    def setup_handlers(self):
        """Setup MCP server handlers"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available time utility tools"""
            return [
                Tool(
                    name="get_current_time",
                    description="Get current time in specified timezone",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "timezone": {
                                "type": "string",
                                "description": "Timezone (e.g., 'UTC', 'Asia/Ho_Chi_Minh', 'America/New_York')",
                                "default": "UTC"
                            },
                            "format": {
                                "type": "string",
                                "description": "Time format string (Python strftime format)",
                                "default": "%Y-%m-%d %H:%M:%S %Z"
                            }
                        }
                    }
                ),
                Tool(
                    name="convert_timezone",
                    description="Convert time from one timezone to another",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "datetime_str": {
                                "type": "string",
                                "description": "Date/time string to convert (ISO format or YYYY-MM-DD HH:MM:SS)"
                            },
                            "from_timezone": {
                                "type": "string",
                                "description": "Source timezone",
                                "default": "UTC"
                            },
                            "to_timezone": {
                                "type": "string",
                                "description": "Target timezone",
                                "default": "Asia/Ho_Chi_Minh"
                            }
                        },
                        "required": ["datetime_str"]
                    }
                ),
                Tool(
                    name="calculate_time_difference",
                    description="Calculate difference between two times",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "start_time": {
                                "type": "string",
                                "description": "Start time (ISO format or YYYY-MM-DD HH:MM:SS)"
                            },
                            "end_time": {
                                "type": "string",
                                "description": "End time (ISO format or YYYY-MM-DD HH:MM:SS)"
                            },
                            "unit": {
                                "type": "string",
                                "enum": ["seconds", "minutes", "hours", "days", "weeks"],
                                "description": "Unit for the result",
                                "default": "hours"
                            }
                        },
                        "required": ["start_time", "end_time"]
                    }
                ),
                Tool(
                    name="add_time_duration",
                    description="Add duration to a given time",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "base_time": {
                                "type": "string",
                                "description": "Base time (ISO format or YYYY-MM-DD HH:MM:SS)"
                            },
                            "duration": {
                                "type": "object",
                                "properties": {
                                    "days": {"type": "integer", "default": 0},
                                    "hours": {"type": "integer", "default": 0},
                                    "minutes": {"type": "integer", "default": 0},
                                    "seconds": {"type": "integer", "default": 0}
                                },
                                "description": "Duration to add"
                            },
                            "timezone": {
                                "type": "string",
                                "description": "Timezone for the result",
                                "default": "UTC"
                            }
                        },
                        "required": ["base_time", "duration"]
                    }
                ),
                Tool(
                    name="format_timestamp",
                    description="Format timestamp in various formats",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "timestamp": {
                                "type": "string",
                                "description": "Timestamp to format (ISO format, Unix timestamp, or YYYY-MM-DD HH:MM:SS)"
                            },
                            "output_formats": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "List of output formats (strftime format strings)",
                                "default": ["%Y-%m-%d", "%H:%M:%S", "%Y-%m-%d %H:%M:%S", "%A, %B %d, %Y"]
                            },
                            "timezone": {
                                "type": "string",
                                "description": "Timezone for formatting",
                                "default": "UTC"
                            }
                        },
                        "required": ["timestamp"]
                    }
                ),
                Tool(
                    name="get_timezone_info",
                    description="Get information about timezones",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "timezone": {
                                "type": "string",
                                "description": "Timezone to get info for (optional - if not provided, lists common timezones)"
                            }
                        }
                    }
                ),
                Tool(
                    name="create_schedule_reminder",
                    description="Create a schedule reminder with time calculations",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "event_name": {
                                "type": "string",
                                "description": "Name of the event"
                            },
                            "event_time": {
                                "type": "string",
                                "description": "Event time (ISO format or YYYY-MM-DD HH:MM:SS)"
                            },
                            "timezone": {
                                "type": "string",
                                "description": "Event timezone",
                                "default": "UTC"
                            },
                            "reminder_intervals": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Reminder intervals (e.g., '1 hour', '30 minutes', '1 day')",
                                "default": ["1 hour", "30 minutes", "10 minutes"]
                            }
                        },
                        "required": ["event_name", "event_time"]
                    }
                ),
                Tool(
                    name="parse_natural_time",
                    description="Parse natural language time expressions",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "natural_time": {
                                "type": "string",
                                "description": "Natural language time expression (e.g., 'tomorrow at 3pm', 'next Monday', 'in 2 hours')"
                            },
                            "base_time": {
                                "type": "string",
                                "description": "Base time for relative expressions (default: current time)"
                            },
                            "timezone": {
                                "type": "string",
                                "description": "Timezone for parsing",
                                "default": "UTC"
                            }
                        },
                        "required": ["natural_time"]
                    }
                )
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls"""
            try:
                if name == "get_current_time":
                    return await self._get_current_time(arguments)
                elif name == "convert_timezone":
                    return await self._convert_timezone(arguments)
                elif name == "calculate_time_difference":
                    return await self._calculate_time_difference(arguments)
                elif name == "add_time_duration":
                    return await self._add_time_duration(arguments)
                elif name == "format_timestamp":
                    return await self._format_timestamp(arguments)
                elif name == "get_timezone_info":
                    return await self._get_timezone_info(arguments)
                elif name == "create_schedule_reminder":
                    return await self._create_schedule_reminder(arguments)
                elif name == "parse_natural_time":
                    return await self._parse_natural_time(arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
            except Exception as e:
                logger.error(f"Error in tool {name}: {str(e)}")
                return [TextContent(type="text", text=f"Error: {str(e)}")]

    async def _get_current_time(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get current time in specified timezone"""
        try:
            timezone_str = arguments.get("timezone", "UTC")
            format_str = arguments.get("format", "%Y-%m-%d %H:%M:%S %Z")
            
            # Get timezone
            if timezone_str == "UTC":
                tz = pytz.UTC
            else:
                tz = pytz.timezone(timezone_str)
            
            # Get current time
            current_time = datetime.now(tz)
            formatted_time = current_time.strftime(format_str)
            
            result = {
                "current_time": formatted_time,
                "timezone": timezone_str,
                "iso_format": current_time.isoformat(),
                "unix_timestamp": int(current_time.timestamp()),
                "utc_offset": str(current_time.utcoffset())
            }
            
            return [TextContent(
                type="text",
                text=f"Current time information:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error getting current time: {str(e)}")]

    async def _convert_timezone(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Convert time from one timezone to another"""
        try:
            datetime_str = arguments["datetime_str"]
            from_tz_str = arguments.get("from_timezone", "UTC")
            to_tz_str = arguments.get("to_timezone", "Asia/Ho_Chi_Minh")
            
            # Parse datetime
            dt = self._parse_datetime(datetime_str)
            
            # Get timezones
            from_tz = pytz.timezone(from_tz_str) if from_tz_str != "UTC" else pytz.UTC
            to_tz = pytz.timezone(to_tz_str) if to_tz_str != "UTC" else pytz.UTC
            
            # Localize and convert
            if dt.tzinfo is None:
                dt = from_tz.localize(dt)
            
            converted_dt = dt.astimezone(to_tz)
            
            result = {
                "original_time": datetime_str,
                "original_timezone": from_tz_str,
                "converted_time": converted_dt.strftime("%Y-%m-%d %H:%M:%S %Z"),
                "converted_timezone": to_tz_str,
                "iso_format": converted_dt.isoformat(),
                "unix_timestamp": int(converted_dt.timestamp())
            }
            
            return [TextContent(
                type="text",
                text=f"Timezone conversion result:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error converting timezone: {str(e)}")]

    async def _calculate_time_difference(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Calculate difference between two times"""
        try:
            start_time_str = arguments["start_time"]
            end_time_str = arguments["end_time"]
            unit = arguments.get("unit", "hours")
            
            # Parse times
            start_time = self._parse_datetime(start_time_str)
            end_time = self._parse_datetime(end_time_str)
            
            # Calculate difference
            diff = end_time - start_time
            
            # Convert to requested unit
            if unit == "seconds":
                value = diff.total_seconds()
            elif unit == "minutes":
                value = diff.total_seconds() / 60
            elif unit == "hours":
                value = diff.total_seconds() / 3600
            elif unit == "days":
                value = diff.days + (diff.seconds / 86400)
            elif unit == "weeks":
                value = diff.days / 7
            else:
                value = diff.total_seconds()
                unit = "seconds"
            
            result = {
                "start_time": start_time_str,
                "end_time": end_time_str,
                "difference": {
                    "value": round(value, 2),
                    "unit": unit
                },
                "raw_difference": {
                    "days": diff.days,
                    "seconds": diff.seconds,
                    "total_seconds": diff.total_seconds()
                },
                "human_readable": str(diff)
            }
            
            return [TextContent(
                type="text",
                text=f"Time difference calculation:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error calculating time difference: {str(e)}")]

    async def _add_time_duration(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Add duration to a given time"""
        try:
            base_time_str = arguments["base_time"]
            duration = arguments["duration"]
            timezone_str = arguments.get("timezone", "UTC")
            
            # Parse base time
            base_time = self._parse_datetime(base_time_str)
            
            # Create timedelta
            delta = timedelta(
                days=duration.get("days", 0),
                hours=duration.get("hours", 0),
                minutes=duration.get("minutes", 0),
                seconds=duration.get("seconds", 0)
            )
            
            # Add duration
            result_time = base_time + delta
            
            # Apply timezone if needed
            if timezone_str != "UTC":
                tz = pytz.timezone(timezone_str)
                if result_time.tzinfo is None:
                    result_time = tz.localize(result_time)
                else:
                    result_time = result_time.astimezone(tz)
            
            result = {
                "base_time": base_time_str,
                "duration_added": duration,
                "result_time": result_time.strftime("%Y-%m-%d %H:%M:%S %Z") if result_time.tzinfo else result_time.strftime("%Y-%m-%d %H:%M:%S"),
                "iso_format": result_time.isoformat(),
                "unix_timestamp": int(result_time.timestamp()) if result_time.tzinfo else None,
                "timezone": timezone_str
            }
            
            return [TextContent(
                type="text",
                text=f"Time duration addition result:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error adding time duration: {str(e)}")]

    async def _format_timestamp(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Format timestamp in various formats"""
        try:
            timestamp_str = arguments["timestamp"]
            output_formats = arguments.get("output_formats", ["%Y-%m-%d", "%H:%M:%S", "%Y-%m-%d %H:%M:%S", "%A, %B %d, %Y"])
            timezone_str = arguments.get("timezone", "UTC")
            
            # Parse timestamp
            dt = self._parse_datetime(timestamp_str)
            
            # Apply timezone
            if timezone_str != "UTC":
                tz = pytz.timezone(timezone_str)
                if dt.tzinfo is None:
                    dt = pytz.UTC.localize(dt).astimezone(tz)
                else:
                    dt = dt.astimezone(tz)
            
            # Format in all requested formats
            formatted_outputs = {}
            for fmt in output_formats:
                try:
                    formatted_outputs[fmt] = dt.strftime(fmt)
                except Exception as e:
                    formatted_outputs[fmt] = f"Error: {str(e)}"
            
            result = {
                "original_timestamp": timestamp_str,
                "timezone": timezone_str,
                "formatted_outputs": formatted_outputs,
                "iso_format": dt.isoformat(),
                "unix_timestamp": int(dt.timestamp()) if dt.tzinfo else None
            }
            
            return [TextContent(
                type="text",
                text=f"Timestamp formatting result:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error formatting timestamp: {str(e)}")]

    async def _get_timezone_info(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get timezone information"""
        try:
            timezone_str = arguments.get("timezone")
            
            if timezone_str:
                # Get info for specific timezone
                tz = pytz.timezone(timezone_str)
                now = datetime.now(tz)
                
                result = {
                    "timezone": timezone_str,
                    "current_time": now.strftime("%Y-%m-%d %H:%M:%S %Z"),
                    "utc_offset": str(now.utcoffset()),
                    "dst_active": bool(now.dst()),
                    "timezone_name": now.tzname()
                }
            else:
                # List common timezones
                common_timezones = [
                    "UTC",
                    "Asia/Ho_Chi_Minh",
                    "America/New_York",
                    "America/Los_Angeles",
                    "Europe/London",
                    "Europe/Paris",
                    "Asia/Tokyo",
                    "Asia/Shanghai",
                    "Australia/Sydney",
                    "America/Chicago"
                ]
                
                timezone_info = {}
                for tz_name in common_timezones:
                    tz = pytz.timezone(tz_name)
                    now = datetime.now(tz)
                    timezone_info[tz_name] = {
                        "current_time": now.strftime("%Y-%m-%d %H:%M:%S %Z"),
                        "utc_offset": str(now.utcoffset())
                    }
                
                result = {
                    "common_timezones": timezone_info,
                    "total_available": len(pytz.all_timezones)
                }
            
            return [TextContent(
                type="text",
                text=f"Timezone information:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error getting timezone info: {str(e)}")]

    async def _create_schedule_reminder(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Create schedule reminder with time calculations"""
        try:
            event_name = arguments["event_name"]
            event_time_str = arguments["event_time"]
            timezone_str = arguments.get("timezone", "UTC")
            reminder_intervals = arguments.get("reminder_intervals", ["1 hour", "30 minutes", "10 minutes"])
            
            # Parse event time
            event_time = self._parse_datetime(event_time_str)
            
            # Apply timezone
            if timezone_str != "UTC":
                tz = pytz.timezone(timezone_str)
                if event_time.tzinfo is None:
                    event_time = tz.localize(event_time)
            
            # Calculate reminder times
            reminders = []
            for interval_str in reminder_intervals:
                try:
                    # Parse interval (simple parsing for common formats)
                    interval_parts = interval_str.lower().split()
                    if len(interval_parts) >= 2:
                        value = int(interval_parts[0])
                        unit = interval_parts[1]
                        
                        if "hour" in unit:
                            delta = timedelta(hours=value)
                        elif "minute" in unit:
                            delta = timedelta(minutes=value)
                        elif "day" in unit:
                            delta = timedelta(days=value)
                        elif "week" in unit:
                            delta = timedelta(weeks=value)
                        else:
                            continue
                        
                        reminder_time = event_time - delta
                        reminders.append({
                            "interval": interval_str,
                            "reminder_time": reminder_time.strftime("%Y-%m-%d %H:%M:%S %Z") if reminder_time.tzinfo else reminder_time.strftime("%Y-%m-%d %H:%M:%S"),
                            "iso_format": reminder_time.isoformat()
                        })
                except:
                    continue
            
            # Calculate time until event
            now = datetime.now(event_time.tzinfo) if event_time.tzinfo else datetime.now()
            time_until = event_time - now
            
            result = {
                "event_name": event_name,
                "event_time": event_time.strftime("%Y-%m-%d %H:%M:%S %Z") if event_time.tzinfo else event_time.strftime("%Y-%m-%d %H:%M:%S"),
                "timezone": timezone_str,
                "time_until_event": {
                    "days": time_until.days,
                    "hours": time_until.seconds // 3600,
                    "minutes": (time_until.seconds % 3600) // 60,
                    "total_seconds": time_until.total_seconds()
                },
                "reminders": reminders,
                "event_iso": event_time.isoformat()
            }
            
            return [TextContent(
                type="text",
                text=f"Schedule reminder created:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error creating schedule reminder: {str(e)}")]

    async def _parse_natural_time(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Parse natural language time expressions"""
        try:
            natural_time = arguments["natural_time"].lower()
            base_time_str = arguments.get("base_time")
            timezone_str = arguments.get("timezone", "UTC")
            
            # Get base time
            if base_time_str:
                base_time = self._parse_datetime(base_time_str)
            else:
                base_time = datetime.now()
            
            # Apply timezone
            if timezone_str != "UTC":
                tz = pytz.timezone(timezone_str)
                if base_time.tzinfo is None:
                    base_time = tz.localize(base_time)
            
            # Simple natural language parsing (can be enhanced with more sophisticated libraries)
            result_time = None
            interpretation = "Unknown format"
            
            if "tomorrow" in natural_time:
                result_time = base_time + timedelta(days=1)
                interpretation = "Tomorrow"
                
                # Extract time if specified
                if "at" in natural_time:
                    time_part = natural_time.split("at")[1].strip()
                    if "pm" in time_part or "am" in time_part:
                        # Simple time parsing
                        time_str = time_part.replace("pm", "").replace("am", "").strip()
                        try:
                            hour = int(time_str)
                            if "pm" in time_part and hour != 12:
                                hour += 12
                            elif "am" in time_part and hour == 12:
                                hour = 0
                            result_time = result_time.replace(hour=hour, minute=0, second=0)
                            interpretation += f" at {time_part}"
                        except:
                            pass
            
            elif "next week" in natural_time:
                result_time = base_time + timedelta(weeks=1)
                interpretation = "Next week"
            
            elif "in" in natural_time and ("hour" in natural_time or "minute" in natural_time or "day" in natural_time):
                # Parse "in X hours/minutes/days"
                parts = natural_time.split()
                try:
                    in_index = parts.index("in")
                    if in_index + 2 < len(parts):
                        value = int(parts[in_index + 1])
                        unit = parts[in_index + 2]
                        
                        if "hour" in unit:
                            result_time = base_time + timedelta(hours=value)
                            interpretation = f"In {value} hour(s)"
                        elif "minute" in unit:
                            result_time = base_time + timedelta(minutes=value)
                            interpretation = f"In {value} minute(s)"
                        elif "day" in unit:
                            result_time = base_time + timedelta(days=value)
                            interpretation = f"In {value} day(s)"
                except:
                    pass
            
            if result_time is None:
                result_time = base_time
                interpretation = "Could not parse - using base time"
            
            result = {
                "natural_input": arguments["natural_time"],
                "interpretation": interpretation,
                "parsed_time": result_time.strftime("%Y-%m-%d %H:%M:%S %Z") if result_time.tzinfo else result_time.strftime("%Y-%m-%d %H:%M:%S"),
                "iso_format": result_time.isoformat(),
                "base_time": base_time.strftime("%Y-%m-%d %H:%M:%S %Z") if base_time.tzinfo else base_time.strftime("%Y-%m-%d %H:%M:%S"),
                "timezone": timezone_str
            }
            
            return [TextContent(
                type="text",
                text=f"Natural time parsing result:\n\n{json.dumps(result, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error parsing natural time: {str(e)}")]

    def _parse_datetime(self, datetime_str: str) -> datetime:
        """Parse datetime string in various formats"""
        # Try different formats
        formats = [
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%dT%H:%M:%S",
            "%Y-%m-%dT%H:%M:%SZ",
            "%Y-%m-%d",
            "%H:%M:%S",
            "%Y-%m-%dT%H:%M:%S.%f",
            "%Y-%m-%dT%H:%M:%S.%fZ"
        ]
        
        # Try parsing as Unix timestamp
        try:
            timestamp = float(datetime_str)
            return datetime.fromtimestamp(timestamp, tz=timezone.utc)
        except:
            pass
        
        # Try ISO format
        try:
            return datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
        except:
            pass
        
        # Try other formats
        for fmt in formats:
            try:
                return datetime.strptime(datetime_str, fmt)
            except:
                continue
        
        raise ValueError(f"Could not parse datetime: {datetime_str}")

    async def run(self):
        """Run the MCP server"""
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="time-utilities",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )

async def main():
    """Main entry point"""
    server = TimeUtilitiesMCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())