#!/usr/bin/env python3
"""
Simple Cached MCP Server for Gemini Search Engine
Memory-based caching for API cost reduction and performance
"""

import asyncio
import json
import logging
import os
import hashlib
import time
from datetime import datetime
from typing import Any, Dict, List, Optional
import google.generativeai as genai
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Tool,
    TextContent,
)
from mcp.server.lowlevel.server import NotificationOptions

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("simple-cached-gemini")

class SimpleCache:
    """Simple memory-based cache"""
    
    def __init__(self, ttl_seconds: int = 7200):  # 2 hours default
        self.cache = {}
        self.timestamps = {}
        self.ttl = ttl_seconds
        self.stats = {"hits": 0, "misses": 0, "sets": 0}
    
    def _generate_key(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """Generate cache key"""
        cache_data = {"tool": tool_name, "args": arguments}
        cache_string = json.dumps(cache_data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(cache_string.encode('utf-8')).hexdigest()
    
    def get(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[str]:
        """Get cached response"""
        key = self._generate_key(tool_name, arguments)
        
        if key in self.cache:
            # Check if expired
            if time.time() - self.timestamps[key] < self.ttl:
                self.stats["hits"] += 1
                logger.info(f"🎯 Cache HIT for {tool_name}")
                return self.cache[key]
            else:
                # Expired
                del self.cache[key]
                del self.timestamps[key]
        
        self.stats["misses"] += 1
        logger.info(f"❌ Cache MISS for {tool_name}")
        return None
    
    def set(self, tool_name: str, arguments: Dict[str, Any], response: str):
        """Cache response"""
        key = self._generate_key(tool_name, arguments)
        self.cache[key] = response
        self.timestamps[key] = time.time()
        self.stats["sets"] += 1
        logger.info(f"💾 Cached response for {tool_name}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_rate = (self.stats["hits"] / max(total_requests, 1)) * 100
        
        return {
            "total_keys": len(self.cache),
            "total_requests": total_requests,
            "cache_hits": self.stats["hits"],
            "cache_misses": self.stats["misses"],
            "hit_rate_percent": hit_rate,
            "ttl_hours": self.ttl / 3600
        }

class SimpleCachedGeminiServer:
    def __init__(self):
        self.server = Server("simple-cached-gemini")
        
        # Get API key
        self.gemini_api_key = os.getenv("GEMINI_API_KEY")
        if not self.gemini_api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        
        # Configure Gemini
        genai.configure(api_key=self.gemini_api_key)
        self.model = genai.GenerativeModel('gemini-1.5-pro')
        
        # Initialize simple cache
        self.cache = SimpleCache(ttl_seconds=7200)  # 2 hours
        
        self.setup_handlers()

    def setup_handlers(self):
        """Setup MCP server handlers"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available cached tools"""
            return [
                Tool(
                    name="cached_search",
                    description="High-quality search with memory caching (saves API costs)",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Search query"
                            },
                            "language": {
                                "type": "string",
                                "description": "Response language (vi/en)",
                                "default": "vi"
                            },
                            "force_refresh": {
                                "type": "boolean",
                                "description": "Force refresh cache",
                                "default": False
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="cache_stats",
                    description="Get cache performance statistics",
                    inputSchema={
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                )
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls with caching"""
            try:
                if name == "cache_stats":
                    return await self._get_cache_stats()
                elif name == "cached_search":
                    return await self._cached_search(arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
                    
            except Exception as e:
                logger.error(f"Error in tool {name}: {str(e)}")
                return [TextContent(type="text", text=f"❌ Lỗi: {str(e)}")]

    async def _cached_search(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Perform cached search"""
        force_refresh = arguments.pop("force_refresh", False)
        
        # Try cache first (unless force refresh)
        if not force_refresh:
            cached_response = self.cache.get("search", arguments)
            if cached_response:
                # Add cache indicator
                cached_response = f"🚀 **CACHED RESPONSE** (⚡ Fast retrieval)\n\n---\n\n{cached_response}"
                return [TextContent(type="text", text=cached_response)]
        
        # Cache miss or force refresh - call API
        query = arguments["query"]
        language = arguments.get("language", "vi")
        
        # Build prompt
        lang_instruction = "Respond in Vietnamese" if language == "vi" else "Respond in English"
        prompt = f"""Tìm kiếm và phân tích thông tin về: "{query}"

Cấu trúc response:
## 🔍 Tổng quan
## 📊 Thông tin chi tiết
## 📈 Xu hướng và phát triển
## 💡 Kết luận

Yêu cầu:
- Thông tin chính xác và cập nhật
- Dữ liệu cụ thể và số liệu
- Phân tích khách quan
- Định dạng markdown chuyên nghiệp

{lang_instruction}. Đảm bảo độ tin cậy cao."""

        # Call Gemini API
        start_time = time.time()
        result = await self._call_gemini_with_grounding(prompt)
        response_time = (time.time() - start_time) * 1000
        
        # Format response
        formatted_response = f"""# 🔍 Tìm kiếm: {query}

**📊 Thông tin:**
- Thời gian phản hồi: {response_time:.0f}ms
- Ngôn ngữ: {'Tiếng Việt' if language == 'vi' else 'English'}
- Timestamp: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}

---

{result}

---
**✅ Chất lượng cao** | **🎯 Grounding verified** | **💾 Cached for reuse**"""

        # Cache the result
        self.cache.set("search", arguments, formatted_response)
        
        return [TextContent(type="text", text=formatted_response)]

    async def _get_cache_stats(self) -> List[TextContent]:
        """Get cache statistics"""
        stats = self.cache.get_stats()
        
        stats_report = f"""# 📊 Cache Performance Statistics

## 🎯 Cache Performance
- **Cache Type**: Memory
- **Total Cached Keys**: {stats['total_keys']}
- **TTL**: {stats['ttl_hours']:.1f} hours

## 📈 Request Statistics
- **Total Requests**: {stats['total_requests']}
- **Cache Hits**: {stats['cache_hits']}
- **Cache Misses**: {stats['cache_misses']}
- **Hit Rate**: {stats['hit_rate_percent']:.1f}%

## 💰 Cost Savings
- **API Calls Saved**: {stats['cache_hits']}
- **Estimated Cost Saved**: ~${stats['cache_hits'] * 0.01:.2f}
- **Time Saved**: ~{stats['cache_hits'] * 15:.0f} seconds

## 💡 Benefits
- **Speed**: Cache hits are ~95% faster
- **Cost**: Each hit saves ~$0.01
- **Reliability**: Reduced API dependency

---
*Updated: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}*"""

        return [TextContent(type="text", text=stats_report)]

    async def _call_gemini_with_grounding(self, prompt: str) -> str:
        """Call Gemini API with grounding"""
        tools = [{
            "google_search_retrieval": {
                "dynamic_retrieval_config": {
                    "mode": "MODE_DYNAMIC",
                    "dynamic_threshold": 0.3
                }
            }
        }]
        
        try:
            response = self.model.generate_content(
                prompt,
                generation_config={
                    "temperature": 0.1,
                    "max_output_tokens": 8192,
                },
                tools=tools
            )
            
            content = response.text
            if not content:
                raise Exception("No content from Gemini API")
            
            return content.strip()
            
        except Exception as e:
            raise Exception(f"Gemini API error: {str(e)}")

    async def run(self):
        """Run the server"""
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="simple-cached-gemini",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )

async def main():
    """Main entry point"""
    server = SimpleCachedGeminiServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())
