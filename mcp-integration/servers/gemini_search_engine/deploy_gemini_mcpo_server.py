import os
import subprocess
import time
import json

# --- Configuration ---
CONFIG_DIR = "mcpo_config"
CONFIG_FILE = os.path.join(CONFIG_DIR, "config.json")
CONTAINER_NAME = "mcpo-gemini-container"
IMAGE_NAME = "ghcr.io/open-webui/mcpo:main"
PORT = 8000
NETWORK_NAME = "acca-network"
SERVER_DIR = "mem0-owui/mcp-integration/servers"
DOCKER_SERVER_MOUNT = "/app/servers"

# --- ---
# --- ONLY DEPLOY SERVERS FROM THIS ALLOWLIST ---
# --- ---
ALLOWED_SERVERS = [
    "brave_search/server.py",
    "document_processing/server.py",
    "filesystem/server.py",
    "github/server.py",
    "mem0_system/server.py",
    "oracle_db/server.py",
    "sqlite/server.py",
    "time_utilities/server.py",
    "vietnamese_language/server.py",
    "weather_service/server.py",
    "web_automation/server.py",
    "wikipedia/server.py"
]

# --- Main Script ---
def run_command(command, check=True):
    """Runs a command in the shell and returns its output."""
    print(f"Executing: {' '.join(command)}")
    result = subprocess.run(command, capture_output=True, text=True)
    if check and result.returncode != 0:
        print(f"Error: {result.stderr}")
        raise RuntimeError(f"Command failed: {' '.join(command)}")
    return result

def stop_and_remove_container():
    """Stops and removes the existing container if it's running."""
    print(f"--- Stopping and removing old container: {CONTAINER_NAME} ---")
    run_command(["docker", "stop", CONTAINER_NAME], check=False)
    run_command(["docker", "rm", CONTAINER_NAME], check=False)
    print("--- Old container removed ---")

def create_config_file():
    """Creates the configuration file based on the allowlist."""
    print(f"--- Creating config file from allowlist ---")
    os.makedirs(CONFIG_DIR, exist_ok=True)
    
    mcp_servers = {}
    
    # --- Environment Variables for Tools ---
    # It's better to fetch these from the environment or a secure vault in production
    env_vars = {
        "GEMINI_API_KEY": os.environ.get("GEMINI_API_KEY", "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"),
        "GEMINI_MODEL_NAME": os.environ.get("GEMINI_MODEL_NAME", "gemini-2.5-flash-lite"),
        "BRAVE_API_KEY": os.environ.get("BRAVE_API_KEY", "your_brave_api_key"),
        "WEATHER_API_KEY": os.environ.get("WEATHER_API_KEY", "your_weather_api_key"),
        "MEM0_API_KEY": os.environ.get("MEM0_API_KEY", "your_mem0_api_key"),
        "ORACLE_CONNECTION_STRING": os.environ.get("ORACLE_CONNECTION_STRING", "username/password@host:1521/service_name"),
        "TARGET_SCHEMA": os.environ.get("TARGET_SCHEMA", ""),
        "CACHE_DIR": os.environ.get("CACHE_DIR", ".cache"),
        "THICK_MODE": os.environ.get("THICK_MODE", ""),
        "ORACLE_CLIENT_LIB_DIR": os.environ.get("ORACLE_CLIENT_LIB_DIR", ""),
        "REDIS_URL": os.environ.get("REDIS_URL", "redis://redis-persistent-cache:6379"),
        "REDIS_DB": os.environ.get("REDIS_DB", "0"),
        "CACHE_TTL": os.environ.get("CACHE_TTL", "7200")
    }

    for server_path in ALLOWED_SERVERS:
        tool_name = os.path.dirname(server_path)
        
        docker_path = os.path.join(DOCKER_SERVER_MOUNT, server_path)

        mcp_servers[tool_name] = {
            "command": "python3.12",
            "args": [docker_path],
            "env": env_vars
        }

    config_content = {"mcpServers": mcp_servers}
    
    with open(CONFIG_FILE, "w") as f:
        json.dump(config_content, f, indent=2)
        
    print(f"--- Config file created with {len(mcp_servers)} approved servers ---")
    return list(mcp_servers.keys())

def create_combined_requirements():
    """Combines all requirements.txt from allowlisted servers into one file."""
    print("--- Creating combined requirements file ---")
    
    combined_reqs = set()
    
    for server_path in ALLOWED_SERVERS:
        tool_name = os.path.dirname(server_path)
        req_file = os.path.join(SERVER_DIR, f"{tool_name}_requirements.txt")
        
        if os.path.exists(req_file):
            print(f"Found requirements for {tool_name}")
            with open(req_file, "r") as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith("#"):
                        combined_reqs.add(line)
    
    output_path = os.path.join(SERVER_DIR, "combined_requirements.txt")
    with open(output_path, "w") as f:
        for req in sorted(list(combined_reqs)):
            f.write(f"{req}\n")
            
    print(f"--- Combined requirements created with {len(combined_reqs)} packages ---")

def pull_latest_image():
    """Pulls the latest mcpo image from ghcr.io."""
    print(f"--- Pulling latest image: {IMAGE_NAME} ---")
    run_command(["docker", "pull", IMAGE_NAME])
    print("--- Image pulled successfully ---")
    
def start_mcpo_container():
    """Starts the mcpo container with the generated config."""
    print("--- Starting new mcpo container ---")
    
    config_abs_path = os.path.abspath(CONFIG_FILE)
    servers_abs_path = os.path.abspath(SERVER_DIR)
    
    # Add Oracle wallet mount
    oracle_wallet_path = os.path.abspath("oracle_wallet")

    volumes = [
        "-v", f"{config_abs_path}:/app/config.json",
        "-v", f"{servers_abs_path}:{DOCKER_SERVER_MOUNT}",
        "-v", f"{oracle_wallet_path}:/app/oracle_wallet"  # Mount Oracle wallet
    ]

    docker_command = [
        "docker", "run", "-d",
        "--name", CONTAINER_NAME,
        "-p", f"{PORT}:{PORT}",
        "--network", NETWORK_NAME,
        *volumes,
        IMAGE_NAME,
        "--config", "/app/config.json",
        "--port", str(PORT),
        "--host", "0.0.0.0",
    ]
    
    run_command(docker_command)
    print("--- MCPO container started ---")
    
    # Wait a bit for container to be ready
    print("--- Waiting for container to be ready ---")
    time.sleep(5)
    
    # Install core MCP dependencies first
    print("--- Installing core MCP dependencies ---")
    core_deps = [
        "docker", "exec", CONTAINER_NAME,
        "pip", "install", "mcp", "aiohttp", "httpx", "pytz", "playwright", "beautifulsoup4", "requests"
    ]
    run_command(core_deps)
    
    # Install dependencies from combined requirements
    print("--- Installing server dependencies ---")
    requirements_path = f"{DOCKER_SERVER_MOUNT}/combined_requirements.txt"
    install_command = [
        "docker", "exec", CONTAINER_NAME,
        "pip", "install", "-r", requirements_path
    ]
    run_command(install_command, check=False)
    
    # Install playwright browsers for jina_crawler
    print("--- Installing playwright browsers ---")
    playwright_command = [
        "docker", "exec", CONTAINER_NAME,
        "playwright", "install"
    ]
    run_command(playwright_command, check=False)
    print("--- Dependencies installed ---")
    
    # Restart container to reload with new dependencies
    print("--- Restarting container to apply dependencies ---")
    run_command(["docker", "restart", CONTAINER_NAME])
    print("--- Container restarted ---")

def wait_for_server(tool_names):
    """Waits for all tool servers to become healthy."""
    print("--- Waiting for all servers to be ready ---")
    time.sleep(10)
    
    ready_tools = []
    for i in range(45):
        if len(ready_tools) == len(tool_names):
            break

        print(f"Attempt {i+1}/45: Checking server status... ({len(ready_tools)}/{len(tool_names)} ready)")
        for tool in tool_names:
            if tool in ready_tools:
                continue
                
            url = f"http://localhost:{PORT}/{tool}/docs"
            try:
                result = subprocess.run(["curl", "-s", "-f", "-L", url], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    print(f"   - {tool} is ready.")
                    ready_tools.append(tool)
            except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
                pass
        
        if len(ready_tools) < len(tool_names):
            time.sleep(3)

    if len(ready_tools) == len(tool_names):
         print("--- All servers are ready! ---")
    else:
        print("\nError: Not all servers became ready in time.")
        print(f"Ready tools: {ready_tools}")
        missing_tools = sorted(list(set(tool_names) - set(ready_tools)))
        print(f"Missing tools: {missing_tools}")
        run_command(["docker", "logs", CONTAINER_NAME])
        raise SystemExit("Deployment failed.")

if __name__ == "__main__":
    stop_and_remove_container()
    tool_names = create_config_file()
    if not tool_names:
        print("No servers found in allowlist. Exiting.")
        exit()
    create_combined_requirements()
    pull_latest_image()
    start_mcpo_container()
    wait_for_server(tool_names)
    print("\n🚀🚀🚀 Official MCPO server with approved tools deployed successfully! 🚀🚀🚀")
    for tool in sorted(tool_names):
        print(f"Access the {tool} tool docs at: http://localhost:{PORT}/{tool}/docs")
