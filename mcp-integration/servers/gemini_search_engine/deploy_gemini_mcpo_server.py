import os
import subprocess
import time
import json

# --- Configuration ---
CONFIG_DIR = "mcpo_gemini_config"
CONFIG_FILE = os.path.join(CONFIG_DIR, "config.json")
CONTAINER_NAME = "mcpo-gemini-search-engine"
IMAGE_NAME = "ghcr.io/open-webui/mcpo:main"
PORT = 8001
NETWORK_NAME = "acca-network"
SERVER_DIR = "mcp-integration/servers"
DOCKER_SERVER_MOUNT = "/app/servers"

# --- ---
# --- ONLY DEPLOY SERVERS FROM THIS ALLOWLIST ---
# --- ---
ALLOWED_SERVERS = [
    "gemini_search_engine/server.py",
   ]

# --- Main Script ---
def run_command(command, check=True):
    """Runs a command in the shell and returns its output."""
    print(f"Executing: {' '.join(command)}")
    result = subprocess.run(command, capture_output=True, text=True)
    if check and result.returncode != 0:
        print(f"Error: {result.stderr}")
        raise RuntimeError(f"Command failed: {' '.join(command)}")
    return result

def stop_and_remove_container():
    """Stops and removes the existing container if it's running."""
    print(f"--- Stopping and removing old container: {CONTAINER_NAME} ---")
    run_command(["docker", "stop", CONTAINER_NAME], check=False)
    run_command(["docker", "rm", CONTAINER_NAME], check=False)
    print("--- Old container removed ---")

def create_config_file():
    """Creates the configuration file based on the allowlist."""
    print(f"--- Creating config file from allowlist ---")
    os.makedirs(CONFIG_DIR, exist_ok=True)
    
    mcp_servers = {}
    
    # --- Environment Variables for Gemini Search Engine ---
    env_vars = {
        "GEMINI_API_KEY": os.environ.get("GEMINI_API_KEY", "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"),
        "GEMINI_MODEL_NAME": os.environ.get("GEMINI_MODEL_NAME", "gemini-2.5-flash-lite"),
    }

    for server_path in ALLOWED_SERVERS:
        tool_name = os.path.dirname(server_path)
        
        docker_path = os.path.join(DOCKER_SERVER_MOUNT, server_path)

        mcp_servers[tool_name] = {
            "command": "python3.12",
            "args": [docker_path],
            "env": env_vars
        }

    config_content = {"mcpServers": mcp_servers}
    
    with open(CONFIG_FILE, "w") as f:
        json.dump(config_content, f, indent=2)
        
    print(f"--- Config file created with {len(mcp_servers)} approved servers ---")
    return list(mcp_servers.keys())

def create_combined_requirements():
    """Creates requirements file for Gemini search engine."""
    print("--- Creating requirements file for Gemini search engine ---")

    # Gemini search engine specific requirements
    gemini_reqs = [
        "google-generativeai>=0.3.0",
        "requests>=2.31.0",
        "beautifulsoup4>=4.12.0",
        "aiohttp>=3.8.0",
        "httpx>=0.24.0",
        "mcp>=0.1.0"
    ]

    output_path = os.path.join(SERVER_DIR, "gemini_requirements.txt")
    with open(output_path, "w") as f:
        for req in gemini_reqs:
            f.write(f"{req}\n")

    print(f"--- Gemini requirements created with {len(gemini_reqs)} packages ---")

def pull_latest_image():
    """Pulls the latest mcpo image from ghcr.io."""
    print(f"--- Pulling latest image: {IMAGE_NAME} ---")
    run_command(["docker", "pull", IMAGE_NAME])
    print("--- Image pulled successfully ---")
    
def start_mcpo_container():
    """Starts the mcpo container for Gemini search engine."""
    print("--- Starting Gemini search engine MCPO container ---")

    config_abs_path = os.path.abspath(CONFIG_FILE)
    servers_abs_path = os.path.abspath(SERVER_DIR)

    volumes = [
        "-v", f"{config_abs_path}:/app/config.json",
        "-v", f"{servers_abs_path}:{DOCKER_SERVER_MOUNT}",
    ]

    docker_command = [
        "docker", "run", "-d",
        "--name", CONTAINER_NAME,
        "-p", f"{PORT}:{PORT}",
        "--network", NETWORK_NAME,
        *volumes,
        IMAGE_NAME,
        "--config", "/app/config.json",
        "--port", str(PORT),
        "--host", "0.0.0.0",
    ]

    run_command(docker_command)
    print("--- Gemini MCPO container started ---")
    
    # Wait a bit for container to be ready
    print("--- Waiting for container to be ready ---")
    time.sleep(5)
    
    # Install Gemini search engine dependencies
    print("--- Installing Gemini search engine dependencies ---")
    requirements_path = f"{DOCKER_SERVER_MOUNT}/gemini_requirements.txt"
    install_command = [
        "docker", "exec", CONTAINER_NAME,
        "pip", "install", "-r", requirements_path
    ]
    run_command(install_command, check=False)
    print("--- Gemini dependencies installed ---")
    
    # Restart container to reload with new dependencies
    print("--- Restarting container to apply dependencies ---")
    run_command(["docker", "restart", CONTAINER_NAME])
    print("--- Container restarted ---")

def wait_for_server(tool_names):
    """Waits for all tool servers to become healthy."""
    print("--- Waiting for all servers to be ready ---")
    time.sleep(10)
    
    ready_tools = []
    for i in range(45):
        if len(ready_tools) == len(tool_names):
            break

        print(f"Attempt {i+1}/45: Checking server status... ({len(ready_tools)}/{len(tool_names)} ready)")
        for tool in tool_names:
            if tool in ready_tools:
                continue
                
            url = f"http://localhost:{PORT}/{tool}/docs"
            try:
                result = subprocess.run(["curl", "-s", "-f", "-L", url], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    print(f"   - {tool} is ready.")
                    ready_tools.append(tool)
            except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
                pass
        
        if len(ready_tools) < len(tool_names):
            time.sleep(3)

    if len(ready_tools) == len(tool_names):
         print("--- All servers are ready! ---")
    else:
        print("\nError: Not all servers became ready in time.")
        print(f"Ready tools: {ready_tools}")
        missing_tools = sorted(list(set(tool_names) - set(ready_tools)))
        print(f"Missing tools: {missing_tools}")
        run_command(["docker", "logs", CONTAINER_NAME])
        raise SystemExit("Deployment failed.")

if __name__ == "__main__":
    stop_and_remove_container()
    tool_names = create_config_file()
    if not tool_names:
        print("No servers found in allowlist. Exiting.")
        exit()
    create_combined_requirements()
    pull_latest_image()
    start_mcpo_container()
    wait_for_server(tool_names)
    print("\n🚀🚀🚀 Gemini Search Engine MCPO server deployed successfully! 🚀🚀🚀")
    for tool in sorted(tool_names):
        print(f"Access the {tool} tool docs at: http://localhost:{PORT}/{tool}/docs")
