#!/usr/bin/env python3
"""
Test script for Improved Gemini Search Engine with High-Quality Responses
"""

import asyncio
import json
import os
import sys
import time
from typing import Dict, Any

# Add the server directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from server_with_grounding_improved import ImprovedGeminiSearchEngineGroundingMCPServer

class ImprovedMCPServerTester:
    def __init__(self):
        self.server = None
        self.test_results = []
        
    async def setup(self):
        """Setup the improved MCP server for testing"""
        try:
            # Set API key if not already set
            if not os.getenv("GEMINI_API_KEY"):
                os.environ["GEMINI_API_KEY"] = "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"
            
            self.server = ImprovedGeminiSearchEngineGroundingMCPServer()
            print("✅ Improved MCP Server initialized successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to initialize server: {e}")
            return False
    
    async def test_tool(self, tool_name: str, arguments: Dict[str, Any], description: str):
        """Test a specific tool and evaluate response quality"""
        print(f"\n🧪 Testing {tool_name}: {description}")
        print(f"Arguments: {json.dumps(arguments, indent=2, ensure_ascii=False)}")
        
        start_time = time.time()
        try:
            # Call the tool handler directly
            handler = getattr(self.server, f"_{tool_name}")
            result = await handler(arguments)
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            
            print(f"✅ Success! Response time: {response_time:.2f}ms")
            
            # Evaluate response quality
            if result and len(result) > 0:
                content = result[0].text
                quality_score = self._evaluate_response_quality(content, tool_name)
                
                print(f"📊 Quality Score: {quality_score}/100")
                print(f"📝 Response preview (first 300 chars):")
                print(f"   {content[:300]}...")
                
                # Show full response for demonstration
                if len(sys.argv) > 1 and sys.argv[1] == "--full":
                    print(f"\n📄 Full Response:\n{content}")
            
            self.test_results.append({
                "tool": tool_name,
                "description": description,
                "success": True,
                "response_time_ms": response_time,
                "quality_score": quality_score if 'quality_score' in locals() else 0,
                "arguments": arguments
            })
            
        except Exception as e:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            print(f"❌ Failed! Error: {e}")
            print(f"Response time: {response_time:.2f}ms")
            
            self.test_results.append({
                "tool": tool_name,
                "description": description,
                "success": False,
                "error": str(e),
                "response_time_ms": response_time,
                "quality_score": 0,
                "arguments": arguments
            })
    
    def _evaluate_response_quality(self, content: str, tool_name: str) -> int:
        """Evaluate response quality based on multiple criteria"""
        score = 0
        
        # Basic structure (20 points)
        if "# " in content:  # Has headers
            score += 10
        if "**" in content or "*" in content:  # Has formatting
            score += 5
        if len(content) > 500:  # Substantial content
            score += 5
        
        # Content quality (30 points)
        if "✅" in content or "❌" in content:  # Has quality indicators
            score += 10
        if any(word in content.lower() for word in ["phân tích", "đánh giá", "nghiên cứu", "analysis"]):
            score += 10
        if any(word in content.lower() for word in ["số liệu", "thống kê", "dữ liệu", "data", "statistics"]):
            score += 10
        
        # Citations and sources (20 points)
        if "nguồn" in content.lower() or "source" in content.lower():
            score += 10
        if "tham khảo" in content.lower() or "reference" in content.lower():
            score += 10
        
        # Professional formatting (20 points)
        if "---" in content:  # Has separators
            score += 5
        if content.count("##") >= 2:  # Multiple sections
            score += 10
        if "Thời gian:" in content or "Time:" in content:  # Has metadata
            score += 5
        
        # Tool-specific criteria (10 points)
        if tool_name == "fact_verification":
            if any(word in content.lower() for word in ["đúng", "sai", "true", "false", "accuracy"]):
                score += 10
        elif tool_name == "expert_research":
            if any(word in content.lower() for word in ["executive summary", "tóm tắt", "khuyến nghị", "recommendation"]):
                score += 10
        elif tool_name == "high_quality_search":
            if any(word in content.lower() for word in ["xu hướng", "trend", "tương lai", "future"]):
                score += 10
        
        return min(score, 100)  # Cap at 100
    
    async def run_quality_tests(self):
        """Run comprehensive quality tests"""
        print("🚀 Starting High-Quality MCP Gemini Search Engine Tests")
        print("=" * 70)
        
        if not await self.setup():
            return
        
        # High-quality test cases
        test_cases = [
            {
                "tool": "high_quality_search",
                "args": {
                    "query": "Trí tuệ nhân tạo trong giáo dục Việt Nam 2024",
                    "analysis_depth": "comprehensive",
                    "focus_areas": ["chính sách", "ứng dụng thực tế", "thách thức"],
                    "language": "vi"
                },
                "desc": "Comprehensive AI in education analysis"
            },
            {
                "tool": "expert_research",
                "args": {
                    "topic": "Blockchain và fintech tại Việt Nam",
                    "research_questions": [
                        "Tình hình phát triển hiện tại?",
                        "Các rào cản pháp lý?",
                        "Triển vọng 5 năm tới?"
                    ],
                    "perspective": "trend_analysis",
                    "language": "vi"
                },
                "desc": "Expert blockchain fintech research"
            },
            {
                "tool": "fact_verification",
                "args": {
                    "statement": "Việt Nam là nước xuất khẩu cà phê lớn thứ 2 thế giới",
                    "verification_level": "thorough",
                    "language": "vi"
                },
                "desc": "Thorough fact verification"
            },
            {
                "tool": "high_quality_search",
                "args": {
                    "query": "Climate change impact on Vietnam agriculture",
                    "analysis_depth": "expert",
                    "focus_areas": ["rice production", "adaptation strategies"],
                    "language": "en"
                },
                "desc": "Expert-level climate analysis in English"
            },
            {
                "tool": "fact_verification",
                "args": {
                    "statement": "ChatGPT was released in November 2022",
                    "verification_level": "forensic",
                    "language": "en"
                },
                "desc": "Forensic-level fact checking"
            }
        ]
        
        # Run tests with delays for rate limiting
        for i, test_case in enumerate(test_cases):
            await self.test_tool(
                test_case["tool"],
                test_case["args"], 
                test_case["desc"]
            )
            
            # Rate limiting - wait between tests
            if i < len(test_cases) - 1:
                print(f"\n⏳ Waiting 3 seconds for rate limiting...")
                await asyncio.sleep(3)
        
        # Print comprehensive summary
        self.print_quality_summary()
    
    def print_quality_summary(self):
        """Print comprehensive test summary with quality metrics"""
        print("\n" + "=" * 70)
        print("📋 HIGH-QUALITY TEST SUMMARY")
        print("=" * 70)
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for r in self.test_results if r["success"])
        failed_tests = total_tests - successful_tests
        
        print(f"📊 Test Statistics:")
        print(f"   Total tests: {total_tests}")
        print(f"   ✅ Successful: {successful_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   📈 Success rate: {(successful_tests/total_tests*100):.1f}%")
        
        if successful_tests > 0:
            avg_response_time = sum(r["response_time_ms"] for r in self.test_results if r["success"]) / successful_tests
            avg_quality_score = sum(r["quality_score"] for r in self.test_results if r["success"]) / successful_tests
            
            print(f"\n⚡ Performance Metrics:")
            print(f"   Average response time: {avg_response_time:.2f}ms")
            print(f"   Average quality score: {avg_quality_score:.1f}/100")
            
            # Quality rating
            if avg_quality_score >= 80:
                quality_rating = "🏆 Excellent"
            elif avg_quality_score >= 60:
                quality_rating = "✅ Good"
            elif avg_quality_score >= 40:
                quality_rating = "⚠️ Fair"
            else:
                quality_rating = "❌ Poor"
            
            print(f"   Overall quality: {quality_rating}")
        
        print(f"\n📊 Detailed Results:")
        for result in self.test_results:
            status = "✅" if result["success"] else "❌"
            quality = f" (Quality: {result['quality_score']}/100)" if result["success"] else ""
            print(f"{status} {result['tool']}: {result['description']}{quality}")
            if not result["success"]:
                print(f"   ❌ Error: {result['error']}")
            print(f"   ⏱️ Response time: {result['response_time_ms']:.2f}ms")
        
        print(f"\n💡 Quality Improvement Recommendations:")
        if avg_quality_score < 80:
            print("   • Enhance content structure and formatting")
            print("   • Add more data and statistics")
            print("   • Improve citation and source attribution")
            print("   • Strengthen professional presentation")
        else:
            print("   • Quality is excellent! Consider adding more specialized features")

async def main():
    """Main test function"""
    tester = ImprovedMCPServerTester()
    await tester.run_quality_tests()

if __name__ == "__main__":
    asyncio.run(main())
