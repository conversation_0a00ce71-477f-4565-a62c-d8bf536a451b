#!/usr/bin/env python3
"""
MCP Server with Multiple Persistent Cache Options
Supports: SQLite, Redis, File-based, and Hybrid caching
"""

import asyncio
import json
import logging
import os
import hashlib
import time
import sqlite3
import pickle
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
from pathlib import Path
import google.generativeai as genai
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import Tool, TextContent
from mcp.server.lowlevel.server import NotificationOptions

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("persistent-cached-gemini")

class SQLiteCache:
    """SQLite-based persistent cache with TTL"""
    
    def __init__(self, db_path: str = "cache.db", ttl_seconds: int = 7200):
        self.db_path = db_path
        self.ttl = ttl_seconds
        self.stats = {"hits": 0, "misses": 0, "sets": 0}
        self._init_db()
    
    def _init_db(self):
        """Initialize SQLite database"""
        conn = sqlite3.connect(self.db_path)
        conn.execute("""
            CREATE TABLE IF NOT EXISTS cache (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                created_at REAL NOT NULL,
                expires_at REAL NOT NULL
            )
        """)
        conn.execute("CREATE INDEX IF NOT EXISTS idx_expires_at ON cache(expires_at)")
        conn.commit()
        conn.close()
        logger.info(f"✅ SQLite cache initialized: {self.db_path}")
    
    def _generate_key(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """Generate cache key"""
        cache_data = {"tool": tool_name, "args": arguments}
        cache_string = json.dumps(cache_data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(cache_string.encode('utf-8')).hexdigest()
    
    def get(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[str]:
        """Get cached response"""
        key = self._generate_key(tool_name, arguments)
        current_time = time.time()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get non-expired entry
        cursor.execute(
            "SELECT value FROM cache WHERE key = ? AND expires_at > ?",
            (key, current_time)
        )
        result = cursor.fetchone()
        
        if result:
            self.stats["hits"] += 1
            logger.info(f"🎯 SQLite Cache HIT for {tool_name}")
            conn.close()
            return result[0]
        
        # Clean up expired entries
        cursor.execute("DELETE FROM cache WHERE expires_at <= ?", (current_time,))
        conn.commit()
        conn.close()
        
        self.stats["misses"] += 1
        logger.info(f"❌ SQLite Cache MISS for {tool_name}")
        return None
    
    def set(self, tool_name: str, arguments: Dict[str, Any], response: str):
        """Cache response"""
        key = self._generate_key(tool_name, arguments)
        current_time = time.time()
        expires_at = current_time + self.ttl
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Insert or replace
        cursor.execute(
            "INSERT OR REPLACE INTO cache (key, value, created_at, expires_at) VALUES (?, ?, ?, ?)",
            (key, response, current_time, expires_at)
        )
        conn.commit()
        conn.close()
        
        self.stats["sets"] += 1
        logger.info(f"💾 SQLite cached response for {tool_name}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Count total and expired entries
        current_time = time.time()
        cursor.execute("SELECT COUNT(*) FROM cache")
        total_keys = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM cache WHERE expires_at > ?", (current_time,))
        active_keys = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM cache WHERE expires_at <= ?", (current_time,))
        expired_keys = cursor.fetchone()[0]
        
        conn.close()
        
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_rate = (self.stats["hits"] / max(total_requests, 1)) * 100
        
        return {
            "type": "SQLite",
            "db_path": self.db_path,
            "total_keys": total_keys,
            "active_keys": active_keys,
            "expired_keys": expired_keys,
            "total_requests": total_requests,
            "cache_hits": self.stats["hits"],
            "cache_misses": self.stats["misses"],
            "hit_rate_percent": hit_rate,
            "ttl_hours": self.ttl / 3600
        }
    
    def cleanup_expired(self):
        """Clean up expired entries"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM cache WHERE expires_at <= ?", (time.time(),))
        deleted = cursor.rowcount
        conn.commit()
        conn.close()
        logger.info(f"🧹 Cleaned up {deleted} expired cache entries")
        return deleted

class FileCache:
    """File-based persistent cache with TTL"""
    
    def __init__(self, cache_dir: str = "cache_files", ttl_seconds: int = 7200):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.ttl = ttl_seconds
        self.stats = {"hits": 0, "misses": 0, "sets": 0}
        logger.info(f"✅ File cache initialized: {self.cache_dir}")
    
    def _generate_key(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """Generate cache key"""
        cache_data = {"tool": tool_name, "args": arguments}
        cache_string = json.dumps(cache_data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(cache_string.encode('utf-8')).hexdigest()
    
    def _get_cache_file(self, key: str) -> Path:
        """Get cache file path"""
        return self.cache_dir / f"{key}.cache"
    
    def get(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[str]:
        """Get cached response"""
        key = self._generate_key(tool_name, arguments)
        cache_file = self._get_cache_file(key)
        
        if not cache_file.exists():
            self.stats["misses"] += 1
            logger.info(f"❌ File Cache MISS for {tool_name}")
            return None
        
        try:
            with open(cache_file, 'rb') as f:
                cache_data = pickle.load(f)
            
            # Check if expired
            if time.time() > cache_data['expires_at']:
                cache_file.unlink()  # Delete expired file
                self.stats["misses"] += 1
                logger.info(f"❌ File Cache EXPIRED for {tool_name}")
                return None
            
            self.stats["hits"] += 1
            logger.info(f"🎯 File Cache HIT for {tool_name}")
            return cache_data['value']
            
        except Exception as e:
            logger.error(f"File cache read error: {e}")
            self.stats["misses"] += 1
            return None
    
    def set(self, tool_name: str, arguments: Dict[str, Any], response: str):
        """Cache response"""
        key = self._generate_key(tool_name, arguments)
        cache_file = self._get_cache_file(key)
        
        cache_data = {
            'value': response,
            'created_at': time.time(),
            'expires_at': time.time() + self.ttl,
            'tool_name': tool_name
        }
        
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
            
            self.stats["sets"] += 1
            logger.info(f"💾 File cached response for {tool_name}")
            
        except Exception as e:
            logger.error(f"File cache write error: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        cache_files = list(self.cache_dir.glob("*.cache"))
        total_keys = len(cache_files)
        
        active_keys = 0
        expired_keys = 0
        current_time = time.time()
        
        for cache_file in cache_files:
            try:
                with open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                
                if current_time <= cache_data['expires_at']:
                    active_keys += 1
                else:
                    expired_keys += 1
            except:
                expired_keys += 1
        
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_rate = (self.stats["hits"] / max(total_requests, 1)) * 100
        
        return {
            "type": "File",
            "cache_dir": str(self.cache_dir),
            "total_keys": total_keys,
            "active_keys": active_keys,
            "expired_keys": expired_keys,
            "total_requests": total_requests,
            "cache_hits": self.stats["hits"],
            "cache_misses": self.stats["misses"],
            "hit_rate_percent": hit_rate,
            "ttl_hours": self.ttl / 3600
        }
    
    def cleanup_expired(self):
        """Clean up expired files"""
        cache_files = list(self.cache_dir.glob("*.cache"))
        deleted = 0
        current_time = time.time()
        
        for cache_file in cache_files:
            try:
                with open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                
                if current_time > cache_data['expires_at']:
                    cache_file.unlink()
                    deleted += 1
            except:
                cache_file.unlink()  # Delete corrupted files
                deleted += 1
        
        logger.info(f"🧹 Cleaned up {deleted} expired cache files")
        return deleted

class PersistentCachedGeminiServer:
    def __init__(self):
        self.server = Server("persistent-cached-gemini")
        
        # Get API key
        self.gemini_api_key = os.getenv("GEMINI_API_KEY")
        if not self.gemini_api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        
        # Configure Gemini
        genai.configure(api_key=self.gemini_api_key)
        self.model = genai.GenerativeModel('gemini-1.5-pro')
        
        # Initialize cache based on environment
        cache_type = os.getenv("CACHE_TYPE", "sqlite").lower()
        cache_ttl = int(os.getenv("CACHE_TTL", "7200"))  # 2 hours default
        
        if cache_type == "sqlite":
            cache_path = os.getenv("CACHE_PATH", "gemini_cache.db")
            self.cache = SQLiteCache(cache_path, cache_ttl)
        elif cache_type == "file":
            cache_dir = os.getenv("CACHE_DIR", "gemini_cache")
            self.cache = FileCache(cache_dir, cache_ttl)
        else:
            raise ValueError(f"Unsupported cache type: {cache_type}")
        
        self.setup_handlers()

    def setup_handlers(self):
        """Setup MCP server handlers"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available tools"""
            return [
                Tool(
                    name="persistent_search",
                    description="Search with persistent caching (survives server restarts)",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Search query"
                            },
                            "language": {
                                "type": "string",
                                "description": "Response language (vi/en)",
                                "default": "vi"
                            },
                            "force_refresh": {
                                "type": "boolean",
                                "description": "Force refresh cache",
                                "default": False
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="cache_stats",
                    description="Get persistent cache statistics",
                    inputSchema={
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                ),
                Tool(
                    name="cache_cleanup",
                    description="Clean up expired cache entries",
                    inputSchema={
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                )
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls"""
            try:
                if name == "persistent_search":
                    return await self._persistent_search(arguments)
                elif name == "cache_stats":
                    return await self._get_cache_stats()
                elif name == "cache_cleanup":
                    return await self._cleanup_cache()
                else:
                    raise ValueError(f"Unknown tool: {name}")
                    
            except Exception as e:
                logger.error(f"Error in tool {name}: {str(e)}")
                return [TextContent(type="text", text=f"❌ Lỗi: {str(e)}")]

    async def _persistent_search(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Perform search with persistent caching"""
        force_refresh = arguments.pop("force_refresh", False)

        # Try cache first (unless force refresh)
        if not force_refresh:
            cached_response = self.cache.get("search", arguments)
            if cached_response:
                # Add persistent cache indicator
                cache_type = self.cache.__class__.__name__.replace("Cache", "")
                cached_response = f"🏛️ **PERSISTENT CACHED RESPONSE** ({cache_type})\n📅 Retrieved from persistent storage\n\n---\n\n{cached_response}"
                return [TextContent(type="text", text=cached_response)]

        # Cache miss or force refresh - call API
        query = arguments["query"]
        language = arguments.get("language", "vi")

        # Build prompt
        lang_instruction = "Respond in Vietnamese" if language == "vi" else "Respond in English"
        prompt = f"""Tìm kiếm và phân tích thông tin về: "{query}"

Cấu trúc response:
## 🔍 Tổng quan
## 📊 Thông tin chi tiết
## 📈 Xu hướng và phát triển
## 💡 Kết luận

Yêu cầu:
- Thông tin chính xác và cập nhật
- Dữ liệu cụ thể và số liệu
- Phân tích khách quan
- Định dạng markdown chuyên nghiệp

{lang_instruction}. Đảm bảo độ tin cậy cao."""

        # Call Gemini API
        start_time = time.time()
        result = await self._call_gemini_with_grounding(prompt)
        response_time = (time.time() - start_time) * 1000

        # Format response
        cache_type = self.cache.__class__.__name__.replace("Cache", "")
        formatted_response = f"""# 🔍 Tìm kiếm: {query}

**📊 Thông tin:**
- Thời gian phản hồi: {response_time:.0f}ms
- Cache type: {cache_type} (Persistent)
- Ngôn ngữ: {'Tiếng Việt' if language == 'vi' else 'English'}
- Timestamp: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}

---

{result}

---
**✅ Chất lượng cao** | **🎯 Grounding verified** | **🏛️ Persistent cached**"""

        # Cache the result persistently
        self.cache.set("search", arguments, formatted_response)

        return [TextContent(type="text", text=formatted_response)]

    async def _get_cache_stats(self) -> List[TextContent]:
        """Get persistent cache statistics"""
        stats = self.cache.get_stats()

        stats_report = f"""# 📊 Persistent Cache Statistics

## 🏛️ Cache Configuration
- **Type**: {stats['type']} (Persistent)
- **Storage**: {stats.get('db_path', stats.get('cache_dir', 'N/A'))}
- **TTL**: {stats['ttl_hours']:.1f} hours

## 📈 Storage Statistics
- **Total Keys**: {stats['total_keys']}
- **Active Keys**: {stats['active_keys']}
- **Expired Keys**: {stats['expired_keys']}

## 📊 Request Statistics
- **Total Requests**: {stats['total_requests']}
- **Cache Hits**: {stats['cache_hits']}
- **Cache Misses**: {stats['cache_misses']}
- **Hit Rate**: {stats['hit_rate_percent']:.1f}%

## 💰 Cost & Performance
- **API Calls Saved**: {stats['cache_hits']}
- **Estimated Cost Saved**: ~${stats['cache_hits'] * 0.01:.2f}
- **Time Saved**: ~{stats['cache_hits'] * 15:.0f} seconds

## 🏛️ Persistence Benefits
- **Survives Restarts**: ✅ Yes
- **Shared Storage**: ✅ Multiple instances can share
- **Data Integrity**: ✅ Reliable storage
- **Cleanup**: ✅ Automatic TTL expiration

## 🔧 Maintenance
- **Expired Entries**: {stats['expired_keys']} (can be cleaned up)
- **Storage Efficiency**: {(stats['active_keys'] / max(stats['total_keys'], 1) * 100):.1f}% active

---
*Updated: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}*"""

        return [TextContent(type="text", text=stats_report)]

    async def _cleanup_cache(self) -> List[TextContent]:
        """Clean up expired cache entries"""
        deleted_count = self.cache.cleanup_expired()

        cleanup_report = f"""# 🧹 Cache Cleanup Complete

## 📊 Cleanup Results
- **Expired Entries Removed**: {deleted_count}
- **Cleanup Time**: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}

## 💾 Storage Benefits
- **Space Freed**: Expired entries removed
- **Performance**: Improved cache lookup speed
- **Maintenance**: Automatic cleanup completed

## 🔄 Recommendations
- **Regular Cleanup**: Run cleanup weekly for optimal performance
- **Monitor Storage**: Check cache stats regularly
- **TTL Optimization**: Adjust TTL based on usage patterns

---
**✅ Cache maintenance completed successfully!**"""

        return [TextContent(type="text", text=cleanup_report)]

    async def _call_gemini_with_grounding(self, prompt: str) -> str:
        """Call Gemini API with grounding"""
        tools = [{
            "google_search_retrieval": {
                "dynamic_retrieval_config": {
                    "mode": "MODE_DYNAMIC",
                    "dynamic_threshold": 0.3
                }
            }
        }]

        try:
            response = self.model.generate_content(
                prompt,
                generation_config={
                    "temperature": 0.1,
                    "max_output_tokens": 8192,
                },
                tools=tools
            )

            content = response.text
            if not content:
                raise Exception("No content from Gemini API")

            return content.strip()

        except Exception as e:
            raise Exception(f"Gemini API error: {str(e)}")

    async def run(self):
        """Run the server"""
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="persistent-cached-gemini",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )

async def main():
    """Main entry point"""
    server = PersistentCachedGeminiServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())
