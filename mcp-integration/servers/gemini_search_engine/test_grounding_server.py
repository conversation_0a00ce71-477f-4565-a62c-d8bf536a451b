#!/usr/bin/env python3
"""
Test script for Gemini Search Engine with Grounding MCP Server
"""

import asyncio
import json
import os
import sys
import time
from typing import Dict, Any

# Add the server directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from server_with_grounding import GeminiSearchEngineGroundingMCPServer

class MCPServerTester:
    def __init__(self):
        self.server = None
        self.test_results = []
        
    async def setup(self):
        """Setup the MCP server for testing"""
        try:
            # Set API key if not already set
            if not os.getenv("GEMINI_API_KEY"):
                # Use the key from config (for testing only)
                os.environ["GEMINI_API_KEY"] = "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"
            
            self.server = GeminiSearchEngineGroundingMCPServer()
            print("✅ MCP Server initialized successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to initialize server: {e}")
            return False
    
    async def test_tool(self, tool_name: str, arguments: Dict[str, Any], description: str):
        """Test a specific tool"""
        print(f"\n🧪 Testing {tool_name}: {description}")
        print(f"Arguments: {json.dumps(arguments, indent=2)}")
        
        start_time = time.time()
        try:
            # Call the tool handler directly
            handler = getattr(self.server, f"_{tool_name}")
            result = await handler(arguments)
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            
            print(f"✅ Success! Response time: {response_time:.2f}ms")
            
            # Parse and display result
            if result and len(result) > 0:
                content = result[0].text
                try:
                    # Try to parse JSON response
                    if content.startswith("Gemini Search Results") or content.startswith("Quick Search") or content.startswith("Research Results") or content.startswith("Fact Check") or content.startswith("Current Events"):
                        json_start = content.find('{')
                        if json_start != -1:
                            json_content = content[json_start:]
                            parsed = json.loads(json_content)
                            print(f"📊 Result preview: {parsed.get('result', parsed.get('answer', parsed.get('findings', parsed.get('analysis', parsed.get('information', 'N/A')))))[:200]}...")
                    else:
                        print(f"📊 Result: {content[:200]}...")
                except:
                    print(f"📊 Raw result: {content[:200]}...")
            
            self.test_results.append({
                "tool": tool_name,
                "description": description,
                "success": True,
                "response_time_ms": response_time,
                "arguments": arguments
            })
            
        except Exception as e:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            print(f"❌ Failed! Error: {e}")
            print(f"Response time: {response_time:.2f}ms")
            
            self.test_results.append({
                "tool": tool_name,
                "description": description,
                "success": False,
                "error": str(e),
                "response_time_ms": response_time,
                "arguments": arguments
            })
    
    async def run_all_tests(self):
        """Run all test cases"""
        print("🚀 Starting MCP Gemini Search Engine with Grounding Tests")
        print("=" * 60)
        
        if not await self.setup():
            return
        
        # Test cases
        test_cases = [
            {
                "tool": "search_with_google_grounding",
                "args": {"query": "latest developments in AI 2024", "detailed": True},
                "desc": "Detailed search about AI developments"
            },
            {
                "tool": "quick_search_with_grounding", 
                "args": {"query": "current weather in Vietnam"},
                "desc": "Quick search for weather info"
            },
            {
                "tool": "research_topic_with_grounding",
                "args": {"topic": "renewable energy trends", "focus_areas": ["solar power", "wind energy"]},
                "desc": "Research on renewable energy with focus areas"
            },
            {
                "tool": "fact_check_with_grounding",
                "args": {"statement": "The Earth is flat"},
                "desc": "Fact-check a false statement"
            },
            {
                "tool": "current_events_with_grounding",
                "args": {"topic": "Vietnam economy", "timeframe": "this week"},
                "desc": "Current events about Vietnam economy"
            }
        ]
        
        # Run tests
        for test_case in test_cases:
            await self.test_tool(
                test_case["tool"],
                test_case["args"], 
                test_case["desc"]
            )
            await asyncio.sleep(1)  # Rate limiting
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📋 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for r in self.test_results if r["success"])
        failed_tests = total_tests - successful_tests
        
        print(f"Total tests: {total_tests}")
        print(f"✅ Successful: {successful_tests}")
        print(f"❌ Failed: {failed_tests}")
        
        if successful_tests > 0:
            avg_response_time = sum(r["response_time_ms"] for r in self.test_results if r["success"]) / successful_tests
            print(f"⏱️  Average response time: {avg_response_time:.2f}ms")
        
        print("\n📊 Detailed Results:")
        for result in self.test_results:
            status = "✅" if result["success"] else "❌"
            print(f"{status} {result['tool']}: {result['description']}")
            if not result["success"]:
                print(f"   Error: {result['error']}")
            print(f"   Response time: {result['response_time_ms']:.2f}ms")

async def main():
    """Main test function"""
    tester = MCPServerTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
