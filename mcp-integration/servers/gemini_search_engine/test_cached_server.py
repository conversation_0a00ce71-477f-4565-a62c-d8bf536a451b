#!/usr/bin/env python3
"""
Test script for Cached Gemini Search Engine - Performance and Cost Optimization
"""

import asyncio
import json
import os
import sys
import time
from typing import Dict, Any

# Add the server directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from server_with_caching import CachedGeminiSearchEngineServer

class CachedMCPServerTester:
    def __init__(self):
        self.server = None
        self.test_results = []
        
    async def setup(self):
        """Setup the cached MCP server for testing"""
        try:
            # Set API key if not already set
            if not os.getenv("GEMINI_API_KEY"):
                os.environ["GEMINI_API_KEY"] = "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"
            
            # Set Redis URL (fallback to memory cache if Redis not available)
            if not os.getenv("REDIS_URL"):
                os.environ["REDIS_URL"] = "redis://localhost:6379"
            
            self.server = CachedGeminiSearchEngineServer()
            print("✅ Cached MCP Server initialized successfully")
            print(f"💾 Cache type: {self.server.cache.redis_client is not None and 'Redis' or 'Memory'}")
            return True
        except Exception as e:
            print(f"❌ Failed to initialize server: {e}")
            return False
    
    async def test_caching_performance(self):
        """Test caching performance with repeated queries"""
        print("\n🚀 Testing Caching Performance")
        print("=" * 60)
        
        if not await self.setup():
            return
        
        # Test query that will be repeated
        test_query = {
            "tool": "cached_search",
            "args": {
                "query": "Artificial Intelligence trends in Vietnam 2024",
                "analysis_depth": "standard",
                "focus_areas": ["technology adoption", "market growth"],
                "language": "en"
            }
        }
        
        print(f"🧪 Testing query: {test_query['args']['query']}")
        
        # First call (cache miss - should be slow)
        print("\n1️⃣ First call (Cache MISS expected):")
        start_time = time.time()
        result1 = await self._call_tool(test_query["tool"], test_query["args"])
        time1 = (time.time() - start_time) * 1000
        print(f"   ⏱️ Response time: {time1:.0f}ms")
        
        # Second call (cache hit - should be fast)
        print("\n2️⃣ Second call (Cache HIT expected):")
        start_time = time.time()
        result2 = await self._call_tool(test_query["tool"], test_query["args"])
        time2 = (time.time() - start_time) * 1000
        print(f"   ⏱️ Response time: {time2:.0f}ms")
        
        # Calculate performance improvement
        if time1 > 0:
            improvement = ((time1 - time2) / time1) * 100
            print(f"\n📊 Performance Analysis:")
            print(f"   🐌 First call (API): {time1:.0f}ms")
            print(f"   ⚡ Second call (Cache): {time2:.0f}ms")
            print(f"   🚀 Speed improvement: {improvement:.1f}%")
            print(f"   💰 API cost saved: ~$0.01")
        
        # Test cache stats
        print("\n📈 Cache Statistics:")
        stats_result = await self._call_tool("cache_stats", {})
        if stats_result and len(stats_result) > 0:
            # Extract key metrics from stats
            stats_content = stats_result[0].text
            print("   " + "\n   ".join(stats_content.split('\n')[:15]))  # Show first 15 lines
        
        return time1, time2, improvement if 'improvement' in locals() else 0

    async def test_different_cache_scenarios(self):
        """Test different caching scenarios"""
        print("\n🎯 Testing Different Cache Scenarios")
        print("=" * 60)
        
        scenarios = [
            {
                "name": "Quick Search",
                "tool": "cached_search",
                "args": {
                    "query": "Vietnam economy 2024",
                    "analysis_depth": "quick",
                    "language": "vi"
                }
            },
            {
                "name": "Research Topic",
                "tool": "cached_research",
                "args": {
                    "topic": "Blockchain technology in Southeast Asia",
                    "perspective": "trend_analysis",
                    "language": "en"
                }
            },
            {
                "name": "Fact Check",
                "tool": "cached_fact_check",
                "args": {
                    "statement": "Vietnam is the world's second-largest coffee exporter",
                    "verification_level": "thorough",
                    "language": "en"
                }
            }
        ]
        
        results = []
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n{i}️⃣ Testing {scenario['name']}:")
            
            # First call
            start_time = time.time()
            result1 = await self._call_tool(scenario["tool"], scenario["args"])
            time1 = (time.time() - start_time) * 1000
            
            # Second call (should be cached)
            start_time = time.time()
            result2 = await self._call_tool(scenario["tool"], scenario["args"])
            time2 = (time.time() - start_time) * 1000
            
            improvement = ((time1 - time2) / time1) * 100 if time1 > 0 else 0
            
            print(f"   📊 First: {time1:.0f}ms | Second: {time2:.0f}ms | Improvement: {improvement:.1f}%")
            
            results.append({
                "scenario": scenario["name"],
                "first_call_ms": time1,
                "second_call_ms": time2,
                "improvement_percent": improvement
            })
            
            # Small delay between tests
            await asyncio.sleep(1)
        
        return results

    async def test_force_refresh(self):
        """Test force refresh functionality"""
        print("\n🔄 Testing Force Refresh")
        print("=" * 40)
        
        test_args = {
            "query": "Latest AI developments",
            "analysis_depth": "quick",
            "language": "en"
        }
        
        # Normal call (will be cached)
        print("1️⃣ Normal call:")
        start_time = time.time()
        await self._call_tool("cached_search", test_args)
        time1 = (time.time() - start_time) * 1000
        print(f"   ⏱️ Time: {time1:.0f}ms")
        
        # Cached call
        print("2️⃣ Cached call:")
        start_time = time.time()
        await self._call_tool("cached_search", test_args)
        time2 = (time.time() - start_time) * 1000
        print(f"   ⏱️ Time: {time2:.0f}ms")
        
        # Force refresh call
        print("3️⃣ Force refresh call:")
        test_args["force_refresh"] = True
        start_time = time.time()
        await self._call_tool("cached_search", test_args)
        time3 = (time.time() - start_time) * 1000
        print(f"   ⏱️ Time: {time3:.0f}ms")
        
        print(f"\n📊 Force refresh bypassed cache: {time3 > time2 * 2}")

    async def _call_tool(self, tool_name: str, arguments: Dict[str, Any]):
        """Call a tool and return result"""
        try:
            handler = getattr(self.server, f"_{tool_name}")
            return await handler(arguments)
        except Exception as e:
            print(f"❌ Error calling {tool_name}: {e}")
            return None

    async def run_comprehensive_cache_test(self):
        """Run comprehensive caching tests"""
        print("🚀 Comprehensive Caching Performance Test")
        print("=" * 70)
        
        if not await self.setup():
            return
        
        # Test 1: Basic caching performance
        time1, time2, improvement = await self.test_caching_performance()
        
        # Test 2: Different scenarios
        scenario_results = await self.test_different_cache_scenarios()
        
        # Test 3: Force refresh
        await self.test_force_refresh()
        
        # Final summary
        print("\n" + "=" * 70)
        print("📋 COMPREHENSIVE CACHE TEST SUMMARY")
        print("=" * 70)
        
        print(f"🎯 Basic Performance Test:")
        print(f"   First call: {time1:.0f}ms")
        print(f"   Cached call: {time2:.0f}ms")
        print(f"   Speed improvement: {improvement:.1f}%")
        
        print(f"\n📊 Scenario Results:")
        total_improvement = 0
        for result in scenario_results:
            print(f"   {result['scenario']}: {result['improvement_percent']:.1f}% improvement")
            total_improvement += result['improvement_percent']
        
        avg_improvement = total_improvement / len(scenario_results) if scenario_results else 0
        print(f"   Average improvement: {avg_improvement:.1f}%")
        
        # Cost savings estimate
        total_requests = len(scenario_results) * 2 + 3  # Each scenario has 2 calls + 3 force refresh calls
        cache_hits = len(scenario_results) + 1  # Second calls are cache hits
        cost_saved = cache_hits * 0.01  # $0.01 per API call saved
        
        print(f"\n💰 Cost Analysis:")
        print(f"   Total requests: {total_requests}")
        print(f"   Cache hits: {cache_hits}")
        print(f"   API calls saved: {cache_hits}")
        print(f"   Estimated cost saved: ${cost_saved:.2f}")
        
        print(f"\n🏆 Cache Performance Rating:")
        if avg_improvement >= 80:
            rating = "🥇 Excellent"
        elif avg_improvement >= 60:
            rating = "🥈 Very Good"
        elif avg_improvement >= 40:
            rating = "🥉 Good"
        else:
            rating = "⚠️ Needs Improvement"
        
        print(f"   {rating} ({avg_improvement:.1f}% average improvement)")
        
        print(f"\n💡 Recommendations:")
        if avg_improvement >= 80:
            print("   ✅ Caching is working excellently!")
            print("   ✅ Significant API cost savings achieved")
            print("   ✅ Ready for production deployment")
        else:
            print("   🔧 Consider optimizing cache TTL")
            print("   🔧 Check Redis connection stability")
            print("   🔧 Monitor cache hit rates in production")

async def main():
    """Main test function"""
    tester = CachedMCPServerTester()
    await tester.run_comprehensive_cache_test()

if __name__ == "__main__":
    asyncio.run(main())
