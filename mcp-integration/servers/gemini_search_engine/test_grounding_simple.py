#!/usr/bin/env python3
"""
Simple test for Google Search grounding
"""

import os
import google.generativeai as genai

# Set API key
os.environ["GEMINI_API_KEY"] = "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"
genai.configure(api_key=os.getenv("GEMINI_API_KEY"))

# Test different approaches
def test_grounding_approaches():
    model = genai.GenerativeModel('gemini-2.5-flash-lite')
    
    print("Testing different grounding approaches...")
    
    # Approach 1: New google_search format
    try:
        print("\n1. Testing google_search format:")
        tools = [{"google_search": {}}]
        response = model.generate_content(
            "What are the latest AI developments in 2024?",
            tools=tools
        )
        print("✅ Success with google_search format")
        print(f"Response: {response.text[:200]}...")
    except Exception as e:
        print(f"❌ Failed with google_search: {e}")
    
    # Approach 2: Legacy googleSearchRetrieval format
    try:
        print("\n2. Testing googleSearchRetrieval format:")
        tools = [{
            "googleSearchRetrieval": {
                "dynamicRetrievalConfig": {
                    "mode": "MODE_DYNAMIC",
                    "dynamicThreshold": 0.5
                }
            }
        }]
        response = model.generate_content(
            "What are the latest AI developments in 2024?",
            tools=tools
        )
        print("✅ Success with googleSearchRetrieval format")
        print(f"Response: {response.text[:200]}...")
    except Exception as e:
        print(f"❌ Failed with googleSearchRetrieval: {e}")
    
    # Approach 3: Without grounding (baseline)
    try:
        print("\n3. Testing without grounding (baseline):")
        response = model.generate_content(
            "What are the latest AI developments in 2024?"
        )
        print("✅ Success without grounding")
        print(f"Response: {response.text[:200]}...")
    except Exception as e:
        print(f"❌ Failed without grounding: {e}")

if __name__ == "__main__":
    test_grounding_approaches()
