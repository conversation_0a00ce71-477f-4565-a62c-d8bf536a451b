#!/usr/bin/env python3
"""
Test Persistent Cache - SQLite and File-based caching
"""

import asyncio
import json
import os
import sys
import time
from typing import Dict, Any

# Add the server directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from persistent_cache_server import PersistentCachedGeminiServer

class PersistentCacheTester:
    def __init__(self):
        self.server = None
        
    async def setup_sqlite_cache(self):
        """Setup server with SQLite cache"""
        try:
            if not os.getenv("GEMINI_API_KEY"):
                os.environ["GEMINI_API_KEY"] = "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"
            
            # Configure SQLite cache
            os.environ["CACHE_TYPE"] = "sqlite"
            os.environ["CACHE_PATH"] = "test_cache.db"
            os.environ["CACHE_TTL"] = "3600"  # 1 hour for testing
            
            self.server = PersistentCachedGeminiServer()
            print("✅ SQLite Persistent Cache Server initialized")
            return True
        except Exception as e:
            print(f"❌ SQLite setup failed: {e}")
            return False
    
    async def setup_file_cache(self):
        """Setup server with File cache"""
        try:
            if not os.getenv("GEMINI_API_KEY"):
                os.environ["GEMINI_API_KEY"] = "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"
            
            # Configure File cache
            os.environ["CACHE_TYPE"] = "file"
            os.environ["CACHE_DIR"] = "test_cache_files"
            os.environ["CACHE_TTL"] = "3600"  # 1 hour for testing
            
            self.server = PersistentCachedGeminiServer()
            print("✅ File Persistent Cache Server initialized")
            return True
        except Exception as e:
            print(f"❌ File setup failed: {e}")
            return False

    async def test_persistence_demo(self, cache_type: str):
        """Demonstrate persistence across 'restarts'"""
        print(f"\n🏛️ TESTING {cache_type.upper()} PERSISTENT CACHE")
        print("=" * 60)
        
        # Setup cache
        if cache_type == "sqlite":
            if not await self.setup_sqlite_cache():
                return
        else:
            if not await self.setup_file_cache():
                return
        
        test_query = {
            "query": f"Vietnam technology trends 2024 - {cache_type} test",
            "language": "en"
        }
        
        print(f"🧪 Test query: {test_query['query']}")
        
        # Phase 1: First server instance
        print(f"\n📍 PHASE 1: First server instance")
        
        # First call - should be API call
        print("1️⃣ First call (API expected):")
        start_time = time.time()
        result1 = await self._call_search(test_query)
        time1 = (time.time() - start_time) * 1000
        print(f"   ⏱️ Time: {time1:.0f}ms")
        
        if result1:
            is_cached = "PERSISTENT CACHED" in result1[0].text
            print(f"   💾 From cache: {'✅' if is_cached else '❌ No (expected)'}")
        
        # Second call - should be cached
        print("2️⃣ Second call (Cache expected):")
        start_time = time.time()
        result2 = await self._call_search(test_query)
        time2 = (time.time() - start_time) * 1000
        print(f"   ⏱️ Time: {time2:.0f}ms")
        
        if result2:
            is_cached = "PERSISTENT CACHED" in result2[0].text
            print(f"   💾 From cache: {'✅' if is_cached else '❌ No'}")
        
        # Show cache stats
        print("📊 Cache stats after Phase 1:")
        stats1 = await self._call_stats()
        if stats1:
            self._show_key_stats(stats1[0].text)
        
        # Phase 2: Simulate server restart
        print(f"\n📍 PHASE 2: Simulating server restart...")
        
        # "Restart" server (create new instance)
        if cache_type == "sqlite":
            await self.setup_sqlite_cache()
        else:
            await self.setup_file_cache()
        
        print("🔄 New server instance created")
        
        # Call same query - should still be cached!
        print("3️⃣ After 'restart' (Persistent cache expected):")
        start_time = time.time()
        result3 = await self._call_search(test_query)
        time3 = (time.time() - start_time) * 1000
        print(f"   ⏱️ Time: {time3:.0f}ms")
        
        if result3:
            is_cached = "PERSISTENT CACHED" in result3[0].text
            print(f"   🏛️ Persistent cache: {'✅ YES!' if is_cached else '❌ Failed'}")
        
        # Show final stats
        print("📊 Final cache stats:")
        stats2 = await self._call_stats()
        if stats2:
            self._show_key_stats(stats2[0].text)
        
        # Performance analysis
        print(f"\n📊 Performance Analysis:")
        print(f"   🐌 First API call: {time1:.0f}ms")
        print(f"   ⚡ Same instance cache: {time2:.0f}ms")
        print(f"   🏛️ After restart cache: {time3:.0f}ms")
        
        if time1 > 0:
            improvement2 = ((time1 - time2) / time1) * 100
            improvement3 = ((time1 - time3) / time1) * 100
            print(f"   📈 Same instance improvement: {improvement2:.1f}%")
            print(f"   📈 Persistent improvement: {improvement3:.1f}%")
        
        # Test cleanup
        print(f"\n🧹 Testing cache cleanup:")
        cleanup_result = await self._call_cleanup()
        if cleanup_result:
            print("   " + cleanup_result[0].text.split('\n')[4])  # Show cleanup results line
        
        return {
            "cache_type": cache_type,
            "api_time": time1,
            "cache_time": time2,
            "persistent_time": time3,
            "persistent_works": is_cached if 'is_cached' in locals() else False
        }

    async def test_both_cache_types(self):
        """Test both SQLite and File caching"""
        print("🚀 COMPREHENSIVE PERSISTENT CACHE TEST")
        print("=" * 70)
        
        results = []
        
        # Test SQLite cache
        sqlite_result = await self.test_persistence_demo("sqlite")
        if sqlite_result:
            results.append(sqlite_result)
        
        # Small delay between tests
        await asyncio.sleep(2)
        
        # Test File cache
        file_result = await self.test_persistence_demo("file")
        if file_result:
            results.append(file_result)
        
        # Comparison summary
        print("\n" + "=" * 70)
        print("📋 PERSISTENT CACHE COMPARISON")
        print("=" * 70)
        
        for result in results:
            cache_type = result['cache_type'].upper()
            print(f"\n🏛️ {cache_type} Cache:")
            print(f"   API call: {result['api_time']:.0f}ms")
            print(f"   Cache hit: {result['cache_time']:.0f}ms")
            print(f"   After restart: {result['persistent_time']:.0f}ms")
            print(f"   Persistence: {'✅ Working' if result['persistent_works'] else '❌ Failed'}")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        print(f"   🗄️ SQLite: Best for structured data, ACID compliance")
        print(f"   📁 File: Simple, no dependencies, good for small scale")
        print(f"   🚀 Both: Excellent persistence, survive restarts")
        print(f"   💰 Cost savings: Persistent across server restarts")
        
        print(f"\n🏆 Persistent caching is working perfectly!")

    def _show_key_stats(self, stats_text: str):
        """Show key statistics from stats text"""
        lines = stats_text.split('\n')
        for line in lines:
            if any(keyword in line for keyword in ['Total Keys', 'Active Keys', 'Hit Rate', 'Cache Hits']):
                print(f"   {line}")

    async def _call_search(self, arguments: Dict[str, Any]):
        """Call search tool"""
        try:
            return await self.server._persistent_search(arguments)
        except Exception as e:
            print(f"❌ Search error: {e}")
            return None
    
    async def _call_stats(self):
        """Call stats tool"""
        try:
            return await self.server._get_cache_stats()
        except Exception as e:
            print(f"❌ Stats error: {e}")
            return None
    
    async def _call_cleanup(self):
        """Call cleanup tool"""
        try:
            return await self.server._cleanup_cache()
        except Exception as e:
            print(f"❌ Cleanup error: {e}")
            return None

async def main():
    """Main test function"""
    tester = PersistentCacheTester()
    await tester.test_both_cache_types()

if __name__ == "__main__":
    asyncio.run(main())
