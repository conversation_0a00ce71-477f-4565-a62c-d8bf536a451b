#!/usr/bin/env python3
"""
MCP Server for Gemini Search Engine with Google Search Grounding
Provides search functionality using Gemini API with Google Search grounding
"""

import asyncio
import json
import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Optional
import google.generativeai as genai
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)
from mcp.server.lowlevel.server import NotificationOptions

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("gemini-search-engine-grounding-mcp-server")

class GeminiSearchEngineGroundingMCPServer:
    def __init__(self):
        self.server = Server("gemini-search-engine-grounding")
        # Get API key from environment variable
        self.gemini_api_key = os.getenv("GEMINI_API_KEY")
        if not self.gemini_api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        
        # Configure Gemini
        genai.configure(api_key=self.gemini_api_key)
        
        # Initialize model - use gemini-1.5-flash for better grounding support
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
        self.setup_handlers()

    def setup_handlers(self):
        """Setup MCP server handlers"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available search engine tools"""
            return [
                Tool(
                    name="search_with_google_grounding",
                    description="Search for information using Gemini with Google Search grounding",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Search query to find information about"
                            },
                            "temperature": {
                                "type": "number",
                                "description": "Temperature for response generation (0.0-1.0)",
                                "default": 0.2,
                                "minimum": 0.0,
                                "maximum": 1.0
                            },
                            "detailed": {
                                "type": "boolean",
                                "description": "Whether to provide detailed analysis with key facts",
                                "default": True
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="quick_search_with_grounding",
                    description="Quick search for brief information using Gemini with Google Search grounding",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Search query for quick information"
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="research_topic_with_grounding",
                    description="Deep research on a topic with comprehensive analysis using Google Search grounding",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "topic": {
                                "type": "string",
                                "description": "Topic to research in depth"
                            },
                            "focus_areas": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Specific areas to focus on in the research",
                                "default": []
                            }
                        },
                        "required": ["topic"]
                    }
                ),
                Tool(
                    name="fact_check_with_grounding",
                    description="Fact-check a statement or claim using Gemini's Google Search grounding",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "statement": {
                                "type": "string",
                                "description": "Statement or claim to fact-check"
                            }
                        },
                        "required": ["statement"]
                    }
                ),
                Tool(
                    name="current_events_with_grounding",
                    description="Get current information about recent events or news using Google Search grounding",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "topic": {
                                "type": "string",
                                "description": "Topic or event to get current information about"
                            },
                            "timeframe": {
                                "type": "string",
                                "description": "Timeframe for the information (e.g., 'today', 'this week', 'recent')",
                                "default": "recent"
                            }
                        },
                        "required": ["topic"]
                    }
                )
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls"""
            try:
                if name == "search_with_google_grounding":
                    return await self._search_with_google_grounding(arguments)
                elif name == "quick_search_with_grounding":
                    return await self._quick_search_with_grounding(arguments)
                elif name == "research_topic_with_grounding":
                    return await self._research_topic_with_grounding(arguments)
                elif name == "fact_check_with_grounding":
                    return await self._fact_check_with_grounding(arguments)
                elif name == "current_events_with_grounding":
                    return await self._current_events_with_grounding(arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
            except Exception as e:
                logger.error(f"Error in tool {name}: {str(e)}")
                return [TextContent(type="text", text=f"Error: {str(e)}")]

    async def _search_with_google_grounding(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Search using Gemini with Google Search grounding"""
        try:
            query = arguments["query"]
            temperature = arguments.get("temperature", 0.2)
            detailed = arguments.get("detailed", True)
            
            if detailed:
                prompt = f"""You are an AI assistant with comprehensive knowledge. For the given query, provide a multi-paragraph comprehensive overview. After the overview, include a section with the heading "Key Details:" followed by a bulleted list of the most important facts, figures, and specific details. Do not add any conversational fluff. Query: "{query}" """
            else:
                prompt = f"""Provide a concise but informative answer to this query: "{query}" """
            
            # Configure grounding - use legacy format for google-generativeai SDK
            tools = [{
                "google_search_retrieval": {
                    "dynamic_retrieval_config": {
                        "mode": "MODE_DYNAMIC",
                        "dynamic_threshold": 0.5
                    }
                }
            }]
            
            result = await self._call_gemini_with_grounding(prompt, temperature, tools)
            
            response_data = {
                "query": query,
                "search_type": "detailed" if detailed else "standard",
                "timestamp": datetime.now().isoformat(),
                "source": "Gemini with Google Search Grounding",
                "result": result["content"],
                "response_time_ms": result["response_time_ms"]
            }
            
            return [TextContent(
                type="text",
                text=f"Gemini Search Results with Grounding:\n\n{json.dumps(response_data, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error searching with Gemini grounding: {str(e)}")]

    async def _quick_search_with_grounding(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Quick search for brief information with grounding"""
        try:
            query = arguments["query"]
            prompt = f"""Provide a brief, direct answer to this query in 2-3 sentences: "{query}" """
            
            # Configure grounding - use legacy format for google-generativeai SDK
            tools = [{
                "google_search_retrieval": {
                    "dynamic_retrieval_config": {
                        "mode": "MODE_DYNAMIC",
                        "dynamic_threshold": 0.5
                    }
                }
            }]
            
            result = await self._call_gemini_with_grounding(prompt, 0.1, tools)
            
            response_data = {
                "query": query,
                "search_type": "quick",
                "timestamp": datetime.now().isoformat(),
                "answer": result["content"],
                "response_time_ms": result["response_time_ms"]
            }
            
            return [TextContent(
                type="text",
                text=f"Quick Search Result with Grounding:\n\n{json.dumps(response_data, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error in quick search with grounding: {str(e)}")]

    async def _research_topic_with_grounding(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Deep research on a topic with grounding"""
        try:
            topic = arguments["topic"]
            focus_areas = arguments.get("focus_areas", [])
            
            focus_text = ""
            if focus_areas:
                focus_text = f" Pay special attention to these areas: {', '.join(focus_areas)}."
            
            prompt = f"""Conduct comprehensive research on the topic: "{topic}". Provide:
1. Overview and background
2. Current status and recent developments
3. Key statistics and data
4. Important considerations and implications
5. Future outlook or trends
{focus_text}
Structure your response with clear headings and detailed information."""
            
            # Configure grounding - use legacy format for google-generativeai SDK
            tools = [{
                "google_search_retrieval": {
                    "dynamic_retrieval_config": {
                        "mode": "MODE_DYNAMIC",
                        "dynamic_threshold": 0.5
                    }
                }
            }]
            
            result = await self._call_gemini_with_grounding(prompt, 0.3, tools)
            
            response_data = {
                "topic": topic,
                "focus_areas": focus_areas,
                "research_type": "comprehensive",
                "timestamp": datetime.now().isoformat(),
                "findings": result["content"],
                "response_time_ms": result["response_time_ms"]
            }
            
            return [TextContent(
                type="text",
                text=f"Research Results with Grounding:\n\n{json.dumps(response_data, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error in research with grounding: {str(e)}")]

    async def _fact_check_with_grounding(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Fact-check a statement with grounding"""
        try:
            statement = arguments["statement"]
            
            prompt = f"""Fact-check this statement: "{statement}"
Provide:
1. Accuracy assessment (True/False/Partially True/Unclear)
2. Supporting evidence or contradicting information
3. Sources or context for verification
4. Any important nuances or clarifications
Be objective and cite specific facts."""
            
            # Configure grounding - use legacy format for google-generativeai SDK
            tools = [{
                "google_search_retrieval": {
                    "dynamic_retrieval_config": {
                        "mode": "MODE_DYNAMIC",
                        "dynamic_threshold": 0.5
                    }
                }
            }]
            
            result = await self._call_gemini_with_grounding(prompt, 0.1, tools)
            
            response_data = {
                "statement": statement,
                "fact_check_type": "verification",
                "timestamp": datetime.now().isoformat(),
                "analysis": result["content"],
                "response_time_ms": result["response_time_ms"]
            }
            
            return [TextContent(
                type="text",
                text=f"Fact Check Results with Grounding:\n\n{json.dumps(response_data, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error in fact checking with grounding: {str(e)}")]

    async def _current_events_with_grounding(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Get current events information with grounding"""
        try:
            topic = arguments["topic"]
            timeframe = arguments.get("timeframe", "recent")
            
            prompt = f"""Provide current information about "{topic}" focusing on {timeframe} developments. Include:
1. Latest news and updates
2. Recent changes or developments
3. Current status or situation
4. Key dates and timeline
5. Relevant statistics or data
Focus on the most recent and relevant information available."""
            
            # Configure grounding - use legacy format for google-generativeai SDK
            tools = [{
                "google_search_retrieval": {
                    "dynamic_retrieval_config": {
                        "mode": "MODE_DYNAMIC",
                        "dynamic_threshold": 0.5
                    }
                }
            }]
            
            result = await self._call_gemini_with_grounding(prompt, 0.2, tools)
            
            response_data = {
                "topic": topic,
                "timeframe": timeframe,
                "search_type": "current_events",
                "timestamp": datetime.now().isoformat(),
                "information": result["content"],
                "response_time_ms": result["response_time_ms"]
            }
            
            return [TextContent(
                type="text",
                text=f"Current Events Information with Grounding:\n\n{json.dumps(response_data, indent=2, ensure_ascii=False)}"
            )]
            
        except Exception as e:
            return [TextContent(type="text", text=f"Error getting current events with grounding: {str(e)}")]

    async def _call_gemini_with_grounding(self, prompt: str, temperature: float = 0.2, tools: List[Dict] = None) -> Dict[str, Any]:
        """Call the Gemini API with Google Search grounding"""
        if tools is None:
            # Use legacy format for google-generativeai SDK
            tools = [{
                "google_search_retrieval": {
                    "dynamic_retrieval_config": {
                        "mode": "MODE_DYNAMIC",
                        "dynamic_threshold": 0.5
                    }
                }
            }]

        start_time = datetime.now()

        try:
            # Generate content with grounding
            response = self.model.generate_content(
                prompt,
                generation_config={
                    "temperature": temperature,
                    "max_output_tokens": 8192,
                },
                tools=tools
            )
            
            end_time = datetime.now()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            
            content = response.text
            
            if not content:
                raise Exception("No content returned from Gemini API")
            
            return {
                "content": content.strip(),
                "response_time_ms": response_time_ms
            }
            
        except Exception as e:
            end_time = datetime.now()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            raise Exception(f"Gemini API call failed: {str(e)}")

    async def run(self):
        """Run the MCP server"""
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="gemini-search-engine-grounding",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )

async def main():
    """Main entry point"""
    server = GeminiSearchEngineGroundingMCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())