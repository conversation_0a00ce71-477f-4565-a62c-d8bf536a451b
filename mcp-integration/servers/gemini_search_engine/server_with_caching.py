#!/usr/bin/env python3
"""
High-Performance MCP Server for Gemini Search Engine with Redis Caching
Optimized for speed and API cost reduction
"""

import asyncio
import json
import logging
import os
import hashlib
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
import google.generativeai as genai
import redis
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)
from mcp.server.lowlevel.server import NotificationOptions

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("gemini-search-engine-cached")

class ResponseCache:
    """Redis-based caching system for API responses"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379", default_ttl: int = 7200):
        """
        Initialize cache with Redis connection
        Args:
            redis_url: Redis connection URL
            default_ttl: Default TTL in seconds (2 hours)
        """
        try:
            self.redis_client = redis.from_url(redis_url, decode_responses=True)
            self.redis_client.ping()  # Test connection
            self.default_ttl = default_ttl
            logger.info("✅ Redis cache connected successfully")
        except Exception as e:
            logger.warning(f"⚠️ Redis not available, using memory cache: {e}")
            self.redis_client = None
            self.memory_cache = {}
            self.cache_timestamps = {}
            self.default_ttl = default_ttl
    
    def _generate_cache_key(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """Generate unique cache key from tool name and arguments"""
        # Create deterministic hash from tool name and sorted arguments
        cache_data = {
            "tool": tool_name,
            "args": arguments
        }
        cache_string = json.dumps(cache_data, sort_keys=True, ensure_ascii=False)
        cache_hash = hashlib.md5(cache_string.encode('utf-8')).hexdigest()
        return f"gemini_search:{tool_name}:{cache_hash}"
    
    def get(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get cached response"""
        cache_key = self._generate_cache_key(tool_name, arguments)
        
        try:
            if self.redis_client:
                # Redis cache
                cached_data = self.redis_client.get(cache_key)
                if cached_data:
                    result = json.loads(cached_data)
                    logger.info(f"🎯 Cache HIT for {tool_name}")
                    return result
            else:
                # Memory cache fallback
                if cache_key in self.memory_cache:
                    timestamp = self.cache_timestamps.get(cache_key, 0)
                    if time.time() - timestamp < self.default_ttl:
                        logger.info(f"🎯 Memory cache HIT for {tool_name}")
                        return self.memory_cache[cache_key]
                    else:
                        # Expired
                        del self.memory_cache[cache_key]
                        del self.cache_timestamps[cache_key]
            
            logger.info(f"❌ Cache MISS for {tool_name}")
            return None
            
        except Exception as e:
            logger.error(f"Cache get error: {e}")
            return None
    
    def set(self, tool_name: str, arguments: Dict[str, Any], response: Dict[str, Any], ttl: Optional[int] = None):
        """Cache response"""
        cache_key = self._generate_cache_key(tool_name, arguments)
        ttl = ttl or self.default_ttl
        
        try:
            # Add cache metadata
            cached_response = {
                **response,
                "cached_at": datetime.now().isoformat(),
                "cache_ttl": ttl,
                "from_cache": False
            }
            
            if self.redis_client:
                # Redis cache
                self.redis_client.setex(
                    cache_key, 
                    ttl, 
                    json.dumps(cached_response, ensure_ascii=False)
                )
                logger.info(f"💾 Cached response for {tool_name} (TTL: {ttl}s)")
            else:
                # Memory cache fallback
                self.memory_cache[cache_key] = cached_response
                self.cache_timestamps[cache_key] = time.time()
                logger.info(f"💾 Memory cached response for {tool_name}")
                
        except Exception as e:
            logger.error(f"Cache set error: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        try:
            if self.redis_client:
                info = self.redis_client.info()
                return {
                    "type": "redis",
                    "connected": True,
                    "keys": self.redis_client.dbsize(),
                    "memory_usage": info.get("used_memory_human", "N/A"),
                    "hits": info.get("keyspace_hits", 0),
                    "misses": info.get("keyspace_misses", 0)
                }
            else:
                return {
                    "type": "memory",
                    "connected": True,
                    "keys": len(self.memory_cache),
                    "memory_usage": "N/A",
                    "hits": "N/A",
                    "misses": "N/A"
                }
        except Exception as e:
            return {
                "type": "error",
                "connected": False,
                "error": str(e)
            }

class CachedGeminiSearchEngineServer:
    def __init__(self):
        self.server = Server("gemini-search-engine-cached")
        
        # Get API key
        self.gemini_api_key = os.getenv("GEMINI_API_KEY")
        if not self.gemini_api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        
        # Configure Gemini
        genai.configure(api_key=self.gemini_api_key)
        self.model = genai.GenerativeModel('gemini-1.5-pro')
        
        # Initialize cache
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
        cache_ttl = int(os.getenv("CACHE_TTL", "7200"))  # 2 hours default
        self.cache = ResponseCache(redis_url, cache_ttl)
        
        # Performance tracking
        self.stats = {
            "total_requests": 0,
            "cache_hits": 0,
            "api_calls": 0,
            "total_response_time": 0,
            "api_cost_saved": 0
        }
        
        self.setup_handlers()

    def setup_handlers(self):
        """Setup MCP server handlers"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available cached search tools"""
            return [
                Tool(
                    name="cached_search",
                    description="High-performance search with intelligent caching (saves API costs)",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Search query"
                            },
                            "analysis_depth": {
                                "type": "string",
                                "enum": ["quick", "standard", "comprehensive", "expert"],
                                "description": "Analysis depth",
                                "default": "standard"
                            },
                            "focus_areas": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Focus areas",
                                "default": []
                            },
                            "language": {
                                "type": "string",
                                "description": "Response language (vi/en)",
                                "default": "vi"
                            },
                            "force_refresh": {
                                "type": "boolean",
                                "description": "Force refresh cache (bypass cache)",
                                "default": False
                            }
                        },
                        "required": ["query"]
                    }
                ),
                Tool(
                    name="cached_research",
                    description="Expert research with caching for repeated topics",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "topic": {
                                "type": "string",
                                "description": "Research topic"
                            },
                            "research_questions": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Research questions",
                                "default": []
                            },
                            "perspective": {
                                "type": "string",
                                "enum": ["neutral", "critical", "comparative", "trend_analysis"],
                                "description": "Research perspective",
                                "default": "neutral"
                            },
                            "language": {
                                "type": "string",
                                "description": "Response language",
                                "default": "vi"
                            },
                            "force_refresh": {
                                "type": "boolean",
                                "description": "Force refresh cache",
                                "default": False
                            }
                        },
                        "required": ["topic"]
                    }
                ),
                Tool(
                    name="cached_fact_check",
                    description="Fact verification with caching for repeated statements",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "statement": {
                                "type": "string",
                                "description": "Statement to verify"
                            },
                            "verification_level": {
                                "type": "string",
                                "enum": ["basic", "thorough", "forensic"],
                                "description": "Verification level",
                                "default": "thorough"
                            },
                            "language": {
                                "type": "string",
                                "description": "Response language",
                                "default": "vi"
                            },
                            "force_refresh": {
                                "type": "boolean",
                                "description": "Force refresh cache",
                                "default": False
                            }
                        },
                        "required": ["statement"]
                    }
                ),
                Tool(
                    name="cache_stats",
                    description="Get cache performance statistics and API cost savings",
                    inputSchema={
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                )
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle cached tool calls"""
            try:
                self.stats["total_requests"] += 1
                
                if name == "cache_stats":
                    return await self._get_cache_stats()
                elif name == "cached_search":
                    return await self._cached_search(arguments)
                elif name == "cached_research":
                    return await self._cached_research(arguments)
                elif name == "cached_fact_check":
                    return await self._cached_fact_check(arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
                    
            except Exception as e:
                logger.error(f"Error in tool {name}: {str(e)}")
                return [TextContent(type="text", text=f"❌ Lỗi: {str(e)}")]

    async def _cached_search(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Cached high-quality search"""
        return await self._execute_with_cache("search", arguments, self._perform_search)
    
    async def _cached_research(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Cached research"""
        return await self._execute_with_cache("research", arguments, self._perform_research)
    
    async def _cached_fact_check(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Cached fact checking"""
        return await self._execute_with_cache("fact_check", arguments, self._perform_fact_check)

    async def _execute_with_cache(self, tool_name: str, arguments: Dict[str, Any], executor_func) -> List[TextContent]:
        """Execute function with caching logic"""
        start_time = time.time()
        force_refresh = arguments.pop("force_refresh", False)
        
        # Try cache first (unless force refresh)
        if not force_refresh:
            cached_result = self.cache.get(tool_name, arguments)
            if cached_result:
                self.stats["cache_hits"] += 1
                
                # Mark as from cache and add performance info
                cached_result["from_cache"] = True
                cached_result["cache_hit"] = True
                response_time = (time.time() - start_time) * 1000
                
                formatted_response = self._format_cached_response(cached_result, response_time)
                return [TextContent(type="text", text=formatted_response)]
        
        # Cache miss or force refresh - call API
        self.stats["api_calls"] += 1
        result = await executor_func(arguments)
        
        # Cache the result
        response_time = (time.time() - start_time) * 1000
        self.stats["total_response_time"] += response_time
        
        # Estimate API cost saved (rough estimate)
        self.stats["api_cost_saved"] += 0.01  # ~$0.01 per API call saved
        
        cache_result = {
            "content": result[0].text if result else "",
            "response_time_ms": response_time,
            "from_cache": False
        }
        
        # Cache for future use
        self.cache.set(tool_name, arguments, cache_result)

        return result

    async def _perform_search(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Perform actual search (called when cache miss)"""
        query = arguments["query"]
        analysis_depth = arguments.get("analysis_depth", "standard")
        focus_areas = arguments.get("focus_areas", [])
        language = arguments.get("language", "vi")

        prompt = self._build_search_prompt(query, analysis_depth, focus_areas, language)
        result = await self._call_gemini_with_grounding(prompt, temperature=0.1)

        formatted_response = self._format_search_response(result, query, analysis_depth, language)
        return [TextContent(type="text", text=formatted_response)]

    async def _perform_research(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Perform actual research (called when cache miss)"""
        topic = arguments["topic"]
        research_questions = arguments.get("research_questions", [])
        perspective = arguments.get("perspective", "neutral")
        language = arguments.get("language", "vi")

        prompt = self._build_research_prompt(topic, research_questions, perspective, language)
        result = await self._call_gemini_with_grounding(prompt, temperature=0.05)

        formatted_response = self._format_research_response(result, topic, perspective, language)
        return [TextContent(type="text", text=formatted_response)]

    async def _perform_fact_check(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Perform actual fact check (called when cache miss)"""
        statement = arguments["statement"]
        verification_level = arguments.get("verification_level", "thorough")
        language = arguments.get("language", "vi")

        prompt = self._build_verification_prompt(statement, verification_level, language)
        result = await self._call_gemini_with_grounding(prompt, temperature=0.0)

        formatted_response = self._format_verification_response(result, statement, verification_level, language)
        return [TextContent(type="text", text=formatted_response)]

    async def _get_cache_stats(self) -> List[TextContent]:
        """Get cache performance statistics"""
        cache_stats = self.cache.get_cache_stats()

        # Calculate performance metrics
        hit_rate = (self.stats["cache_hits"] / max(self.stats["total_requests"], 1)) * 100
        avg_response_time = self.stats["total_response_time"] / max(self.stats["api_calls"], 1)

        stats_report = f"""# 📊 Cache Performance Statistics

## 🎯 Cache Performance
- **Cache Type**: {cache_stats.get('type', 'unknown').title()}
- **Connection Status**: {'✅ Connected' if cache_stats.get('connected') else '❌ Disconnected'}
- **Total Cached Keys**: {cache_stats.get('keys', 0)}
- **Memory Usage**: {cache_stats.get('memory_usage', 'N/A')}

## 📈 Request Statistics
- **Total Requests**: {self.stats['total_requests']}
- **Cache Hits**: {self.stats['cache_hits']}
- **API Calls**: {self.stats['api_calls']}
- **Hit Rate**: {hit_rate:.1f}%

## ⚡ Performance Metrics
- **Average Response Time**: {avg_response_time:.0f}ms
- **API Cost Saved**: ~${self.stats['api_cost_saved']:.2f}
- **Time Saved**: ~{(self.stats['cache_hits'] * 15):.0f} seconds

## 💡 Cache Benefits
- **Speed**: Cache hits are ~95% faster than API calls
- **Cost**: Each cache hit saves ~$0.01 in API costs
- **Reliability**: Reduced dependency on external API
- **Rate Limits**: Helps avoid API rate limiting

---
*Cache TTL: {self.cache.default_ttl // 3600}h | Updated: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}*"""

        return [TextContent(type="text", text=stats_report)]

    def _format_cached_response(self, cached_result: Dict[str, Any], response_time: float) -> str:
        """Format cached response with cache indicators"""
        content = cached_result.get("content", "")
        cached_at = cached_result.get("cached_at", "")
        original_time = cached_result.get("response_time_ms", 0)

        # Add cache header
        cache_header = f"""🚀 **CACHED RESPONSE** (⚡ {response_time:.0f}ms vs {original_time:.0f}ms original)
📅 Cached at: {cached_at}
💾 Speed improvement: {((original_time - response_time) / original_time * 100):.0f}%

---

"""

        return cache_header + content

    def _build_search_prompt(self, query: str, analysis_depth: str, focus_areas: List[str], language: str) -> str:
        """Build optimized search prompt"""
        lang_instruction = "Respond in Vietnamese" if language == "vi" else "Respond in English"
        focus_text = f"\n\nTrọng tâm: {', '.join(focus_areas)}" if focus_areas else ""

        depth_configs = {
            "quick": {
                "instruction": "Cung cấp thông tin ngắn gọn nhưng chất lượng cao",
                "structure": "**Điểm chính** | **Số liệu** | **Xu hướng**"
            },
            "standard": {
                "instruction": "Phân tích toàn diện với cấu trúc rõ ràng",
                "structure": "Tổng quan | Phân tích chi tiết | Xu hướng | Kết luận"
            },
            "comprehensive": {
                "instruction": "Phân tích chuyên sâu đa chiều",
                "structure": "Bối cảnh | Tình hình hiện tại | Phân tích đa chiều | Thách thức & Cơ hội | Xu hướng | Kết luận"
            },
            "expert": {
                "instruction": "Phân tích chuyên gia với độ sâu academic",
                "structure": "Executive Summary | Phân tích chuyên sâu | Benchmarking | Risk Assessment | Strategic Recommendations"
            }
        }

        config = depth_configs.get(analysis_depth, depth_configs["standard"])

        return f"""{config['instruction']} về: "{query}"

Cấu trúc: {config['structure']}

Yêu cầu chất lượng:
- Thông tin chính xác và cập nhật
- Dữ liệu cụ thể và số liệu
- Phân tích khách quan
- Định dạng markdown chuyên nghiệp{focus_text}

{lang_instruction}. Đảm bảo độ tin cậy cao."""

    def _build_research_prompt(self, topic: str, research_questions: List[str], perspective: str, language: str) -> str:
        """Build research prompt"""
        lang_instruction = "Respond in Vietnamese" if language == "vi" else "Respond in English"
        questions_text = f"\n\nCâu hỏi nghiên cứu:\n" + "\n".join([f"- {q}" for q in research_questions]) if research_questions else ""

        perspective_map = {
            "neutral": "Trình bày khách quan, cân bằng",
            "critical": "Phân tích phê phán, chỉ ra vấn đề",
            "comparative": "So sánh với các trường hợp khác",
            "trend_analysis": "Tập trung xu hướng và dự báo"
        }

        return f"""Nghiên cứu chuyên sâu: "{topic}"

Góc độ: {perspective_map.get(perspective, "Khách quan")}

Cấu trúc nghiên cứu:
## 🎯 Executive Summary
## 📊 Phân tích Dữ liệu
## 🔍 Phân tích Chuyên sâu
## 🌍 Bối cảnh Quốc tế
## ⚖️ Rủi ro & Cơ hội
## 🚀 Khuyến nghị{questions_text}

{lang_instruction}. Chất lượng academic cao."""

    def _build_verification_prompt(self, statement: str, verification_level: str, language: str) -> str:
        """Build fact verification prompt"""
        lang_instruction = "Respond in Vietnamese" if language == "vi" else "Respond in English"

        level_map = {
            "basic": "Xác minh cơ bản với 2-3 nguồn",
            "thorough": "Xác minh kỹ lưỡng đa nguồn",
            "forensic": "Xác minh pháp y chi tiết"
        }

        return f"""Xác minh sự thật ({level_map.get(verification_level, "thorough")}): "{statement}"

Cấu trúc xác minh:
## 🎯 Kết luận: [ĐÚNG/SAI/MỘT PHẦN/KHÔNG RÕ] (Tin cậy: X%)
## 📋 Phân tích Chi tiết
## 🔍 Bằng chứng & Nguồn tin
## 📊 Ngữ cảnh
## ⚠️ Cảnh báo
## 💡 Khuyến nghị

{lang_instruction}. Khách quan và chính xác."""

    def _format_search_response(self, result: Dict[str, Any], query: str, analysis_depth: str, language: str) -> str:
        """Format search response"""
        content = result.get("content", "")
        response_time = result.get("response_time_ms", 0)

        header = f"""# 🔍 Tìm kiếm: {query}

**📊 Thông tin:**
- Độ sâu: {analysis_depth.title()}
- Thời gian: {response_time:.0f}ms
- Ngôn ngữ: {'Tiếng Việt' if language == 'vi' else 'English'}
- Timestamp: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}

---

"""

        footer = f"""

---
**✅ Chất lượng cao** | **🎯 Grounding verified** | **💾 Cached for reuse**"""

        return header + content + footer

    def _format_research_response(self, result: Dict[str, Any], topic: str, perspective: str, language: str) -> str:
        """Format research response"""
        content = result.get("content", "")
        response_time = result.get("response_time_ms", 0)

        header = f"""# 🔬 Nghiên cứu: {topic}

**📋 Thông tin:**
- Góc độ: {perspective.title()}
- Thời gian: {response_time:.0f}ms
- Ngôn ngữ: {'Tiếng Việt' if language == 'vi' else 'English'}
- Timestamp: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}

---

"""

        footer = f"""

---
**🏆 Chất lượng nghiên cứu cao** | **📊 Data-driven** | **💾 Cached**"""

        return header + content + footer

    def _format_verification_response(self, result: Dict[str, Any], statement: str, verification_level: str, language: str) -> str:
        """Format verification response"""
        content = result.get("content", "")
        response_time = result.get("response_time_ms", 0)

        header = f"""# ✅ Xác minh: "{statement}"

**🔍 Thông tin:**
- Mức độ: {verification_level.title()}
- Thời gian: {response_time:.0f}ms
- Ngôn ngữ: {'Tiếng Việt' if language == 'vi' else 'English'}
- Timestamp: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}

---

"""

        footer = f"""

---
**🛡️ Xác minh đáng tin cậy** | **📚 Multi-source** | **💾 Cached**"""

        return header + content + footer

    async def _call_gemini_with_grounding(self, prompt: str, temperature: float = 0.1) -> Dict[str, Any]:
        """Call Gemini API with grounding"""
        tools = [{
            "google_search_retrieval": {
                "dynamic_retrieval_config": {
                    "mode": "MODE_DYNAMIC",
                    "dynamic_threshold": 0.3
                }
            }
        }]

        start_time = datetime.now()

        try:
            response = self.model.generate_content(
                prompt,
                generation_config={
                    "temperature": temperature,
                    "max_output_tokens": 8192,
                    "top_p": 0.8,
                    "top_k": 40
                },
                tools=tools
            )

            end_time = datetime.now()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)

            content = response.text
            if not content:
                raise Exception("No content from Gemini API")

            return {
                "content": content.strip(),
                "response_time_ms": response_time_ms
            }

        except Exception as e:
            raise Exception(f"Gemini API error: {str(e)}")

    async def run(self):
        """Run the cached MCP server"""
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="gemini-search-engine-cached",
                    server_version="3.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )

async def main():
    """Main entry point"""
    server = CachedGeminiSearchEngineServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())
