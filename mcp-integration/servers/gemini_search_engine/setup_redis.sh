#!/bin/bash
# Quick Redis setup script for testing

echo "🚀 Setting up Red<PERSON> for MCP Gemini Cache Testing"
echo "================================================"

# Check if Redis is already running
if redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis is already running"
else
    echo "📦 Installing and starting Redis..."
    
    # Install Redis (Ubuntu/Debian)
    if command -v apt-get > /dev/null; then
        sudo apt-get update
        sudo apt-get install -y redis-server
    # Install Redis (CentOS/RHEL)
    elif command -v yum > /dev/null; then
        sudo yum install -y redis
    # Install Redis (macOS)
    elif command -v brew > /dev/null; then
        brew install redis
    else
        echo "❌ Could not detect package manager. Please install Redis manually."
        exit 1
    fi
    
    # Start Redis
    if command -v systemctl > /dev/null; then
        sudo systemctl start redis
        sudo systemctl enable redis
    else
        redis-server --daemonize yes
    fi
    
    # Wait for Red<PERSON> to start
    sleep 2
    
    # Test connection
    if redis-cli ping > /dev/null 2>&1; then
        echo "✅ Redis started successfully"
    else
        echo "❌ Failed to start Redis"
        exit 1
    fi
fi

# Configure Redis for persistence
echo "🔧 Configuring Redis for optimal persistence..."

redis-cli config set save "900 1 300 10 60 10000" > /dev/null 2>&1
redis-cli config set appendonly yes > /dev/null 2>&1
redis-cli config set appendfsync everysec > /dev/null 2>&1

echo "✅ Redis persistence configured"

# Show Redis info
echo ""
echo "📊 Redis Information:"
echo "   Version: $(redis-cli info server | grep redis_version | cut -d: -f2 | tr -d '\r')"
echo "   Memory: $(redis-cli info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r')"
echo "   Persistence: RDB + AOF enabled"
echo "   Port: 6379"
echo "   Status: $(redis-cli ping)"

echo ""
echo "🎯 Ready to test Redis persistent caching!"
echo "Run: python test_redis_persistent.py"
