#!/usr/bin/env python3
"""
SQLite MCP Server
Provides SQLite database operations for Open WebUI integration
"""

import asyncio
import json
import sqlite3
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)

# Initialize the MCP server
server = Server("sqlite")

# Safe directory for SQLite databases
SAFE_ROOT = Path("/home/<USER>/AccA/AccA/mcp-integration/data/sqlite")
SAFE_ROOT.mkdir(parents=True, exist_ok=True)

def get_safe_db_path(db_name: str) -> Path:
    """Get safe database path"""
    # Sanitize database name
    safe_name = "".join(c for c in db_name if c.isalnum() or c in "._-")
    if not safe_name.endswith('.db'):
        safe_name += '.db'
    
    return SAFE_ROOT / safe_name

class SQLiteManager:
    def __init__(self):
        self.connections = {}
    
    def get_connection(self, db_path: Path) -> sqlite3.Connection:
        """Get or create database connection"""
        db_str = str(db_path)
        if db_str not in self.connections:
            self.connections[db_str] = sqlite3.connect(db_str)
            self.connections[db_str].row_factory = sqlite3.Row
        return self.connections[db_str]
    
    def close_connection(self, db_path: Path):
        """Close database connection"""
        db_str = str(db_path)
        if db_str in self.connections:
            self.connections[db_str].close()
            del self.connections[db_str]
    
    def close_all_connections(self):
        """Close all database connections"""
        for conn in self.connections.values():
            conn.close()
        self.connections.clear()
    
    def execute_query(self, db_path: Path, query: str, params: Optional[List] = None) -> Dict[str, Any]:
        """Execute SQL query"""
        conn = self.get_connection(db_path)
        cursor = conn.cursor()
        
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            # Check if it's a SELECT query
            if query.strip().upper().startswith('SELECT'):
                rows = cursor.fetchall()
                columns = [description[0] for description in cursor.description]
                
                result = {
                    "type": "select",
                    "rows": [dict(row) for row in rows],
                    "columns": columns,
                    "row_count": len(rows)
                }
            else:
                conn.commit()
                result = {
                    "type": "modify",
                    "rows_affected": cursor.rowcount,
                    "last_row_id": cursor.lastrowid
                }
            
            return result
            
        except sqlite3.Error as e:
            conn.rollback()
            raise Exception(f"SQLite error: {str(e)}")
        finally:
            cursor.close()
    
    def get_table_info(self, db_path: Path, table_name: str) -> Dict[str, Any]:
        """Get table schema information"""
        conn = self.get_connection(db_path)
        cursor = conn.cursor()
        
        try:
            # Get table schema
            cursor.execute("PRAGMA table_info(?)", (table_name,))
            columns = cursor.fetchall()
            
            # Get table statistics
            cursor.execute(f"SELECT COUNT(*) as row_count FROM {table_name}")
            row_count = cursor.fetchone()[0]
            
            return {
                "table_name": table_name,
                "columns": [dict(col) for col in columns],
                "row_count": row_count
            }
            
        except sqlite3.Error as e:
            raise Exception(f"SQLite error: {str(e)}")
        finally:
            cursor.close()
    
    def list_tables(self, db_path: Path) -> List[str]:
        """List all tables in database"""
        conn = self.get_connection(db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            return tables
            
        except sqlite3.Error as e:
            raise Exception(f"SQLite error: {str(e)}")
        finally:
            cursor.close()

# Global SQLite manager
sqlite_manager = SQLiteManager()

@server.list_resources()
async def list_resources() -> List[Resource]:
    """List available SQLite resources"""
    resources = []
    
    # List all database files
    for db_file in SAFE_ROOT.glob("*.db"):
        resources.append(
            Resource(
                uri=f"sqlite://{db_file.name}",
                name=f"SQLite Database: {db_file.name}",
                description=f"SQLite database file: {db_file.name}",
                mimeType="application/x-sqlite3",
            )
        )
    
    return resources

@server.read_resource()
async def read_resource(uri: str) -> str:
    """Read SQLite resource"""
    if uri.startswith("sqlite://"):
        db_name = uri.replace("sqlite://", "")
        db_path = get_safe_db_path(db_name)
        
        if not db_path.exists():
            return json.dumps({"error": f"Database {db_name} does not exist"}, indent=2)
        
        try:
            tables = sqlite_manager.list_tables(db_path)
            db_info = {
                "database": db_name,
                "path": str(db_path),
                "size": db_path.stat().st_size,
                "tables": tables,
                "table_count": len(tables)
            }
            
            return json.dumps(db_info, indent=2)
            
        except Exception as e:
            return json.dumps({"error": str(e)}, indent=2)
    
    raise ValueError(f"Unknown resource: {uri}")

@server.list_tools()
async def list_tools() -> List[Tool]:
    """List available SQLite tools"""
    return [
        Tool(
            name="create_database",
            description="Create a new SQLite database",
            inputSchema={
                "type": "object",
                "properties": {
                    "database_name": {
                        "type": "string",
                        "description": "Name of the database to create"
                    }
                },
                "required": ["database_name"]
            }
        ),
        Tool(
            name="execute_sql",
            description="Execute SQL query on a database",
            inputSchema={
                "type": "object",
                "properties": {
                    "database_name": {
                        "type": "string",
                        "description": "Name of the database"
                    },
                    "query": {
                        "type": "string",
                        "description": "SQL query to execute"
                    },
                    "parameters": {
                        "type": "array",
                        "description": "Query parameters for prepared statements",
                        "items": {"type": "string"}
                    }
                },
                "required": ["database_name", "query"]
            }
        ),
        Tool(
            name="list_tables",
            description="List all tables in a database",
            inputSchema={
                "type": "object",
                "properties": {
                    "database_name": {
                        "type": "string",
                        "description": "Name of the database"
                    }
                },
                "required": ["database_name"]
            }
        ),
        Tool(
            name="describe_table",
            description="Get detailed information about a table",
            inputSchema={
                "type": "object",
                "properties": {
                    "database_name": {
                        "type": "string",
                        "description": "Name of the database"
                    },
                    "table_name": {
                        "type": "string",
                        "description": "Name of the table"
                    }
                },
                "required": ["database_name", "table_name"]
            }
        ),
        Tool(
            name="create_table",
            description="Create a new table in the database",
            inputSchema={
                "type": "object",
                "properties": {
                    "database_name": {
                        "type": "string",
                        "description": "Name of the database"
                    },
                    "table_name": {
                        "type": "string",
                        "description": "Name of the table to create"
                    },
                    "columns": {
                        "type": "array",
                        "description": "Column definitions",
                        "items": {
                            "type": "object",
                            "properties": {
                                "name": {"type": "string"},
                                "type": {"type": "string"},
                                "constraints": {"type": "string"}
                            },
                            "required": ["name", "type"]
                        }
                    }
                },
                "required": ["database_name", "table_name", "columns"]
            }
        ),
        Tool(
            name="insert_data",
            description="Insert data into a table",
            inputSchema={
                "type": "object",
                "properties": {
                    "database_name": {
                        "type": "string",
                        "description": "Name of the database"
                    },
                    "table_name": {
                        "type": "string",
                        "description": "Name of the table"
                    },
                    "data": {
                        "type": "array",
                        "description": "Array of objects representing rows to insert",
                        "items": {"type": "object"}
                    }
                },
                "required": ["database_name", "table_name", "data"]
            }
        ),
        Tool(
            name="query_data",
            description="Query data from a table with optional conditions",
            inputSchema={
                "type": "object",
                "properties": {
                    "database_name": {
                        "type": "string",
                        "description": "Name of the database"
                    },
                    "table_name": {
                        "type": "string",
                        "description": "Name of the table"
                    },
                    "columns": {
                        "type": "array",
                        "description": "Columns to select (default: all)",
                        "items": {"type": "string"}
                    },
                    "where_clause": {
                        "type": "string",
                        "description": "WHERE clause conditions"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of rows to return"
                    }
                },
                "required": ["database_name", "table_name"]
            }
        ),
        Tool(
            name="backup_database",
            description="Create a backup of the database",
            inputSchema={
                "type": "object",
                "properties": {
                    "database_name": {
                        "type": "string",
                        "description": "Name of the database to backup"
                    },
                    "backup_name": {
                        "type": "string",
                        "description": "Name for the backup file"
                    }
                },
                "required": ["database_name"]
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle tool calls"""
    
    try:
        if name == "create_database":
            db_name = arguments["database_name"]
            db_path = get_safe_db_path(db_name)
            
            if db_path.exists():
                return [TextContent(type="text", text=f"Database {db_name} already exists")]
            
            # Create database by connecting to it
            conn = sqlite3.connect(str(db_path))
            conn.close()
            
            result = {
                "database_name": db_name,
                "path": str(db_path),
                "status": "created"
            }
            
            return [TextContent(type="text", text=json.dumps(result, indent=2))]
        
        elif name == "execute_sql":
            db_name = arguments["database_name"]
            query = arguments["query"]
            parameters = arguments.get("parameters", [])
            
            db_path = get_safe_db_path(db_name)
            
            if not db_path.exists():
                return [TextContent(type="text", text=f"Database {db_name} does not exist")]
            
            result = sqlite_manager.execute_query(db_path, query, parameters)
            result["database"] = db_name
            result["query"] = query
            
            return [TextContent(type="text", text=json.dumps(result, indent=2))]
        
        elif name == "list_tables":
            db_name = arguments["database_name"]
            db_path = get_safe_db_path(db_name)
            
            if not db_path.exists():
                return [TextContent(type="text", text=f"Database {db_name} does not exist")]
            
            tables = sqlite_manager.list_tables(db_path)
            
            result = {
                "database": db_name,
                "tables": tables,
                "table_count": len(tables)
            }
            
            return [TextContent(type="text", text=json.dumps(result, indent=2))]
        
        elif name == "describe_table":
            db_name = arguments["database_name"]
            table_name = arguments["table_name"]
            db_path = get_safe_db_path(db_name)
            
            if not db_path.exists():
                return [TextContent(type="text", text=f"Database {db_name} does not exist")]
            
            table_info = sqlite_manager.get_table_info(db_path, table_name)
            table_info["database"] = db_name
            
            return [TextContent(type="text", text=json.dumps(table_info, indent=2))]
        
        elif name == "create_table":
            db_name = arguments["database_name"]
            table_name = arguments["table_name"]
            columns = arguments["columns"]
            
            db_path = get_safe_db_path(db_name)
            
            if not db_path.exists():
                return [TextContent(type="text", text=f"Database {db_name} does not exist")]
            
            # Build CREATE TABLE query
            column_defs = []
            for col in columns:
                col_def = f"{col['name']} {col['type']}"
                if col.get('constraints'):
                    col_def += f" {col['constraints']}"
                column_defs.append(col_def)
            
            query = f"CREATE TABLE {table_name} ({', '.join(column_defs)})"
            
            result = sqlite_manager.execute_query(db_path, query)
            result["database"] = db_name
            result["table_name"] = table_name
            result["query"] = query
            
            return [TextContent(type="text", text=json.dumps(result, indent=2))]
        
        elif name == "insert_data":
            db_name = arguments["database_name"]
            table_name = arguments["table_name"]
            data = arguments["data"]
            
            db_path = get_safe_db_path(db_name)
            
            if not db_path.exists():
                return [TextContent(type="text", text=f"Database {db_name} does not exist")]
            
            if not data:
                return [TextContent(type="text", text="No data provided")]
            
            # Get column names from first row
            columns = list(data[0].keys())
            placeholders = ', '.join(['?' for _ in columns])
            
            query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
            
            conn = sqlite_manager.get_connection(db_path)
            cursor = conn.cursor()
            
            try:
                rows_inserted = 0
                for row in data:
                    values = [row.get(col) for col in columns]
                    cursor.execute(query, values)
                    rows_inserted += 1
                
                conn.commit()
                
                result = {
                    "database": db_name,
                    "table_name": table_name,
                    "rows_inserted": rows_inserted,
                    "status": "success"
                }
                
                return [TextContent(type="text", text=json.dumps(result, indent=2))]
                
            except sqlite3.Error as e:
                conn.rollback()
                raise Exception(f"SQLite error: {str(e)}")
            finally:
                cursor.close()
        
        elif name == "query_data":
            db_name = arguments["database_name"]
            table_name = arguments["table_name"]
            columns = arguments.get("columns", ["*"])
            where_clause = arguments.get("where_clause")
            limit = arguments.get("limit")
            
            db_path = get_safe_db_path(db_name)
            
            if not db_path.exists():
                return [TextContent(type="text", text=f"Database {db_name} does not exist")]
            
            # Build SELECT query
            if columns == ["*"] or not columns:
                column_str = "*"
            else:
                column_str = ", ".join(columns)
            
            query = f"SELECT {column_str} FROM {table_name}"
            
            if where_clause:
                query += f" WHERE {where_clause}"
            
            if limit:
                query += f" LIMIT {limit}"
            
            result = sqlite_manager.execute_query(db_path, query)
            result["database"] = db_name
            result["table_name"] = table_name
            result["query"] = query
            
            return [TextContent(type="text", text=json.dumps(result, indent=2))]
        
        elif name == "backup_database":
            db_name = arguments["database_name"]
            backup_name = arguments.get("backup_name", f"{db_name}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            
            db_path = get_safe_db_path(db_name)
            backup_path = get_safe_db_path(backup_name)
            
            if not db_path.exists():
                return [TextContent(type="text", text=f"Database {db_name} does not exist")]
            
            # Copy database file
            import shutil
            shutil.copy2(db_path, backup_path)
            
            result = {
                "original_database": db_name,
                "backup_database": backup_name,
                "backup_path": str(backup_path),
                "status": "backup_created"
            }
            
            return [TextContent(type="text", text=json.dumps(result, indent=2))]
        
        else:
            return [TextContent(type="text", text=f"Unknown tool: {name}")]
    
    except Exception as e:
        return [TextContent(type="text", text=f"Error: {str(e)}")]

async def main():
    """Main server function"""
    from mcp.types import ServerCapabilities
    try:
        async with stdio_server() as (read_stream, write_stream):
            await server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="sqlite",
                    server_version="1.0.0",
                    capabilities=ServerCapabilities(
                        tools={}
                    ),
                ),
            )
    finally:
        sqlite_manager.close_all_connections()

if __name__ == "__main__":
    asyncio.run(main())