# Pandas MCP Server

A Model Context Protocol (MCP) server for pandas data analysis and visualization, based on the [pandas-mcp-server](https://github.com/marlonluo2018/pandas-mcp-server) project.

## Features

This MCP server provides three main tools for data analysis:

### 1. `read_metadata`
Read comprehensive metadata from Excel and CSV files including:
- File type, size, encoding, and structure
- Column names, data types, and sample values
- Statistical summaries (null counts, unique values, min/max/mean)
- Data quality warnings and suggested operations
- Memory-optimized processing for large files

### 2. `run_pandas_code`
Execute pandas operations with:
- Security filtering against malicious code
- Memory optimization for large datasets
- Comprehensive error handling and debugging
- Support for DataFrame, Series, and dictionary results

### 3. `generate_chartjs`
Generate interactive charts with Chart.js:
- **Bar charts** - For categorical comparisons
- **Line charts** - For trend analysis
- **Pie charts** - For proportional data
- Interactive HTML templates with customization controls

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Ensure the pandas_mcp directory is properly set up in the parent directory.

## Usage

### Starting the Server

```bash
python server.py
```

### Tool Examples

#### Reading File Metadata
```json
{
  "tool": "read_metadata",
  "args": {
    "file_path": "/path/to/sales_data.xlsx"
  }
}
```

#### Executing Pandas Code
```json
{
  "tool": "run_pandas_code",
  "args": {
    "code": "import pandas as pd\ndf = pd.read_excel('/path/to/data.xlsx')\nresult = df.groupby('Region')['Sales'].sum()"
  }
}
```

#### Generating Charts
```json
{
  "tool": "generate_chartjs",
  "args": {
    "data": {
      "columns": [
        {
          "name": "Region",
          "type": "string",
          "examples": ["North", "South", "East", "West"]
        },
        {
          "name": "Sales",
          "type": "number",
          "examples": [15000, 12000, 18000, 9000]
        }
      ]
    },
    "chart_types": ["bar"],
    "title": "Sales by Region"
  }
}
```

## Configuration

The server uses the pandas_mcp core modules for:
- File metadata extraction
- Secure code execution
- Chart generation

## Security Features

- Code execution sandboxing
- Blacklisted operations (file system, network, eval)
- Memory usage monitoring
- Input validation and sanitization

## Performance Optimization

- Chunked processing for large files
- Automatic garbage collection
- Memory usage logging
- Dataset size limits

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure pandas_mcp directory is properly set up
2. **File Not Found**: Verify file paths are absolute
3. **Memory Issues**: Monitor memory usage for large datasets
4. **Chart Generation Errors**: Check data structure format

### Debug Mode

Enable debug logging by setting environment variable:
```bash
export LOG_LEVEL=DEBUG
python server.py
```

## License

This project is based on the pandas-mcp-server project and is licensed under the MIT License. 