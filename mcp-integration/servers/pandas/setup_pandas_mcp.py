#!/usr/bin/env python3
"""
Setup script for pandas MCP server
"""

import os
import subprocess
import sys
from pathlib import Path

def install_dependencies():
    """Install required dependencies."""
    print("📦 Installing pandas MCP dependencies...")
    
    try:
        # Install pandas MCP dependencies
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "pandas>=2.0.0",
            "chardet>=5.0.0", 
            "fastmcp>=1.0.0",
            "psutil>=5.9.0",
            "openpyxl>=3.1.0",
            "xlrd>=2.0.0"
        ], check=True)
        print("✅ Pandas MCP dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def verify_pandas_mcp_setup():
    """Verify pandas_mcp directory is properly set up."""
    pandas_mcp_dir = Path(__file__).parent.parent / "pandas_mcp"
    
    if not pandas_mcp_dir.exists():
        print(f"❌ pandas_mcp directory not found at: {pandas_mcp_dir}")
        return False
    
    required_files = [
        "server.py",
        "core/config.py",
        "core/metadata.py", 
        "core/execution.py",
        "core/visualization.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = pandas_mcp_dir / file_path
        if not full_path.exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    
    print("✅ Pandas MCP directory structure verified")
    return True

def test_imports():
    """Test if all required modules can be imported."""
    print("🧪 Testing module imports...")
    
    try:
        # Add pandas_mcp to path
        pandas_mcp_dir = Path(__file__).parent.parent / "pandas_mcp"
        sys.path.insert(0, str(pandas_mcp_dir))
        
        # Test imports
        from core.metadata import read_metadata
        from core.execution import run_pandas_code
        from core.visualization import generate_chartjs
        
        print("✅ All pandas MCP modules imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import test failed: {e}")
        return False

def create_test_data():
    """Create test data files for testing."""
    print("📁 Creating test data files...")
    
    test_dir = Path(__file__).parent / "test_data"
    test_dir.mkdir(exist_ok=True)
    
    # Create test CSV file
    test_csv = test_dir / "sample_data.csv"
    test_csv.write_text("""Name,Age,City,Salary
John,25,New York,50000
Jane,30,Los Angeles,60000
Bob,35,Chicago,55000
Alice,28,Boston,52000
Charlie,32,Seattle,58000""")
    
    # Create test Excel file (if openpyxl is available)
    try:
        import pandas as pd
        test_excel = test_dir / "sample_data.xlsx"
        
        df = pd.DataFrame({
            'Name': ['John', 'Jane', 'Bob', 'Alice', 'Charlie'],
            'Age': [25, 30, 35, 28, 32],
            'City': ['New York', 'Los Angeles', 'Chicago', 'Boston', 'Seattle'],
            'Salary': [50000, 60000, 55000, 52000, 58000]
        })
        
        df.to_excel(test_excel, index=False)
        print("✅ Test data files created successfully")
        return True
    except ImportError:
        print("⚠️  openpyxl not available, skipping Excel test file")
        return True
    except Exception as e:
        print(f"❌ Failed to create test data: {e}")
        return False

def run_tests():
    """Run the test suite."""
    print("🧪 Running pandas MCP tests...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_server.py"
        ], cwd=Path(__file__).parent, capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        if result.returncode == 0:
            print("✅ All tests passed!")
            return True
        else:
            print("❌ Some tests failed")
            return False
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        return False

def main():
    """Main setup function."""
    print("🚀 Setting up Pandas MCP Server...")
    print("=" * 50)
    
    steps = [
        ("Installing Dependencies", install_dependencies),
        ("Verifying Setup", verify_pandas_mcp_setup),
        ("Testing Imports", test_imports),
        ("Creating Test Data", create_test_data),
        ("Running Tests", run_tests)
    ]
    
    passed = 0
    total = len(steps)
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if step_func():
            passed += 1
        else:
            print(f"❌ {step_name} failed")
    
    print("\n" + "=" * 50)
    print(f"📊 Setup Results: {passed}/{total} steps completed successfully")
    
    if passed == total:
        print("🎉 Pandas MCP server setup completed successfully!")
        print("\n📝 Next steps:")
        print("1. Start the server: python server.py")
        print("2. Test with MCP client")
        print("3. Integrate with your MCP system")
        return True
    else:
        print("⚠️  Setup incomplete. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 