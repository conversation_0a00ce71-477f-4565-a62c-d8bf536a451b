#!/usr/bin/env python3
"""
Enhanced Pandas MCP Server
Full-featured pandas operations with graceful error handling
"""

import asyncio
import json
import logging
import sys
import os
import io
import traceback
from datetime import datetime
from typing import Any, Dict, List, Optional
import base64

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.server.lowlevel.server import NotificationOptions
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("enhanced-pandas-mcp-server")

# Initialize the MCP server
server = Server("enhanced_pandas")

# Try to import pandas and related libraries
try:
    import pandas as pd
    import numpy as np
    PANDAS_AVAILABLE = True
    logger.info("✅ Pandas and numpy imported successfully")
except ImportError as e:
    PANDAS_AVAILABLE = False
    logger.warning(f"⚠️ Pandas/numpy not available: {e}")

# Try to import additional libraries
try:
    import matplotlib
    matplotlib.use('Agg')  # Use non-interactive backend
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
    logger.info("✅ Matplotlib available")
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    logger.warning("⚠️ Matplotlib not available")

class EnhancedPandasProcessor:
    """Enhanced pandas data processor with full features"""
    
    def __init__(self):
        self._initialized = PANDAS_AVAILABLE
        self.dataframes = {}  # Store dataframes by name
        self.metadata_cache = {}  # Cache for file metadata
    
    async def initialize(self):
        """Initialize the processor"""
        if not PANDAS_AVAILABLE:
            logger.warning("Pandas not available - running in limited mode")
            return False
        self._initialized = True
        logger.info("✅ Enhanced Pandas Processor initialized")
        return True
    
    async def read_file_metadata(self, file_path: str) -> Dict[str, Any]:
        """Read file metadata (Excel or CSV) and return structured information"""
        if not self._initialized:
            return {"success": False, "error": "Pandas not available"}
        
        try:
            if not os.path.exists(file_path):
                return {"success": False, "error": f"File not found: {file_path}"}
            
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.csv':
                # Read CSV metadata
                df = pd.read_csv(file_path, nrows=100)  # Sample first 100 rows
                
                metadata = {
                    "success": True,
                    "file_info": {
                        "type": "csv",
                        "path": file_path,
                        "size_bytes": os.path.getsize(file_path)
                    },
                    "data": {
                        "rows": len(df),
                        "columns": len(df.columns),
                        "column_info": []
                    }
                }
                
                # Analyze each column
                for col in df.columns:
                    col_info = {
                        "name": col,
                        "type": str(df[col].dtype),
                        "null_count": int(df[col].isnull().sum()),
                        "unique_count": int(df[col].nunique()),
                        "examples": df[col].dropna().head(3).tolist()
                    }
                    
                    if df[col].dtype in ['int64', 'float64']:
                        col_info["stats"] = {
                            "mean": float(df[col].mean()) if not df[col].isnull().all() else None,
                            "min": float(df[col].min()) if not df[col].isnull().all() else None,
                            "max": float(df[col].max()) if not df[col].isnull().all() else None
                        }
                    
                    metadata["data"]["column_info"].append(col_info)
                
                return metadata
                
            elif file_ext in ['.xlsx', '.xls']:
                # Read Excel metadata
                excel_file = pd.ExcelFile(file_path)
                sheets_info = []
                
                for sheet_name in excel_file.sheet_names:
                    df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=100)
                    sheet_info = {
                        "sheet_name": sheet_name,
                        "rows": len(df),
                        "columns": len(df.columns),
                        "column_names": df.columns.tolist()
                    }
                    sheets_info.append(sheet_info)
                
                return {
                    "success": True,
                    "file_info": {
                        "type": "excel",
                        "path": file_path,
                        "sheet_count": len(excel_file.sheet_names),
                        "sheet_names": excel_file.sheet_names
                    },
                    "data": {
                        "sheets": sheets_info
                    }
                }
            
            else:
                return {"success": False, "error": f"Unsupported file type: {file_ext}"}
                
        except Exception as e:
            return {"success": False, "error": str(e), "traceback": traceback.format_exc()}
    
    async def execute_pandas_code(self, code: str, df_name: str = None) -> Dict[str, Any]:
        """Execute pandas code with security checks"""
        if not self._initialized:
            return {"success": False, "error": "Pandas not available"}
        
        try:
            # Security check - basic filtering
            dangerous_keywords = ['import', 'exec', 'eval', 'open', 'file', '__', 'os.', 'sys.']
            if any(keyword in code.lower() for keyword in dangerous_keywords):
                return {"success": False, "error": "Code contains potentially dangerous operations"}
            
            # Prepare execution environment
            exec_globals = {
                'pd': pd,
                'np': np,
                'dataframes': self.dataframes
            }
            
            if df_name and df_name in self.dataframes:
                exec_globals['df'] = self.dataframes[df_name]
            
            # Capture output
            old_stdout = sys.stdout
            sys.stdout = captured_output = io.StringIO()
            
            try:
                # Execute code
                exec(code, exec_globals)
                output = captured_output.getvalue()
                
                # Check if any new dataframes were created
                new_dfs = {}
                for key, value in exec_globals.items():
                    if isinstance(value, pd.DataFrame) and key not in ['pd', 'np', 'dataframes']:
                        new_dfs[key] = {
                            "shape": value.shape,
                            "columns": value.columns.tolist(),
                            "head": value.head().to_dict('records') if len(value) > 0 else []
                        }
                        # Store the dataframe
                        self.dataframes[key] = value
                
                return {
                    "success": True,
                    "output": output,
                    "new_dataframes": new_dfs,
                    "execution_time": datetime.now().isoformat()
                }
                
            finally:
                sys.stdout = old_stdout
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "traceback": traceback.format_exc()
            }
    
    async def create_visualization(self, df_name: str, chart_type: str = "bar", 
                                 x_col: str = None, y_col: str = None, 
                                 title: str = "Data Visualization") -> Dict[str, Any]:
        """Create visualization from dataframe"""
        if not self._initialized:
            return {"success": False, "error": "Pandas not available"}
        
        if not MATPLOTLIB_AVAILABLE:
            return {"success": False, "error": "Matplotlib not available for visualization"}
        
        if df_name not in self.dataframes:
            return {"success": False, "error": f"Dataframe '{df_name}' not found"}
        
        try:
            df = self.dataframes[df_name]
            
            plt.figure(figsize=(10, 6))
            
            if chart_type == "bar" and x_col and y_col:
                plt.bar(df[x_col], df[y_col])
                plt.xlabel(x_col)
                plt.ylabel(y_col)
            elif chart_type == "line" and x_col and y_col:
                plt.plot(df[x_col], df[y_col])
                plt.xlabel(x_col)
                plt.ylabel(y_col)
            elif chart_type == "hist" and x_col:
                plt.hist(df[x_col], bins=20)
                plt.xlabel(x_col)
                plt.ylabel("Frequency")
            else:
                # Default: show basic info
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 0:
                    df[numeric_cols].plot(kind='bar')
                else:
                    return {"success": False, "error": "No suitable columns for visualization"}
            
            plt.title(title)
            plt.tight_layout()
            
            # Save to base64
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return {
                "success": True,
                "chart_type": chart_type,
                "image_base64": image_base64,
                "title": title
            }
            
        except Exception as e:
            return {"success": False, "error": str(e), "traceback": traceback.format_exc()}
    
    async def load_dataframe(self, file_path: str, df_name: str, **kwargs) -> Dict[str, Any]:
        """Load dataframe from file"""
        if not self._initialized:
            return {"success": False, "error": "Pandas not available"}
        
        try:
            if not os.path.exists(file_path):
                return {"success": False, "error": f"File not found: {file_path}"}
            
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.csv':
                df = pd.read_csv(file_path, **kwargs)
            elif file_ext in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path, **kwargs)
            else:
                return {"success": False, "error": f"Unsupported file type: {file_ext}"}
            
            self.dataframes[df_name] = df
            
            return {
                "success": True,
                "dataframe_name": df_name,
                "shape": df.shape,
                "columns": df.columns.tolist(),
                "head": df.head().to_dict('records'),
                "dtypes": df.dtypes.to_dict()
            }
            
        except Exception as e:
            return {"success": False, "error": str(e), "traceback": traceback.format_exc()}
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        return {
            "status": "healthy" if self._initialized else "degraded",
            "pandas_available": PANDAS_AVAILABLE,
            "matplotlib_available": MATPLOTLIB_AVAILABLE,
            "dataframes_count": len(self.dataframes),
            "dataframes": list(self.dataframes.keys()),
            "features": {
                "file_reading": PANDAS_AVAILABLE,
                "code_execution": PANDAS_AVAILABLE,
                "visualization": PANDAS_AVAILABLE and MATPLOTLIB_AVAILABLE,
                "metadata_analysis": PANDAS_AVAILABLE
            },
            "timestamp": datetime.now().isoformat()
        }

# Global processor instance
pandas_processor = EnhancedPandasProcessor()

@server.list_resources()
async def list_resources() -> List[Resource]:
    """List available Pandas resources"""
    return [
        Resource(
            uri="pandas://processor",
            name="Enhanced Pandas Data Processor",
            description="Full-featured pandas operations with file I/O and visualization",
            mimeType="application/json",
        ),
        Resource(
            uri="pandas://health",
            name="Health Status",
            description="Processor health and available features",
            mimeType="application/json",
        )
    ]

@server.read_resource()
async def read_resource(uri: str) -> str:
    """Read Pandas resource"""
    if uri == "pandas://processor":
        return json.dumps({
            "description": "Enhanced pandas processor with full feature set",
            "features": [
                "File metadata reading (CSV, Excel)",
                "Pandas code execution with security checks",
                "Data visualization with matplotlib",
                "Dataframe loading and management",
                "Statistical operations"
            ],
            "available": PANDAS_AVAILABLE,
            "visualization_available": MATPLOTLIB_AVAILABLE
        }, indent=2)
    
    elif uri == "pandas://health":
        try:
            health = await pandas_processor.health_check()
            return json.dumps(health, indent=2)
        except Exception as e:
            return json.dumps({"error": str(e)}, indent=2)
    
    raise ValueError(f"Unknown resource: {uri}")

@server.list_tools()
async def list_tools() -> List[Tool]:
    """List available Pandas tools"""
    return [
        Tool(
            name="read_file_metadata",
            description="Read metadata from CSV or Excel files",
            inputSchema={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the data file (CSV or Excel)"
                    }
                },
                "required": ["file_path"]
            }
        ),
        Tool(
            name="load_dataframe",
            description="Load dataframe from file",
            inputSchema={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the data file"
                    },
                    "dataframe_name": {
                        "type": "string",
                        "description": "Name to assign to the loaded dataframe"
                    },
                    "sheet_name": {
                        "type": "string",
                        "description": "Sheet name for Excel files (optional)"
                    }
                },
                "required": ["file_path", "dataframe_name"]
            }
        ),
        Tool(
            name="execute_pandas_code",
            description="Execute pandas code with security checks",
            inputSchema={
                "type": "object",
                "properties": {
                    "code": {
                        "type": "string",
                        "description": "Pandas code to execute"
                    },
                    "dataframe_name": {
                        "type": "string",
                        "description": "Name of dataframe to use as 'df' in code (optional)"
                    }
                },
                "required": ["code"]
            }
        ),
        Tool(
            name="create_visualization",
            description="Create visualization from dataframe",
            inputSchema={
                "type": "object",
                "properties": {
                    "dataframe_name": {
                        "type": "string",
                        "description": "Name of dataframe to visualize"
                    },
                    "chart_type": {
                        "type": "string",
                        "enum": ["bar", "line", "hist"],
                        "description": "Type of chart to create"
                    },
                    "x_column": {
                        "type": "string",
                        "description": "Column for X axis"
                    },
                    "y_column": {
                        "type": "string",
                        "description": "Column for Y axis"
                    },
                    "title": {
                        "type": "string",
                        "description": "Chart title"
                    }
                },
                "required": ["dataframe_name"]
            }
        ),
        Tool(
            name="health_check",
            description="Check the health status of the pandas processor",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle tool calls"""
    
    try:
        # Initialize processor if needed
        if not pandas_processor._initialized and PANDAS_AVAILABLE:
            await pandas_processor.initialize()
        
        if name == "read_file_metadata":
            file_path = arguments["file_path"]
            logger.info(f"📊 Reading metadata for: {file_path}")
            
            result = await pandas_processor.read_file_metadata(file_path)
            
            response = {
                **result,
                "timestamp": datetime.now().isoformat(),
                "processor_type": "enhanced_pandas"
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2, ensure_ascii=False))]
        
        elif name == "load_dataframe":
            file_path = arguments["file_path"]
            df_name = arguments["dataframe_name"]
            sheet_name = arguments.get("sheet_name")
            
            logger.info(f"📥 Loading dataframe '{df_name}' from: {file_path}")
            
            kwargs = {}
            if sheet_name:
                kwargs["sheet_name"] = sheet_name
            
            result = await pandas_processor.load_dataframe(file_path, df_name, **kwargs)
            
            response = {
                **result,
                "timestamp": datetime.now().isoformat()
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2, ensure_ascii=False))]
        
        elif name == "execute_pandas_code":
            code = arguments["code"]
            df_name = arguments.get("dataframe_name")
            
            logger.info(f"🐍 Executing pandas code (df: {df_name})")
            
            result = await pandas_processor.execute_pandas_code(code, df_name)
            
            response = {
                **result,
                "timestamp": datetime.now().isoformat()
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2, ensure_ascii=False))]
        
        elif name == "create_visualization":
            df_name = arguments["dataframe_name"]
            chart_type = arguments.get("chart_type", "bar")
            x_col = arguments.get("x_column")
            y_col = arguments.get("y_column")
            title = arguments.get("title", "Data Visualization")
            
            logger.info(f"📈 Creating {chart_type} chart for: {df_name}")
            
            result = await pandas_processor.create_visualization(df_name, chart_type, x_col, y_col, title)
            
            response = {
                **result,
                "timestamp": datetime.now().isoformat()
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2, ensure_ascii=False))]
        
        elif name == "health_check":
            logger.info("🏥 Performing health check")
            
            health = await pandas_processor.health_check()
            
            response = {
                "timestamp": datetime.now().isoformat(),
                "health_check": health,
                "server_status": "operational" if health.get("status") == "healthy" else "degraded"
            }
            
            return [TextContent(type="text", text=json.dumps(response, indent=2))]
        
        else:
            return [TextContent(type="text", text=f"Unknown tool: {name}")]
    
    except Exception as e:
        logger.error(f"Error in tool {name}: {str(e)}")
        error_response = {
            "error": str(e),
            "tool": name,
            "pandas_available": PANDAS_AVAILABLE,
            "traceback": traceback.format_exc(),
            "timestamp": datetime.now().isoformat()
        }
        return [TextContent(type="text", text=json.dumps(error_response, indent=2))]

async def main():
    """Main server function"""
    try:
        async with stdio_server() as (read_stream, write_stream):
            await server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="enhanced_pandas",
                    server_version="1.0.0",
                    capabilities=server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )
    finally:
        logger.info("✅ Enhanced Pandas MCP Server shutdown")

if __name__ == "__main__":
    asyncio.run(main())