# 🚀 Improved Gemini 2.5 Flash Lite Prompts trong Improved <PERSON><PERSON> Crawler

## 📋 **Prompt mới được cải tiến**

### **News Extraction (Mới - Chính)**
```text
Bạn là trợ lý chuyên tổng hợp tin tức từ các trang công nghệ và báo chí. Hãy phân tích nội dung sau và trích xuất TẤT CẢ tin tức, theo cấu trúc sau:

### [Tên chuyên mục]
- **[Tiêu đề tin]**
  [Mô tả ngắn 2–3 câu, giữ nguyên tiếng Việt]
  *(Thời gian: X giờ trước / Ngày DD/MM)* – [Xem chi tiết](URL)

---

**Yêu cầu:**
- Giữ nguyên toàn bộ tin, không lược bỏ (trừ quảng cáo, menu, footer)
- <PERSON><PERSON> theo chuyên mục: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ớ<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> tr<PERSON>, <PERSON><PERSON><PERSON> thao...
- Ưu tiên tin mới nhất lên đầu
- Nếu không có thời gian, ghi "(chưa rõ thời gian)"
- Nếu không có link, không thêm

**Input:**
{content}

**Output:**
```

## ⚙️ **Cấu hình API được cải tiến**

### **Model**: `gemini-2.5-flash-lite`
### **Generation Config**:
```json
{
  "temperature": 0.0,
  "maxOutputTokens": 8192,  // Tăng từ 2048 lên 8192
  "topK": 1,
  "topP": 1.0
}
```

## 🎯 **Quy trình xử lý mới**

1. **Crawl HTML** → BeautifulSoup cleaning → **Extract main content**
2. **Truncate content** (max 20000 chars) → **Apply news_extraction prompt**
3. **Call Gemini API** → **Return structured news output**

## 🇻🇳 **Tối ưu cho tiếng Việt (Cải tiến)**

### **Smart Truncation**:
- Tìm vị trí kết thúc câu tiếng Việt hợp lý
- Các dấu kết thúc: `. `, `.\n`, `? `, `?\n`, `! `, `!\n`, v.v.
- **Tăng giới hạn từ 8000 lên 20000 ký tự** để giữ nhiều tin hơn

### **Vietnamese Sentence Endings**:
```python
vietnamese_endings = [
    '. ', '.\n', '? ', '?\n', '! ', '!\n',
    '." ', '."', '?" ', '?"', '!" ', '!"',
    '.)', '.)', '?)', '?)', '!)', '!)'
]
```

## 📊 **Thông số hiệu suất cải tiến**

- **Max content length**: `20000` characters (tăng từ 8000)
- **Max output tokens**: `8192` tokens (tăng từ 2048)
- **Processing time**: ~3-5 seconds (vì content lớn hơn)
- **Temperature**: `0.0` (đầu ra xác định)

## 🧪 **Test với nội dung mẫu**

### **Input HTML**:
```html
<div class="news-container">
  <article>
    <h2>Công nghệ AI phát triển vượt bậc</h2>
    <p>Trí tuệ nhân tạo đang thay đổi cách chúng ta làm việc...</p>
    <time>2024-01-15</time>
    <a href="/ai-phat-trien">Xem chi tiết</a>
  </article>
  <article>
    <h2>Thế giới số hóa mạnh mẽ</h2>
    <p>Chuyển đổi số đang diễn ra trên toàn cầu...</p>
    <time>2024-01-14</time>
    <a href="/so-hoa-the-gioi">Xem chi tiết</a>
  </article>
</div>
```

### **Output Markdown**:
```markdown
### Công nghệ
- **Công nghệ AI phát triển vượt bậc**
  Trí tuệ nhân tạo đang thay đổi cách chúng ta làm việc...
  *(Thời gian: 15/01)* – [Xem chi tiết](/ai-phat-trien)

### Thế giới
- **Thế giới số hóa mạnh mẽ**
  Chuyển đổi số đang diễn ra trên toàn cầu...
  *(Thời gian: 14/01)* – [Xem chi tiết](/so-hoa-the-gioi)
```

## 🔧 **Retry Logic**

### **Exponential Backoff**:
```python
for attempt in range(max_retries + 1):
    try:
        # API call
    except Exception:
        wait_time = 2 ** attempt  # 1s, 2s, 4s, 8s...
        await asyncio.sleep(wait_time)
```

## 🚀 **Ưu điểm cải tiến**

1. **Giữ nguyên toàn bộ tin**: Không bị mất tin như trước
2. **Định dạng rõ ràng**: Cấu trúc chuyên mục và tin tức dễ đọc
3. **Tốc độ xử lý**: Vẫn nhanh nhờ Gemini Flash Lite
4. **Tối ưu tiếng Việt**: Bảo tồn chính xác văn bản
5. **Tăng dung lượng**: 20K chars giúp xử lý nhiều tin hơn
6. **Đa dạng task**: Hỗ trợ nhiều loại xử lý khác nhau

## 📈 **Use Cases**

### **1. News Aggregation**:
- Tổng hợp tin tức từ nhiều nguồn
- Phân loại theo chuyên mục
- Giữ nguyên tất cả tin tức

### **2. Content Curation**:
- Trích xuất tin tức có cấu trúc
- Chuẩn bị cho AI processing tiếp theo

### **3. Research & Analysis**:
- Xử lý trang tin tức lớn
- Trích xuất thông tin chi tiết

## 🛠️ **Các loại task được hỗ trợ**

1. **news_extraction** - Trích xuất tin tức (mặc định)
2. **html_to_markdown** - Chuyển HTML sang Markdown
3. **summarize** - Tóm tắt nội dung
4. **clean** - Làm sạch nội dung

---

**📝 Note**: Prompt mới `news_extraction` giúp trích xuất TẤT CẢ tin tức với cấu trúc rõ ràng, phân loại theo chuyên mục, ưu tiên tin mới nhất và giữ nguyên tiếng Việt.