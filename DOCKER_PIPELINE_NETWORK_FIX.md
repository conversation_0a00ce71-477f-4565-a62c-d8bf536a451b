# Docker Pipeline Network Fix - Final Solution

## 🎯 Problem Analysis
- **Issue**: Open WebUI (Docker) cannot connect to `http://pipelines:9099`
- **Root Cause**: Pipeline server running on host `localhost:9099`, not accessible from Docker container
- **Solution**: Run pipeline server in same Docker network as Open WebUI

## 🐳 Docker Network Solution

### Current Setup
- **Open WebUI Container**: `catomanton-webui` in network `acca_catomanton-network`
- **Pipeline Server**: Running on host `localhost:9099` (not accessible from container)

### Solution Implemented
1. **Pipeline Container**: `acca-pipelines` in same network `acca_catomanton-network`
2. **Network Access**: Both containers can communicate via container names
3. **Port Mapping**: Host port 9099 still mapped for external access

## 📁 Files Created

### 1. Dockerfile.pipeline
```dockerfile
FROM python:3.11-slim
WORKDIR /app
RUN apt-get update && apt-get install -y curl
COPY requirements.pipeline.txt .
RUN pip install --no-cache-dir -r requirements.pipeline.txt
COPY simple_rag_pipeline.py .
COPY webui-data/pipelines/ ./pipelines/
EXPOSE 9099
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:9099/health || exit 1
CMD ["python3", "simple_rag_pipeline.py"]
```

### 2. docker-compose.pipeline.yml
```yaml
services:
  acca-pipelines:
    build:
      context: .
      dockerfile: Dockerfile.pipeline
    container_name: acca-pipelines
    ports:
      - "9099:9099"
    networks:
      - acca_catomanton-network
    volumes:
      - ./webui-data/pipelines:/app/pipelines
    restart: unless-stopped

networks:
  acca_catomanton-network:
    external: true
```

### 3. requirements.pipeline.txt
```
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.0.0
mem0ai
qdrant-client
google-generativeai
aiohttp>=3.9.0
requests>=2.31.0
```

## 🚀 Deployment Commands

### Build and Start Pipeline Container
```bash
docker compose -f docker-compose.pipeline.yml up --build -d
```

### Verify Container Status
```bash
docker ps | grep acca-pipelines
docker logs acca-pipelines
```

### Test Network Connectivity
```bash
# From Open WebUI container
docker exec catomanton-webui curl http://acca-pipelines:9099/health

# From host
curl http://localhost:9099/health
```

## ⚙️ Open WebUI Configuration

### Connection Settings
- **API URL**: `http://acca-pipelines:9099` (container name, not localhost)
- **API Key**: `0p3n-w3bu!`

### Pipeline Configuration Steps
1. **Admin Panel → Settings → Connections**
2. **Add Connection**: `http://acca-pipelines:9099` with key `0p3n-w3bu!`
3. **Admin Panel → Settings → Pipelines**
4. **Configure AccA Mem0 Memory Filter** with Gemini API key

## 🔍 Network Verification

### Container Network Info
```bash
# Check container network
docker inspect acca-pipelines | grep -A 10 "Networks"
docker inspect catomanton-webui | grep -A 10 "Networks"

# Both should show "acca_catomanton-network"
```

### Connectivity Tests
```bash
# Test from Open WebUI container to pipeline
docker exec catomanton-webui curl -s http://acca-pipelines:9099/health

# Test from pipeline container to verify it's running
docker exec acca-pipelines curl -s http://localhost:9099/health

# Test from host
curl http://localhost:9099/health
```

## 🎉 Expected Results

### Successful Deployment
- ✅ Pipeline container running in `acca_catomanton-network`
- ✅ Open WebUI can connect to `http://acca-pipelines:9099`
- ✅ Pipeline server accessible from both container and host
- ✅ Filter pipeline loaded and functional

### Open WebUI Admin Panel
- ✅ Connection shows "Pipelines" icon
- ✅ Pipeline status: "Active"
- ✅ AccA Mem0 Memory Filter available for configuration

## 🛠️ Troubleshooting

### Container Not Starting
```bash
docker logs acca-pipelines
docker compose -f docker-compose.pipeline.yml logs
```

### Network Issues
```bash
# Verify network exists
docker network ls | grep acca_catomanton-network

# Check container connectivity
docker exec catomanton-webui ping acca-pipelines
```

### Pipeline Not Loading
```bash
# Check pipeline files
docker exec acca-pipelines ls -la /app/pipelines/
docker exec acca-pipelines curl http://localhost:9099/pipelines
```

## 📋 Final Configuration

Once container is running, configure in Open WebUI:

1. **Connection**: `http://acca-pipelines:9099`
2. **Pipeline Valves**:
   ```json
   {
     "mem0_enabled": true,
     "gemini_api_key": "YOUR_GEMINI_API_KEY",
     "qdrant_host": "localhost",
     "qdrant_port": 6333,
     "memory_relevance_threshold": 0.2,
     "max_memories_to_inject": 3,
     "debug_logging": true
   }
   ```

This solution resolves the Docker networking issue by running the pipeline server in the same network as Open WebUI, enabling proper container-to-container communication.