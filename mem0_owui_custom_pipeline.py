"""
title: AccA Mem0 Integration Pipeline
author: Custom for AccA Project
date: 2025-01-08
version: 1.0.0
license: MIT
description: Custom mem0 pipeline for AccA project using Gemini API and existing Qdrant setup
"""

import os
import json
import asyncio
from typing import ClassVar, List, Optional

# Graceful imports with error handling
try:
    from pydantic import BaseModel, Field
    PYDANTIC_AVAILABLE = True
except ImportError:
    print("[AccA-Mem0-WARNING] Pydantic not available. Pipeline may not work correctly.")
    # Fallback for basic functionality
    class BaseModel:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)
    def Field(default=None, description=""):
        return default
    PYDANTIC_AVAILABLE = False

try:
    from mem0 import AsyncMemory
    MEM0_AVAILABLE = True
except ImportError:
    print("[AccA-Mem0-ERROR] mem0ai not available. Please install: pip install mem0ai")
    MEM0_AVAILABLE = False
    class AsyncMemory:
        @classmethod
        async def from_config(cls, config):
            raise ImportError("mem0ai not installed")


class Pipeline:
    class Valves(BaseModel):
        pipelines: List[str] = ["*"]
        priority: int = 0
        user_id: str = Field(
            default="default_user", 
            description="Default user ID for memory operations"
        )

        # Vector store config - using existing Qdrant setup
        qdrant_host: str = Field(
            default="localhost", 
            description="Qdrant vector database host"
        )
        qdrant_port: str = Field(
            default="6333", 
            description="Qdrant vector database port"
        )
        collection_name: str = Field(
            default="mem0_gemini_768", 
            description="Qdrant collection name for 768-dimensional vectors (Gemini embeddings)"
        )

        # Gemini LLM config
        llm_provider: str = Field(
            default="gemini", 
            description="LLM provider"
        )
        llm_api_key: str = Field(
            default=os.getenv("GEMINI_API_KEY", "AIzaSyDHsg-8CCSEBUO9N3V0vaRsCSNq2iS4oec"), 
            description="Gemini API key"
        )
        llm_model: str = Field(
            default="gemini-2.5-flash", 
            description="Gemini model name"
        )
        llm_temperature: float = Field(
            default=0.1, 
            description="LLM temperature"
        )
        llm_max_tokens: int = Field(
            default=1000, 
            description="LLM max tokens"
        )

        # Gemini Embedder config
        embedder_provider: str = Field(
            default="gemini", 
            description="Embedding provider"
        )
        embedder_api_key: str = Field(
            default=os.getenv("GEMINI_API_KEY", "AIzaSyDHsg-8CCSEBUO9N3V0vaRsCSNq2iS4oec"), 
            description="Gemini API key for embeddings"
        )
        embedder_model: str = Field(
            default="text-embedding-004", 
            description="Gemini embedding model"
        )
        embedder_dims: int = Field(
            default=768, 
            description="Embedding dimensions"
        )

        # Memory behavior settings
        max_memories_to_inject: int = Field(
            default=5, 
            description="Maximum number of memories to inject into context"
        )
        memory_relevance_threshold: float = Field(
            default=0.7, 
            description="Minimum relevance score for memory to be included"
        )
        auto_store_messages: bool = Field(
            default=True, 
            description="Automatically store messages in memory"
        )
        enable_debug_logging: bool = Field(
            default=False, 
            description="Enable detailed debug logging"
        )

    def __init__(self):
        self.type = "filter"
        self.valves = self.Valves()
        self.m = None  # Will be initialized on first use
        
        # Check dependencies
        if not MEM0_AVAILABLE:
            print("[AccA-Mem0-ERROR] mem0ai not available. Memory functions will be disabled.")
        if not PYDANTIC_AVAILABLE:
            print("[AccA-Mem0-WARNING] Pydantic not available. Some validation may be limited.")
            
        self.debug_log("AccA Mem0 Pipeline initialized")

    def debug_log(self, message: str):
        """Debug logging with toggle"""
        if self.valves.enable_debug_logging:
            print(f"[AccA-Mem0-DEBUG] {message}")

    async def on_valves_updated(self):
        """Called when valves are updated - reinitialize mem0 client"""
        self.debug_log("Valves updated, reinitializing mem0 client...")
        try:
            self.m = await self.init_mem_zero()
            self.debug_log("mem0 client reinitialized successfully")
        except Exception as e:
            print(f"[AccA-Mem0-ERROR] Failed to reinitialize mem0: {str(e)}")

    async def on_startup(self):
        """Called when pipeline starts"""
        print(f"[AccA-Mem0] Starting up: {__name__}")
        pass

    async def on_shutdown(self):
        """Called when pipeline shuts down"""
        print(f"[AccA-Mem0] Shutting down: {__name__}")
        pass

    async def add_message_to_mem0(self, user_id: str, message: dict):
        """Add a message to mem0 memory storage"""
        if not self.valves.auto_store_messages:
            return
            
        try:
            await self.m.add(user_id=user_id, messages=[message])
            self.debug_log(f"Added message to mem0 for user {user_id}: {message.get('content', '')[:50]}...")
        except Exception as e:
            print(f"[AccA-Mem0-ERROR] Failed to add message: {str(e)}")

    async def inlet(self, body: dict, user: Optional[dict] = None) -> dict:
        """
        Main filter method - inject memory context into prompts
        This is called before the message goes to the LLM
        """
        
        # Check if mem0 is available
        if not MEM0_AVAILABLE:
            self.debug_log("mem0 not available, passing through without memory")
            return body
        
        # Initialize mem0 client if not already done
        if self.m is None:
            self.debug_log("Initializing mem0 client for first use...")
            try:
                self.m = await self.init_mem_zero()
                self.debug_log("mem0 client initialized successfully")
            except Exception as e:
                print(f"[AccA-Mem0-ERROR] Failed to initialize mem0: {str(e)}")
                print(f"[AccA-Mem0-INFO] Continuing without memory functionality")
                return body  # Continue without memory if initialization fails

        self.debug_log("=== Inlet method triggered ===")
        self.debug_log(f"Request body keys: {list(body.keys())}")
        
        # Extract messages from request
        messages = body.get("messages", [])
        if not messages:
            self.debug_log("No messages found in request")
            return body

        # Skip if this is a task or special request
        if "metadata" in body and "task" in body["metadata"]:
            self.debug_log("Skipping task request")
            return body

        # Determine user ID
        current_user_id = self.valves.user_id
        if user and "id" in user:
            current_user_id = user["id"]
        self.debug_log(f"Using user ID: {current_user_id}")

        # Find the latest user message for memory query
        user_message = None
        assistant_message = None
        
        for msg in reversed(messages):
            if msg.get("role") == "user" and not user_message:
                user_message = msg.get("content")
                self.debug_log(f"Found user message: {user_message[:100]}...")
            elif msg.get("role") == "assistant" and not assistant_message:
                assistant_message = msg.get("content")
                self.debug_log(f"Found assistant message: {assistant_message[:100]}...")

        if not user_message:
            self.debug_log("No user message found")
            return body

        try:
            # Search for relevant memories
            self.debug_log("Searching for relevant memories...")
            memories = await self.m.search(
                user_id=current_user_id, 
                query=user_message,
                limit=self.valves.max_memories_to_inject
            )
            
            self.debug_log(f"Found {len(memories.get('results', []))} memories")

            # Store previous assistant message if available
            if assistant_message and self.valves.auto_store_messages:
                asyncio.create_task(
                    self.add_message_to_mem0(
                        current_user_id,
                        {"role": "assistant", "content": assistant_message}
                    )
                )

            # Store current user message
            if self.valves.auto_store_messages:
                asyncio.create_task(
                    self.add_message_to_mem0(
                        current_user_id,
                        {"role": "user", "content": user_message}
                    )
                )

            # Filter memories by relevance threshold
            relevant_memories = []
            if memories and "results" in memories:
                for mem in memories["results"]:
                    # Apply relevance threshold if score is available
                    if "score" in mem:
                        if mem["score"] >= self.valves.memory_relevance_threshold:
                            relevant_memories.append(mem)
                    else:
                        # Include memory if no score available
                        relevant_memories.append(mem)

            self.debug_log(f"Filtered to {len(relevant_memories)} relevant memories")

            # Inject memory context if relevant memories found
            if relevant_memories:
                memory_context = "\n\n📚 Relevant memories from previous conversations:\n"
                for i, mem in enumerate(relevant_memories, 1):
                    memory_text = mem.get('memory', str(mem))
                    memory_context += f"  {i}. {memory_text}\n"
                
                memory_context += "\nUse these memories to provide contextual and personalized responses.\n"

                # Find existing system message or create new one
                system_message = None
                for msg in messages:
                    if msg.get("role") == "system":
                        system_message = msg
                        break

                if system_message:
                    system_message["content"] += memory_context
                    self.debug_log("Added memory context to existing system message")
                else:
                    # Insert new system message at the beginning
                    messages.insert(0, {
                        "role": "system",
                        "content": f"You are a helpful AI assistant with access to conversation history.{memory_context}"
                    })
                    self.debug_log("Created new system message with memory context")

                # Update body with modified messages
                body["messages"] = messages
                self.debug_log(f"Injected {len(relevant_memories)} memories into context")

        except Exception as e:
            print(f"[AccA-Mem0-ERROR] Memory integration error: {str(e)}")
            # Continue without memory context if there's an error

        self.debug_log("=== Inlet processing complete ===")
        return body

    async def init_mem_zero(self):
        """Initialize mem0 AsyncMemory client with current configuration"""
        if not MEM0_AVAILABLE:
            raise ImportError("mem0ai not available")
            
        config = {
            "vector_store": {
                "provider": "qdrant",
                "config": {
                    "host": self.valves.qdrant_host,
                    "port": int(self.valves.qdrant_port),
                    "collection_name": self.valves.collection_name,
                },
            },
            "llm": {
                "provider": self.valves.llm_provider,
                "config": {
                    "api_key": self.valves.llm_api_key,
                    "model": self.valves.llm_model,
                    "temperature": self.valves.llm_temperature,
                    "max_tokens": self.valves.llm_max_tokens,
                },
            },
            "embedder": {
                "provider": self.valves.embedder_provider,
                "config": {
                    "api_key": self.valves.embedder_api_key,
                    "model": self.valves.embedder_model,
                    "embedding_dims": self.valves.embedder_dims,
                },
            },
        }

        self.debug_log(f"Initializing memory with config: {json.dumps(config, indent=2)}")
        try:
            return await AsyncMemory.from_config(config)
        except Exception as e:
            print(f"[AccA-Mem0-ERROR] Failed to create AsyncMemory: {str(e)}")
            print(f"[AccA-Mem0-HINT] Check Qdrant connection and Gemini API key")
            raise 