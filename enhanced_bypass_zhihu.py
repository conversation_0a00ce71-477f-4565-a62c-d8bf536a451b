#!/usr/bin/env python3
"""
Enhanced bypass techniques specifically for zhihu.com and similar sites
"""

import subprocess

def add_enhanced_bypass():
    """Add enhanced bypass techniques"""
    
    print("🔧 Adding enhanced bypass techniques for zhihu.com...")
    
    # Copy current file to edit
    subprocess.run(['docker', 'cp', 'jina-crawler-mcp:/app/jini_crawler.py', './jini_crawler_enhanced.py'])
    
    # Read the file
    with open('./jini_crawler_enhanced.py', 'r') as f:
        content = f.read()
    
    # Find and replace the Google Cache section to add better validation
    old_cache_section = '''                # Technique 3: Google Cache
                if not html_content:
                    logger.info(f"🔍 Trying Google Cache...")
                    try:
                        from urllib.parse import quote
                        cache_url = f"https://webcache.googleusercontent.com/search?q=cache:{quote(url)}"
                        
                        cache_headers = {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                            'Referer': 'https://www.google.com/',
                        }
                        
                        async with self.session.get(cache_url, headers=cache_headers, timeout=30) as response:
                            if response.status == 200:
                                cache_content = await response.text()
                                if 'cache:' in cache_content and len(cache_content) > 1000:
                                    html_content = cache_content
                                    status = 200
                                    logger.info(f"✅ Google Cache bypass successful for {url}")
                                    
                    except Exception as e:
                        logger.debug(f"❌ Google Cache error: {e}")'''
    
    new_enhanced_section = '''                # Technique 3: Enhanced Google Cache with validation
                if not html_content:
                    logger.info(f"🔍 Trying Google Cache...")
                    try:
                        from urllib.parse import quote
                        cache_url = f"https://webcache.googleusercontent.com/search?q=cache:{quote(url)}"
                        
                        cache_headers = {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                            'Referer': 'https://www.google.com/',
                        }
                        
                        async with self.session.get(cache_url, headers=cache_headers, timeout=30) as response:
                            if response.status == 200:
                                cache_content = await response.text()
                                # Better validation - check for actual content, not just error pages
                                if (len(cache_content) > 1000 and 
                                    'display:none' not in cache_content and
                                    'having trouble accessing' not in cache_content and
                                    'click here' not in cache_content):
                                    html_content = cache_content
                                    status = 200
                                    logger.info(f"✅ Google Cache bypass successful for {url}")
                                else:
                                    logger.debug(f"⚠️ Google Cache returned error page or insufficient content")
                                    
                    except Exception as e:
                        logger.debug(f"❌ Google Cache error: {e}")
                
                # Technique 4: Jina Reader API (external service)
                if not html_content:
                    logger.info(f"📖 Trying Jina Reader API...")
                    try:
                        reader_url = f"https://r.jina.ai/{url}"
                        reader_headers = {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            'Accept': 'text/plain,text/html,application/xhtml+xml',
                        }
                        
                        async with self.session.get(reader_url, headers=reader_headers, timeout=30) as response:
                            if response.status == 200:
                                reader_content = await response.text()
                                if len(reader_content.strip()) > 100:
                                    # Convert markdown to HTML for consistency
                                    html_content = f"<html><body><div class='reader-content'>{reader_content}</div></body></html>"
                                    status = 200
                                    logger.info(f"✅ Jina Reader API bypass successful for {url}")
                                    
                    except Exception as e:
                        logger.debug(f"❌ Jina Reader API error: {e}")
                
                # Technique 5: 12ft.io Paywall Bypass
                if not html_content:
                    logger.info(f"🪜 Trying 12ft.io paywall bypass...")
                    try:
                        bypass_url = f"https://12ft.io/{url}"
                        bypass_headers = {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                            'Referer': 'https://12ft.io/',
                        }
                        
                        async with self.session.get(bypass_url, headers=bypass_headers, timeout=30) as response:
                            if response.status == 200:
                                bypass_content = await response.text()
                                if len(bypass_content) > 1000 and 'article' in bypass_content.lower():
                                    html_content = bypass_content
                                    status = 200
                                    logger.info(f"✅ 12ft.io bypass successful for {url}")
                                    
                    except Exception as e:
                        logger.debug(f"❌ 12ft.io error: {e}")
                
                # Technique 6: Outline.com
                if not html_content:
                    logger.info(f"📄 Trying Outline.com...")
                    try:
                        outline_url = f"https://outline.com/{url}"
                        outline_headers = {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        }
                        
                        async with self.session.get(outline_url, headers=outline_headers, timeout=30) as response:
                            if response.status == 200:
                                outline_content = await response.text()
                                if len(outline_content) > 1000 and 'outline-article' in outline_content:
                                    html_content = outline_content
                                    status = 200
                                    logger.info(f"✅ Outline.com bypass successful for {url}")
                                    
                    except Exception as e:
                        logger.debug(f"❌ Outline.com error: {e}")'''
    
    # Replace the old section
    content = content.replace(old_cache_section, new_enhanced_section)
    
    # Also update the mobile section to be more aggressive
    old_mobile_section = '''                # Technique 4: Mobile User Agent
                if not html_content:
                    logger.info(f"📱 Trying mobile user agent...")
                    try:
                        mobile_headers = {
                            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                            'Accept-Language': 'en-US,en;q=0.9',
                            'Accept-Encoding': 'gzip, deflate, br',
                            'Referer': 'https://www.google.com/',
                        }
                        
                        async with self.session.get(url, headers=mobile_headers, timeout=30) as response:
                            if response.status == 200:
                                html_content = await response.text()
                                status = 200
                                logger.info(f"✅ Mobile bypass successful for {url}")
                                
                    except Exception as e:
                        logger.debug(f"❌ Mobile bypass error: {e}")'''
    
    new_mobile_section = '''                # Technique 7: Multiple Mobile User Agents
                if not html_content:
                    logger.info(f"📱 Trying multiple mobile user agents...")
                    mobile_agents = [
                        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
                        'Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
                        'Mozilla/5.0 (iPad; CPU OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
                    ]
                    
                    for i, mobile_ua in enumerate(mobile_agents, 1):
                        try:
                            logger.debug(f"📱 Trying mobile agent {i}/{len(mobile_agents)}")
                            mobile_headers = {
                                'User-Agent': mobile_ua,
                                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                                'Accept-Encoding': 'gzip, deflate, br',
                                'Referer': 'https://www.baidu.com/',
                            }
                            
                            async with self.session.get(url, headers=mobile_headers, timeout=30) as response:
                                if response.status == 200:
                                    mobile_content = await response.text()
                                    if len(mobile_content) > 1000:
                                        html_content = mobile_content
                                        status = 200
                                        logger.info(f"✅ Mobile bypass successful with agent {i}")
                                        break
                                        
                        except Exception as e:
                            logger.debug(f"❌ Mobile agent {i} error: {e}")
                            continue'''
    
    # Replace mobile section
    content = content.replace(old_mobile_section, new_mobile_section)
    
    # Write enhanced content
    with open('./jini_crawler_enhanced.py', 'w') as f:
        f.write(content)
    
    # Copy back to container
    subprocess.run(['docker', 'cp', './jini_crawler_enhanced.py', 'jina-crawler-mcp:/app/jini_crawler.py'])
    
    print("✅ Enhanced bypass techniques added")
    return True

def main():
    """Main function"""
    print("🚀 Adding enhanced bypass techniques for zhihu.com...")
    print("🎯 New techniques:")
    print("1. 🔍 Enhanced Google Cache (with better validation)")
    print("2. 📖 Jina Reader API (external service)")
    print("3. 🪜 12ft.io Paywall Bypass")
    print("4. 📄 Outline.com")
    print("5. 📱 Multiple Mobile User Agents")
    print()
    
    if add_enhanced_bypass():
        print("\n🎉 Enhanced bypass techniques added successfully!")
        print("🔄 Restarting container...")
        
        # Restart container
        subprocess.run(['docker', 'restart', 'jina-crawler-mcp'])
        print("✅ Container restarted")
        
        print("\n📊 Enhanced bypass chain:")
        print("1. Regular request")
        print("2. TLS bypass (Cloudflare)")
        print("3. 🤖 Bot user agents (5 different)")
        print("4. 🏛️ Archive.org Wayback Machine")
        print("5. 🔍 Enhanced Google Cache (with validation)")
        print("6. 📖 Jina Reader API")
        print("7. 🪜 12ft.io Paywall Bypass")
        print("8. 📄 Outline.com")
        print("9. 📱 Multiple Mobile User Agents")
        print("🎯 Should get real content from zhihu.com")
    else:
        print("\n❌ Failed to add enhanced bypass techniques")

if __name__ == "__main__":
    main()
