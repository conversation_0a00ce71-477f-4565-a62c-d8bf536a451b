# 🎯 HƯỚNG DẪN CẤU HÌNH CUỐI CÙNG - JINA-CRAWLER MCP

## ✅ Trạng thái hiện tại:
- ✅ Jina-Crawler service: Đang chạy trên port 8001
- ✅ MCPO container: Đang chạy với host network mode trên port 8000
- ✅ Open WebUI: Đang chạy trên port 3000

## 🔧 Cấu hình trong Open WebUI Tools Settings

### 📝 Thông tin cần khai báo:

**URL (QUAN TRỌNG):**
```
http://**************:8000/jina_crawler
```
*Hoặc nếu truy cập từ localhost:*
```
http://localhost:8000/jina_crawler
```

**Name:**
```
<PERSON><PERSON>rawler
```

**Description:**
```
AI-powered web crawler with SmolDocling-256M-preview model for intelligent content extraction, Oracle DB caching, and ARM64 optimizations
```

**Auth:**
```
Bearer (để trống - không cần authentication)
```

**Visibility:**
```
Public - Accessible to all users
```

## 🛠️ 10 MCP Tools có sẵn:

1. **`crawl_url`** - Thu thập nội dung từ URL
2. **`crawl_multiple_urls`** - Thu thập nhiều URL cùng lúc  
3. **`search_site`** - Tìm kiếm trong website cụ thể
4. **`get_cached_content`** - Lấy nội dung đã cache từ Oracle DB
5. **`clear_cache`** - Xóa cache
6. **`get_performance_stats`** - Thống kê hiệu suất
7. **`health_check`** - Kiểm tra tình trạng dịch vụ
8. **`optimize_arm64_performance`** - Tối ưu hiệu suất ARM64
9. **`get_model_info`** - Thông tin về SmolDocling model
10. **`list_tools`** - Liệt kê các tools có sẵn

## 🎯 Cách sử dụng sau khi kết nối:

### Ví dụ câu lệnh trong chat:
- "Hãy crawl website https://vnexpress.net và tóm tắt nội dung"
- "Thu thập thông tin từ https://github.com/microsoft/vscode"
- "Tìm kiếm 'AI' trong website https://openai.com"
- "Kiểm tra hiệu suất của jina-crawler"
- "Crawl và phân tích nội dung từ https://techcrunch.com"

## 🔍 Kiểm tra kết nối:

### Test từ terminal:
```bash
# Test MCPO endpoint
curl -X POST http://localhost:8000/jina_crawler/health_check \
  -H "Content-Type: application/json" -d '{}'

# Test crawl functionality  
curl -X POST http://localhost:8000/jina_crawler/crawl_url \
  -H "Content-Type: application/json" \
  -d '{"url": "https://httpbin.org/get", "timeout": 10}'
```

## 📊 Kiến trúc hệ thống:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Open WebUI    │    │  MCPO Container  │    │ Jina-Crawler    │
│ (Port 3000)     │◄──►│  (Port 8000)     │◄──►│ Host Service    │
│ acca-network    │    │  host network    │    │   (Port 8001)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## ⚠️ Lưu ý quan trọng:

1. **URL phải sử dụng IP thực** (**************) thay vì localhost khi Open WebUI chạy trên container
2. **Port 8000** là port của MCPO container (không phải 5000)
3. **Endpoint `/jina_crawler`** là bắt buộc
4. **Không cần authentication** - để trống phần Bearer token

## 🎉 Sau khi cấu hình thành công:

- Jina-Crawler tools sẽ xuất hiện trong Open WebUI
- Có thể sử dụng trực tiếp trong chat conversation
- AI sẽ tự động chọn tool phù hợp khi user yêu cầu crawl web
- Hỗ trợ crawling, content processing, và caching tự động

## 🔧 Troubleshooting:

Nếu kết nối thất bại:
1. Kiểm tra MCPO container: `docker ps --filter "name=mcpo-container-host"`
2. Kiểm tra jina-crawler service: `sudo systemctl status jina-crawler`
3. Test endpoint trực tiếp: `curl http://localhost:8000/jina_crawler/health_check`
4. Thử URL alternative: `http://localhost:8000/jina_crawler`

**✅ Jina-Crawler MCP integration đã sẵn sàng sử dụng!**