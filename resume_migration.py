#!/usr/bin/env python3
"""
Resume Memory Migration Script
Continues migrating failed points from mem0_gemini_768 to mem0_gemini_3072
"""

import os
import time
import json
import requests
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
QDRANT_HOST = "localhost"
QDRANT_PORT = 6333
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
OLD_COLLECTION = "mem0_gemini_768"
NEW_COLLECTION = "mem0_gemini_3072"
BATCH_SIZE = 10  # Smaller batch to avoid rate limits
DELAY_BETWEEN_REQUESTS = 2  # 2 seconds delay between API calls

class MemoryMigrationResumer:
    def __init__(self):
        self.client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)
        
    def get_gemini_embedding(self, text):
        """Get embedding from Gemini API using gemini-embedding-001 (3072 dims)"""
        url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-embedding-001:embedContent"
        headers = {
            "Content-Type": "application/json",
            "x-goog-api-key": GEMINI_API_KEY
        }
        
        data = {
            "model": "models/gemini-embedding-001",
            "content": {
                "parts": [{"text": text}]
            }
        }
        
        try:
            response = requests.post(url, headers=headers, json=data)
            if response.status_code == 200:
                result = response.json()
                return result['embedding']['values']
            else:
                error_info = response.json() if response.content else {"error": "Unknown error"}
                print(f"❌ Gemini API error: {response.status_code} - {json.dumps(error_info, indent=2)}")
                return None
        except Exception as e:
            print(f"❌ Exception calling Gemini API: {e}")
            return None

    def get_existing_point_ids(self, collection_name):
        """Get all existing point IDs in a collection"""
        try:
            points = []
            offset = None
            
            while True:
                result = self.client.scroll(
                    collection_name=collection_name,
                    limit=1000,
                    offset=offset,
                    with_payload=False,
                    with_vectors=False
                )
                
                if not result[0]:  # No more points
                    break
                    
                points.extend([point.id for point in result[0]])
                offset = result[1]  # Next offset
                
                if offset is None:  # No more pages
                    break
                    
            return set(points)
        except Exception as e:
            print(f"❌ Error getting existing points: {e}")
            return set()

    def get_failed_points(self):
        """Get points that failed to migrate (exist in old but not in new collection)"""
        print("🔍 Identifying failed migration points...")
        
        # Get all points from old collection
        old_points = []
        offset = None
        
        while True:
            result = self.client.scroll(
                collection_name=OLD_COLLECTION,
                limit=100,
                offset=offset,
                with_payload=True,
                with_vectors=False
            )
            
            if not result[0]:
                break
                
            old_points.extend(result[0])
            offset = result[1]
            
            if offset is None:
                break
        
        # Get existing point IDs in new collection
        existing_ids = self.get_existing_point_ids(NEW_COLLECTION)
        
        # Find points that need migration
        failed_points = []
        for point in old_points:
            if point.id not in existing_ids:
                failed_points.append(point)
        
        print(f"📊 Found {len(failed_points)} points that need migration")
        return failed_points

    def migrate_failed_points(self):
        """Resume migration for failed points"""
        failed_points = self.get_failed_points()
        
        if not failed_points:
            print("✅ No failed points found - migration is complete!")
            return
        
        print(f"🚀 Resuming migration for {len(failed_points)} failed points")
        migrated_count = 0
        
        for i, point in enumerate(failed_points, 1):
            print(f"📝 Processing point {i}/{len(failed_points)}")
            
            # Extract memory text from payload
            payload = point.payload or {}
            memory_text = (
                payload.get('data') or 
                payload.get('memory') or 
                payload.get('text') or 
                str(payload)
            )
            
            # Skip very short content
            if len(memory_text.strip()) < 10:
                print(f"⏭️ Skipping point {point.id} - content too short")
                continue
            
            # Truncate very long content to avoid API limits
            if len(memory_text) > 30000:  # 30KB limit
                memory_text = memory_text[:30000] + "..."
                print(f"✂️ Truncated long content for point {point.id}")
            
            print(f"🔄 Migrating: {memory_text[:50]}...")
            
            # Get new embedding
            new_embedding = self.get_gemini_embedding(memory_text)
            if new_embedding is None:
                print(f"❌ Failed to get embedding for point {point.id}")
                # Add delay before continuing
                time.sleep(DELAY_BETWEEN_REQUESTS)
                continue
            
            # Create new point with migration metadata
            new_payload = payload.copy()
            new_payload.update({
                'migrated_from': OLD_COLLECTION,
                'migration_timestamp': time.time(),
                'embedding_model': 'gemini-embedding-001',
                'embedding_dims': 3072
            })
            
            new_point = PointStruct(
                id=point.id,
                vector=new_embedding,
                payload=new_payload
            )
            
            # Insert into new collection
            try:
                self.client.upsert(
                    collection_name=NEW_COLLECTION,
                    points=[new_point]
                )
                print(f"✅ Migrated point {point.id}")
                migrated_count += 1
            except Exception as e:
                print(f"❌ Failed to insert point {point.id}: {e}")
            
            # Add delay to avoid rate limits
            time.sleep(DELAY_BETWEEN_REQUESTS)
        
        print(f"\n🎉 Resume Migration Complete!")
        print(f"   Additional points migrated: {migrated_count}")
        print(f"   Target collection: {NEW_COLLECTION}")

def main():
    print("🔧 Starting Memory Migration Resume Process")
    
    migrator = MemoryMigrationResumer()
    migrator.migrate_failed_points()

if __name__ == "__main__":
    main()