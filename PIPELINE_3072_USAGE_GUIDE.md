# 🎯 Hướng dẫn sử dụng Pipeline Mem0 3072 Dimensions

## ✅ Đã hoàn thành

Pipeline **mem0-owui-gemini-3072-fixed** đã được tạo thành công với:
- **✅ Gemini API:** 3072 dimensions thực tế
- **✅ Qdrant collection:** `mem0_gemini_3072_fixed` với 3072 dims
- **✅ Custom embedding wrapper:** Bypass bug mem0 library
- **✅ OpenWebUI:** Đã restart và load pipeline

## 🔧 Cách kích hoạt pipeline

### 1. Vào Admin Panel
- Truy cập: `http://localhost:3000`
- Đăng nhập admin
- Vào **Admin Panel** → **Pipelines**

### 2. Kích hoạt pipeline mới
- Tìm pipeline: **`mem0-owui-gemini-3072-fixed`**
- **Enable** pipeline này
- **Disable** pipeline cũ `mem0-owui-gemini-3072` (nếu có)

### 3. <PERSON><PERSON><PERSON> h<PERSON>nh (nế<PERSON> cần)
Pipeline đã được cấu hình sẵn:
```json
{
  "collection_name": "mem0_gemini_3072_fixed",
  "gemini_api_key": "AIzaSyDdXCS0Cw58rfurmaKW061IdkOy1UHK51U",
  "max_memories_to_inject": 5,
  "memory_relevance_threshold": 0.2,
  "auto_store_messages": true,
  "enable_debug_logging": true
}
```

## 🧪 Test pipeline

### Test đơn giản:
1. Tạo chat mới
2. Gửi tin nhắn: "Tôi thích màu xanh"
3. Gửi tin nhắn khác: "Màu yêu thích của tôi là gì?"
4. Pipeline sẽ nhớ và trả lời dựa trên memory 3072-dimensional

### Kiểm tra logs:
```bash
docker logs -f catomanton-webui
```

Tìm logs:
```
💾 Stored memory with 3072 dimensions: Tôi thích màu xanh...
🔍 Searching memories for: Màu yêu thích của tôi là gì?...
📝 Memory 1: Content: Tôi thích màu xanh
📊 Relevance: 0.85
📏 Dimensions: 3072
```

## 🔍 Troubleshooting

### Nếu pipeline không xuất hiện:
```bash
# Restart OpenWebUI
docker restart catomanton-webui

# Check logs
docker logs catomanton-webui | grep pipeline
```

### Nếu có lỗi dimensions:
```bash
# Test lại pipeline
python3 test_3072_pipeline.py

# Check Qdrant collection
python3 -c "
from qdrant_client import QdrantClient
client = QdrantClient(host='localhost', port=6333)
info = client.get_collection('mem0_gemini_3072_fixed')
print(f'Dimensions: {info.config.params.vectors.size}')
"
```

### Nếu memory không hoạt động:
1. Check API key trong valves.json
2. Check debug logs trong OpenWebUI
3. Verify Qdrant connection

## 📊 So sánh với pipeline cũ

| Feature | Pipeline cũ | Pipeline mới |
|---------|-------------|--------------|
| Embedding Model | mem0.GoogleGenAIEmbedding | Custom GeminiEmbedding3072 |
| Dimensions | 768 (bug) | 3072 (thực tế) |
| Collection | mem0_gemini_3072 | mem0_gemini_3072_fixed |
| Memory Quality | Thấp (768 dims) | Cao (3072 dims) |
| Bug Status | Có bug mem0 library | Đã bypass bug |

## 🎉 Kết quả mong đợi

Với 3072 dimensions:
- **Memory chính xác hơn** (4x dimensions so với 768)
- **Semantic search tốt hơn**
- **Context awareness cao hơn**
- **Không còn dimension mismatch errors**

## 📝 Files quan trọng

```
webui-data/pipelines/mem0-owui-gemini-3072-fixed/
├── mem0-owui-gemini-3072-fixed.py  # Pipeline code
└── valves.json                      # Configuration

Scripts:
├── fix_mem0_3072_dimensions.py     # Tạo pipeline
└── test_3072_pipeline.py           # Test pipeline
```

## 🚀 Sẵn sàng sử dụng!

Pipeline đã sẵn sàng với **3072 dimensions thực tế** từ Gemini API!