# Hướng dẫn kết nối Gemini CLI Tools với Open WebUI

## Tình trạng hiện tại ✅

Các container đã được kết nối thành công trong cùng network `acca-network`:

- **open-webui-mcpo** - 172.28.0.3/16 (port 3000)
- **mcpo-unified-server** - 172.28.0.6/16 (port 8000) 
- **gemini-cli-unified-server** - 172.28.0.7/16 (port 8003 → 8000)
- **pandas-unified-server** - 172.28.0.8/16 (port 8004)

## Cấu hình đã hoàn thành

### 1. Network Configuration
```bash
# Đã thêm các container vào acca-network
docker network connect acca-network mcpo-unified-server
docker network connect acca-network gemini-cli-unified-server
# pandas-unified-server đã có sẵn trong acca-network
```

### 2. OpenWebUI Tools Config
File: `mem0-owui/mcp-integration/config/openwebui_tools_config.json`
```json
{
  "tools": [
    {
      "name": "Gemini CLI Tools",
      "url": "http://gemini-cli-unified-server:8000/gemini_cli_tools/openapi.json",
      "description": "Comprehensive tool collection with 11 powerful tools",
      "enabled": true,
      "tools": [
        {
          "name": "web_search",
          "description": "Search the web using Gemini CLI"
        },
        {
          "name": "read_file", 
          "description": "Read and analyze files"
        },
        {
          "name": "write_file",
          "description": "Write content to files"
        },
        {
          "name": "execute_command",
          "description": "Execute shell commands (restricted)"
        },
        {
          "name": "list_directory",
          "description": "List directory contents"
        },
        {
          "name": "search_file_content",
          "description": "Search file content with regex"
        },
        {
          "name": "glob",
          "description": "Find files matching glob patterns"
        },
        {
          "name": "web_fetch",
          "description": "Fetch content from URLs"
        },
        {
          "name": "read_many_files",
          "description": "Read multiple files at once"
        },
        {
          "name": "analyze_code",
          "description": "Analyze code structure"
        },
        {
          "name": "store_memory",
          "description": "Store memory for conversation context"
        }
      ]
    }
  ]
}
```

## Kiểm tra kết nối

### 1. Health Check
```bash
# Từ host
curl -s http://localhost:8003/health

# Từ Open WebUI container
docker exec open-webui-mcpo curl -s http://gemini-cli-unified-server:8000/health
```

### 2. OpenAPI Endpoint
```bash
# Từ Open WebUI container
docker exec open-webui-mcpo curl -s http://gemini-cli-unified-server:8000/gemini_cli_tools/openapi.json
```

### 3. Test Tool
```bash
# Test list_directory tool
curl -X POST http://localhost:8003/tools/list_directory \
  -H "Content-Type: application/json" \
  -d '{"path": "."}'
```

## Cách sử dụng trong Open WebUI

### 1. Truy cập Open WebUI
```
http://localhost:3000
```

### 2. Cấu hình Tools
1. Vào **Settings** → **Tools**
2. Thêm URL: `http://gemini-cli-unified-server:8000/gemini_cli_tools/openapi.json`
3. Enable các tools cần thiết

### 3. Sử dụng Tools trong Chat
Các tools có sẵn:
- `web_search` - Tìm kiếm web
- `read_file` - Đọc file
- `write_file` - Ghi file
- `execute_command` - Chạy lệnh (hạn chế)
- `list_directory` - Liệt kê thư mục
- `search_file_content` - Tìm kiếm nội dung file
- `glob` - Tìm file theo pattern
- `web_fetch` - Lấy nội dung từ URL
- `read_many_files` - Đọc nhiều file
- `analyze_code` - Phân tích code
- `store_memory` - Lưu trữ memory

## Troubleshooting

### Lỗi kết nối
```bash
# Kiểm tra network
docker network inspect acca-network

# Kiểm tra container status
docker ps | grep -E "(open-webui|gemini-cli|mcpo|pandas)"

# Restart container nếu cần
docker restart gemini-cli-unified-server
```

### Lỗi OpenAPI
```bash
# Kiểm tra OpenAPI endpoint
curl -s http://localhost:8003/gemini_cli_tools/openapi.json | jq .

# Kiểm tra logs
docker logs gemini-cli-unified-server
```

## Container Ports Summary

| Container | Internal Port | External Port | Network |
|-----------|---------------|---------------|---------|
| open-webui-mcpo | 8080 | 3000 | acca-network |
| mcpo-unified-server | 5000 | 8000 | acca-network |
| gemini-cli-unified-server | 8000 | 8003 | acca-network |
| pandas-unified-server | 8004 | 8004 | acca-network |

## Lưu ý quan trọng

1. **Network**: Tất cả container phải cùng network để kết nối
2. **Hostname**: Sử dụng container name thay vì localhost trong config
3. **Port mapping**: Container port 8000 được map ra external port 8003
4. **Security**: Gemini CLI Tools chạy ở restricted mode mặc định

## Các lệnh hữu ích

```bash
# Xem tất cả container trong acca-network
docker network inspect acca-network --format '{{range .Containers}}{{.Name}} - {{.IPv4Address}}{{"\n"}}{{end}}'

# Test kết nối giữa containers
docker exec open-webui-mcpo ping gemini-cli-unified-server

# Xem logs real-time
docker logs -f gemini-cli-unified-server

# Restart tất cả MCPO containers
docker restart mcpo-unified-server gemini-cli-unified-server pandas-unified-server
```

---

**Trạng thái**: ✅ Đã kết nối thành công
**Ngày cập nhật**: $(date)
**Tác giả**: Kilo Code