#!/usr/bin/env python3
"""
Final Network Connectivity Test for Jina-Crawler MCP Integration
Tests all connection paths between Open WebUI, MCPO, and Jina-Crawler
"""

import subprocess
import requests
import time
import json

def run_command(cmd, description):
    """Run a command and return the result"""
    print(f"🔧 {description}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(f"✅ Success: {result.stdout.strip()}")
            return True, result.stdout.strip()
        else:
            print(f"❌ Failed: {result.stderr.strip()}")
            return False, result.stderr.strip()
    except Exception as e:
        print(f"💥 Error: {str(e)}")
        return False, str(e)

def test_http_endpoint(url, description, method="GET", data=None, timeout=10):
    """Test an HTTP endpoint"""
    try:
        if method == "POST":
            response = requests.post(url, json=data or {}, timeout=timeout)
        else:
            response = requests.get(url, timeout=timeout)
        
        if response.status_code == 200:
            print(f"✅ {description}: Working (200)")
            return True, response.text[:200] + "..." if len(response.text) > 200 else response.text
        else:
            print(f"❌ {description}: Failed ({response.status_code})")
            return False, f"HTTP {response.status_code}"
    except Exception as e:
        print(f"💥 {description}: Error - {str(e)}")
        return False, str(e)

def main():
    print("🚀 FINAL NETWORK CONNECTIVITY TEST")
    print("=" * 60)
    
    # Step 1: Check all containers are running
    print("\n📋 Step 1: Container Status Check")
    run_command("docker ps --filter 'name=mcpo-container-host' --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'", "MCPO Container Status")
    run_command("docker ps --filter 'name=catomanton-webui' --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'", "Open WebUI Container Status")
    run_command("sudo systemctl is-active jina-crawler", "Jina-Crawler Service Status")
    
    # Step 2: Test direct connections
    print("\n📋 Step 2: Direct Connection Tests")
    test_http_endpoint("http://localhost:8001/health", "Jina-Crawler Direct")
    test_http_endpoint("http://localhost:5000/jina_crawler/health_check", "MCPO from Host", "POST", {})
    
    # Step 3: Test from Open WebUI container perspective
    print("\n📋 Step 3: Container-to-Container Tests")
    success, output = run_command(
        "docker exec catomanton-webui curl -s -o /dev/null -w '%{http_code}' http://mcpo-container-host:8000/jina_crawler/health_check -X POST -H 'Content-Type: application/json' -d '{}'",
        "Open WebUI → MCPO Connection"
    )
    
    # Step 4: Test MCPO to Jina-Crawler connection
    print("\n📋 Step 4: MCPO to Jina-Crawler Test")
    success, output = run_command(
        "docker exec mcpo-container-host curl -s -o /dev/null -w '%{http_code}' http://host.docker.internal:8001/health",
        "MCPO → Jina-Crawler Connection"
    )
    
    # Step 5: End-to-end functionality test
    print("\n📋 Step 5: End-to-End Functionality Test")
    test_http_endpoint(
        "http://localhost:5000/jina_crawler/crawl_url",
        "End-to-End Crawl Test",
        "POST",
        {"url": "https://httpbin.org/get", "timeout": 10}
    )
    
    # Step 6: Provide final configuration
    print("\n🎯 FINAL CONFIGURATION FOR OPEN WEBUI")
    print("=" * 60)
    print("URL to use in Open WebUI Tools Settings:")
    print("🔗 http://mcpo-container-host:8000/jina_crawler")
    print("")
    print("Alternative URLs to try:")
    print("🔗 http://localhost:5000/jina_crawler (if accessing from host)")
    print("")
    print("Name: Jina Crawler")
    print("Description: AI-powered web crawler with SmolDocling-256M-preview")
    print("Auth: Bearer (leave empty)")
    print("Visibility: Public")
    
    print("\n🎉 Test completed!")

if __name__ == "__main__":
    main()