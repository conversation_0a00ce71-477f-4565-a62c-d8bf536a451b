#!/usr/bin/env python3
"""
Oracle Advanced Memory Service
Core service for AccA AI Assistant Advanced Memory System using Oracle Autonomous DB
Supports both thin mode and wallet connections for optimal performance
"""

import oracledb
import asyncio
import json
import hashlib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import re
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MemoryItem:
    """Memory item data structure"""
    memory_id: str
    user_id: str
    memory_type: str
    content: str
    confidence_score: float
    relevance_score: float
    created_date: str
    session_id: Optional[str] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict] = None

@dataclass
class UserPattern:
    """User pattern data structure"""
    pattern_id: str
    user_id: str
    pattern_type: str
    pattern_data: Dict
    strength: float
    confidence: float
    sample_count: int

@dataclass
class SearchResult:
    """Memory search result"""
    memory: MemoryItem
    relevance_score: float
    context_match: bool
    pattern_boost: float

class OracleAdvancedMemoryService:
    """
    Advanced Memory Service using Oracle Autonomous Database
    Supports hybrid thin mode + wallet connection architecture
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.thin_pool = None
        self.wallet_pool = None
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Performance metrics
        self.metrics = {
            'total_searches': 0,
            'total_stores': 0,
            'avg_search_time': 0.0,
            'avg_store_time': 0.0,
            'thin_mode_operations': 0,
            'wallet_operations': 0,
            'pattern_extractions': 0,
            'cache_hits': 0
        }
        
        # Simple in-memory cache for frequently accessed patterns
        self.pattern_cache = {}
        self.cache_ttl = 300  # 5 minutes
        
    async def initialize(self):
        """Initialize Oracle connection pools"""
        try:
            logger.info("🚀 Initializing Oracle Advanced Memory Service...")
            
            # Initialize thin mode pool for analytics and heavy operations
            self.thin_pool = await asyncio.get_event_loop().run_in_executor(
                self.executor,
                self._create_thin_pool
            )
            logger.info("✅ Oracle thin mode pool initialized")
            
            # Initialize wallet pool for real-time operations
            await asyncio.get_event_loop().run_in_executor(
                self.executor,
                self._init_wallet_client
            )
            
            self.wallet_pool = await asyncio.get_event_loop().run_in_executor(
                self.executor,
                self._create_wallet_pool
            )
            logger.info("✅ Oracle wallet pool initialized")
            
            # Test connections
            await self._test_connections()
            
            logger.info("🧠 Oracle Advanced Memory Service ready!")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Oracle Memory Service: {e}")
            raise
    
    def _create_thin_pool(self):
        """Create thin mode connection pool (synchronous)"""
        return oracledb.create_pool(
            user=self.config['oracle_user'],
            password=self.config['oracle_password'],
            dsn=self.config['oracle_dsn'],
            min=3,
            max=15,
            increment=2,
            ping_interval=60,
            timeout=300
        )
    
    def _init_wallet_client(self):
        """Initialize Oracle client with wallet (synchronous)"""
        if self.config.get('oracle_wallet_location'):
            oracledb.init_oracle_client(
                config_dir=self.config['oracle_wallet_location']
            )
    
    def _create_wallet_pool(self):
        """Create wallet connection pool (synchronous)"""
        return oracledb.create_pool(
            user=self.config['oracle_user'],
            password=self.config['oracle_password'],
            dsn=self.config['oracle_dsn'],
            min=2,
            max=8,
            increment=1,
            ping_interval=30,
            timeout=60
        )
    
    async def _test_connections(self):
        """Test both connection pools"""
        try:
            # Test thin mode
            async with self._get_thin_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 'thin_mode_ok' FROM DUAL")
                result = cursor.fetchone()
                logger.info(f"✅ Thin mode test: {result[0]}")
            
            # Test wallet mode
            async with self._get_wallet_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 'wallet_mode_ok' FROM DUAL")
                result = cursor.fetchone()
                logger.info(f"✅ Wallet mode test: {result[0]}")
                
        except Exception as e:
            logger.error(f"❌ Connection test failed: {e}")
            raise
    
    async def _get_thin_connection(self):
        """Get connection from thin mode pool"""
        return await asyncio.get_event_loop().run_in_executor(
            self.executor,
            self.thin_pool.acquire
        )
    
    async def _get_wallet_connection(self):
        """Get connection from wallet pool"""
        return await asyncio.get_event_loop().run_in_executor(
            self.executor,
            self.wallet_pool.acquire
        )
    
    async def store_memory(self, user_id: str, content: str, memory_type: str = "conversation",
                          session_id: str = None, confidence: float = 0.8,
                          tags: List[str] = None, metadata: Dict = None) -> str:
        """
        Store memory using wallet connection for immediate availability
        """
        start_time = time.time()
        
        try:
            # Generate unique memory ID
            memory_id = hashlib.md5(
                f"{user_id}_{content}_{datetime.now().isoformat()}".encode()
            ).hexdigest()
            
            # Use wallet connection for immediate storage
            conn = await self._get_wallet_connection()
            try:
                cursor = conn.cursor()
                
                # Store memory
                sql = """
                INSERT INTO USER_MEMORY (
                    memory_id, user_id, memory_type, memory_content,
                    confidence_score, session_id, tags, metadata, created_date
                ) VALUES (
                    :memory_id, :user_id, :memory_type, :content,
                    :confidence, :session_id, :tags, :metadata, SYSDATE
                )
                """
                
                cursor.execute(sql, {
                    'memory_id': memory_id,
                    'user_id': user_id,
                    'memory_type': memory_type,
                    'content': content,
                    'confidence': confidence,
                    'session_id': session_id,
                    'tags': json.dumps(tags) if tags else None,
                    'metadata': json.dumps(metadata) if metadata else None
                })
                
                conn.commit()
                
                # Update metrics
                self.metrics['total_stores'] += 1
                self.metrics['wallet_operations'] += 1
                
                store_time = time.time() - start_time
                self.metrics['avg_store_time'] = (
                    (self.metrics['avg_store_time'] * (self.metrics['total_stores'] - 1) + store_time) /
                    self.metrics['total_stores']
                )
                
                logger.info(f"💾 Stored memory {memory_id[:8]}... for user {user_id} ({store_time:.3f}s)")
                
                # Trigger background pattern extraction
                if memory_type == "conversation":
                    asyncio.create_task(
                        self._extract_patterns_background(user_id, content, memory_id)
                    )
                
                return memory_id
                
            finally:
                conn.close()
                
        except Exception as e:
            logger.error(f"❌ Error storing memory: {e}")
            raise
    
    async def search_memories(self, user_id: str, query: str, limit: int = 5,
                            min_confidence: float = 0.3, include_patterns: bool = True) -> List[SearchResult]:
        """
        Search memories using thin mode for complex queries
        """
        start_time = time.time()
        
        try:
            # Use thin mode for complex search operations
            conn = await self._get_thin_connection()
            try:
                cursor = conn.cursor()
                
                # Get user patterns for context boosting
                user_patterns = {}
                if include_patterns:
                    user_patterns = await self._get_user_patterns_cached(user_id)
                
                # Advanced search with Oracle Text and pattern boosting
                sql = """
                SELECT 
                    memory_id, memory_type, memory_content, confidence_score,
                    created_date, session_id, tags, metadata,
                    SCORE(1) as text_score
                FROM USER_MEMORY
                WHERE user_id = :user_id
                AND status = 'ACTIVE'
                AND confidence_score >= :min_confidence
                AND CONTAINS(memory_content, :query, 1) > 0
                ORDER BY SCORE(1) DESC, confidence_score DESC, created_date DESC
                FETCH FIRST :limit ROWS ONLY
                """
                
                # Prepare Oracle Text query
                oracle_text_query = self._prepare_oracle_text_query(query)
                
                cursor.execute(sql, {
                    'user_id': user_id,
                    'query': oracle_text_query,
                    'min_confidence': min_confidence,
                    'limit': limit * 2  # Get more results for pattern boosting
                })
                
                results = []
                for row in cursor.fetchall():
                    memory = MemoryItem(
                        memory_id=row[0],
                        user_id=user_id,
                        memory_type=row[1],
                        content=row[2],
                        confidence_score=float(row[3]),
                        relevance_score=float(row[8]) if row[8] else 0.5,
                        created_date=row[4].isoformat(),
                        session_id=row[5],
                        tags=json.loads(row[6]) if row[6] else [],
                        metadata=json.loads(row[7]) if row[7] else {}
                    )
                    
                    # Calculate pattern boost
                    pattern_boost = self._calculate_pattern_boost(memory, user_patterns, query)
                    
                    # Calculate final relevance score
                    final_relevance = min(1.0, memory.relevance_score + pattern_boost)
                    
                    results.append(SearchResult(
                        memory=memory,
                        relevance_score=final_relevance,
                        context_match=pattern_boost > 0.1,
                        pattern_boost=pattern_boost
                    ))
                
                # Sort by final relevance and limit results
                results.sort(key=lambda x: x.relevance_score, reverse=True)
                results = results[:limit]
                
                # Update metrics
                self.metrics['total_searches'] += 1
                self.metrics['thin_mode_operations'] += 1
                
                search_time = time.time() - start_time
                self.metrics['avg_search_time'] = (
                    (self.metrics['avg_search_time'] * (self.metrics['total_searches'] - 1) + search_time) /
                    self.metrics['total_searches']
                )
                
                logger.info(f"🔍 Found {len(results)} memories for user {user_id} ({search_time:.3f}s)")
                
                return results
                
            finally:
                conn.close()
                
        except Exception as e:
            logger.error(f"❌ Error searching memories: {e}")
            return []
    
    async def get_user_patterns(self, user_id: str) -> Dict[str, UserPattern]:
        """
        Get user behavioral patterns using thin mode
        """
        try:
            # Check cache first
            cache_key = f"patterns_{user_id}"
            if cache_key in self.pattern_cache:
                cache_entry = self.pattern_cache[cache_key]
                if time.time() - cache_entry['timestamp'] < self.cache_ttl:
                    self.metrics['cache_hits'] += 1
                    return cache_entry['data']
            
            # Use thin mode for pattern analysis
            conn = await self._get_thin_connection()
            try:
                cursor = conn.cursor()
                
                sql = """
                SELECT pattern_type, pattern_data, strength, confidence, sample_count
                FROM USER_PATTERNS
                WHERE user_id = :user_id
                AND strength > 0.2
                ORDER BY strength DESC, confidence DESC
                """
                
                cursor.execute(sql, {'user_id': user_id})
                
                patterns = {}
                for row in cursor.fetchall():
                    pattern = UserPattern(
                        pattern_id=f"{user_id}_{row[0]}",
                        user_id=user_id,
                        pattern_type=row[0],
                        pattern_data=json.loads(row[1]) if row[1] else {},
                        strength=float(row[2]),
                        confidence=float(row[3]),
                        sample_count=int(row[4])
                    )
                    patterns[row[0]] = pattern
                
                # Cache the results
                self.pattern_cache[cache_key] = {
                    'data': patterns,
                    'timestamp': time.time()
                }
                
                logger.info(f"📊 Retrieved {len(patterns)} patterns for user {user_id}")
                return patterns
                
            finally:
                conn.close()
                
        except Exception as e:
            logger.error(f"❌ Error getting user patterns: {e}")
            return {}
    
    async def _get_user_patterns_cached(self, user_id: str) -> Dict[str, UserPattern]:
        """Get user patterns with caching"""
        return await self.get_user_patterns(user_id)
    
    async def _extract_patterns_background(self, user_id: str, content: str, memory_id: str):
        """
        Background pattern extraction using thin mode
        """
        try:
            # Use thin mode for heavy pattern analysis
            conn = await self._get_thin_connection()
            try:
                cursor = conn.cursor()
                
                # Extract communication patterns
                comm_patterns = self._analyze_communication_style(content)
                if comm_patterns:
                    await self._update_user_pattern(
                        cursor, user_id, 'communication_style', comm_patterns
                    )
                
                # Extract topic interests
                topic_patterns = self._analyze_topic_interests(content)
                if topic_patterns:
                    await self._update_user_pattern(
                        cursor, user_id, 'topic_interest', topic_patterns
                    )
                
                # Extract response preferences
                response_patterns = self._analyze_response_preferences(content)
                if response_patterns:
                    await self._update_user_pattern(
                        cursor, user_id, 'response_style', response_patterns
                    )
                
                conn.commit()
                
                # Clear pattern cache for this user
                cache_key = f"patterns_{user_id}"
                if cache_key in self.pattern_cache:
                    del self.pattern_cache[cache_key]
                
                self.metrics['pattern_extractions'] += 1
                logger.info(f"🧠 Extracted patterns for user {user_id} from memory {memory_id[:8]}...")
                
            finally:
                conn.close()
                
        except Exception as e:
            logger.error(f"❌ Error extracting patterns: {e}")
    
    async def _update_user_pattern(self, cursor, user_id: str, pattern_type: str, pattern_data: Dict):
        """Update or create user pattern"""
        try:
            # Check if pattern exists
            cursor.execute("""
                SELECT pattern_id, strength, sample_count
                FROM USER_PATTERNS
                WHERE user_id = :user_id AND pattern_type = :pattern_type
            """, {'user_id': user_id, 'pattern_type': pattern_type})
            
            existing = cursor.fetchone()
            
            if existing:
                # Update existing pattern
                new_strength = min(1.0, existing[1] + 0.05)  # Gradual learning
                new_sample_count = existing[2] + 1
                
                cursor.execute("""
                    UPDATE USER_PATTERNS
                    SET pattern_data = :pattern_data,
                        strength = :strength,
                        sample_count = :sample_count,
                        updated_date = SYSDATE
                    WHERE pattern_id = :pattern_id
                """, {
                    'pattern_data': json.dumps(pattern_data),
                    'strength': new_strength,
                    'sample_count': new_sample_count,
                    'pattern_id': existing[0]
                })
            else:
                # Create new pattern
                pattern_id = hashlib.md5(f"{user_id}_{pattern_type}_{datetime.now().isoformat()}".encode()).hexdigest()
                
                cursor.execute("""
                    INSERT INTO USER_PATTERNS (
                        pattern_id, user_id, pattern_type, pattern_data,
                        strength, confidence, sample_count
                    ) VALUES (
                        :pattern_id, :user_id, :pattern_type, :pattern_data,
                        0.3, 0.5, 1
                    )
                """, {
                    'pattern_id': pattern_id,
                    'user_id': user_id,
                    'pattern_type': pattern_type,
                    'pattern_data': json.dumps(pattern_data)
                })
                
        except Exception as e:
            logger.error(f"❌ Error updating pattern: {e}")
    
    def _analyze_communication_style(self, content: str) -> Dict:
        """Analyze communication style from content"""
        patterns = {}
        
        # Analyze formality
        formal_indicators = ['please', 'thank you', 'could you', 'would you']
        informal_indicators = ['hey', 'yeah', 'ok', 'cool']
        
        formal_count = sum(1 for indicator in formal_indicators if indicator in content.lower())
        informal_count = sum(1 for indicator in informal_indicators if indicator in content.lower())
        
        if formal_count > informal_count:
            patterns['formality'] = 'formal'
        elif informal_count > formal_count:
            patterns['formality'] = 'informal'
        else:
            patterns['formality'] = 'balanced'
        
        # Analyze question style
        question_count = content.count('?')
        if question_count > 2:
            patterns['question_style'] = 'inquisitive'
        elif question_count > 0:
            patterns['question_style'] = 'moderate'
        else:
            patterns['question_style'] = 'declarative'
        
        # Analyze length preference
        word_count = len(content.split())
        if word_count > 50:
            patterns['length_preference'] = 'detailed'
        elif word_count > 20:
            patterns['length_preference'] = 'moderate'
        else:
            patterns['length_preference'] = 'concise'
        
        return patterns
    
    def _analyze_topic_interests(self, content: str) -> Dict:
        """Analyze topic interests from content"""
        topics = {
            'technology': ['python', 'programming', 'code', 'software', 'api', 'database', 'ai', 'ml'],
            'business': ['strategy', 'management', 'marketing', 'sales', 'revenue', 'profit'],
            'science': ['research', 'study', 'analysis', 'data', 'experiment', 'theory'],
            'education': ['learn', 'teach', 'course', 'training', 'knowledge', 'skill']
        }
        
        interests = {}
        content_lower = content.lower()
        
        for topic, keywords in topics.items():
            score = sum(1 for keyword in keywords if keyword in content_lower)
            if score > 0:
                interests[topic] = min(1.0, score / len(keywords))
        
        return interests
    
    def _analyze_response_preferences(self, content: str) -> Dict:
        """Analyze response preferences from content"""
        preferences = {}
        
        # Analyze detail preference
        detail_indicators = ['explain', 'detail', 'how', 'why', 'example']
        detail_score = sum(1 for indicator in detail_indicators if indicator in content.lower())
        
        if detail_score > 2:
            preferences['detail_level'] = 'high'
        elif detail_score > 0:
            preferences['detail_level'] = 'medium'
        else:
            preferences['detail_level'] = 'low'
        
        # Analyze example preference
        if 'example' in content.lower() or 'show me' in content.lower():
            preferences['wants_examples'] = True
        else:
            preferences['wants_examples'] = False
        
        return preferences
    
    def _prepare_oracle_text_query(self, query: str) -> str:
        """Prepare query for Oracle Text search"""
        # Clean and prepare query for Oracle Text
        cleaned_query = re.sub(r'[^\w\s]', ' ', query)
        words = cleaned_query.split()
        
        if len(words) == 1:
            return words[0]
        elif len(words) <= 3:
            return ' AND '.join(words)
        else:
            # For longer queries, use fuzzy matching
            return ' OR '.join(words[:5])
    
    def _calculate_pattern_boost(self, memory: MemoryItem, user_patterns: Dict, query: str) -> float:
        """Calculate pattern-based relevance boost"""
        boost = 0.0
        
        # Boost based on communication style match
        if 'communication_style' in user_patterns:
            comm_pattern = user_patterns['communication_style']
            if comm_pattern.pattern_data.get('formality') == 'formal' and len(query.split()) > 10:
                boost += 0.1 * comm_pattern.strength
        
        # Boost based on topic interests
        if 'topic_interest' in user_patterns:
            topic_pattern = user_patterns['topic_interest']
            for topic, score in topic_pattern.pattern_data.items():
                if any(keyword in memory.content.lower() for keyword in [topic]):
                    boost += 0.15 * score * topic_pattern.strength
        
        # Boost recent memories
        try:
            memory_date = datetime.fromisoformat(memory.created_date)
            days_old = (datetime.now() - memory_date).days
            if days_old < 7:
                boost += 0.1 * (7 - days_old) / 7
        except:
            pass
        
        return min(0.5, boost)  # Cap boost at 0.5
    
    async def get_metrics(self) -> Dict:
        """Get service performance metrics"""
        return {
            **self.metrics,
            'thin_pool_status': 'active' if self.thin_pool else 'inactive',
            'wallet_pool_status': 'active' if self.wallet_pool else 'inactive',
            'cache_size': len(self.pattern_cache),
            'uptime': time.time()
        }
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.thin_pool:
                self.thin_pool.close()
            if self.wallet_pool:
                self.wallet_pool.close()
            self.executor.shutdown(wait=True)
            logger.info("🧹 Oracle Advanced Memory Service cleaned up")
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")

# Example usage and testing
async def main():
    """Test the Oracle Advanced Memory Service"""
    config = {
        'oracle_user': 'ADMIN',
        'oracle_password': 'your_password',
        'oracle_dsn': 'your_dsn',
        'oracle_wallet_location': './oracle_wallet/Wallet_SWIV8HV5Y96IWO2T'
    }
    
    service = OracleAdvancedMemoryService(config)
    
    try:
        await service.initialize()
        
        # Test storing memory
        memory_id = await service.store_memory(
            user_id="test_user",
            content="I'm working on a Python project using FastAPI and need help with database optimization",
            memory_type="conversation",
            tags=["python", "fastapi", "database"]
        )
        
        print(f"Stored memory: {memory_id}")
        
        # Test searching memories
        results = await service.search_memories(
            user_id="test_user",
            query="Python database optimization",
            limit=5
        )
        
        print(f"Found {len(results)} memories")
        for result in results:
            print(f"- {result.memory.content[:50]}... (relevance: {result.relevance_score:.2f})")
        
        # Test getting patterns
        patterns = await service.get_user_patterns("test_user")
        print(f"User patterns: {list(patterns.keys())}")
        
        # Get metrics
        metrics = await service.get_metrics()
        print(f"Service metrics: {metrics}")
        
    finally:
        await service.cleanup()

if __name__ == "__main__":
    asyncio.run(main())