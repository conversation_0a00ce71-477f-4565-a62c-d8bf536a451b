# Hướng Dẫn Sử Dụng Pipeline Vision - Đơn Giản

## Tóm Tắt
Pipeline đã được tạo và deploy. Bây giờ chỉ cần làm theo các bước đơn giản để test.

## Bước 1: Kiểm Tra Pipeline Đã Hoạt Động
```bash
# Kiểm tra Open WebUI đang chạy
docker ps | grep catomanton-webui

# Kiểm tra pipeline files
ls -la webui-data/pipelines/
```

**Kết quả mong đợi:**
```
image_vision_pipeline.py
requirements.txt
```

## Bước 2: T<PERSON>y Cập Open WebUI
- Mở browser: http://localhost:3000
- Hoặc: https://catomanton.duckdns.org
- Login vào tài khoản admin

## Bước 3: Kiểm Tra Pipeline Trong Admin Panel
1. Vào **Settings** (⚙️)
2. Chọn **Admin Panel**
3. <PERSON>ọn **Pipelines**
4. <PERSON><PERSON><PERSON> **"Image Vision Pipeline"**
5. <PERSON><PERSON><PERSON> bảo nó đang **Enabled** (màu xanh)

## Bước 4: Chọn Model Có Vision
**<PERSON>uan trọng:** Phải chọn model hỗ trợ vision!

### Option A: Dùng Gemini 2.5 Flash (Khuyến nghị - Nhẹ & Nhanh)
1. Trong chat, click dropdown model
2. Chọn **gemini-2.5-flash**
3. Cần có Google API key
4. **Ưu điểm:** Nhẹ, nhanh, vision tốt, giá rẻ

### Option B: Dùng GPT-4o (Chất lượng cao)
1. Chọn **gpt-4o** hoặc **gpt-4o-mini**
2. Cần có OpenAI API key
3. **Ưu điểm:** Vision chất lượng cao

### Option C: Dùng Local LLaVA (Miễn phí nhưng TO)
```bash
# Cài LLaVA model (CẢNH BÁO: ~4.7GB)
ollama pull llava:latest
```
1. Chọn model **llava:latest**
2. Không cần API key
3. **Nhược điểm:** Model rất to (~4.7GB), chậm
4. **Chỉ dùng nếu:** Có đủ RAM và storage

### Option D: Dùng Claude 3 (Alternative)
1. Chọn **claude-3-sonnet-20240229**
2. Cần có Anthropic API key

## Bước 5: Test Upload Ảnh
1. **Tạo chat mới**
2. **Click nút attachment** (📎 hoặc 🖼️)
3. **Chọn ảnh** từ máy tính (JPEG, PNG, WebP)
4. **Gõ câu hỏi:** "Mô tả ảnh này"
5. **Gửi message**

## Bước 6: Kết Quả Mong Đợi

### ✅ Thành Công:
- LLM mô tả chi tiết nội dung ảnh
- Có thông tin về kích thước, format
- Pipeline logs hiển thị xử lý thành công

### ❌ Lỗi Thường Gặp:

**"I can't see the image"**
- ➡️ Chưa chọn vision model
- ➡️ API key không hợp lệ
- ➡️ Model không hỗ trợ vision

**"Image processing failed"**
- ➡️ File ảnh bị lỗi
- ➡️ Format không hỗ trợ
- ➡️ Pipeline chưa load

## Debug Nhanh

### Kiểm Tra Pipeline Logs:
```bash
docker logs catomanton-webui | grep -i "image\|pipeline" | tail -10
```

### Restart Pipeline:
```bash
docker restart catomanton-webui
sleep 10
```

### Test Pipeline Trực Tiếp:
```bash
# Test endpoint backend (optional)
curl -s http://localhost:8000/api/v1/images/health
```

## Cấu Hình API Keys (Nếu Cần)

### Cho Gemini 2.5 Flash (Khuyến nghị):
```bash
echo "GOOGLE_API_KEY=your-google-api-key-here" >> .env.catomanton
docker restart catomanton-webui
```

### Cho GPT-4o:
```bash
echo "OPENAI_API_KEY=sk-your-key-here" >> .env.catomanton
docker restart catomanton-webui
```

### Cho Claude:
```bash
echo "ANTHROPIC_API_KEY=sk-ant-your-key-here" >> .env.catomanton
docker restart catomanton-webui
```

## Test Case Đơn Giản

### 1. Chuẩn Bị:
- Có 1 ảnh đơn giản (ví dụ: ảnh con mèo, xe hơi, etc.)
- Format: JPEG hoặc PNG
- Size: < 10MB

### 2. Thực Hiện:
1. Vào http://localhost:3000
2. Chọn model: **gemini-2.5-flash** (khuyến nghị) hoặc **gpt-4o**
3. Upload ảnh
4. Hỏi: "What's in this image?"

### 3. Kết Quả:
- Model sẽ mô tả ảnh chi tiết
- Ví dụ: "I can see a orange cat sitting on a wooden table..."

## Troubleshooting Nhanh

### Pipeline Không Hoạt Động:
```bash
# Restart everything
docker restart catomanton-webui
sleep 15

# Check status
docker ps | grep catomanton-webui
```

### Model Không Thấy Ảnh:
1. Đảm bảo chọn đúng vision model
2. Kiểm tra API key hợp lệ
3. Thử model khác (LLaVA local)

### Upload Ảnh Lỗi:
1. Thử ảnh khác (JPEG/PNG)
2. Giảm size ảnh (< 5MB)
3. Restart browser

## Kết Luận

**Pipeline đã sẵn sàng!** Chỉ cần:
1. ✅ Chọn vision model (gemini-2.5-flash khuyến nghị)
2. ✅ Upload ảnh
3. ✅ Hỏi LLM về ảnh
4. ✅ Nhận mô tả chi tiết

**Lưu ý:** Pipeline tự động xử lý ảnh, bạn không cần làm gì thêm!

## So Sánh Model Sizes

| Model | Size | RAM Cần | Tốc Độ | Vision Quality | Chi Phí |
|-------|------|---------|--------|----------------|---------|
| **gemini-2.5-flash** | 0MB (API) | 0MB | ⚡ Rất nhanh | 🌟 Tốt | 💰 Rẻ |
| **gpt-4o-mini** | 0MB (API) | 0MB | ⚡ Nhanh | 🌟 Tốt | 💰 Rẻ |
| **gpt-4o** | 0MB (API) | 0MB | 🐌 Chậm | ⭐ Xuất sắc | 💸 Đắt |
| **llava:latest** | ~4.7GB | 8GB+ | 🐌 Rất chậm | 🌟 Tạm được | 🆓 Miễn phí |

**Khuyến nghị:** Dùng **gemini-2.5-flash** - nhẹ, nhanh, rẻ, vision tốt!