# 🕷️ Jina Crawler - <PERSON>ết cấu và Tính năng

## 🏗️ **Ki<PERSON>n trúc tổng thể**

```
Jina Crawler System
├── 🕷️ JiniCrawler (Core Engine)
├── 🤖 GeminiProcessor (AI Processing)
├── 🎭 JinaStyleCrawler (Advanced Playwright)
└── 📡 MCP Server (API Interface)
```

## 🔧 **Ba Engine Crawler**

### **1. <PERSON><PERSON><PERSON><PERSON><PERSON> (Lightweight Engine)**
**File**: [`jini_crawler.py`](mcp-integration/servers/jina_crawler/jini_crawler.py:1)

**Đặc điểm**:
- **Engine**: `aiohttp + BeautifulSoup + Gemini`
- **Ưu điểm**: <PERSON><PERSON><PERSON>, nhẹ, không c<PERSON>n Playwright
- **Use case**: Crawling cơ bản, production-ready

**Tính năng chính**:
```python
class JiniCrawler:
    - async def scrap_url()          # Scrape với BeautifulSoup
    - async def crawl_and_process()  # Pipeline hoàn chỉnh
    - async def health_check()       # Kiểm tra sức khỏe
    - _smart_truncate()              # Cắt văn bản thông minh
```

**Pipeline xử lý**:
```
URL → aiohttp Request → BeautifulSoup Clean → Gemini AI → Markdown
```

### **2. JinaStyleCrawler (Advanced Engine)**
**File**: [`jina_style_crawler.py`](mcp-integration/servers/jina_crawler/jina_style_crawler.py:1)

**Đặc điểm**:
- **Engine**: `Playwright + BeautifulSoup + Gemini`
- **Ưu điểm**: Xử lý JavaScript, snapshot-based crawling
- **Use case**: Dynamic content, complex websites

**Tính năng nâng cao**:
```python
class JinaStyleCrawler:
    - async def scrap_url()          # Jina-style snapshots
    - content_extraction_script      # JavaScript injection
    - RequestController              # Request monitoring
    - _handle_request()              # Request filtering
```

**Snapshot System**:
```python
@dataclass
class PageSnapshot:
    title: str
    description: str
    html: str
    text: str
    parsed: Dict
    imgs: List[Dict]
    max_elem_depth: int
    elem_count: int
    is_intermediate: bool
```

### **3. GeminiProcessor (AI Core)**
**File**: [`gemini_processor.py`](mcp-integration/servers/jina_crawler/gemini_processor.py:1)

**Đặc điểm**:
- **Engine**: `Gemini 2.5 Flash Lite API`
- **Ưu điểm**: Sub-second processing, Vietnamese optimized
- **Use case**: Content transformation, AI processing

**AI Capabilities**:
```python
class GeminiProcessor:
    - async def process_content()    # Main processing
    - _smart_truncate()              # Vietnamese-aware truncation
    - _call_gemini_api()             # API with retry logic
    - health_check()                 # AI health monitoring
```

## 🚀 **MCP Tools Available**

### **1. Core Crawling Tools**
```json
{
  "crawl_url": {
    "description": "Crawl và xử lý 1 URL với AI",
    "parameters": {
      "url": "string",
      "max_content_length": "integer (default: 10000)"
    }
  },
  "crawl_batch": {
    "description": "Crawl nhiều URL đồng thời",
    "parameters": {
      "urls": "array[string]",
      "max_content_length": "integer (default: 10000)"
    }
  }
}
```

### **2. Search & Discovery Tools**
```json
{
  "search_site": {
    "description": "Tìm kiếm trong website",
    "parameters": {
      "site_url": "string",
      "query": "string",
      "max_results": "integer (default: 10)"
    }
  }
}
```

### **3. Monitoring Tools**
```json
{
  "health_check": {
    "description": "Kiểm tra sức khỏe hệ thống",
    "parameters": {}
  },
  "get_crawler_stats": {
    "description": "Thống kê hiệu suất",
    "parameters": {}
  }
}
```

## 🇻🇳 **Vietnamese Optimization**

### **Smart Truncation**
```python
vietnamese_endings = [
    '. ', '.\n', '? ', '?\n', '! ', '!\n',
    '." ', '."', '?" ', '?"', '!" ', '!"',
    '.)', '.)', '?)', '?)', '!)', '!)'
]
```

### **Vietnamese Prompts**
```python
prompts = {
    "html_to_markdown": """Convert the following HTML content to clean, well-formatted Markdown. 
    Preserve Vietnamese text exactly, maintain proper formatting...""",
    
    "summarize": """Summarize the following Vietnamese content in a concise manner 
    while preserving key information...""",
    
    "clean": """Clean and format the following Vietnamese content to readable Markdown..."""
}
```

## ⚡ **Performance Features**

### **1. Async Processing**
- **Concurrent requests**: 32 per page
- **Parallel batch processing**: Multiple URLs simultaneously
- **Non-blocking I/O**: Full async/await support

### **2. Smart Content Handling**
- **Content length limits**: 8000 characters default
- **Output tokens**: Up to 65K tokens
- **Smart truncation**: Preserves sentence structure

### **3. Retry Logic**
```python
for attempt in range(max_retries + 1):
    try:
        # API call
    except Exception:
        wait_time = 2 ** attempt  # Exponential backoff
        await asyncio.sleep(wait_time)
```

### **4. Request Filtering**
```python
# Block resource types for performance
blocked_types = ['image', 'media', 'font']

# Block tracking domains
blocked_domains = [
    'google-analytics.com', 'googletagmanager.com',
    'facebook.com', 'doubleclick.net'
]
```

## 🔒 **Security & Authentication**

### **HTTPBearer Security**
```json
{
  "securitySchemes": {
    "HTTPBearer": {
      "type": "http",
      "scheme": "bearer"
    }
  }
}
```

### **Environment Configuration**
```bash
GEMINI_API_KEY=AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM
GEMINI_MODEL_NAME=gemini-2.5-flash-lite
GEMINI_API_URL=https://generativelanguage.googleapis.com/v1beta/models
GEMINI_TIMEOUT=30
GEMINI_MAX_RETRIES=3
```

## 📊 **Workflow Examples**

### **Single URL Crawl**
```mermaid
graph TD
    A[URL Input] --> B[HTTP Request]
    B --> C[BeautifulSoup Clean]
    C --> D[Extract Main Content]
    D --> E[Smart Truncation]
    E --> F[Gemini Processing]
    F --> G[Markdown Output]
```

### **Batch Processing**
```mermaid
graph TD
    A[URLs Array] --> B[Create Async Tasks]
    B --> C[Parallel Execution]
    C --> D[Individual Results]
    D --> E[Aggregate Response]
    E --> F[Success/Failure Stats]
```

### **Site Search**
```mermaid
graph TD
    A[Site URL + Query] --> B[Try Internal Search]
    B --> C{Found Results?}
    C -->|Yes| D[Extract Results]
    C -->|No| E[Google Site Search]
    E --> F[Parse Google Results]
    D --> G[Return Results]
    F --> G
```

## 🎯 **Use Cases**

### **1. News Crawling**
- Tối ưu cho các trang tin tức Việt Nam (dantri.com.vn, vnexpress.net)
- Xử lý layout phức tạp với ads và navigation
- Trích xuất nội dung chính chính xác

### **2. Content Transformation**
- HTML → Markdown conversion
- Preserve Vietnamese formatting
- Clean output for AI processing

### **3. Research & Analysis**
- Batch crawling for data collection
- Site-specific search capabilities
- Performance monitoring and stats

### **4. API Integration**
- MCP protocol compliance
- Open WebUI integration
- RESTful API endpoints

## 📈 **Performance Metrics**

### **Speed Benchmarks**
- **Gemini Processing**: Sub-second (0.90s average)
- **Simple Crawl**: 2-5 seconds per URL
- **Batch Processing**: Parallel execution
- **Memory Usage**: Lightweight with cleanup

### **Reliability Features**
- **Health Checks**: Real-time monitoring
- **Error Recovery**: Comprehensive exception handling
- **Circuit Breaker**: Prevent host overload
- **Retry Logic**: Exponential backoff

## 🔧 **Configuration Options**

### **Crawler Settings**
```python
timeout = 30                    # Request timeout
concurrent_requests = 32        # Max concurrent requests
max_content_length = 10000      # Content processing limit
user_agent = "Mozilla/5.0..."   # Browser user agent
```

### **Gemini Settings**
```python
model_name = "gemini-2.5-flash-lite"
max_output_tokens = 65536
temperature = 0.0               # Deterministic output
max_retries = 3                 # API retry attempts
```

## 🚨 **Known Limitations**

1. **Authentication Required**: HTTPBearer token needed for API access
2. **Rate Limits**: Gemini API rate limiting applies
3. **Content Size**: 8000 character limit for optimal performance
4. **JavaScript**: JiniCrawler doesn't execute JS (use JinaStyleCrawler)

## 📝 **Integration Status**

- ✅ **MCP Protocol**: Fully compliant
- ✅ **Vietnamese Support**: Optimized
- ✅ **Open WebUI**: Ready for integration
- ⚠️ **Authentication**: Requires API key configuration
- ✅ **Performance**: Production-ready
- ✅ **Error Handling**: Comprehensive

---

**Kết luận**: Jina Crawler là một hệ thống crawling tiên tiến với 3 engine khác nhau, tối ưu đặc biệt cho nội dung tiếng Việt, sử dụng AI Gemini để chuyển đổi nội dung web thành Markdown chất lượng cao. Hệ thống hỗ trợ cả crawling đơn lẻ và batch processing, với khả năng tìm kiếm trong website và monitoring hiệu suất real-time.