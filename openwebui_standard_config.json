{"mcpServers": {"mcpo_complete_proxy_8000": {"transport": {"type": "http", "url": "http://mcpo-complete-proxy-8000:8000"}, "description": "MCPO Complete Proxy - 12 MCP tools with namespace separation"}, "jina_crawler_8002": {"transport": {"type": "http", "url": "http://jina-crawler-mcp-proxy-8002:8002"}, "description": "<PERSON><PERSON> - Advanced web crawling with AI processing"}, "pandas_unified_8004": {"transport": {"type": "http", "url": "http://pandas-unified-server:8004"}, "description": "Pandas Server - Data analysis tools via HTTP"}, "document_processing": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/document_processing/server.py"], "env": {}, "description": "Document processing and analysis tools"}, "vietnamese_language": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/vietnamese_language/server.py"], "env": {}, "description": "Vietnamese language processing and translation"}, "web_automation": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/web_automation/server_playwright.py"], "env": {}, "description": "Web browser automation with Playwright"}, "time_utilities": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/time_utilities/server.py"], "env": {}, "description": "Time and timezone utilities"}, "weather_service": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/weather_service/server.py"], "env": {}, "description": "Weather information service"}, "filesystem": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/filesystem/server.py"], "env": {}, "description": "File system operations"}, "wikipedia": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/wikipedia/server.py"], "env": {}, "description": "Wikipedia search and information retrieval"}, "sqlite": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/sqlite/server.py"], "env": {}, "description": "SQLite database operations"}, "github": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/github/server.py"], "env": {}, "description": "GitHub repository search and operations"}, "brave_search": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/brave_search/server.py"], "env": {"BRAVE_API_KEY": "BSAWh8paqNo8JFJaBmvd6WX7CqT2_Sk"}, "description": "Brave Search API for web search"}, "gemini_search_engine": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/gemini_search_engine/server_with_grounding.py"], "env": {"GEMINI_API_KEY": "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"}, "description": "Gemini-powered search engine"}, "gemini_cli_tools": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/gemini_cli_tools/server.py"], "env": {"GEMINI_API_KEY": "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM", "GEMINI_UNRESTRICTED_MODE": "false", "GEMINI_RBAC_ENABLED": "true"}, "description": "Gemini CLI tools with RBAC"}, "mem0_system": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/mem0_system/server.py"], "env": {}, "description": "Mem0 memory storage and retrieval system"}}}