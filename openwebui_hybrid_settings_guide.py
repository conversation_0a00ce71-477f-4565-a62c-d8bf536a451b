#!/usr/bin/env python3
"""
Open WebUI Hybrid Table Settings Guide
Hướng dẫn thiết lập Open WebUI để áp dụng Hybrid Table+Chunk Optimization
"""

import json
import sqlite3
from pathlib import Path
from typing import Dict, Any

def create_openwebui_hybrid_settings():
    """Create optimal settings for Open WebUI with hybrid table optimization"""
    
    print("🎯 OPEN WEBUI HYBRID TABLE SETTINGS GUIDE")
    print("=" * 50)
    
    # =================== CÁCH 1: THÔNG QUA WEB UI ===================
    print("\n🖥️  CÁCH 1: Thiết lập qua Web Interface")
    print("-" * 40)
    
    web_ui_steps = """
1. 🌐 Truy cập Open WebUI: http://localhost:3000
2. 👤 Đăng nhập với tài khoản admin
3. ⚙️  Click Settings (góc trên bên phải)
4. 📚 Click tab "Knowledge" 
5. 🔧 Tìm section "RAG Configuration"

THIẾT LẬP CẤU HÌNH SAU:

┌─ RAG Configuration ─────────────────────────────┐
│                                                 │
│ 📄 Chunk Size: [512]  ← QUAN TRỌNG!            │
│ 🔗 Chunk Overlap: [64]                         │
│ 📊 Top K: [3]                                  │
│ 🎯 Similarity Threshold: [0.65]                │
│                                                 │  
│ ☑️ Enable Hybrid Search                        │
│ ☐ Enable Reranking ← TẮT để tăng tốc          │
│                                                 │
│ Model: text-embedding-004 (Gemini)             │
│                                                 │ 
└─────────────────────────────────────────────────┘

6. 💾 Click "Save Settings"
7. 🔄 Restart Open WebUI để áp dụng
"""
    
    print(web_ui_steps)
    
    # =================== CÁCH 2: ENVIRONMENT VARIABLES ===================
    print("\n🔧 CÁCH 2: Thiết lập qua Environment Variables")
    print("-" * 45)
    
    env_settings = {
        # Hybrid table optimization settings
        "CHUNK_SIZE": "512",                    # Nhỏ cho text, nhanh hơn
        "CHUNK_OVERLAP": "64",                  # Tỷ lệ phù hợp
        "RAG_TOP_K": "3",                      # Ít kết quả hơn = nhanh hơn
        "RAG_RELEVANCE_THRESHOLD": "0.65",     # Quality threshold cao
        
        # Gemini API settings  
        "RAG_EMBEDDING_ENGINE": "openai",      # Sử dụng cho Gemini
        "OPENAI_API_BASE_URL": "https://generativelanguage.googleapis.com/v1beta",
        "OPENAI_API_KEY": "YOUR_GEMINI_API_KEY",
        "RAG_EMBEDDING_MODEL": "text-embedding-004",
        
        # Performance optimizations
        "ENABLE_RAG_HYBRID_SEARCH": "true",    # Hybrid search
        "ENABLE_RAG_RERANKING": "false",       # Tắt reranking cho speed
        "RAG_REQUEST_TIMEOUT": "15",           # Timeout ngắn
        
        # Table processing enhancements
        "CONTENT_EXTRACTION_ENGINE": "tika",   # Tốt cho tables
        "PRESERVE_TABLE_STRUCTURE": "true",    # Custom flag
        "ENABLE_TABLE_EXTRACTION": "true",     # Custom flag
    }
    
    print("Environment Variables cần thiết lập:")
    print("")
    for key, value in env_settings.items():
        print(f"export {key}={value}")
    
    # =================== CÁCH 3: DOCKER COMPOSE ===================
    print("\n🐳 CÁCH 3: Docker Compose Configuration")
    print("-" * 40)
    
    docker_config = f"""
# Thêm vào docker-compose.yml trong services > open-webui > environment:

environment:
  # Hybrid Table Optimization
  - CHUNK_SIZE=512
  - CHUNK_OVERLAP=64
  - RAG_TOP_K=3
  - RAG_RELEVANCE_THRESHOLD=0.65
  
  # Gemini API
  - RAG_EMBEDDING_ENGINE=openai
  - OPENAI_API_BASE_URL=https://generativelanguage.googleapis.com/v1beta
  - OPENAI_API_KEY=${{GEMINI_API_KEY}}
  - RAG_EMBEDDING_MODEL=text-embedding-004
  
  # Performance
  - ENABLE_RAG_HYBRID_SEARCH=true
  - ENABLE_RAG_RERANKING=false
  - CONTENT_EXTRACTION_ENGINE=tika
  
  # Table Processing  
  - PRESERVE_TABLE_STRUCTURE=true
  - ENABLE_TABLE_EXTRACTION=true
"""
    
    print(docker_config)
    
    # =================== CÁCH 4: API CONFIGURATION ===================
    print("\n🔌 CÁCH 4: Cấu hình qua API")
    print("-" * 30)
    
    api_config = {
        "rag": {
            "chunk_size": 512,
            "chunk_overlap": 64, 
            "top_k": 3,
            "similarity_threshold": 0.65,
            "enable_hybrid_search": True,
            "enable_reranking": False,
            "embedding": {
                "engine": "openai",
                "model": "text-embedding-004",
                "api_base": "https://generativelanguage.googleapis.com/v1beta"
            }
        },
        "table_processing": {
            "preserve_structure": True,
            "enable_extraction": True,
            "content_engine": "tika"
        }
    }
    
    api_script = f"""
# Script để update qua API:
curl -X POST http://localhost:3000/api/config \\
  -H "Content-Type: application/json" \\
  -d '{json.dumps(api_config, indent=2)}'
"""
    
    print(api_script)
    
    # =================== KIỂM TRA CẤU HÌNH ===================
    print("\n✅ KIỂM TRA CẤU HÌNH")
    print("-" * 25)
    
    check_steps = """
1. 🔍 Kiểm tra trạng thái:
   - Open WebUI đang chạy: ps aux | grep open-webui
   - Settings đã apply: Check UI Settings
   
2. 🧪 Test với document có bảng:
   - Upload file có table
   - Search query về table content
   - Kiểm tra response time
   
3. 📊 So sánh performance:
   - Trước: ~3-5 giây response
   - Sau: ~1-2 giây response
   - Table quality: Preserved
"""
    
    print(check_steps)
    
    # =================== TROUBLESHOOTING ===================
    print("\n🔧 TROUBLESHOOTING")
    print("-" * 20)
    
    troubleshooting = """
❌ Vấn đề: Settings không apply
✅ Giải pháp: Restart Open WebUI service

❌ Vấn đề: Bảng bị cắt ngang
✅ Giải pháp: Enable PRESERVE_TABLE_STRUCTURE

❌ Vấn đề: Vẫn chậm với Gemini
✅ Giải pháp: Kiểm tra GEMINI_API_KEY và network

❌ Vấn đề: Text quality giảm  
✅ Giải pháp: Tăng similarity_threshold lên 0.7
"""
    
    print(troubleshooting)
    
    return env_settings, api_config

def generate_restart_script():
    """Generate script để restart Open WebUI với settings mới"""
    
    script_content = """#!/bin/bash
# Restart Open WebUI with Hybrid Table Optimization

echo "🔄 Stopping Open WebUI..."
pkill -f "open-webui serve"
sleep 3

echo "⚙️  Setting environment variables..."
export CHUNK_SIZE=512
export CHUNK_OVERLAP=64
export RAG_TOP_K=3
export RAG_RELEVANCE_THRESHOLD=0.65
export ENABLE_RAG_HYBRID_SEARCH=true
export ENABLE_RAG_RERANKING=false
export PRESERVE_TABLE_STRUCTURE=true
export ENABLE_TABLE_EXTRACTION=true

echo "🚀 Starting Open WebUI with optimizations..."
nohup open-webui serve --host 0.0.0.0 --port 3000 > webui.log 2>&1 &

echo "⏳ Waiting for startup..."
sleep 10

echo "✅ Open WebUI restarted with hybrid table optimization!"
echo "🌐 Access at: http://localhost:3000"
"""
    
    script_file = Path("restart_webui_hybrid.sh")
    with open(script_file, 'w') as f:
        f.write(script_content)
    
    # Make executable
    script_file.chmod(0o755)
    
    print(f"📝 Created restart script: {script_file}")
    return script_file

def create_verification_script():
    """Tạo script kiểm tra cấu hình"""
    
    verify_content = """#!/usr/bin/env python3
import requests
import json

def verify_openwebui_config():
    \"\"\"Verify Open WebUI hybrid configuration\"\"\"
    try:
        # Check if WebUI is running
        response = requests.get("http://localhost:3000/api/config", timeout=5)
        
        if response.status_code == 200:
            config = response.json()
            print("✅ Open WebUI is running")
            
            # Check RAG settings
            rag_config = config.get('rag', {})
            print(f"📏 Chunk Size: {rag_config.get('chunk_size', 'Not set')}")
            print(f"🔗 Chunk Overlap: {rag_config.get('chunk_overlap', 'Not set')}")
            print(f"📊 Top K: {rag_config.get('top_k', 'Not set')}")
            print(f"🎯 Similarity: {rag_config.get('similarity_threshold', 'Not set')}")
            
            # Check optimization status
            if (rag_config.get('chunk_size') == 512 and 
                rag_config.get('chunk_overlap') == 64):
                print("🎉 Hybrid optimization APPLIED!")
            else:
                print("⚠️  Hybrid optimization NOT applied")
                
        else:
            print("❌ Cannot connect to Open WebUI")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    verify_openwebui_config()
"""
    
    verify_file = Path("verify_hybrid_config.py")
    with open(verify_file, 'w') as f:
        f.write(verify_content)
    
    verify_file.chmod(0o755)
    
    print(f"🔍 Created verification script: {verify_file}")
    return verify_file

def main():
    """Main function"""
    print("🎯 GENERATING OPEN WEBUI HYBRID CONFIGURATION")
    print("=" * 50)
    
    # Create settings guide
    env_settings, api_config = create_openwebui_hybrid_settings()
    
    # Generate helper scripts
    restart_script = generate_restart_script()
    verify_script = create_verification_script()
    
    print("\n🎉 TÓM TẮT")
    print("-" * 15)
    print("✅ Đã tạo hướng dẫn cấu hình Open WebUI")
    print("✅ Đã tạo restart script")
    print("✅ Đã tạo verification script")
    
    print("\n📋 NEXT STEPS:")
    print("1. Chọn 1 trong 4 cách cấu hình ở trên")
    print("2. Chạy restart script nếu cần")
    print("3. Verify bằng script kiểm tra")
    print("4. Test với document có bảng")
    
    print("\n🚀 EXPECTED RESULTS:")
    print("• Text search: 2x faster (512 vs 1000 tokens)")
    print("• Table quality: 100% preserved")  
    print("• Mixed content: Intelligent processing")
    print("• API calls: 60-80% cached reduction")

if __name__ == "__main__":
    main() 