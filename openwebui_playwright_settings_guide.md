# Open WebUI Playwright Settings Guide

## 🎭 PLAYWRIGHT TRONG OPEN WEBUI SETTINGS

### ❌ **QUAN TRỌNG: KHÔNG CẦN CÀI ĐẶT TRONG UI!**

Playwright **KHÔNG** được cài đặt qua Open WebUI Settings UI. Thay vào đó:

### ✅ **CÁCH HOẠT ĐỘNG THỰC TẾ:**

1. **Playwright tích hợp qua Environment Variables**
   - Open WebUI tự động detect Playwright khi có env vars
   - Không có UI setting riêng cho Playwright
   - Hoạt động "behind the scenes"

2. **Web Content Extraction Settings:**
   ```
   Settings → Admin → General → Content Extraction
   - Content Extraction Engine: "playwright" (auto-detected)
   - Web Fetch Timeout: 60 seconds
   - Enable JavaScript Rendering: ✅
   ```

3. **RAG Web Search Settings:**
   ```
   Settings → Admin → RAG → Web Search
   - Enable Web Search: ✅
   - Web Search Engine: searxng/brave/google
   - Max Results: 5-10
   ```

### 🔗 **KHÔNG CẦN WEBSOCKET URL TRONG SETTINGS**

- Playwright WebSocket chỉ dùng cho debugging
- Open WebUI không cần WebSocket URL
- Chỉ cần environment variables đã được set

### 📋 **KIỂM TRA PLAYWRIGHT HOẠT ĐỘNG:**

1. **Vào Open WebUI**: http://**************:3000
2. **Test command**: "Search web for latest Python news"
3. **Hoặc**: "Get content from https://httpbin.org/html"
4. **Nếu hoạt động** → Playwright đang work

### 🛠️ **TROUBLESHOOTING:**

- **Nếu web search không work**: Check environment variables
- **Nếu JavaScript sites fail**: Check Playwright browsers installed
- **Nếu timeout errors**: Increase WEB_FETCH_TIMEOUT

### ⚡ **CURRENT STATUS:**
- ✅ Playwright: Installed & Configured
- ✅ Environment: Loaded via persistent config
- ✅ Open WebUI: Ready to use Playwright
- ✅ No additional UI setup needed 