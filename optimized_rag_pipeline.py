
"""
RAG Optimization Pipeline for Open WebUI
Optimized RAG system with Vietnamese language support and context-aware processing
"""

import sys
import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from rag_pipeline_integration import RAGPipelineManager, PipelineCompatibilityLayer
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure rag_pipeline_integration.py is in the same directory")
    # Fallback minimal implementation
    class PipelineCompatibilityLayer:
        def __init__(self):
            self.type = "filter"
            self.name = "RAG Optimization System (Fallback)"
            self.description = "Optimized RAG system - fallback mode"
        
        async def inlet(self, body: Dict[str, Any], user: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
            return body

class Pipeline:
    """Open WebUI Pipeline Class"""
    
    def __init__(self):
        try:
            # Initialize the RAG pipeline manager
            self.manager = RAGPipelineManager()
            self.pipeline = self.manager.get_pipeline()
            
            # Pipeline metadata
            self.type = "filter"  # or "function" depending on your needs
            self.name = "RAG Optimization System"
            self.description = """
            🚀 Optimized RAG system with Vietnamese language support
            
            Features:
            • Context-aware chunking for better information retention
            • Multi-strategy retrieval with query expansion
            • Vietnamese language processing optimization
            • Rich metadata storage (timestamps, speakers, topics)
            • Intelligent conversation boundary detection
            
            Addresses "goldfish brain" issues with enhanced memory and retrieval.
            """
            
        except Exception as e:
            print(f"Pipeline initialization error: {e}")
            # Fallback initialization
            self.pipeline = PipelineCompatibilityLayer()
            self.type = self.pipeline.type
            self.name = self.pipeline.name
            self.description = self.pipeline.description
    
    async def inlet(self, body: Dict[str, Any], user: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process incoming requests through the optimized RAG system
        
        Args:
            body: Request body from Open WebUI
            user: User information (optional)
            
        Returns:
            Processed body with enhanced RAG capabilities
        """
        try:
            # Use the optimized pipeline
            result = await self.pipeline.inlet(body, user)
            return result
            
        except Exception as e:
            print(f"Pipeline processing error: {e}")
            # Fallback: return original body
            return body
    
    def get_status(self) -> Dict[str, Any]:
        """Get pipeline status information"""
        return {
            "status": "active",
            "type": self.type,
            "name": self.name,
            "features": [
                "Context-aware chunking",
                "Vietnamese language support", 
                "Multi-strategy retrieval",
                "Rich metadata storage",
                "Query expansion"
            ]
        }

# Create pipeline instance for Open WebUI
pipeline = Pipeline()

# Export required attributes
type = pipeline.type
name = pipeline.name
description = pipeline.description

# Export required methods
async def inlet(body, user=None):
    return await pipeline.inlet(body, user)
