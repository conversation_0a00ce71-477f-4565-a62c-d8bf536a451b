#!/usr/bin/env python3

import re

def main():
    print("🔧 Carefully adding table extraction to DoclingLoader...")
    
    # Read the current file
    with open('/app/backend/open_webui/retrieval/loaders/main.py', 'r') as f:
        content = f.read()
    
    # Check if table extraction method already exists
    if '_extract_table_text' in content:
        print("✅ Table extraction method already exists")
        return
    
    # Find the DoclingLoader class
    docling_match = re.search(r'class DoclingLoader:', content)
    if not docling_match:
        print("❌ DoclingLoader class not found")
        return
    
    # Find the load method within DoclingLoader
    load_pattern = r'(class DoclingLoader:.*?)(def load\(self\).*?)(return \[Document\(page_content=text, metadata=metadata\)\])'
    match = re.search(load_pattern, content, re.DOTALL)
    
    if not match:
        print("❌ DoclingLoader load method pattern not found")
        return
    
    class_start = match.group(1)
    load_method = match.group(2)
    return_statement = match.group(3)
    
    # Add the table extraction method before the load method
    table_method = '''    def _extract_table_text(self, tables):
        """Extract readable text from Docling table structure"""
        import re
        table_texts = []
        
        for table in tables:
            if isinstance(table, dict) and "markdown" in table:
                markdown_str = table["markdown"]
                # Extract text values using regex
                text_matches = re.findall(r"text='([^']*)'", markdown_str)
                
                if text_matches:
                    # Remove duplicates while preserving order
                    seen = set()
                    unique_texts = []
                    for text in text_matches:
                        if text not in seen and text.strip():
                            seen.add(text)
                            unique_texts.append(text)
                    
                    if unique_texts:
                        # Format as readable table
                        table_text = "TABLE DATA:\\n" + "\\n".join(f"- {text}" for text in unique_texts)
                        table_texts.append(table_text)
        
        return table_texts

'''
    
    # Modify the load method to include table extraction
    # Find the specific line where text is assigned
    text_assignment_pattern = r'text = "\\\\n\\\\n"\.join\(str\(item\) for item in content if item\)'
    
    if re.search(text_assignment_pattern, load_method):
        enhanced_assignment = '''# ENHANCED: Handle content, tables, and images
            text_parts = []
            
            # 1. Main content (text)
            main_text = "\\\\n\\\\n".join(str(item) for item in content if item)
            if main_text:
                text_parts.append(main_text)
            
            # 2. Extract tables
            if "tables" in result and result["tables"]:
                table_texts = self._extract_table_text(result["tables"])
                text_parts.extend(table_texts)
            
            # 3. Handle images (placeholder for now)
            if "images" in result and result["images"]:
                image_text = f"[DOCUMENT CONTAINS {len(result['images'])} IMAGES]"
                text_parts.append(image_text)
            
            # Combine all parts
            if text_parts:
                text = "\\\\n\\\\n".join(text_parts)
            else:
                text = "<No content found>"'''
        
        enhanced_load_method = re.sub(text_assignment_pattern, enhanced_assignment, load_method)
        
        # Reconstruct the class with the new method and enhanced load method
        new_class_content = class_start + table_method + enhanced_load_method + return_statement
        
        # Replace the entire DoclingLoader class in the content
        original_class_pattern = r'class DoclingLoader:.*?return \[Document\(page_content=text, metadata=metadata\)\]'
        new_content = re.sub(original_class_pattern, new_class_content, content, flags=re.DOTALL)
        
        # Write back to file
        with open('/app/backend/open_webui/retrieval/loaders/main.py', 'w') as f:
            f.write(new_content)
        
        print("✅ Table extraction added successfully")
    else:
        print("❌ Text assignment pattern not found in load method")

if __name__ == "__main__":
    main()