#!/usr/bin/env python3
"""
Bulk Upload MobiFone Documents to Open WebUI RAG with Gemini Embedding
Upload tất cả tài liệu MobiFone vào hệ thống RAG
"""

import os
import requests
import json
import time
import glob
from pathlib import Path
from typing import List, Dict

class MobiFoneBulkUploader:
    def __init__(self, openwebui_url: str = "http://localhost:3000"):
        self.openwebui_url = openwebui_url
        self.session = requests.Session()
        
        # Collection info
        self.collection_name = "MobiFone Knowledge Base"
        self.collection_description = "Tất cả tài liệu nội bộ <PERSON> - <PERSON><PERSON>, ch<PERSON><PERSON>, h<PERSON><PERSON><PERSON> dẫn"
        
        # Progress tracking
        self.uploaded_files = []
        self.failed_files = []
        
    def create_collection(self) -> str:
        """Create MobiFone knowledge collection"""
        print("🏢 Creating MobiFone Knowledge Collection...")
        
        collection_data = {
            "name": self.collection_name,
            "description": self.collection_description,
            "visibility": "private"
        }
        
        try:
            response = self.session.post(
                f"{self.openwebui_url}/api/v1/knowledge",
                json=collection_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                collection = response.json()
                collection_id = collection.get("id")
                print(f"✅ Created collection: {collection_id}")
                return collection_id
            else:
                print(f"❌ Failed to create collection: {response.status_code}")
                print(response.text)
                return None
                
        except Exception as e:
            print(f"❌ Error creating collection: {e}")
            return None
    
    def get_or_create_collection(self) -> str:
        """Get existing or create new collection"""
        try:
            # Try to get existing collections
            response = self.session.get(f"{self.openwebui_url}/api/v1/knowledge")
            
            if response.status_code == 200:
                collections = response.json()
                
                # Look for existing MobiFone collection
                for collection in collections:
                    if collection.get("name") == self.collection_name:
                        collection_id = collection.get("id")
                        print(f"📁 Found existing collection: {collection_id}")
                        return collection_id
            
            # Create new collection if not found
            return self.create_collection()
            
        except Exception as e:
            print(f"❌ Error getting collections: {e}")
            return self.create_collection()
    
    def upload_document_to_collection(self, file_path: str, collection_id: str) -> bool:
        """Upload single document to collection"""
        file_name = os.path.basename(file_path)
        print(f"📄 Uploading: {file_name}")
        
        try:
            with open(file_path, 'rb') as f:
                files = {
                    'file': (file_name, f, 'text/plain')
                }
                
                data = {
                    'collection_id': collection_id
                }
                
                response = self.session.post(
                    f"{self.openwebui_url}/api/v1/knowledge/{collection_id}/file",
                    files=files,
                    data=data
                )
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ {file_name}: {result.get('chunks_count', 0)} chunks processed")
                    self.uploaded_files.append(file_name)
                    return True
                else:
                    print(f"❌ {file_name}: Failed ({response.status_code})")
                    print(response.text[:200])
                    self.failed_files.append(file_name)
                    return False
                    
        except Exception as e:
            print(f"❌ {file_name}: Error - {e}")
            self.failed_files.append(file_name)
            return False
    
    def find_mobifone_documents(self) -> List[str]:
        """Find all MobiFone documents to upload"""
        document_dirs = ["knowledge_files", "upload_ready", "migrated_docs"]
        all_files = []
        
        for dir_name in document_dirs:
            if os.path.exists(dir_name):
                # Text files
                txt_files = glob.glob(f"{dir_name}/*.txt")
                # PDF files  
                pdf_files = glob.glob(f"{dir_name}/*.pdf")
                # DOCX files
                docx_files = glob.glob(f"{dir_name}/*.docx")
                
                dir_files = txt_files + pdf_files + docx_files
                all_files.extend(dir_files)
                print(f"📁 {dir_name}: Found {len(dir_files)} files")
        
        print(f"📊 Total files found: {len(all_files)}")
        return all_files
    
    def bulk_upload(self):
        """Upload all MobiFone documents"""
        print("🚀 MOBIFONE BULK DOCUMENT UPLOAD")
        print("=" * 50)
        
        # Get or create collection
        collection_id = self.get_or_create_collection()
        if not collection_id:
            print("❌ Cannot proceed without collection")
            return
        
        # Find documents
        documents = self.find_mobifone_documents()
        if not documents:
            print("❌ No documents found to upload")
            return
        
        # Upload documents
        print(f"\n📤 Starting upload of {len(documents)} documents...")
        start_time = time.time()
        
        for i, doc_path in enumerate(documents, 1):
            print(f"\n[{i}/{len(documents)}]", end=" ")
            success = self.upload_document_to_collection(doc_path, collection_id)
            
            # Small delay to avoid overwhelming the API
            time.sleep(1)
        
        # Summary
        elapsed = time.time() - start_time
        print("\n" + "=" * 50)
        print("📊 UPLOAD SUMMARY")
        print(f"✅ Successful: {len(self.uploaded_files)} files")
        print(f"❌ Failed: {len(self.failed_files)} files")
        print(f"⏱️  Time: {elapsed:.1f} seconds")
        
        if self.failed_files:
            print("\n❌ Failed files:")
            for file in self.failed_files:
                print(f"   - {file}")
        
        print(f"\n🎯 Collection ID: {collection_id}")
        print(f"🌐 Access via: {self.openwebui_url}")

def main():
    """Main execution"""
    print("🏢 MobiFone Knowledge Base Bulk Upload")
    print("Using Gemini text-embedding-004 for embeddings")
    
    uploader = MobiFoneBulkUploader()
    uploader.bulk_upload()

if __name__ == "__main__":
    main() 