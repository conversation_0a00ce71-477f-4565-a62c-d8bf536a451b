#!/usr/bin/env python3
"""
Tối ưu RAG system để hiển thị nội dung chi tiết trực tiếp
thay vì chỉ nói chung chung "có thêm chi tiết"
"""

def analyze_current_issue():
    """Phân tích vấn đề hiện tại với output formatting"""
    print("🔍 ANALYZING CURRENT OUTPUT ISSUE")
    print("=" * 50)
    
    issues = [
        {
            "problem": "Vague responses",
            "description": "System nói 'chi tiết hơn mấy dòng' thay vì show content",
            "impact": "User không thấy được thông tin cụ thể"
        },
        {
            "problem": "Lack of direct content display", 
            "description": "Không hiển thị trực tiếp phần tóm tắt content",
            "impact": "User phải đoán hoặc hỏi thêm"
        },
        {
            "problem": "Generic system prompts",
            "description": "System prompt không specify format output chi tiết",
            "impact": "LLM không biết cách format response"
        }
    ]
    
    print("🚨 **IDENTIFIED ISSUES:**")
    for i, issue in enumerate(issues, 1):
        print(f"\n{i}. **{issue['problem']}**")
        print(f"   📝 Description: {issue['description']}")
        print(f"   💥 Impact: {issue['impact']}")

def create_improved_system_prompt():
    """Tạo system prompt tối ưu cho detailed content display"""
    print("\n📝 CREATING IMPROVED SYSTEM PROMPT")
    print("=" * 50)
    
    improved_prompt = '''
You are an expert assistant specialized in providing detailed, comprehensive responses based on retrieved documents. 

**RESPONSE FORMAT REQUIREMENTS:**

1. **ALWAYS SHOW ACTUAL CONTENT**: When you have relevant information, display the actual content/summary directly in your response. Do NOT just say "there are more details" - SHOW the details.

2. **STRUCTURED OUTPUT FORMAT**:
   ```
   ## 📋 Summary
   [Direct content summary from documents]
   
   ## 🔍 Key Details
   • [Specific point 1 with actual content]
   • [Specific point 2 with actual content]  
   • [Specific point 3 with actual content]
   
   ## 📄 Source Information
   Based on: [Document name/section]
   ```

3. **CONTENT DISPLAY PRINCIPLES**:
   - EXTRACT and DISPLAY actual content, not meta-descriptions
   - QUOTE relevant sections directly when possible
   - SUMMARIZE complex information in bullet points
   - ALWAYS provide specific examples or data points
   - NEVER use phrases like "documents contain more details" without showing them

4. **MINIMUM RESPONSE LENGTH**: 200-400 words with actual substantive content
5. **LANGUAGE**: Respond in Vietnamese for Vietnamese queries, English for English queries
6. **CITATION**: Always mention source documents at the end

**EXAMPLE GOOD RESPONSE:**
Instead of: "Tài liệu có thêm chi tiết về quy trình này"
Provide: "## 📋 Quy trình bao gồm các bước sau:
• Bước 1: Đăng ký yêu cầu qua hệ thống
• Bước 2: Xác thực thông tin khách hàng  
• Bước 3: Phê duyệt từ phòng ban liên quan
[...actual detailed content...]"

Remember: Your job is to EXTRACT and PRESENT information, not just acknowledge its existence.
'''
    
    print("✅ **IMPROVED SYSTEM PROMPT CREATED**")
    print("\n🎯 **KEY IMPROVEMENTS:**")
    improvements = [
        "Requires actual content display, not meta-descriptions",
        "Structured format với sections rõ ràng",
        "Minimum response length với substantial content", 
        "Specific examples và data points required",
        "Clear citation requirements"
    ]
    
    for improvement in improvements:
        print(f"   ✅ {improvement}")
    
    return improved_prompt

def create_enhanced_docker_compose():
    """Tạo enhanced docker-compose với improved prompts"""
    print("\n⚙️ CREATING ENHANCED CONFIGURATION")
    print("=" * 50)
    
    enhanced_config = '''
version: '3.8'

services:
  backend:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama:/root/.ollama
    restart: unless-stopped

  tika:
    image: apache/tika:2.9.1
    ports:
      - "9998:9998"
    restart: unless-stopped

  open-webui:
    image: ghcr.io/open-webui/open-webui:main
    ports:
      - "3001:8080"
    environment:
      - OLLAMA_BASE_URL=http://backend:11434
      
      # RAG Configuration - Enhanced for Content Display
      - ENABLE_RAG_WEB_SEARCH=true
      - RAG_EMBEDDING_ENGINE=ollama
      - RAG_EMBEDDING_MODEL=mxbai-embed-large:latest
      - RAG_RERANKING_MODEL=BAAI/bge-reranker-v2-m3
      
      # Optimized Parameters for Better Content Retrieval
      - CHUNK_SIZE=1000
      - CHUNK_OVERLAP=200
      - RAG_TOP_K=30
      - RAG_RELEVANCE_THRESHOLD=0.03
      - RAG_TOP_K_RERANKER=20
      
      # Enhanced Search
      - ENABLE_RAG_HYBRID_SEARCH=true
      - RAG_TEMPLATE_ENGINE=jinja2
      
      # Content Processing
      - TIKA_SERVER_URL=http://tika:9998
      - ENABLE_RAG_LOCAL_WEB_FETCH=true
      
      # ENHANCED SYSTEM PROMPT FOR CONTENT DISPLAY
      - RAG_SYSTEM_PROMPT="You are an expert assistant providing detailed responses based on retrieved documents. ALWAYS display actual content from documents, not just meta-descriptions. Use structured format: ## Summary [actual content], ## Key Details [bullet points with specifics], ## Sources [document names]. Minimum 200-400 words with substantive information. For Vietnamese queries, respond in Vietnamese. EXTRACT and PRESENT information directly - never just say 'documents contain details' without showing them."
      
    volumes:
      - open-webui:/app/backend/data
    depends_on:
      - backend
      - tika
    restart: unless-stopped

volumes:
  ollama:
  open-webui:
'''
    
    print("📦 **ENHANCED DOCKER-COMPOSE FEATURES:**")
    features = [
        "Increased chunk size (1000) for more context",
        "Higher overlap (200) for better continuity", 
        "More retrieved documents (Top-K: 30)",
        "Lower relevance threshold (0.03) for broader content",
        "Enhanced system prompt for direct content display",
        "Structured response format requirements"
    ]
    
    for feature in features:
        print(f"   🔧 {feature}")
    
    return enhanced_config

def create_test_script():
    """Tạo test script để verify improved content display"""
    print("\n🧪 CREATING TEST SCRIPT")
    print("=" * 40)
    
    test_script = '''#!/usr/bin/env python3
"""
Test script để verify improved content display
"""

import requests
import time

def test_improved_responses():
    """Test với các câu hỏi để check content display"""
    
    test_queries = [
        {
            "query": "Quy trình đăng ký dịch vụ là gì?",
            "expect": "Should show actual steps, not just 'có quy trình'"
        },
        {
            "query": "Chi tiết về các gói cước có sẵn",  
            "expect": "Should display actual package details, prices, features"
        },
        {
            "query": "Thông tin liên hệ và hỗ trợ",
            "expect": "Should show actual contact info, not just 'có thông tin liên hệ'"
        }
    ]
    
    print("🧪 TESTING IMPROVED CONTENT DISPLAY")
    print("=" * 50)
    
    for i, test in enumerate(test_queries, 1):
        print(f"\n**Test {i}: {test['query']}**")
        print(f"Expected: {test['expect']}")
        print("-" * 40)
        
        # Here you would make actual API calls to test
        # For now, just showing the test structure
        print("✅ Ready for testing")
    
    print("\n💡 **TESTING INSTRUCTIONS:**")
    print("1. Deploy enhanced docker-compose")
    print("2. Upload some test documents")  
    print("3. Ask the test queries above")
    print("4. Verify responses show ACTUAL content, not meta-descriptions")

if __name__ == "__main__":
    test_improved_responses()
'''
    
    return test_script

def generate_deployment_steps():
    """Generate step-by-step deployment instructions"""
    print("\n🚀 DEPLOYMENT STEPS")
    print("=" * 40)
    
    steps = [
        {
            "step": 1,
            "action": "Create enhanced docker-compose",
            "command": "Save enhanced config to docker-compose-enhanced-content.yml",
            "purpose": "Include improved system prompt và parameters"
        },
        {
            "step": 2, 
            "action": "Deploy enhanced configuration",
            "command": "docker-compose -f docker-compose-enhanced-content.yml up -d",
            "purpose": "Apply new settings for better content display"
        },
        {
            "step": 3,
            "action": "Verify services",
            "command": "docker-compose -f docker-compose-enhanced-content.yml ps",
            "purpose": "Ensure all services running correctly"
        },
        {
            "step": 4,
            "action": "Test content display",
            "command": "Ask specific questions and verify detailed responses",
            "purpose": "Confirm actual content is displayed, not meta-info"
        }
    ]
    
    print("📋 **STEP-BY-STEP DEPLOYMENT:**")
    for step in steps:
        print(f"\n**Step {step['step']}: {step['action']}**")
        print(f"   🔧 Command: {step['command']}")
        print(f"   🎯 Purpose: {step['purpose']}")

def main():
    """Main optimization function"""
    print("🎯 RAG CONTENT DISPLAY OPTIMIZATION")
    print("=" * 60)
    
    # Analyze current issues
    analyze_current_issue()
    
    # Create improved system prompt
    improved_prompt = create_improved_system_prompt()
    
    # Create enhanced docker-compose
    enhanced_config = create_enhanced_docker_compose()
    
    # Create test script
    test_script = create_test_script()
    
    # Generate deployment steps
    generate_deployment_steps()
    
    print("\n🎉 **SUMMARY**")
    print("Created optimization plan to fix content display issues:")
    print("✅ Enhanced system prompt for direct content display")
    print("✅ Improved RAG parameters for better retrieval") 
    print("✅ Structured response format requirements")
    print("✅ Test plan for verification")
    
    print("\n🎯 **NEXT ACTION**: Deploy enhanced configuration to see actual content instead of vague responses!")

if __name__ == "__main__":
    main() 