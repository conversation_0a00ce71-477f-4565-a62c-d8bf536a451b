GEMINI 2.5 FLASH TEST DOCUMENT

This document is created to test the new TASK_MODEL configuration using Gemini 2.5 Flash.

Key features being tested:
1. Document processing with Docling
2. Vector embedding storage
3. SQLite database metadata insertion
4. Background tasks (title generation, tags generation, follow-up generation)

Expected behavior:
- Docling should extract this text successfully
- Vector database should store embeddings
- SQLite database should store document metadata
- Background tasks should run without rate limit errors

Test timestamp: 2025-07-27 16:22:00 UTC
Model: gemini-2.5-flash
Status: Testing in progress...