"""
title: AccA Mem0 Memory Pipeline (Optimized)
author: AccA Project
date: 2025-01-15
version: 1.1.0
license: MIT
description: Optimized memory pipeline using mem0 with better recall settings
"""

import os
import json
import asyncio
from typing import List, Optional

# Safe imports
try:
    from pydantic import BaseModel, Field
    PYDANTIC_AVAILABLE = True
except ImportError:
    class BaseModel:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)
    def Field(default=None, description=""):
        return default
    PYDANTIC_AVAILABLE = False

try:
    from mem0 import AsyncMemory
    MEM0_AVAILABLE = True
except ImportError:
    print("[AccA-Mem0-ERROR] mem0ai not available. Please install: pip install mem0ai")
    MEM0_AVAILABLE = False
    class AsyncMemory:
        @classmethod
        async def from_config(cls, config):
            raise ImportError("mem0ai not installed")

class Pipeline:
    class Valves(BaseModel):
        pipelines: List[str] = ["*"]
        priority: int = 0
        user_id: str = Field(
            default="default_user", 
            description="Default user ID for memory operations"
        )

        # Qdrant config - optimized for Docker
        qdrant_host: str = Field(
            default="**********", 
            description="Qdrant host (Docker bridge IP)"
        )
        qdrant_port: str = Field(
            default="6333", 
            description="Qdrant port"
        )
        collection_name: str = Field(
            default="mem0_gemini_768", 
            description="Qdrant collection name"
        )

        # Gemini LLM config
        llm_provider: str = Field(default="gemini", description="LLM provider")
        llm_api_key: str = Field(
            default=os.getenv("GEMINI_API_KEY", ""), 
            description="Gemini API key"
        )
        llm_model: str = Field(default="gemini-2.5-flash", description="Gemini model")
        llm_temperature: float = Field(default=0.1, description="LLM temperature")
        llm_max_tokens: int = Field(default=1000, description="LLM max tokens")

        # Gemini Embedder config
        embedder_provider: str = Field(default="gemini", description="Embedding provider")
        embedder_api_key: str = Field(
            default=os.getenv("GEMINI_API_KEY", ""), 
            description="Gemini API key for embeddings"
        )
        embedder_model: str = Field(default="text-embedding-004", description="Gemini embedding model")
        embedder_dims: int = Field(default=768, description="Embedding dimensions")

        # Memory behavior - OPTIMIZED SETTINGS
        max_memories_to_inject: int = Field(default=3, description="Max memories to inject")
        memory_relevance_threshold: float = Field(
            default=0.2,  # LOWERED from 0.7 to 0.2 for better recall
            description="Minimum relevance score (lowered for better memory recall)"
        )
        auto_store_messages: bool = Field(default=True, description="Auto store messages")
        enable_debug_logging: bool = Field(default=True, description="Enable debug logging")
        
        # New optimization settings
        memory_search_limit: int = Field(default=10, description="Search more memories before filtering")
        enable_fallback_search: bool = Field(default=True, description="Enable fallback search with lower threshold")

    def __init__(self):
        self.type = "filter"
        self.valves = self.Valves()
        self.m = None
        self.initialization_attempts = 0
        self.max_init_attempts = 3
        
        if not MEM0_AVAILABLE:
            print("[AccA-Mem0-ERROR] mem0ai not available. Memory functions disabled.")
        
        self.debug_log("AccA Mem0 Pipeline (Optimized) initialized")

    def debug_log(self, message: str):
        """Enhanced debug logging"""
        if self.valves.enable_debug_logging:
            print(f"[AccA-Mem0-OPTIMIZED] {message}")

    async def on_startup(self):
        """Enhanced startup with better error handling"""
        print("[AccA-Mem0-OPTIMIZED] Starting optimized pipeline...")
        if MEM0_AVAILABLE:
            try:
                await self._ensure_memory_client()
            except Exception as e:
                print(f"[AccA-Mem0-WARNING] Startup initialization failed: {e}")

    async def on_valves_updated(self):
        """Reinitialize on valve updates"""
        self.debug_log("Valves updated - reinitializing...")
        self.m = None  # Force reinitialization
        if MEM0_AVAILABLE:
            try:
                await self._ensure_memory_client()
            except Exception as e:
                print(f"[AccA-Mem0-WARNING] Valve update initialization failed: {e}")

    async def _ensure_memory_client(self):
        """Ensure memory client is initialized with retry logic"""
        if self.m is not None:
            return self.m
            
        if self.initialization_attempts >= self.max_init_attempts:
            raise Exception(f"Max initialization attempts ({self.max_init_attempts}) exceeded")
            
        try:
            self.initialization_attempts += 1
            self.debug_log(f"Initializing memory client (attempt {self.initialization_attempts})")
            
            config = {
                "vector_store": {
                    "provider": "qdrant",
                    "config": {
                        "host": self.valves.qdrant_host,
                        "port": int(self.valves.qdrant_port),
                        "collection_name": self.valves.collection_name,
                    },
                },
                "llm": {
                    "provider": self.valves.llm_provider,
                    "config": {
                        "api_key": self.valves.llm_api_key,
                        "model": self.valves.llm_model,
                        "temperature": self.valves.llm_temperature,
                        "max_tokens": self.valves.llm_max_tokens,
                    },
                },
                "embedder": {
                    "provider": self.valves.embedder_provider,
                    "config": {
                        "api_key": self.valves.embedder_api_key,
                        "model": self.valves.embedder_model,
                        "embedding_dims": self.valves.embedder_dims,
                    },
                },
            }
            
            self.m = await AsyncMemory.from_config(config)
            self.debug_log("Memory client initialized successfully")
            self.initialization_attempts = 0  # Reset on success
            return self.m
            
        except Exception as e:
            print(f"[AccA-Mem0-ERROR] Memory client initialization failed: {e}")
            if self.initialization_attempts < self.max_init_attempts:
                self.debug_log(f"Will retry initialization on next request")
            raise

    async def _search_memories_with_fallback(self, user_id: str, query: str):
        """Enhanced memory search with fallback logic"""
        try:
            # Primary search with higher limit
            memories = await self.m.search(
                user_id=user_id,
                query=query,
                limit=self.valves.memory_search_limit
            )
            
            results = memories.get('results', [])
            self.debug_log(f"Primary search found {len(results)} memories")
            
            # Filter by relevance threshold
            relevant_memories = []
            for mem in results:
                score = mem.get('score', 0.0)
                if score >= self.valves.memory_relevance_threshold:
                    relevant_memories.append(mem)
            
            self.debug_log(f"Filtered to {len(relevant_memories)} relevant memories (threshold: {self.valves.memory_relevance_threshold})")
            
            # Fallback search if no relevant memories and fallback enabled
            if not relevant_memories and self.valves.enable_fallback_search and self.valves.memory_relevance_threshold > 0.1:
                self.debug_log("No relevant memories found, trying fallback search with lower threshold")
                
                fallback_memories = []
                fallback_threshold = max(0.1, self.valves.memory_relevance_threshold - 0.2)
                
                for mem in results:
                    score = mem.get('score', 0.0)
                    if score >= fallback_threshold:
                        fallback_memories.append(mem)
                
                if fallback_memories:
                    self.debug_log(f"Fallback search found {len(fallback_memories)} memories (threshold: {fallback_threshold})")
                    relevant_memories = fallback_memories[:self.valves.max_memories_to_inject]
            
            return relevant_memories[:self.valves.max_memories_to_inject]
            
        except Exception as e:
            print(f"[AccA-Mem0-ERROR] Memory search failed: {e}")
            return []

    async def inlet(self, body: dict, user: Optional[dict] = None) -> dict:
        """Optimized inlet method with better error handling"""
        
        if not MEM0_AVAILABLE:
            self.debug_log("mem0 not available, passing through")
            return body

        try:
            # Ensure memory client is ready
            await self._ensure_memory_client()
        except Exception as e:
            print(f"[AccA-Mem0-ERROR] Failed to initialize memory client: {e}")
            return body

        self.debug_log("=== Processing request with optimized memory ===")
        
        # Extract and validate messages
        messages = body.get("messages", [])
        if not messages:
            self.debug_log("No messages found")
            return body

        # Skip special requests
        if "metadata" in body and "task" in body["metadata"]:
            self.debug_log("Skipping task request")
            return body

        # Determine user ID
        current_user_id = self.valves.user_id
        if user and "id" in user:
            current_user_id = user["id"]
        
        self.debug_log(f"Processing for user: {current_user_id}")

        # Find latest messages
        user_message = None
        assistant_message = None
        
        for msg in reversed(messages):
            if msg.get("role") == "user" and not user_message:
                user_message = msg.get("content", "")
            elif msg.get("role") == "assistant" and not assistant_message:
                assistant_message = msg.get("content", "")

        if not user_message:
            self.debug_log("No user message found")
            return body

        try:
            # Enhanced memory search
            self.debug_log(f"Searching memories for: {user_message[:100]}...")
            relevant_memories = await self._search_memories_with_fallback(current_user_id, user_message)

            # Store messages asynchronously
            if self.valves.auto_store_messages:
                if assistant_message:
                    asyncio.create_task(self._store_message(current_user_id, "assistant", assistant_message))
                asyncio.create_task(self._store_message(current_user_id, "user", user_message))

            # Inject memory context if relevant memories exist
            if relevant_memories:
                memory_context = "\n\n🧠 Relevant memories from previous conversations:\n"
                
                for i, mem in enumerate(relevant_memories, 1):
                    memory_text = mem.get('memory', str(mem))
                    score = mem.get('score', 0.0)
                    memory_context += f"  {i}. {memory_text} (relevance: {score:.2f})\n"
                
                memory_context += "\nUse these memories to provide contextual and personalized responses.\n"

                # Find existing system message or create new one
                system_message = None
                for msg in messages:
                    if msg.get("role") == "system":
                        system_message = msg
                        break

                if system_message:
                    system_message["content"] += memory_context
                    self.debug_log("Added memory context to existing system message")
                else:
                    # Insert new system message at the beginning
                    messages.insert(0, {
                        "role": "system",
                        "content": f"You are a helpful AI assistant with access to conversation history.{memory_context}"
                    })
                    self.debug_log("Created new system message with memory context")

                # Update body with modified messages
                body["messages"] = messages
                self.debug_log(f"Successfully injected {len(relevant_memories)} memories into context")

        except Exception as e:
            print(f"[AccA-Mem0-ERROR] Memory integration error: {e}")
            # Continue without memory context if there's an error

        self.debug_log("=== Inlet processing complete ===")
        return body

    async def _store_message(self, user_id: str, role: str, content: str):
        """Store message in memory"""
        try:
            await self.m.add(
                user_id=user_id,
                messages=[{"role": role, "content": content}]
            )
            self.debug_log(f"Stored {role} message for user: {user_id}")
        except Exception as e:
            print(f"[AccA-Mem0-ERROR] Failed to store message: {e}")
