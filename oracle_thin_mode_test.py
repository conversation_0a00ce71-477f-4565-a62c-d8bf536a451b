#!/usr/bin/env python3
"""
Oracle Thin Mode Connection Test
Test pure thin mode connection without wallet dependencies
"""

import os
import oracledb
from dotenv import load_dotenv

def test_oracle_thin_mode():
    """Test Oracle thin mode connection"""
    print("🧪 Testing Oracle Thin Mode Connection...")
    
    # Load environment variables
    load_dotenv('.env.oracle')
    
    oracle_user = os.getenv("ORACLE_USER", "ADMIN")
    oracle_password = os.getenv("ORACLE_PASSWORD", "")
    oracle_dsn = os.getenv("ORACLE_DSN", "")
    
    print(f"📋 Configuration:")
    print(f"  User: {oracle_user}")
    print(f"  DSN: {oracle_dsn}")
    print(f"  Password: {'*' * len(oracle_password) if oracle_password else 'NOT SET'}")
    
    if not oracle_password or not oracle_dsn:
        print("❌ Oracle credentials not configured")
        return False
    
    try:
        print("\n🔧 Testing thin mode connection (no wallet)...")
        
        # Create thin mode connection string
        # Format: user/password@host:port/service_name
        # For Autonomous DB, we need to construct the full connection string
        
        # If DSN is just the service name, we need to construct full connection
        if '/' not in oracle_dsn and ':' not in oracle_dsn:
            # This is likely just the service name, we need the full connection string
            # For Oracle Autonomous Database, the format is typically:
            # (description=(retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.ap-singapore-2.oraclecloud.com))(connect_data=(service_name=your_service_name))(security=(ssl_server_dn_match=yes)))
            
            print("⚠️ DSN appears to be just service name. For thin mode, we need full connection string.")
            print("💡 Trying to connect with service name only...")
            
            # Try direct connection with service name
            connection = oracledb.connect(
                user=oracle_user,
                password=oracle_password,
                dsn=oracle_dsn
            )
        else:
            # Full DSN provided
            connection = oracledb.connect(
                user=oracle_user,
                password=oracle_password,
                dsn=oracle_dsn
            )
        
        print("✅ Oracle thin mode connection successful!")
        
        # Test query
        cursor = connection.cursor()
        cursor.execute("SELECT 'Hello from Oracle Thin Mode!' as message, SYSDATE as current_time FROM DUAL")
        result = cursor.fetchone()
        
        print(f"📋 Test query result:")
        print(f"  Message: {result[0]}")
        print(f"  Time: {result[1]}")
        
        # Test database version
        cursor.execute("SELECT banner FROM v$version WHERE rownum = 1")
        version = cursor.fetchone()
        print(f"  Database: {version[0]}")
        
        cursor.close()
        connection.close()
        
        print("✅ Oracle thin mode test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Oracle thin mode connection failed: {e}")
        print(f"Error type: {type(e).__name__}")
        
        # Provide troubleshooting info
        print("\n🔍 Troubleshooting:")
        print("1. Check if DSN is correct for thin mode")
        print("2. For Autonomous DB, you might need the full connection descriptor")
        print("3. Ensure network connectivity to Oracle Cloud")
        print("4. Verify credentials are correct")
        
        return False

if __name__ == "__main__":
    success = test_oracle_thin_mode()
    exit(0 if success else 1)