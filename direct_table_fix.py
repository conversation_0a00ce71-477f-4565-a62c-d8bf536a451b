#!/usr/bin/env python3

import subprocess
import sys

def run_command(cmd, description):
    """Run a command and return success status"""
    print(f"🔧 {description}")
    print(f"   Command: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("   ✅ Success")
            return True
        else:
            print(f"   ❌ Failed (exit code: {result.returncode})")
            if result.stderr:
                print(f"   Error: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False

def main():
    print("🔧 DIRECT TABLE EXTRACTION FIX")
    print("=" * 40)
    
    # Step 1: Copy the table method file to container
    if not run_command(
        "docker cp table_extraction_method.py catomanton-webui:/tmp/table_extraction_method.py",
        "Copy table extraction method to container"
    ):
        return False
    
    # Step 2: Use sed to add the table extraction method to DoclingLoader
    print("\n🔄 STEP 1: Add table extraction method")
    
    # First, check if method already exists
    check_cmd = 'docker exec catomanton-webui grep -q "_extract_table_text" /app/backend/open_webui/retrieval/loaders/main.py'
    result = subprocess.run(check_cmd, shell=True, capture_output=True)
    
    if result.returncode == 0:
        print("   ✅ Table extraction method already exists")
    else:
        # Add the method before the load method
        add_method_cmd = '''docker exec catomanton-webui bash -c "
        # Find line number of 'def load(' in DoclingLoader class
        LINE_NUM=\\$(grep -n 'def load(' /app/backend/open_webui/retrieval/loaders/main.py | head -1 | cut -d: -f1)
        
        # Insert the table extraction method before the load method
        sed -i '\\${LINE_NUM}i\\    def _extract_table_text(self, tables):\\
\\        \"\"\"Extract readable text from Docling table structure\"\"\"\\
\\        import re\\
\\        table_texts = []\\
\\        \\
\\        for table in tables:\\
\\            if isinstance(table, dict) and \"markdown\" in table:\\
\\                markdown_str = table[\"markdown\"]\\
\\                # Extract text values using regex\\
\\                text_matches = re.findall(r\"text=\\'([^\\']*)\\'\" , markdown_str)\\
\\                \\
\\                if text_matches:\\
\\                    # Remove duplicates while preserving order\\
\\                    seen = set()\\
\\                    unique_texts = []\\
\\                    for text in text_matches:\\
\\                        if text not in seen and text.strip():\\
\\                            seen.add(text)\\
\\                            unique_texts.append(text)\\
\\                    \\
\\                    if unique_texts:\\
\\                        # Format as readable table\\
\\                        table_text = \"TABLE DATA:\\\\n\" + \"\\\\n\".join(f\"- {text}\" for text in unique_texts)\\
\\                        table_texts.append(table_text)\\
\\        \\
\\        return table_texts\\
\\
' /app/backend/open_webui/retrieval/loaders/main.py
        "'''
        
        if not run_command(add_method_cmd, "Add table extraction method"):
            return False
    
    # Step 3: Enhance the load method to use table extraction
    print("\n🔄 STEP 2: Enhance load method for table extraction")
    
    enhance_cmd = '''docker exec catomanton-webui bash -c "
    # Replace the simple text assignment with enhanced version
    sed -i '/text = \"\\\\\\\\n\\\\\\\\n\".join(str(item) for item in content if item)/c\\
            # ENHANCED: Handle content, tables, and images\\
            text_parts = []\\
            \\
            # 1. Main content (text)\\
            main_text = \"\\\\\\\\n\\\\\\\\n\".join(str(item) for item in content if item)\\
            if main_text:\\
                text_parts.append(main_text)\\
            \\
            # 2. Extract tables\\
            if \"tables\" in result and result[\"tables\"]:\\
                table_texts = self._extract_table_text(result[\"tables\"])\\
                text_parts.extend(table_texts)\\
            \\
            # 3. Handle images (placeholder for now)\\
            if \"images\" in result and result[\"images\"]:\\
                image_text = f\"[DOCUMENT CONTAINS {len(result[\\\"images\\\"])} IMAGES]\"\\
                text_parts.append(image_text)\\
            \\
            # Combine all parts\\
            if text_parts:\\
                text = \"\\\\\\\\n\\\\\\\\n\".join(text_parts)\\
            else:\\
                text = \"<No content found>\"' /app/backend/open_webui/retrieval/loaders/main.py
    "'''
    
    if not run_command(enhance_cmd, "Enhance load method"):
        return False
    
    # Step 4: Restart Open WebUI
    print("\n🔄 STEP 3: Restart Open WebUI")
    if not run_command(
        "docker restart catomanton-webui",
        "Restarting Open WebUI container"
    ):
        print("❌ Failed to restart container")
        return False
    
    print("\n✅ DIRECT TABLE EXTRACTION FIX COMPLETED!")
    print("🔄 Open WebUI is restarting...")
    print("📊 The DoclingLoader now extracts both text content AND table data")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)