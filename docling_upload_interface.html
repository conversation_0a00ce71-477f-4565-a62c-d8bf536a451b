<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Docling - Upload & Process Documents</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 700px; 
            margin: 0 auto; 
            padding: 20px; 
            background: #f8f9fa;
        }
        .container { 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #007bff, #28a745);
            color: white;
            border-radius: 8px;
        }
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            background: #f8f9ff;
            cursor: pointer;
            transition: all 0.3s;
        }
        .upload-area:hover {
            border-color: #0056b3;
            background: #e6f3ff;
        }
        .upload-area.dragover {
            border-color: #28a745;
            background: #e8f5e8;
        }
        .file-input {
            display: none;
        }
        .upload-btn {
            background: #28a745;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
        }
        .upload-btn:hover { background: #218838; }
        .upload-btn:disabled { background: #6c757d; cursor: not-allowed; }
        .status {
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .file-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            font-size: 14px;
        }
        .supported-formats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .results {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: #28a745;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Docling Document Processor</h1>
            <p>Upload documents for intelligent table extraction and content analysis</p>
        </div>
        
        <div class="supported-formats">
            <strong>📄 Supported formats:</strong> PDF, DOCX, MD
            <br><strong>📊 Max size:</strong> 50MB per file
            <br><strong>🔧 Processing:</strong> Table extraction, content analysis, Vietnamese language support
        </div>

        <form id="uploadForm">
            <div class="upload-area" id="uploadArea">
                <div id="uploadText">
                    <strong>📤 Click to select files or drag & drop</strong>
                    <br>
                    <span style="color: #6c757d;">Select PDF, DOCX, MD files for processing</span>
                </div>
                <input type="file" id="fileInput" class="file-input" multiple accept=".pdf,.docx,.md">
            </div>
            
            <div id="fileInfo" class="file-info" style="display: none;"></div>
            
            <div id="progress" class="progress" style="display: none;">
                <div id="progressBar" class="progress-bar"></div>
            </div>
            
            <button type="submit" id="uploadBtn" class="upload-btn">
                📊 Process Documents with Docling
            </button>
        </form>
        
        <div id="status" class="status info">Ready to process documents!</div>
        
        <div id="results" class="results" style="display: none;">
            <h3>📊 Processing Results</h3>
            <div id="resultsContent"></div>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const uploadForm = document.getElementById('uploadForm');
        const statusDiv = document.getElementById('status');
        const uploadBtn = document.getElementById('uploadBtn');
        const fileInfo = document.getElementById('fileInfo');
        const progress = document.getElementById('progress');
        const progressBar = document.getElementById('progressBar');
        const results = document.getElementById('results');
        const resultsContent = document.getElementById('resultsContent');
        
        let selectedFiles = [];

        // Click to select files
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = Array.from(e.dataTransfer.files);
            handleFiles(files);
        });

        // File selection
        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            handleFiles(files);
        });

        function handleFiles(files) {
            selectedFiles = files.filter(file => {
                const validExtensions = ['.pdf', '.docx', '.md'];
                const hasValidExtension = validExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
                return hasValidExtension;
            });

            if (selectedFiles.length === 0) {
                showStatus('Please select valid files (PDF, DOCX, MD)', 'error');
                return;
            }

            // Show file info
            const totalSize = selectedFiles.reduce((sum, file) => sum + file.size, 0);
            const sizeStr = (totalSize / (1024 * 1024)).toFixed(2);
            
            fileInfo.innerHTML = `
                <strong>Selected files:</strong><br>
                ${selectedFiles.map(f => `• ${f.name} (${(f.size/1024/1024).toFixed(2)}MB)`).join('<br>')}
                <br><strong>Total size:</strong> ${sizeStr}MB
            `;
            fileInfo.style.display = 'block';
            
            showStatus(`${selectedFiles.length} files selected for processing`, 'success');
        }

        // Upload form
        uploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            if (selectedFiles.length === 0) {
                showStatus('Please select files to process', 'error');
                return;
            }

            uploadBtn.disabled = true;
            uploadBtn.textContent = 'Processing...';
            progress.style.display = 'block';
            results.style.display = 'none';

            let processedFiles = [];
            let successCount = 0;
            let failCount = 0;

            for (let i = 0; i < selectedFiles.length; i++) {
                const file = selectedFiles[i];
                const progressPercent = ((i + 1) / selectedFiles.length) * 100;
                progressBar.style.width = progressPercent + '%';
                
                try {
                    showStatus(`Processing ${file.name}... (${i + 1}/${selectedFiles.length})`, 'info');
                    
                    const formData = new FormData();
                    formData.append('files', file);

                    const response = await fetch('http://localhost:5001/extract_tables', {
                        method: 'POST',
                        body: formData
                    });

                    if (response.ok) {
                        const result = await response.json();
                        console.log(`✅ ${file.name} processed successfully`);
                        processedFiles.push({
                            filename: file.name,
                            success: true,
                            result: result
                        });
                        successCount++;
                    } else {
                        const error = await response.text();
                        console.error(`❌ ${file.name} processing failed:`, error);
                        processedFiles.push({
                            filename: file.name,
                            success: false,
                            error: error
                        });
                        failCount++;
                    }
                } catch (error) {
                    console.error(`❌ ${file.name} processing error:`, error);
                    processedFiles.push({
                        filename: file.name,
                        success: false,
                        error: error.message
                    });
                    failCount++;
                }
            }

            // Show results
            displayResults(processedFiles);

            // Final status
            if (successCount > 0 && failCount === 0) {
                showStatus(`🎉 All ${successCount} files processed successfully!`, 'success');
            } else if (successCount > 0 && failCount > 0) {
                showStatus(`⚠️ ${successCount} files processed, ${failCount} failed`, 'warning');
            } else {
                showStatus(`❌ All processing failed. Check results below.`, 'error');
            }

            // Reset form
            uploadBtn.disabled = false;
            uploadBtn.textContent = '📊 Process Documents with Docling';
            progress.style.display = 'none';
            progressBar.style.width = '0%';
        });

        function displayResults(processedFiles) {
            let html = '';
            
            processedFiles.forEach(file => {
                if (file.success) {
                    const result = file.result;
                    html += `
                        <div style="border: 1px solid #28a745; padding: 15px; margin: 10px 0; border-radius: 4px; background: #f8fff8;">
                            <h4 style="color: #28a745; margin: 0 0 10px 0;">✅ ${file.filename}</h4>
                            <p><strong>Tables found:</strong> ${result.tables_count || 0}</p>
                            <p><strong>Content length:</strong> ${result.content_length || 0} characters</p>
                            ${result.tables && result.tables.length > 0 ? `
                                <details style="margin: 10px 0;">
                                    <summary><strong>📊 Tables (${result.tables.length})</strong></summary>
                                    ${result.tables.map((table, idx) => `
                                        <div style="margin: 10px 0; padding: 10px; background: #f9f9f9; border-radius: 4px;">
                                            <strong>Table ${idx + 1}:</strong>
                                            <pre style="font-size: 12px; overflow-x: auto;">${table}</pre>
                                        </div>
                                    `).join('')}
                                </details>
                            ` : ''}
                            ${result.content ? `
                                <details style="margin: 10px 0;">
                                    <summary><strong>📄 Content Preview</strong></summary>
                                    <pre style="font-size: 12px; max-height: 200px; overflow: auto; background: #f9f9f9; padding: 10px; border-radius: 4px;">${result.content.substring(0, 500)}${result.content.length > 500 ? '...' : ''}</pre>
                                </details>
                            ` : ''}
                        </div>
                    `;
                } else {
                    html += `
                        <div style="border: 1px solid #dc3545; padding: 15px; margin: 10px 0; border-radius: 4px; background: #fff5f5;">
                            <h4 style="color: #dc3545; margin: 0 0 10px 0;">❌ ${file.filename}</h4>
                            <p><strong>Error:</strong> ${file.error}</p>
                        </div>
                    `;
                }
            });
            
            resultsContent.innerHTML = html;
            results.style.display = 'block';
        }

        function showStatus(message, type) {
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        // Check Docling health on load
        window.onload = async function() {
            try {
                const response = await fetch('http://localhost:5001/health');
                if (response.ok) {
                    const health = await response.json();
                    showStatus(`✅ Docling server ready! Service: ${health.service}`, 'success');
                } else {
                    showStatus('❌ Docling server not responding. Please check the server.', 'error');
                }
            } catch (error) {
                showStatus('❌ Cannot connect to Docling server (http://localhost:5001)', 'error');
            }
        };
    </script>
</body>
</html> 