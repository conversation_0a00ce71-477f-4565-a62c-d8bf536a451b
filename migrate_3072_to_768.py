#!/usr/bin/env python3
"""
Migration Script: 3072 dimensions -> 768 dimensions
Migrate memories from high-dimensional collections to optimized 768-dim collection
"""

import asyncio
import json
import os
from typing import List, Dict, Any
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct

try:
    from mem0 import AsyncMemory
    MEM0_AVAILABLE = True
except ImportError:
    print("⚠️  mem0 not available - please install: pip install mem0ai")
    MEM0_AVAILABLE = False

# Configuration
QDRANT_HOST = "localhost"  # Qdrant accessible on localhost
QDRANT_PORT = 6333
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyDHsg-8CCSEBUO9N3V0vaRsCSNq2iS4oec")

# Source collections (high dimensions)
SOURCE_COLLECTIONS = [
    "mem0_gemini_3072",
    "mem0_gemini_3072_fixed",
    "mem0_gemini_gemi_3072",
    "mem0_gemini_1536",
    "mem0_gemini_gemi_1536"
]

# Target collection (optimized 768 dimensions)
TARGET_COLLECTION = "mem0_gemini_gemi_768"

class MemoryMigrator:
    def __init__(self):
        self.qdrant_client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)
        self.source_memory_client = None
        self.target_memory_client = None
        
    async def setup_target_memory_client(self):
        """Setup target memory client with 768 dimensions"""
        config = {
            "vector_store": {
                "provider": "qdrant",
                "config": {
                    "host": QDRANT_HOST,
                    "port": QDRANT_PORT,
                    "collection_name": TARGET_COLLECTION,
                },
            },
            "llm": {
                "provider": "gemini",
                "config": {
                    "api_key": GEMINI_API_KEY,
                    "model": "gemini-2.5-flash",
                    "temperature": 0.1,
                    "max_tokens": 1000,
                },
            },
            "embedder": {
                "provider": "gemini",
                "config": {
                    "api_key": GEMINI_API_KEY,
                    "model": "text-embedding-004",
                    "embedding_dims": 768,
                },
            },
        }
        
        self.target_memory_client = AsyncMemory.from_config(config)
        print(f"✅ Target memory client initialized: {TARGET_COLLECTION}")
        
    def get_existing_collections(self) -> List[str]:
        """Get list of existing collections"""
        try:
            collections = self.qdrant_client.get_collections()
            collection_names = [col.name for col in collections.collections]
            return [name for name in collection_names if name in SOURCE_COLLECTIONS]
        except Exception as e:
            print(f"❌ Error getting collections: {e}")
            return []
    
    async def export_memories_from_collection(self, collection_name: str) -> List[Dict[str, Any]]:
        """Export memories from a source collection"""
        print(f"📤 Exporting memories from {collection_name}...")
        
        try:
            # Get all points from collection
            points, _ = self.qdrant_client.scroll(
                collection_name=collection_name,
                limit=10000,  # Adjust as needed
                with_payload=True,
                with_vectors=False  # We don't need vectors for re-embedding
            )
            
            memories = []
            for point in points:
                payload = point.payload
                
                # Extract memory text and metadata
                memory_data = {
                    "id": str(point.id),
                    "memory": payload.get("memory", payload.get("text", "")),
                    "user_id": payload.get("user_id", "default_user"),
                    "metadata": payload.get("metadata", {}),
                    "created_at": payload.get("created_at"),
                    "updated_at": payload.get("updated_at")
                }
                
                if memory_data["memory"]:  # Only include non-empty memories
                    memories.append(memory_data)
            
            print(f"📊 Exported {len(memories)} memories from {collection_name}")
            return memories
            
        except Exception as e:
            print(f"❌ Error exporting from {collection_name}: {e}")
            return []
    
    async def import_memories_to_target(self, memories: List[Dict[str, Any]]) -> int:
        """Import memories to target collection with 768 dimensions"""
        if not memories:
            return 0
            
        print(f"📥 Importing {len(memories)} memories to {TARGET_COLLECTION}...")
        imported_count = 0
        
        for memory_data in memories:
            try:
                # Add memory using mem0 client (will re-embed with 768 dims)
                await self.target_memory_client.add(
                    messages=memory_data["memory"],
                    user_id=memory_data["user_id"],
                    metadata=memory_data.get("metadata", {})
                )
                imported_count += 1
                
                if imported_count % 10 == 0:
                    print(f"   ✅ Imported {imported_count}/{len(memories)} memories...")
                    
            except Exception as e:
                print(f"   ❌ Error importing memory {memory_data['id']}: {e}")
                continue
        
        print(f"✅ Successfully imported {imported_count} memories")
        return imported_count
    
    async def migrate_collection(self, source_collection: str) -> int:
        """Migrate a single collection"""
        print(f"\n🔄 Migrating {source_collection} -> {TARGET_COLLECTION}")
        
        # Export memories
        memories = await self.export_memories_from_collection(source_collection)
        if not memories:
            print(f"ℹ️  No memories found in {source_collection}")
            return 0
        
        # Import to target
        imported_count = await self.import_memories_to_target(memories)
        
        return imported_count
    
    def verify_target_collection(self):
        """Verify target collection exists and show stats"""
        try:
            collection_info = self.qdrant_client.get_collection(TARGET_COLLECTION)
            print(f"\n📊 Target collection stats:")
            print(f"   - Collection: {TARGET_COLLECTION}")
            print(f"   - Total points: {collection_info.points_count}")
            print(f"   - Vector size: {collection_info.config.params.vectors.size}")
            print(f"   - Distance: {collection_info.config.params.vectors.distance}")
            return True
        except Exception as e:
            print(f"❌ Error verifying target collection: {e}")
            return False

async def main():
    """Main migration process"""
    if not MEM0_AVAILABLE:
        print("❌ mem0 not available. Please install: pip install mem0ai")
        return
    
    print("🚀 Starting migration from 3072/1536 dimensions to 768 dimensions")
    print(f"📍 Qdrant: {QDRANT_HOST}:{QDRANT_PORT}")
    print(f"🎯 Target collection: {TARGET_COLLECTION}")
    
    migrator = MemoryMigrator()
    
    # Setup target memory client
    await migrator.setup_target_memory_client()
    
    # Get existing source collections
    existing_collections = migrator.get_existing_collections()
    if not existing_collections:
        print("ℹ️  No source collections found to migrate")
        return
    
    print(f"📦 Found source collections: {existing_collections}")
    
    # Migrate each collection
    total_migrated = 0
    for collection in existing_collections:
        migrated_count = await migrator.migrate_collection(collection)
        total_migrated += migrated_count
    
    # Verify results
    print(f"\n🎉 Migration completed!")
    print(f"📊 Total memories migrated: {total_migrated}")
    
    migrator.verify_target_collection()
    
    # Recommendations
    print(f"\n💡 Next steps:")
    print(f"   1. Update your pipeline to use collection: {TARGET_COLLECTION}")
    print(f"   2. Set embedder_dims to 768 in pipeline valves")
    print(f"   3. Test the pipeline with the new configuration")
    print(f"   4. Consider backing up old collections before deletion")

if __name__ == "__main__":
    asyncio.run(main())
