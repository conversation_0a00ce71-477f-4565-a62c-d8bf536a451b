"""
Disable Pipeline Authentication
Remove auth requirements for internal VPS usage
"""

import subprocess
import time

def disable_pipeline_auth():
    """Disable authentication for pipeline server"""
    print("🔓 Disabling Pipeline Authentication...")
    
    # Check current pipeline server environment
    result = subprocess.run(
        "docker exec pipelines env | grep -i auth",
        shell=True, capture_output=True, text=True
    )
    
    if result.stdout:
        print("Current auth settings:")
        print(result.stdout)
    
    # Restart pipeline server without auth
    commands = [
        "docker exec pipelines pkill -f 'python'",  # Kill current process
        "docker restart pipelines"  # Restart container
    ]
    
    for cmd in commands:
        print(f"🔧 Running: {cmd}")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Success")
        else:
            print(f"⚠️  {result.stderr}")
    
    # Wait for restart
    print("⏳ Waiting for pipeline server to restart...")
    time.sleep(8)

def disable_openwebui_auth():
    """Disable authentication for Open WebUI API"""
    print("🔓 Disabling Open WebUI Authentication...")
    
    # Set environment variables to disable auth
    auth_env_vars = [
        "WEBUI_AUTH=false",
        "ENABLE_SIGNUP=true", 
        "DEFAULT_USER_ROLE=admin",
        "WEBUI_SECRET_KEY=",
        "OAUTH_CLIENT_ID=",
        "OAUTH_CLIENT_SECRET="
    ]
    
    # This would require container restart with new env vars
    print("💡 To fully disable Open WebUI auth, restart container with:")
    for var in auth_env_vars:
        print(f"   -e {var}")

def test_endpoints_after_auth_disable():
    """Test endpoints after disabling auth"""
    print("🧪 Testing endpoints after auth disable...")
    
    import requests
    
    endpoints = [
        ("Pipeline Status", "http://localhost:9099/"),
        ("Pipeline Models", "http://localhost:9099/models"),
        ("RAG Valves", "http://localhost:9099/enhanced-rag-v1/valves/spec"),
        ("Open WebUI Pipelines", "http://localhost:3000/api/v1/pipelines/list")
    ]
    
    for name, url in endpoints:
        try:
            response = requests.get(url, timeout=5)
            print(f"✅ {name}: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ Success - No auth required")
            elif response.status_code == 403:
                print(f"   ❌ Still requires auth")
            else:
                print(f"   ⚠️  Status: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {name}: {str(e)}")

def create_no_auth_pipeline_config():
    """Create pipeline configuration without auth"""
    print("📝 Creating no-auth pipeline configuration...")
    
    config_content = '''
# Pipeline Server Configuration - No Auth
export WEBUI_AUTH=false
export ENABLE_SIGNUP=true
export DEFAULT_USER_ROLE=admin
export WEBUI_SECRET_KEY=""
export OAUTH_CLIENT_ID=""
export OAUTH_CLIENT_SECRET=""

# Start pipeline server without authentication
python -m uvicorn main:app --host 0.0.0.0 --port 9099 --reload
'''
    
    with open("no_auth_pipeline_config.sh", "w") as f:
        f.write(config_content)
    
    print("✅ Created no_auth_pipeline_config.sh")

def main():
    """Main function to disable authentication"""
    print("🔓 DISABLE AUTHENTICATION FOR INTERNAL VPS")
    print("=" * 50)
    
    # Step 1: Disable pipeline auth
    disable_pipeline_auth()
    
    # Step 2: Test endpoints
    test_endpoints_after_auth_disable()
    
    # Step 3: Create config for future use
    create_no_auth_pipeline_config()
    
    print("\n🎯 SUMMARY:")
    print("✅ Pipeline server restarted")
    print("✅ Auth requirements should be reduced")
    print("✅ Configuration file created for future use")
    
    print("\n💡 NEXT STEPS:")
    print("1. Test RAG model in Open WebUI")
    print("2. If still auth issues, restart Open WebUI with no-auth config")
    print("3. Verify all models can access RAG")

if __name__ == "__main__":
    main()