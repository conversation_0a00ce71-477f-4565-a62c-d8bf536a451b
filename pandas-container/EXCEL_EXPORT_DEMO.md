# Demo Export Excel từ Pandas Server

## Tổng quan
Pandas server hiện đã hỗ trợ export DataFrame thành file Excel (.xlsx) với base64 encoding, cho phép download trực tiếp.

## Cách sử dụng

### 1. T<PERSON><PERSON> và chỉnh sửa dữ liệu
```bash
# Tạo DataFrame mẫu
curl -X POST "http://localhost:8004/execute_pandas_code" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "df = pd.DataFrame({\"name\": [\"Alice\", \"Bob\", \"Charlie\"], \"age\": [25, 30, 35], \"city\": [\"Hanoi\", \"HCMC\", \"Danang\"]})\nprint(df)"
  }'

# Chỉnh sửa dữ liệu - thêm cột salary
curl -X POST "http://localhost:8004/execute_pandas_code" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "df[\"salary\"] = [50000, 60000, 70000]\nprint(df)"
  }'

# Lọc dữ liệu
curl -X POST "http://localhost:8004/execute_pandas_code" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "df_filtered = df[df[\"age\"] > 28]\nprint(df_filtered)"
  }'
```

### 2. Export thành Excel
```bash
curl -X POST "http://localhost:8004/export_to_excel" \
  -H "Content-Type: application/json" \
  -d '{
    "dataframe_name": "df",
    "filename": "employee_data.xlsx",
    "sheet_name": "Employees"
  }'
```

### 3. Response sẽ chứa:
```json
{
  "success": true,
  "filename": "employee_data.xlsx",
  "sheet_name": "Employees",
  "dataframe_name": "df",
  "rows": 3,
  "columns": 4,
  "excel_base64": "UEsDBBQAAAAIAKIxAlt...",
  "download_instructions": {
    "method_1": "Use the excel_base64 field to create a downloadable file",
    "method_2": "Decode base64 and save as .xlsx file",
    "browser": "Create blob URL from base64 data for download"
  },
  "timestamp": "2025-08-02T06:13:18.705813"
}
```

## Cách download file Excel

### Phương pháp 1: Sử dụng JavaScript trong browser
```javascript
// Lấy base64 data từ response
const base64Data = response.excel_base64;
const filename = response.filename;

// Tạo blob và download
const byteCharacters = atob(base64Data);
const byteNumbers = new Array(byteCharacters.length);
for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
}
const byteArray = new Uint8Array(byteNumbers);
const blob = new Blob([byteArray], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});

// Tạo download link
const url = window.URL.createObjectURL(blob);
const a = document.createElement('a');
a.href = url;
a.download = filename;
document.body.appendChild(a);
a.click();
document.body.removeChild(a);
window.URL.revokeObjectURL(url);
```

### Phương pháp 2: Sử dụng Python
```python
import base64
import requests

# Gọi API export
response = requests.post('http://localhost:8004/export_to_excel', json={
    "dataframe_name": "df",
    "filename": "my_data.xlsx",
    "sheet_name": "Data"
})

data = response.json()
if data['success']:
    # Decode base64 và lưu file
    excel_data = base64.b64decode(data['excel_base64'])
    with open(data['filename'], 'wb') as f:
        f.write(excel_data)
    print(f"File {data['filename']} đã được lưu!")
```

### Phương pháp 3: Sử dụng command line
```bash
# Lưu base64 vào file tạm
curl -X POST "http://localhost:8004/export_to_excel" \
  -H "Content-Type: application/json" \
  -d '{"dataframe_name": "df", "filename": "data.xlsx"}' \
  | jq -r '.excel_base64' > temp_base64.txt

# Decode và lưu thành file Excel
base64 -d temp_base64.txt > data.xlsx

# Xóa file tạm
rm temp_base64.txt
```

## Ví dụ hoàn chỉnh: Phân tích dữ liệu bán hàng

### 1. Tạo dữ liệu mẫu
```bash
curl -X POST "http://localhost:8004/execute_pandas_code" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "sales_data = pd.DataFrame({\"month\": [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\"], \"revenue\": [100000, 150000, 120000, 200000, 180000], \"customers\": [50, 75, 60, 100, 90]})\nprint(sales_data)"
  }'
```

### 2. Thêm phân tích
```bash
curl -X POST "http://localhost:8004/execute_pandas_code" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "sales_data[\"avg_revenue_per_customer\"] = sales_data[\"revenue\"] / sales_data[\"customers\"]\nsales_data[\"growth_rate\"] = sales_data[\"revenue\"].pct_change() * 100\nprint(sales_data)"
  }'
```

### 3. Export kết quả
```bash
curl -X POST "http://localhost:8004/export_to_excel" \
  -H "Content-Type: application/json" \
  -d '{
    "dataframe_name": "sales_data",
    "filename": "sales_analysis.xlsx",
    "sheet_name": "Monthly_Sales"
  }'
```

## Lưu ý
- File Excel được tạo với format .xlsx (Excel 2007+)
- Hỗ trợ tiếng Việt trong tên sheet và dữ liệu
- Không bao gồm index của DataFrame (index=False)
- Base64 data có thể khá lớn với DataFrame lớn
- File Excel có thể mở được trong Microsoft Excel, LibreOffice Calc, Google Sheets

## Troubleshooting
- Nếu DataFrame không tồn tại, sẽ trả về lỗi "Dataframe not found"
- Đảm bảo tên DataFrame chính xác (case-sensitive)
- Base64 decode có thể fail nếu data bị corrupt trong quá trình truyền