# Tích hợp Pandas Server vào Open WebUI

## Thông tin Server
- **URL**: `http://localhost:8004`
- **Type**: HTTP API Server
- **Port**: 8004
- **Status**: ✅ Đang chạy

## Cách thêm vào Open WebUI

### Phương pháp 1: Thêm Tool Server trực tiếp

1. **Mở Open WebUI Admin Panel**
   - Vào Settings → Admin Settings → Tools

2. **Thêm Tool Server mới**
   - Click "Add Tool Server"
   - Điền thông tin:
     ```
     Name: Pandas Data Analysis
     URL: http://localhost:8004
     Description: Full-featured pandas data processing and visualization
     ```

3. **Verify Connection**
   - Test connection để đảm bảo server hoạt động
   - Open WebUI sẽ tự động discover các endpoints

### Phương pháp 2: Sử dụng OpenAPI Integration

1. **Lấy OpenAPI Schema**
   ```bash
   curl http://localhost:8004/openapi.json > pandas_openapi.json
   ```

2. **Import vào Open WebUI**
   - Vào Tools → Import OpenAPI
   - Upload file `pandas_openapi.json`
   - Configure endpoints

### Phương pháp 3: Manual Configuration

Thêm các endpoints sau vào Open WebUI Tools:

#### 1. Execute Pandas Code
```json
{
  "name": "execute_pandas_code",
  "url": "http://localhost:8004/execute_pandas_code",
  "method": "POST",
  "description": "Execute pandas code with security checks",
  "parameters": {
    "code": "string (required)",
    "dataframe_name": "string (optional)"
  }
}
```

#### 2. Create Visualization
```json
{
  "name": "create_visualization",
  "url": "http://localhost:8004/create_visualization",
  "method": "POST",
  "description": "Create charts with embedded markdown images",
  "parameters": {
    "dataframe_name": "string (required)",
    "chart_type": "string (bar/line/scatter/hist)",
    "x_column": "string (optional)",
    "y_column": "string (optional)",
    "title": "string (optional)"
  }
}
```

#### 3. Export to Excel
```json
{
  "name": "export_to_excel",
  "url": "http://localhost:8004/export_to_excel",
  "method": "POST",
  "description": "Export DataFrame to Excel file",
  "parameters": {
    "dataframe_name": "string (required)",
    "filename": "string (optional)",
    "sheet_name": "string (optional)"
  }
}
```

#### 4. Load DataFrame
```json
{
  "name": "load_dataframe",
  "url": "http://localhost:8004/load_dataframe",
  "method": "POST",
  "description": "Load data from CSV/Excel files",
  "parameters": {
    "file_path": "string (required)",
    "dataframe_name": "string (required)",
    "sheet_name": "string (optional)"
  }
}
```

#### 5. Health Check
```json
{
  "name": "pandas_health",
  "url": "http://localhost:8004/health",
  "method": "GET",
  "description": "Check pandas server status and capabilities"
}
```

## Sử dụng trong Open WebUI Chat

Sau khi cấu hình, bạn có thể sử dụng các lệnh sau trong chat:

### 1. Tạo và xử lý dữ liệu
```
@execute_pandas_code
Tạo DataFrame với dữ liệu bán hàng:
df = pd.DataFrame({
    'month': ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
    'revenue': [100000, 150000, 120000, 200000, 180000],
    'customers': [50, 75, 60, 100, 90]
})
print(df.describe())
```

### 2. Tạo biểu đồ
```
@create_visualization
{
  "dataframe_name": "df",
  "chart_type": "line",
  "x_column": "month",
  "y_column": "revenue",
  "title": "Doanh thu theo tháng"
}
```

### 3. Export Excel
```
@export_to_excel
{
  "dataframe_name": "df",
  "filename": "sales_report.xlsx",
  "sheet_name": "Monthly_Sales"
}
```

## Workflow trong Open WebUI

1. **Tạo dữ liệu** → `@execute_pandas_code`
2. **Phân tích** → `@execute_pandas_code` với các operations
3. **Visualize** → `@create_visualization` (hiển thị trực tiếp)
4. **Export** → `@export_to_excel` (download file)

## Lưu ý quan trọng

### Network Configuration
- Đảm bảo Open WebUI có thể access `localhost:8004`
- Nếu Open WebUI chạy trong Docker, sử dụng `host.docker.internal:8004`
- Hoặc add pandas container vào cùng Docker network

### Security
- Pandas server có basic security filtering
- Không cho phép import, exec, eval, file operations
- Chỉ cho phép pandas/numpy operations

### Performance
- DataFrame được lưu trong memory của container
- Restart container sẽ mất tất cả DataFrames
- Với dữ liệu lớn, cân nhắc persistent storage

## Troubleshooting

### Connection Issues
```bash
# Test connection
curl http://localhost:8004/health

# Check if container is running
docker ps | grep pandas-mcp-server
```

### Tool Not Appearing
1. Restart Open WebUI sau khi add tool
2. Check Open WebUI logs for errors
3. Verify URL và port number

### Visualization Not Showing
1. Đảm bảo Open WebUI hỗ trợ markdown images
2. Copy `markdown_image` field từ response
3. Paste trực tiếp vào chat

## Example Complete Workflow

```bash
# 1. Check server status
curl http://localhost:8004/health

# 2. Create sample data
curl -X POST "http://localhost:8004/execute_pandas_code" \
  -H "Content-Type: application/json" \
  -d '{"code": "df = pd.DataFrame({\"x\": [1,2,3,4,5], \"y\": [2,4,6,8,10]})"}'

# 3. Create visualization
curl -X POST "http://localhost:8004/create_visualization" \
  -H "Content-Type: application/json" \
  -d '{"dataframe_name": "df", "chart_type": "line", "x_column": "x", "y_column": "y"}'

# 4. Export to Excel
curl -X POST "http://localhost:8004/export_to_excel" \
  -H "Content-Type: application/json" \
  -d '{"dataframe_name": "df", "filename": "data.xlsx"}'
```

Sau khi cấu hình xong, bạn sẽ có một data analysis tool mạnh mẽ ngay trong Open WebUI!