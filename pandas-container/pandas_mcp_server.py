#!/usr/bin/env python3
"""
Pandas MCP Server - HTTP API Version
Full-featured pandas operations running on port 8004
"""

import asyncio
import json
import logging
import sys
import os
import io
import traceback
import base64
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from pathlib import Path

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("pandas-mcp-server")

# Try to import pandas and related libraries
try:
    import pandas as pd
    import numpy as np
    PANDAS_AVAILABLE = True
    logger.info("✅ Pandas and numpy imported successfully")
    logger.info(f"Pandas version: {pd.__version__}")
    logger.info(f"Numpy version: {np.__version__}")
except ImportError as e:
    PANDAS_AVAILABLE = False
    logger.error(f"❌ Failed to import pandas/numpy: {e}")

# Try to import visualization libraries
try:
    import matplotlib
    matplotlib.use('Agg')  # Use non-interactive backend
    import matplotlib.pyplot as plt
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
    logger.info("✅ Matplotlib and seaborn available")
except ImportError as e:
    MATPLOTLIB_AVAILABLE = False
    logger.warning(f"⚠️ Matplotlib/seaborn not available: {e}")

# Initialize FastAPI app
app = FastAPI(
    title="Pandas MCP Server",
    description="Full-featured pandas operations via HTTP API",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response models
class CodeExecutionRequest(BaseModel):
    code: str
    dataframe_name: Optional[str] = None

class FileMetadataRequest(BaseModel):
    file_path: str

class LoadDataframeRequest(BaseModel):
    file_path: str
    dataframe_name: str
    sheet_name: Optional[str] = None

class VisualizationRequest(BaseModel):
    dataframe_name: str
    chart_type: str = "bar"
    x_column: Optional[str] = None
    y_column: Optional[str] = None
    title: str = "Data Visualization"

class ExportRequest(BaseModel):
    dataframe_name: str
    filename: str = "exported_data.xlsx"
    sheet_name: str = "Sheet1"

class PandasProcessor:
    """Enhanced pandas data processor"""
    
    def __init__(self):
        self._initialized = PANDAS_AVAILABLE
        self.dataframes = {}  # Store dataframes by name
        self.metadata_cache = {}  # Cache for file metadata
        
        if self._initialized:
            logger.info("✅ Pandas Processor initialized successfully")
        else:
            logger.warning("⚠️ Pandas Processor running in limited mode")
    
    async def read_file_metadata(self, file_path: str) -> Dict[str, Any]:
        """Read file metadata (Excel or CSV) and return structured information"""
        if not self._initialized:
            return {"success": False, "error": "Pandas not available"}
        
        try:
            if not os.path.exists(file_path):
                return {"success": False, "error": f"File not found: {file_path}"}
            
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.csv':
                # Read CSV metadata
                df = pd.read_csv(file_path, nrows=100)  # Sample first 100 rows
                
                metadata = {
                    "success": True,
                    "file_info": {
                        "type": "csv",
                        "path": file_path,
                        "size_bytes": os.path.getsize(file_path)
                    },
                    "data": {
                        "rows": len(df),
                        "columns": len(df.columns),
                        "column_info": []
                    }
                }
                
                # Analyze each column
                for col in df.columns:
                    col_info = {
                        "name": col,
                        "type": str(df[col].dtype),
                        "null_count": int(df[col].isnull().sum()),
                        "unique_count": int(df[col].nunique()),
                        "examples": df[col].dropna().head(3).tolist()
                    }
                    
                    if df[col].dtype in ['int64', 'float64']:
                        col_info["stats"] = {
                            "mean": float(df[col].mean()) if not df[col].isnull().all() else None,
                            "min": float(df[col].min()) if not df[col].isnull().all() else None,
                            "max": float(df[col].max()) if not df[col].isnull().all() else None
                        }
                    
                    metadata["data"]["column_info"].append(col_info)
                
                return metadata
                
            elif file_ext in ['.xlsx', '.xls']:
                # Read Excel metadata
                excel_file = pd.ExcelFile(file_path)
                sheets_info = []
                
                for sheet_name in excel_file.sheet_names:
                    df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=100)
                    sheet_info = {
                        "sheet_name": sheet_name,
                        "rows": len(df),
                        "columns": len(df.columns),
                        "column_names": df.columns.tolist()
                    }
                    sheets_info.append(sheet_info)
                
                return {
                    "success": True,
                    "file_info": {
                        "type": "excel",
                        "path": file_path,
                        "sheet_count": len(excel_file.sheet_names),
                        "sheet_names": excel_file.sheet_names
                    },
                    "data": {
                        "sheets": sheets_info
                    }
                }
            
            else:
                return {"success": False, "error": f"Unsupported file type: {file_ext}"}
                
        except Exception as e:
            return {"success": False, "error": str(e), "traceback": traceback.format_exc()}
    
    async def execute_pandas_code(self, code: str, df_name: str = None) -> Dict[str, Any]:
        """Execute pandas code with security checks"""
        if not self._initialized:
            return {"success": False, "error": "Pandas not available"}
        
        try:
            # Security check - basic filtering
            dangerous_keywords = ['import', 'exec', 'eval', 'open', 'file', '__', 'os.', 'sys.']
            if any(keyword in code.lower() for keyword in dangerous_keywords):
                return {"success": False, "error": "Code contains potentially dangerous operations"}
            
            # Prepare execution environment
            exec_globals = {
                'pd': pd,
                'np': np,
                'dataframes': self.dataframes
            }
            
            if df_name and df_name in self.dataframes:
                exec_globals['df'] = self.dataframes[df_name]
            
            # Capture output
            old_stdout = sys.stdout
            sys.stdout = captured_output = io.StringIO()
            
            try:
                # Execute code
                exec(code, exec_globals)
                output = captured_output.getvalue()
                
                # Check if any new dataframes were created
                new_dfs = {}
                for key, value in exec_globals.items():
                    if isinstance(value, pd.DataFrame) and key not in ['pd', 'np', 'dataframes']:
                        new_dfs[key] = {
                            "shape": value.shape,
                            "columns": value.columns.tolist(),
                            "head": value.head().to_dict('records') if len(value) > 0 else []
                        }
                        # Store the dataframe
                        self.dataframes[key] = value
                
                return {
                    "success": True,
                    "output": output,
                    "new_dataframes": new_dfs,
                    "execution_time": datetime.now().isoformat()
                }
                
            finally:
                sys.stdout = old_stdout
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "traceback": traceback.format_exc()
            }
    
    async def create_visualization(self, df_name: str, chart_type: str = "bar", 
                                 x_col: str = None, y_col: str = None, 
                                 title: str = "Data Visualization") -> Dict[str, Any]:
        """Create visualization from dataframe"""
        if not self._initialized:
            return {"success": False, "error": "Pandas not available"}
        
        if not MATPLOTLIB_AVAILABLE:
            return {"success": False, "error": "Matplotlib not available for visualization"}
        
        if df_name not in self.dataframes:
            return {"success": False, "error": f"Dataframe '{df_name}' not found"}
        
        try:
            df = self.dataframes[df_name]
            
            plt.figure(figsize=(10, 6))
            
            if chart_type == "bar" and x_col and y_col:
                plt.bar(df[x_col], df[y_col])
                plt.xlabel(x_col)
                plt.ylabel(y_col)
            elif chart_type == "line" and x_col and y_col:
                plt.plot(df[x_col], df[y_col])
                plt.xlabel(x_col)
                plt.ylabel(y_col)
            elif chart_type == "hist" and x_col:
                plt.hist(df[x_col], bins=20)
                plt.xlabel(x_col)
                plt.ylabel("Frequency")
            elif chart_type == "scatter" and x_col and y_col:
                plt.scatter(df[x_col], df[y_col])
                plt.xlabel(x_col)
                plt.ylabel(y_col)
            else:
                # Default: show basic info
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 0:
                    df[numeric_cols].plot(kind='bar')
                else:
                    return {"success": False, "error": "No suitable columns for visualization"}
            
            plt.title(title)
            plt.tight_layout()
            
            # Save to base64
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            # Create markdown embedded image for Open WebUI
            markdown_image = f"![{title}](data:image/png;base64,{image_base64})"
            
            return {
                "success": True,
                "chart_type": chart_type,
                "image_base64": image_base64,
                "markdown_image": markdown_image,
                "title": title,
                "display_instructions": {
                    "open_webui": "Copy the 'markdown_image' field and paste it directly in chat to display the chart",
                    "jupyter": "Use the 'image_base64' field with IPython.display.Image",
                    "html": f"Use <img src='data:image/png;base64,{image_base64}' alt='{title}'/>"
                }
            }
            
        except Exception as e:
            return {"success": False, "error": str(e), "traceback": traceback.format_exc()}
    
    async def load_dataframe(self, file_path: str, df_name: str, **kwargs) -> Dict[str, Any]:
        """Load dataframe from file"""
        if not self._initialized:
            return {"success": False, "error": "Pandas not available"}
        
        try:
            if not os.path.exists(file_path):
                return {"success": False, "error": f"File not found: {file_path}"}
            
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.csv':
                df = pd.read_csv(file_path, **kwargs)
            elif file_ext in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path, **kwargs)
            else:
                return {"success": False, "error": f"Unsupported file type: {file_ext}"}
            
            self.dataframes[df_name] = df
            
            return {
                "success": True,
                "dataframe_name": df_name,
                "shape": df.shape,
                "columns": df.columns.tolist(),
                "head": df.head().to_dict('records'),
                "dtypes": df.dtypes.to_dict()
            }
            
        except Exception as e:
            return {"success": False, "error": str(e), "traceback": traceback.format_exc()}
    
    async def export_to_excel(self, df_name: str, filename: str = "exported_data.xlsx",
                             sheet_name: str = "Sheet1") -> Dict[str, Any]:
        """Export dataframe to Excel file and return as base64"""
        if not self._initialized:
            return {"success": False, "error": "Pandas not available"}
        
        if df_name not in self.dataframes:
            return {"success": False, "error": f"Dataframe '{df_name}' not found"}
        
        try:
            df = self.dataframes[df_name]
            
            # Create Excel file in memory
            buffer = io.BytesIO()
            with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            buffer.seek(0)
            excel_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            return {
                "success": True,
                "filename": filename,
                "sheet_name": sheet_name,
                "dataframe_name": df_name,
                "rows": len(df),
                "columns": len(df.columns),
                "excel_base64": excel_base64,
                "download_instructions": {
                    "method_1": "Use the excel_base64 field to create a downloadable file",
                    "method_2": "Decode base64 and save as .xlsx file",
                    "browser": "Create blob URL from base64 data for download"
                },
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"success": False, "error": str(e), "traceback": traceback.format_exc()}

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        return {
            "status": "healthy" if self._initialized else "degraded",
            "pandas_available": PANDAS_AVAILABLE,
            "matplotlib_available": MATPLOTLIB_AVAILABLE,
            "dataframes_count": len(self.dataframes),
            "dataframes": list(self.dataframes.keys()),
            "features": {
                "file_reading": PANDAS_AVAILABLE,
                "code_execution": PANDAS_AVAILABLE,
                "visualization": PANDAS_AVAILABLE and MATPLOTLIB_AVAILABLE,
                "metadata_analysis": PANDAS_AVAILABLE,
                "excel_export": PANDAS_AVAILABLE
            },
            "timestamp": datetime.now().isoformat()
        }

# Global processor instance
pandas_processor = PandasProcessor()

# API Routes
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Pandas MCP Server",
        "version": "1.0.0",
        "status": "healthy" if PANDAS_AVAILABLE else "degraded",
        "pandas_available": PANDAS_AVAILABLE,
        "matplotlib_available": MATPLOTLIB_AVAILABLE
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return await pandas_processor.health_check()

@app.post("/read_file_metadata")
async def read_file_metadata(request: FileMetadataRequest):
    """Read file metadata"""
    result = await pandas_processor.read_file_metadata(request.file_path)
    if not result["success"]:
        raise HTTPException(status_code=400, detail=result["error"])
    return result

@app.post("/load_dataframe")
async def load_dataframe(request: LoadDataframeRequest):
    """Load dataframe from file"""
    kwargs = {}
    if request.sheet_name:
        kwargs["sheet_name"] = request.sheet_name
    
    result = await pandas_processor.load_dataframe(request.file_path, request.dataframe_name, **kwargs)
    if not result["success"]:
        raise HTTPException(status_code=400, detail=result["error"])
    return result

@app.post("/execute_pandas_code")
async def execute_pandas_code(request: CodeExecutionRequest):
    """Execute pandas code"""
    result = await pandas_processor.execute_pandas_code(request.code, request.dataframe_name)
    if not result["success"]:
        raise HTTPException(status_code=400, detail=result["error"])
    return result

@app.post("/create_visualization")
async def create_visualization(request: VisualizationRequest):
    """Create visualization"""
    result = await pandas_processor.create_visualization(
        request.dataframe_name, 
        request.chart_type, 
        request.x_column, 
        request.y_column, 
        request.title
    )
    if not result["success"]:
        raise HTTPException(status_code=400, detail=result["error"])
    return result

@app.get("/dataframes")
async def list_dataframes():
    """List available dataframes"""
    return {
        "dataframes": list(pandas_processor.dataframes.keys()),
        "count": len(pandas_processor.dataframes)
    }

@app.get("/dataframes/{df_name}")
async def get_dataframe_info(df_name: str):
    """Get dataframe information"""
    if df_name not in pandas_processor.dataframes:
        raise HTTPException(status_code=404, detail=f"Dataframe '{df_name}' not found")
    
    df = pandas_processor.dataframes[df_name]
    return {
        "name": df_name,
        "shape": df.shape,
        "columns": df.columns.tolist(),
        "dtypes": df.dtypes.to_dict(),
        "head": df.head().to_dict('records'),
        "info": {
            "memory_usage": df.memory_usage(deep=True).sum(),
            "null_counts": df.isnull().sum().to_dict()
        }
    }

@app.post("/export_to_excel")
async def export_to_excel(request: ExportRequest):
    """Export dataframe to Excel file"""
    result = await pandas_processor.export_to_excel(
        request.dataframe_name,
        request.filename,
        request.sheet_name
    )
    if not result["success"]:
        raise HTTPException(status_code=400, detail=result["error"])
    return result

if __name__ == "__main__":
    logger.info("🚀 Starting Pandas MCP Server on port 8004")
    uvicorn.run(app, host="0.0.0.0", port=8004)