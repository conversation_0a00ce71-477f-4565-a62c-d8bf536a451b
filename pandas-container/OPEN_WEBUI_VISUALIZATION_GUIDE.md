# Hướng dẫn sử dụng Pandas Visualization trong Open WebUI

## Tổng quan
Pandas server hiện đã hỗ trợ tạo biểu đồ với embedded markdown images, cho phép hiển thị trực tiếp trong Open WebUI.

## Cách sử dụng

### 1. Tạo DataFrame
```bash
curl -X POST "http://localhost:8004/execute_pandas_code" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "df = pd.DataFrame({\"x\": [1, 2, 3, 4, 5], \"y\": [2, 4, 6, 8, 10]})\nprint(df)"
  }'
```

### 2. Tạo biểu đồ
```bash
curl -X POST "http://localhost:8004/create_visualization" \
  -H "Content-Type: application/json" \
  -d '{
    "dataframe_name": "df",
    "chart_type": "line",
    "x_column": "x",
    "y_column": "y",
    "title": "<PERSON>i<PERSON><PERSON> đồ dữ liệu"
  }'
```

### 3. Hiển thị trong Open WebUI
Response sẽ chứa field `markdown_image` với format:
```
![Biểu đồ dữ liệu](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...)
```

**Cách hiển thị:**
1. Copy nội dung của field `markdown_image`
2. Paste trực tiếp vào chat trong Open WebUI
3. Biểu đồ sẽ hiển thị ngay lập tức

## Các loại biểu đồ hỗ trợ
- `line`: Biểu đồ đường
- `bar`: Biểu đồ cột
- `scatter`: Biểu đồ phân tán
- `hist`: Biểu đồ histogram

## Ví dụ hoàn chỉnh

### Tạo dữ liệu mẫu
```python
# Tạo DataFrame với dữ liệu bán hàng
sales_data = pd.DataFrame({
    'month': ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
    'sales': [100, 150, 120, 200, 180]
})
```

### Tạo biểu đồ cột
```json
{
  "dataframe_name": "sales_data",
  "chart_type": "bar",
  "x_column": "month",
  "y_column": "sales",
  "title": "Doanh số bán hàng theo tháng"
}
```

### Kết quả
Response sẽ chứa:
- `image_base64`: Dữ liệu ảnh dạng base64
- `markdown_image`: Markdown image sẵn sàng để paste vào Open WebUI
- `display_instructions`: Hướng dẫn hiển thị cho các platform khác

## Lưu ý
- Biểu đồ được tạo với độ phân giải cao (150 DPI)
- Format PNG đảm bảo chất lượng tốt
- Tự động điều chỉnh layout để tránh bị cắt text
- Hỗ trợ tiếng Việt trong title và labels

## Troubleshooting
- Nếu biểu đồ không hiển thị, kiểm tra Open WebUI có hỗ trợ markdown images
- Đảm bảo DataFrame đã được tạo trước khi tạo biểu đồ
- Kiểm tra tên cột có chính xác không