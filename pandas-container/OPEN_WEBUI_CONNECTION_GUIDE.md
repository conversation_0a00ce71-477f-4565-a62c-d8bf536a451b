# Hướng dẫn kết nối Pandas Server với Open WebUI

## Thông tin kết nối đã xác minh ✅

- **Pandas Server Status**: ✅ Healthy
- **Port**: 8004
- **OpenAPI**: ✅ Available
- **Host IP**: **********

## URLs để thử trong Open WebUI

### **Option 1: Host IP (Khuyến nghị)**
```
URL: http://**********:8004
OpenAPI: openapi.json
```

### **Option 2: Docker Host**
```
URL: http://host.docker.internal:8004
OpenAPI: openapi.json
```

### **Option 3: Localhost (nếu Open WebUI không chạy trong Docker)**
```
URL: http://localhost:8004
OpenAPI: openapi.json
```

## Cách cấu hình trong Open WebUI

1. **Vào Settings → Tools**
2. **Click "Add Connection"**
3. **Đi<PERSON>n thông tin:**
   - **URL**: `http://**********:8004`
   - **OpenAPI endpoint**: `openapi.json`
   - **Name**: `Pandas Data Analysis`
   - **Description**: `Full-featured pandas data processing and visualization`
   - **Auth**: Bearer (không cần API key)
   - **Visibility**: Private hoặc theo ý muốn

4. **Click "Save"**

## Test connection trước khi add

Mở browser và test các URL sau:

### Test Health Check
```
http://**********:8004/health
```
**Expected Response:**
```json
{
  "status": "healthy",
  "pandas_available": true,
  "matplotlib_available": true,
  "features": {
    "file_reading": true,
    "code_execution": true,
    "visualization": true,
    "excel_export": true
  }
}
```

### Test OpenAPI Schema
```
http://**********:8004/openapi.json
```
**Expected Response:**
```json
{
  "info": {
    "title": "Pandas MCP Server",
    "description": "Full-featured pandas operations via HTTP API",
    "version": "1.0.0"
  }
}
```

## Nếu vẫn không kết nối được

### Troubleshooting Steps:

1. **Check Docker Network**
   ```bash
   docker network ls
   docker inspect <open-webui-container-name>
   ```

2. **Add to same network**
   ```bash
   docker network connect <open-webui-network> pandas-mcp-server
   ```

3. **Use container name**
   ```
   URL: http://pandas-mcp-server:8004
   ```

## Available Endpoints sau khi kết nối

1. **execute_pandas_code** - Execute pandas operations
2. **create_visualization** - Create charts with embedded images
3. **export_to_excel** - Export DataFrames to Excel
4. **load_dataframe** - Load data from files
5. **health** - Check server status

## Example Usage trong Open WebUI Chat

```
@execute_pandas_code
df = pd.DataFrame({'x': [1,2,3,4,5], 'y': [2,4,6,8,10]})
print(df)
```

```
@create_visualization
{
  "dataframe_name": "df",
  "chart_type": "line",
  "x_column": "x",
  "y_column": "y",
  "title": "Sample Chart"
}
```

```
@export_to_excel
{
  "dataframe_name": "df",
  "filename": "my_data.xlsx"
}
```

## Network Architecture

```
Open WebUI Container → http://**********:8004 → Pandas Container
```

Hoặc nếu cùng Docker network:
```
Open WebUI Container → http://pandas-mcp-server:8004 → Pandas Container
```

## Lưu ý quan trọng

- Pandas server đang chạy ổn định trên port 8004
- Tất cả endpoints đã được test và hoạt động
- Hỗ trợ đầy đủ pandas operations, visualization, và Excel export
- DataFrames được lưu trong memory của container
- Restart container sẽ mất tất cả DataFrames đã tạo

**Hãy thử URL `http://**********:8004` trước tiên!**