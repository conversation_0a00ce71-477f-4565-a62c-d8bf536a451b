version: '3.8'

services:
  pandas-mcp-server:
    build: .
    container_name: pandas-mcp-server
    ports:
      - "8004:8004"
    volumes:
      - ./data:/app/data  # Mount data directory for file access
      - ./logs:/app/logs  # Mount logs directory
    environment:
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - mcp-network

networks:
  mcp-network:
    external: true