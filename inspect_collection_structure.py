#!/usr/bin/env python3
"""
Inspect Qdrant collection structure to understand payload format
"""

from qdrant_client import QdrantClient
import json

def inspect_collection(collection_name):
    client = QdrantClient(host="localhost", port=6333)
    
    print(f"🔍 Inspecting collection: {collection_name}")
    
    # Get collection info
    try:
        collection_info = client.get_collection(collection_name)
        print(f"📊 Points count: {collection_info.points_count}")
        print(f"📏 Vector size: {collection_info.config.params.vectors.size}")
        print(f"📐 Distance: {collection_info.config.params.vectors.distance}")
    except Exception as e:
        print(f"❌ Error getting collection info: {e}")
        return
    
    # Get sample points
    try:
        points, _ = client.scroll(
            collection_name=collection_name,
            limit=5,
            with_payload=True,
            with_vectors=False
        )
        
        print(f"\n📄 Sample points ({len(points)} shown):")
        for i, point in enumerate(points):
            print(f"\n--- Point {i+1} (ID: {point.id}) ---")
            if point.payload:
                print("Payload keys:", list(point.payload.keys()))
                for key, value in point.payload.items():
                    if isinstance(value, str) and len(value) > 100:
                        print(f"  {key}: {value[:100]}...")
                    else:
                        print(f"  {key}: {value}")
            else:
                print("  No payload")
                
    except Exception as e:
        print(f"❌ Error getting sample points: {e}")

if __name__ == "__main__":
    inspect_collection("mem0_gemini_768")