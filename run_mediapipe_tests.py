#!/usr/bin/env python3
"""
MediaPipe LLM Integration Test Runner
Tests all Google Gallery MediaPipe features
"""

import subprocess
import sys
import time
import requests
from pathlib import Path

def wait_for_server(url, max_attempts=30):
    """Wait for MediaPipe server to start"""
    for attempt in range(max_attempts):
        try:
            response = requests.get(f"{url}/health", timeout=2)
            if response.status_code == 200:
                return True
        except:
            pass
        
        print(f"⏳ Waiting for MediaPipe server... ({attempt + 1}/{max_attempts})")
        time.sleep(2)
    
    return False

def main():
    print("🧪 MediaPipe LLM Integration Test Runner")
    print("📊 Testing Google Gallery MediaPipe architecture")
    print("=" * 60)
    
    # Check if test file exists
    test_file = Path("mediapipe_integration_test.py")
    if not test_file.exists():
        print("❌ Error: mediapipe_integration_test.py not found")
        sys.exit(1)
    
    # Check if server is running
    server_url = "http://localhost:8020"
    print("🔍 Checking if MediaPipe server is running...")
    
    if not wait_for_server(server_url):
        print("❌ MediaPipe server not responding. Please start it first:")
        print("   python start_mediapipe_server.py")
        sys.exit(1)
    
    print("✅ MediaPipe server is ready")
    print()
    
    try:
        # Run the tests
        result = subprocess.run([sys.executable, "mediapipe_integration_test.py"], 
                              capture_output=False)
        sys.exit(result.returncode)
        
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
