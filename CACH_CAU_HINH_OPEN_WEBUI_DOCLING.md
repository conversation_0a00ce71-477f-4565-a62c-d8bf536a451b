# 🔧 Cách Cấu Hình Open WebUI để Sử Dụng Docling

## 🎯 **HAI CÁCH SỬ DỤNG DOCLING TRONG OPEN WEBUI**

### ✅ **CÁCH 1: Backend Integration (Đã hoàn thành)**
- Hệ thống hybrid đã tự động hoạt động
- Không cần cấu hình gì thêm
- Upload file bình thường → tự động dùng hybrid processor

### ⚙️ **CÁCH 2: External Extraction (Cần cấu hình)**
- Cấu hình qua Open WebUI Settings
- Sử dụng Docling server trực tiếp
- Cần khởi động Docling server trước

---

## 🛠️ **HƯỚNG DẪN CẤU HÌNH EXTERNAL EXTRACTION**

### **Bước 1: Khởi động Docling Server**

```bash
# Mở terminal mới
cd /home/<USER>/AccA/backend/app/rag
source docling_env/bin/activate
python docling_server.py
```

**Kiểm tra server hoạt động:**
```bash
curl http://localhost:5001/health
# Kết quả: {"status": "healthy", "service": "docling-table-extractor"}
```

### **Bước 2: Truy cập Open WebUI Settings**

1. **Mở Open WebUI**: http://**************:3000
2. **Đăng nhập** với tài khoản admin
3. **Click vào Settings** (⚙️ icon ở góc phải)
4. **Chọn Admin Panel** (nếu bạn là admin)

### **Bước 3: Cấu hình External Content Extraction**

#### **3.1 Vào phần Documents**
```
Settings → Admin → Documents → Content Extraction
```

#### **3.2 Cấu hình External Extraction**
- **Enable External Content Extraction**: ✅ Bật
- **External Content Extraction URL**: `http://localhost:5001/extract_tables`
- **Timeout**: `120` seconds
- **Enable JavaScript Rendering**: ✅ Bật (nếu có)

#### **3.3 Các tùy chọn bổ sung**
- **Content Extraction Engine**: Chọn `external` hoặc `custom`
- **Fallback Engine**: `tika` (dự phòng)

### **Bước 4: Cấu hình RAG Settings**

#### **4.1 Vào RAG Settings**
```
Settings → Admin → RAG → General
```

#### **4.2 Cấu hình tối ưu cho bảng tiếng Việt**
- **Chunk Size**: `800` tokens
- **Chunk Overlap**: `150` tokens  
- **Top K**: `7` results
- **Similarity Threshold**: `0.05` (thấp hơn cho tiếng Việt)
- **Enable Hybrid Search**: ✅ Bật

#### **4.3 Embedding Settings**
```
Settings → Admin → RAG → Embedding
```
- **Embedding Engine**: `Google`
- **Embedding Model**: `text-embedding-004`
- **API Base URL**: `https://generativelanguage.googleapis.com/v1beta`
- **API Key**: `AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM`

### **Bước 5: Test External Extraction**

#### **5.1 Tạo Knowledge Collection**
1. **Vào Knowledge** tab
2. **Create New Collection**
3. **Tên**: "Test Docling Extraction"

#### **5.2 Upload Test Document**
1. **Upload file PDF** có chứa bảng
2. **Xem processing log** (nếu có)
3. **Kiểm tra** xem có extract được bảng không

#### **5.3 Test Query**
```
"Tìm thông tin trong bảng về doanh thu"
"Liệt kê các số liệu trong bảng tài chính"
```

---

## 🔍 **KIỂM TRA CẤU HÌNH**

### **Database Check**
```bash
# Kiểm tra cấu hình trong database
sqlite3 ~/.local/share/open-webui/webui.db "
SELECT key, value FROM config 
WHERE key LIKE '%EXTRACT%' OR key LIKE '%DOCLING%';
"
```

### **API Test**
```bash
# Test Docling server trực tiếp
curl -X POST http://localhost:5001/extract_tables \
  -F "file=@your_test_document.pdf"
```

### **Log Check**
```bash
# Xem log Open WebUI
tail -f ~/.local/share/open-webui/logs/app.log
```

---

## ⚠️ **TROUBLESHOOTING**

### **Lỗi thường gặp:**

#### **1. "External extraction failed"**
```bash
# Kiểm tra Docling server
curl http://localhost:5001/health

# Khởi động lại nếu cần
cd /home/<USER>/AccA/backend/app/rag
source docling_env/bin/activate
python docling_server.py
```

#### **2. "Timeout error"**
- Tăng timeout lên `180` seconds
- Hoặc giảm kích thước file test

#### **3. "Server not responding"**
```bash
# Kiểm tra port có bị chiếm không
netstat -tulpn | grep :5001

# Kill process nếu cần
pkill -f docling_server.py
```

#### **4. "Permission denied"**
```bash
# Kiểm tra quyền file
ls -la docling_server.py

# Cấp quyền execute
chmod +x docling_server.py
```

---

## 📊 **SO SÁNH HAI CÁCH**

| Tính năng | Backend Integration | External Extraction |
|-----------|-------------------|-------------------|
| **Cài đặt** | ✅ Tự động | ⚙️ Cần cấu hình |
| **Hiệu suất** | ⚡ Nhanh hơn | 🐌 Chậm hơn (qua API) |
| **Độ tin cậy** | 🛡️ Cao | ⚠️ Phụ thuộc server |
| **Fallback** | ✅ Tự động | ❌ Thủ công |
| **Monitoring** | 📊 Có log chi tiết | 📝 Log cơ bản |
| **Flexibility** | 🔧 Linh hoạt cao | 🔒 Hạn chế |

---

## 💡 **KHUYẾN NGHỊ**

### **Cho Production:**
- ✅ **Sử dụng Backend Integration** (Cách 1)
- Đã stable và optimize
- Tự động fallback
- Performance tốt hơn

### **Cho Testing/Development:**
- ⚙️ **Sử dụng External Extraction** (Cách 2)  
- Dễ debug và monitor
- Có thể thay đổi server độc lập
- Phù hợp cho thử nghiệm

### **Cấu hình Hybrid (Tốt nhất):**
- Backend Integration làm chính
- External Extraction làm backup
- Tự động switch khi cần

---

## 🎯 **TÓM TẮT NHANH**

### **Để sử dụng External Extraction:**

1. **Khởi động server**: `python docling_server.py`
2. **Vào Settings**: Admin → Documents → Content Extraction  
3. **Set URL**: `http://localhost:5001/extract_tables`
4. **Enable**: External Content Extraction ✅
5. **Test**: Upload PDF có bảng

### **Để sử dụng Backend Integration:**
- **Không cần làm gì** - đã hoạt động tự động! 🎉

**Backend Integration đã hoạt động và tối ưu cho tiếng Việt. Bạn có thể upload tài liệu ngay và hệ thống sẽ tự động sử dụng hybrid table processor!** ✅ 