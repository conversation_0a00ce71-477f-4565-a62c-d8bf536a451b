#!/usr/bin/env python3
"""
Oracle Thin Mode with mTLS Authentication
Proper implementation for Oracle Autonomous Database with wallet-based mTLS
"""

import oracledb
import os
from dotenv import load_dotenv
import signal
import sys

def timeout_handler(signum, frame):
    raise TimeoutError('Connection timeout')

def test_oracle_thin_mtls():
    """Test Oracle thin mode connection with proper mTLS wallet authentication"""
    
    # Set timeout
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(20)  # 20 second timeout
    
    try:
        # Load environment variables
        load_dotenv('.env.oracle')
        
        print('🔍 Oracle Thin Mode với mTLS Wallet Authentication')
        print('=' * 60)
        
        # Verify thin mode
        print(f'✅ Oracle client mode: {"Thin" if oracledb.is_thin_mode() else "Thick"}')
        
        # Get configuration
        wallet_location = os.getenv('ORACLE_WALLET_LOCATION')
        wallet_password = os.getenv('ORACLE_WALLET_PASSWORD')
        oracle_user = os.getenv('ORACLE_USER')
        oracle_password = os.getenv('ORACLE_PASSWORD')
        
        print(f'📁 Wallet location: {wallet_location}')
        print(f'🔐 Wallet password configured: {bool(wallet_password)}')
        print(f'👤 Oracle user: {oracle_user}')
        print(f'📂 Wallet exists: {os.path.exists(wallet_location) if wallet_location else False}')
        
        # Check wallet files
        if wallet_location and os.path.exists(wallet_location):
            wallet_files = os.listdir(wallet_location)
            print(f'📄 Wallet files: {", ".join(wallet_files)}')
            
            # Check for required files
            required_files = ['tnsnames.ora', 'sqlnet.ora', 'cwallet.sso', 'ewallet.p12']
            missing_files = [f for f in required_files if f not in wallet_files]
            if missing_files:
                print(f'⚠️  Missing wallet files: {", ".join(missing_files)}')
            else:
                print('✅ All required wallet files present')
        
        print('\n🔄 Attempting connection with mTLS wallet...')
        
        # Method 1: Using wallet with TNS name
        try:
            print('\n📋 Method 1: TNS name with wallet configuration')
            conn = oracledb.connect(
                user=oracle_user,
                password=oracle_password,
                dsn='raxcblotwgf3qzgh_high',  # TNS name from tnsnames.ora
                config_dir=wallet_location,
                wallet_location=wallet_location,
                wallet_password=wallet_password
            )
            
            # Test connection
            cursor = conn.cursor()
            cursor.execute("SELECT 'SUCCESS WITH TNS + WALLET!' FROM DUAL")
            result = cursor.fetchone()
            
            cursor.execute("SELECT SYSDATE FROM DUAL")
            date_result = cursor.fetchone()
            
            cursor.execute("SELECT USER FROM DUAL")
            user_result = cursor.fetchone()
            
            cursor.close()
            conn.close()
            
            print('🎉🎉🎉 METHOD 1 SUCCESS! 🎉🎉🎉')
            print(f'✅ Result: {result[0]}')
            print(f'✅ Database time: {date_result[0]}')
            print(f'✅ Connected as: {user_result[0]}')
            print('🚀 ORACLE MEMORY PIPELINE READY!')
            return True
            
        except Exception as e1:
            print(f'❌ Method 1 failed: {e1}')
            print(f'❌ Error type: {type(e1).__name__}')
        
        # Method 2: Using full DSN with wallet
        try:
            print('\n📋 Method 2: Full DSN with wallet configuration')
            
            # Read DSN from tnsnames.ora
            tns_file = os.path.join(wallet_location, 'tnsnames.ora')
            if os.path.exists(tns_file):
                with open(tns_file, 'r') as f:
                    content = f.read()
                    # Extract the DSN for raxcblotwgf3qzgh_high
                    for line in content.split('\n'):
                        if 'raxcblotwgf3qzgh_high' in line and '=' in line:
                            dsn = line.split('=', 1)[1].strip()
                            print(f'📝 Extracted DSN: {dsn[:80]}...')
                            break
                    else:
                        dsn = '(description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.ap-singapore-2.oraclecloud.com))(connect_data=(service_name=g872ed23dd62a8b_raxcblotwgf3qzgh_high.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))'
            else:
                dsn = '(description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.ap-singapore-2.oraclecloud.com))(connect_data=(service_name=g872ed23dd62a8b_raxcblotwgf3qzgh_high.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))'
            
            conn = oracledb.connect(
                user=oracle_user,
                password=oracle_password,
                dsn=dsn,
                config_dir=wallet_location,
                wallet_location=wallet_location,
                wallet_password=wallet_password
            )
            
            # Test connection
            cursor = conn.cursor()
            cursor.execute("SELECT 'SUCCESS WITH FULL DSN + WALLET!' FROM DUAL")
            result = cursor.fetchone()
            
            cursor.execute("SELECT SYSDATE FROM DUAL")
            date_result = cursor.fetchone()
            
            cursor.execute("SELECT USER FROM DUAL")
            user_result = cursor.fetchone()
            
            cursor.close()
            conn.close()
            
            print('🎉🎉🎉 METHOD 2 SUCCESS! 🎉🎉🎉')
            print(f'✅ Result: {result[0]}')
            print(f'✅ Database time: {date_result[0]}')
            print(f'✅ Connected as: {user_result[0]}')
            print('🚀 ORACLE MEMORY PIPELINE READY!')
            return True
            
        except Exception as e2:
            print(f'❌ Method 2 failed: {e2}')
            print(f'❌ Error type: {type(e2).__name__}')
        
        # Method 3: Using connection pool with wallet
        try:
            print('\n📋 Method 3: Connection pool with wallet')
            
            pool = oracledb.create_pool(
                user=oracle_user,
                password=oracle_password,
                dsn='raxcblotwgf3qzgh_high',
                config_dir=wallet_location,
                wallet_location=wallet_location,
                wallet_password=wallet_password,
                min=1,
                max=5,
                increment=1
            )
            
            # Test pool connection
            conn = pool.acquire()
            cursor = conn.cursor()
            cursor.execute("SELECT 'SUCCESS WITH POOL + WALLET!' FROM DUAL")
            result = cursor.fetchone()
            
            cursor.execute("SELECT SYSDATE FROM DUAL")
            date_result = cursor.fetchone()
            
            cursor.close()
            pool.release(conn)
            pool.close()
            
            print('🎉🎉🎉 METHOD 3 SUCCESS! 🎉🎉🎉')
            print(f'✅ Result: {result[0]}')
            print(f'✅ Database time: {date_result[0]}')
            print('🚀 ORACLE MEMORY PIPELINE WITH POOL READY!')
            return True
            
        except Exception as e3:
            print(f'❌ Method 3 failed: {e3}')
            print(f'❌ Error type: {type(e3).__name__}')
        
        print('\n❌ ALL METHODS FAILED')
        print('🔧 Troubleshooting suggestions:')
        print('   1. Check if Oracle Autonomous Database is running')
        print('   2. Verify network connectivity to Oracle Cloud')
        print('   3. Ensure wallet files are valid and not corrupted')
        print('   4. Check if mTLS is properly configured in database')
        print('   5. Verify wallet password is correct')
        
        return False
        
    except TimeoutError:
        print('❌ Connection timeout (20s)')
        return False
    except Exception as e:
        print(f'❌ Unexpected error: {e}')
        print(f'❌ Error type: {type(e).__name__}')
        return False
    finally:
        signal.alarm(0)

if __name__ == '__main__':
    success = test_oracle_thin_mtls()
    sys.exit(0 if success else 1)