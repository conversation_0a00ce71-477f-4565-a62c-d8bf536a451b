#!/usr/bin/env python3
"""
Advanced bypass techniques for sites like zhihu.com
"""

import subprocess
import re

def add_advanced_bypass_techniques():
    """Add advanced bypass techniques to jini_crawler.py"""
    
    print("🔧 Adding advanced bypass techniques...")
    
    # Read current jini_crawler.py
    result = subprocess.run([
        'docker', 'exec', 'jina-crawler-mcp', 'cat', '/app/jini_crawler.py'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ Error reading jini_crawler.py: {result.stderr}")
        return False
    
    content = result.stdout
    
    # Find the reader mode section and enhance it
    old_reader_section = '''            # 🚀 READER MODE FALLBACK for blocked sites (403, 429, 503)
            if not html_content and status in [403, 429, 503]:
                logger.info(f"📖 Trying Reader Mode fallback for blocked URL: {url}")
                try:
                    # Use Googlebot user agent to bypass blocks
                    reader_headers = {
                        'User-Agent': 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Referer': 'https://www.google.com/',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Cache-Control': 'no-cache',
                    }
                    
                    async with self.session.get(url, headers=reader_headers, timeout=30) as response:
                        if response.status == 200:
                            html_content = await response.text()
                            status = 200
                            logger.info(f"✅ Reader Mode successful for {url}")
                        else:
                            logger.warning(f"⚠️ Reader Mode also failed with status {response.status}")
                            
                except Exception as e:
                    logger.error(f"❌ Reader Mode error: {e}")'''
    
    new_advanced_section = '''            # 🚀 ADVANCED BYPASS TECHNIQUES for blocked sites (403, 429, 503)
            if not html_content and status in [403, 429, 503]:
                logger.info(f"🔓 Trying advanced bypass techniques for blocked URL: {url}")
                
                # Technique 1: Multiple Bot User Agents
                bot_user_agents = [
                    'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
                    'Mozilla/5.0 (compatible; Bingbot/2.0; +http://www.bing.com/bingbot.htm)',
                    'Mozilla/5.0 (compatible; facebookexternalhit/1.1; +http://www.facebook.com/externalhit_uatext.php)',
                    'Twitterbot/1.0',
                    'Mozilla/5.0 (compatible; LinkedInBot/1.0 (compatible; Mozilla/5.0; Apache-HttpClient +http://www.linkedin.com))',
                ]
                
                for i, user_agent in enumerate(bot_user_agents, 1):
                    try:
                        logger.info(f"🤖 Trying bot user agent {i}/{len(bot_user_agents)}: {user_agent[:50]}...")
                        
                        bypass_headers = {
                            'User-Agent': user_agent,
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                            'Accept-Language': 'en-US,en;q=0.9,zh-CN,zh;q=0.8',
                            'Accept-Encoding': 'gzip, deflate, br',
                            'Referer': 'https://www.google.com/',
                            'Cache-Control': 'no-cache',
                            'Pragma': 'no-cache',
                            'Sec-Fetch-Dest': 'document',
                            'Sec-Fetch-Mode': 'navigate',
                            'Sec-Fetch-Site': 'cross-site',
                            'Upgrade-Insecure-Requests': '1',
                        }
                        
                        async with self.session.get(url, headers=bypass_headers, timeout=30) as response:
                            if response.status == 200:
                                html_content = await response.text()
                                status = 200
                                logger.info(f"✅ Bot bypass successful with {user_agent[:30]}...")
                                break
                            else:
                                logger.debug(f"⚠️ Bot {i} failed with status {response.status}")
                                
                    except Exception as e:
                        logger.debug(f"❌ Bot {i} error: {e}")
                        continue
                
                # Technique 2: Archive.org Wayback Machine
                if not html_content:
                    logger.info(f"🏛️ Trying Archive.org Wayback Machine...")
                    try:
                        from urllib.parse import quote
                        wayback_url = f"https://web.archive.org/web/{quote(url)}"
                        
                        async with self.session.get(wayback_url, timeout=30) as response:
                            if response.status == 200:
                                wayback_content = await response.text()
                                # Extract content from wayback machine wrapper
                                if 'playback' in wayback_content and len(wayback_content) > 1000:
                                    html_content = wayback_content
                                    status = 200
                                    logger.info(f"✅ Archive.org bypass successful for {url}")
                                    
                    except Exception as e:
                        logger.debug(f"❌ Archive.org error: {e}")
                
                # Technique 3: Google Cache
                if not html_content:
                    logger.info(f"🔍 Trying Google Cache...")
                    try:
                        from urllib.parse import quote
                        cache_url = f"https://webcache.googleusercontent.com/search?q=cache:{quote(url)}"
                        
                        cache_headers = {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                            'Referer': 'https://www.google.com/',
                        }
                        
                        async with self.session.get(cache_url, headers=cache_headers, timeout=30) as response:
                            if response.status == 200:
                                cache_content = await response.text()
                                if 'cache:' in cache_content and len(cache_content) > 1000:
                                    html_content = cache_content
                                    status = 200
                                    logger.info(f"✅ Google Cache bypass successful for {url}")
                                    
                    except Exception as e:
                        logger.debug(f"❌ Google Cache error: {e}")
                
                # Technique 4: Mobile User Agent
                if not html_content:
                    logger.info(f"📱 Trying mobile user agent...")
                    try:
                        mobile_headers = {
                            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                            'Accept-Language': 'en-US,en;q=0.9',
                            'Accept-Encoding': 'gzip, deflate, br',
                            'Referer': 'https://www.google.com/',
                        }
                        
                        async with self.session.get(url, headers=mobile_headers, timeout=30) as response:
                            if response.status == 200:
                                html_content = await response.text()
                                status = 200
                                logger.info(f"✅ Mobile bypass successful for {url}")
                                
                    except Exception as e:
                        logger.debug(f"❌ Mobile bypass error: {e}")
                
                if html_content:
                    logger.info(f"🎉 Advanced bypass successful for {url}")
                else:
                    logger.warning(f"⚠️ All advanced bypass techniques failed for {url}")'''
    
    # Replace the reader mode section
    content = content.replace(old_reader_section, new_advanced_section)
    
    # Write enhanced content
    with open('/tmp/jini_crawler_advanced_bypass.py', 'w') as f:
        f.write(content)
    
    # Copy back to container
    copy_result = subprocess.run([
        'docker', 'cp', '/tmp/jini_crawler_advanced_bypass.py', 'jina-crawler-mcp:/app/jini_crawler.py'
    ], capture_output=True, text=True)
    
    if copy_result.returncode != 0:
        print(f"❌ Error copying enhanced file: {copy_result.stderr}")
        return False
    
    print("✅ Advanced bypass techniques added")
    return True

def main():
    """Main function"""
    print("🚀 Adding advanced bypass techniques for sites like zhihu.com...")
    print("🎯 Techniques:")
    print("1. 🤖 Multiple Bot User Agents (Google, Bing, Facebook, Twitter, LinkedIn)")
    print("2. 🏛️ Archive.org Wayback Machine")
    print("3. 🔍 Google Cache")
    print("4. 📱 Mobile User Agent")
    print()
    
    if add_advanced_bypass_techniques():
        print("\n🎉 Advanced bypass techniques added successfully!")
        print("🔄 Restarting container...")
        
        # Restart container
        subprocess.run(['docker', 'restart', 'jina-crawler-mcp'])
        print("✅ Container restarted")
        
        print("\n📊 New bypass chain:")
        print("1. Regular request")
        print("2. TLS bypass (Cloudflare)")
        print("3. 🤖 Bot user agents (5 different bots)")
        print("4. 🏛️ Archive.org Wayback Machine")
        print("5. 🔍 Google Cache")
        print("6. 📱 Mobile user agent")
        print("🎯 Should handle most blocked sites including zhihu.com")
    else:
        print("\n❌ Failed to add advanced bypass techniques")

if __name__ == "__main__":
    main()
