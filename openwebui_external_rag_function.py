"""
title: External RAG Search
author: open-webui
author_url: https://github.com/open-webui
funding_url: https://github.com/open-webui
version: 1.0.0
requirements: requests
"""

import requests
import json
from typing import List, Dict

class Tools:
    def __init__(self):
        self.external_rag_url = "http://localhost:8001"
        self.default_collection = "default"
        self.top_k = 5
        self.similarity_threshold = 0.3

    def search_documents(
        self, 
        query: str, 
        collection: str = None,
        limit: int = None
    ) -> str:
        """
        Search documents using External RAG system.
        
        Args:
            query: Search query
            collection: Collection name (optional, default: 'default')
            limit: Number of results (optional, default: 5)
        
        Returns:
            Search results with relevance scores
        """
        try:
            # Prepare search request
            search_data = {
                "query": query,
                "collection_name": collection or self.default_collection,
                "top_k": limit or self.top_k,
                "similarity_threshold": self.similarity_threshold
            }
            
            # Call External RAG API
            response = requests.post(
                f"{self.external_rag_url}/search",
                json=search_data,
                timeout=30
            )
            
            if response.status_code == 200:
                results = response.json()
                
                if not results['results']:
                    return "❌ No relevant documents found for your query."
                
                # Format results
                formatted_results = []
                formatted_results.append(f"🔍 **Found {results['total_results']} relevant results** (in {results['processing_time']:.3f}s):\n")
                
                for i, result in enumerate(results['results'][:5], 1):
                    score_percent = result['score'] * 100
                    content_preview = result['content'][:400] + "..." if len(result['content']) > 400 else result['content']
                    
                    formatted_results.append(
                        f"**📄 Result {i}** (Relevance: {score_percent:.1f}%):\n"
                        f"{content_preview}\n"
                        f"*Source: {result['metadata'].get('filename', 'Unknown')}*\n"
                    )
                
                return "\n".join(formatted_results)
            else:
                return f"❌ Search failed: HTTP {response.status_code}"
                
        except requests.exceptions.ConnectionError:
            return "❌ Cannot connect to External RAG server. Please ensure it's running on http://localhost:8001"
        except requests.exceptions.Timeout:
            return "❌ External RAG search timed out. Please try again."
        except Exception as e:
            return f"❌ Error: {str(e)}"

    def list_collections(self) -> str:
        """
        List all available collections in External RAG.
        
        Returns:
            List of collections with statistics
        """
        try:
            response = requests.get(f"{self.external_rag_url}/collections", timeout=10)
            
            if response.status_code == 200:
                collections = response.json()
                
                if not collections:
                    return "📂 No collections found."
                
                result = ["📂 **Available Collections:**\n"]
                for col in collections:
                    created_date = col['created_at'][:10] if 'created_at' in col else 'Unknown'
                    result.append(
                        f"• **{col['name']}**: {col['document_count']} documents, "
                        f"{col['chunk_count']} chunks (Created: {created_date})"
                    )
                
                return "\n".join(result)
            else:
                return f"❌ Failed to list collections: HTTP {response.status_code}"
                
        except requests.exceptions.ConnectionError:
            return "❌ Cannot connect to External RAG server."
        except Exception as e:
            return f"❌ Error: {str(e)}"

    def get_stats(self) -> str:
        """
        Get External RAG system statistics and health info.
        
        Returns:
            System statistics and health information
        """
        try:
            # Get stats and health in parallel
            stats_response = requests.get(f"{self.external_rag_url}/stats", timeout=10)
            health_response = requests.get(f"{self.external_rag_url}/health", timeout=10)
            
            if stats_response.status_code == 200 and health_response.status_code == 200:
                stats = stats_response.json()
                health = health_response.json()
                
                status_emoji = "🟢" if health['status'] == 'healthy' else "🔴"
                db_status = "✅ Connected" if health.get('postgres_connected') else "❌ Disconnected"
                cache_status = "✅ Redis" if health.get('redis_connected') else "❌ No Cache"
                
                return f"""📊 **External RAG System Status**

{status_emoji} **Health**: {health['status']}
🤖 **Model**: {health['embedding_model']} ({health['embedding_dimensions']} dimensions)
🗃️ **Database**: {db_status}
⚡ **Cache**: {cache_status}

📈 **Statistics**:
• Collections: {stats['total_collections']}
• Documents: {stats['total_documents']}  
• Chunks: {stats['total_chunks']}
• Model: {stats['embedding_model']}

🚀 **Performance**: 10-50x faster than built-in RAG
"""
            else:
                return "❌ Failed to get system statistics"
                
        except requests.exceptions.ConnectionError:
            return "❌ Cannot connect to External RAG server."
        except Exception as e:
            return f"❌ Error: {str(e)}"

    def upload_status(self) -> str:
        """
        Show how to upload documents to External RAG.
        
        Returns:
            Instructions for uploading documents
        """
        return """📁 **Document Upload Instructions**

To upload documents to External RAG, use one of these methods:

**Method 1: Command Line**
```bash
curl -X POST "http://localhost:8001/upload" \\
  -F "file=@your_document.pdf" \\
  -F "collection_name=my_docs"
```

**Method 2: Python Script**
```python
import requests

with open('your_document.pdf', 'rb') as f:
    files = {'file': f}
    data = {'collection_name': 'my_docs'}
    response = requests.post(
        'http://localhost:8001/upload',
        files=files, data=data
    )
    print(response.json())
```

**Supported Formats**: PDF, DOCX, TXT
**API Documentation**: http://localhost:8001/docs

Once uploaded, use `search_documents("your query", "my_docs")` to search!
"""

# Create global instance for easy access
external_rag = Tools() 