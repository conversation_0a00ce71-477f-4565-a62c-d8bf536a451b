#!/bin/bash
# Fix MCP Environment Variables
# Run this script to set proper environment variables for MCP services

echo "🔧 Setting up MCP Environment Variables..."

# Mem0 MCP Configuration (runs in MCPO container on port 8000)
export MEMORY_API_BASE="http://localhost:8000/api/v1/memory"
export GEMINI_API_KEY="AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"

# Oracle DB MCP Configuration  
export ORACLE_USER="ADMIN"
export ORACLE_PASSWORD="Twilv0zera@123"
export ORACLE_DSN="(description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.ap-singapore-2.oraclecloud.com))(connect_data=(service_name=g872ed23dd62a8b_swiv8hv5y96iwo2t_high.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))"
export ORACLE_WALLET_LOCATION="/home/<USER>/AccA/AccA/oracle_wallet/Wallet_SWIV8HV5Y96IWO2T"
export ORACLE_TNS_ALIAS="swiv8hv5y96iwo2t_high"
export ORACLE_WALLET_PASSWORD="Twilv0zera@123"
export TARGET_SCHEMA="ADMIN"
export CACHE_DIR=".cache"

# SearXNG Configuration
export SEARXNG_URL="http://localhost:4000"

echo "✅ Environment variables set successfully!"
echo ""
echo "📋 Summary:"
echo "1. SearXNG URL for Open WebUI: http://**********:4000 (or http://host.docker.internal:4000)"
echo "2. Memory API Base: http://localhost:8000/api/v1/memory (MCPO container)"
echo "3. Oracle wallet path: /home/<USER>/AccA/AccA/oracle_wallet/Wallet_SWIV8HV5Y96IWO2T"
echo ""
echo "🔄 Restart MCP services to apply changes:"
echo "docker restart mcpo-official-container"
