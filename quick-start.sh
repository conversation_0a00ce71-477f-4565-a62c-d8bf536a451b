#!/bin/bash

echo "🚀 Quick Start - Qwen2.5-Coder Agent Models"
echo "============================================"

# Check models
echo "📋 Checking downloaded models..."
ls -lh /opt/llama-cpp/models/qwen2.5-coder-*-instruct.gguf 2>/dev/null

# Kill any existing server
echo "🛑 Stopping existing servers..."
pkill -f llama-server

sleep 2

# Start with 7B model (faster startup)
MODEL_FILE="/opt/llama-cpp/models/qwen2.5-coder-7b-instruct.gguf"

if [ -f "$MODEL_FILE" ]; then
    echo "🚀 Starting Qwen2.5-Coder-7B-Instruct..."
    cd /opt/llama-cpp
    
    ./bin/llama-server \
        --model "$MODEL_FILE" \
        --port 11434 \
        --host 0.0.0.0 \
        --threads 28 \
        --ctx-size 32768 \
        --batch-size 512 \
        --parallel 1 \
        --temp 0.25 \
        --top-p 0.9 \
        --top-k 40 \
        --repeat-penalty 1.1 \
        > /tmp/qwen-7b.log 2>&1 &
    
    echo "⏳ Waiting for server to start..."
    sleep 10
    
    # Test API
    if curl -s http://localhost:11434/v1/models >/dev/null 2>&1; then
        echo "✅ Server started successfully!"
        echo "🌐 API: http://localhost:11434/v1"
        echo "📊 Model: Qwen2.5-Coder-7B-Instruct"
        echo "🤖 Features: Code Agents, Function Calling, 32K context"
        echo ""
        echo "🔗 Usage:"
        echo "   • Cline/Claude Dev: http://localhost:11434/v1"
        echo "   • Open WebUI: Add OpenAI connection"
        echo "   • Custom tools: Use OpenAI-compatible API"
        echo ""
        echo "📝 Logs: tail -f /tmp/qwen-7b.log"
    else
        echo "❌ Server failed to start"
        echo "📋 Check logs: tail /tmp/qwen-7b.log"
    fi
else
    echo "❌ Model not found: $MODEL_FILE"
    echo "💡 Run: ./download-qwen-coder-tools.sh"
fi 