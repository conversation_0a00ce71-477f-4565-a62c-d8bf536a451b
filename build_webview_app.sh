#!/bin/bash

# Build script for AI Assistant WebView wrapper
echo "🚀 Building AI Assistant WebView Wrapper..."

# Navigate to mobile app directory
cd ai_assistant_mobile

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Check for any issues
echo "🔍 Running analysis..."
flutter analyze

# Build APK
echo "🔨 Building APK..."
flutter build apk --release

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo "📱 APK location: build/app/outputs/flutter-apk/app-release.apk"
    
    # Copy APK to root directory with descriptive name
    cp build/app/outputs/flutter-apk/app-release.apk ../ai_assistant_webview_wrapper.apk
    echo "📋 Copied APK to: ai_assistant_webview_wrapper.apk"
    
    echo ""
    echo "🎯 Next steps:"
    echo "1. Install the APK on your Android device"
    echo "2. Make sure your Open WebUI server is running on port 3001"
    echo "3. Update the server URL in app settings if needed"
    echo "4. Test the WebView integration"
    
else
    echo "❌ Build failed!"
    exit 1
fi 