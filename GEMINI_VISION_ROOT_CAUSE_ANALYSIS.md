# Gemini Vision Root Cause Analysis

## 🚨 Vấn Đề Thực Sự

**User báo:** "Gemini models hỗ trợ vision nhưng khi upload ảnh thì báo không thấy hình"

**Root Cause:** Open WebUI không có integration riêng cho Gemini vision - nó chỉ có generic LiteLLM integration!

## 🔍 Phân Tích Kỹ Thuật

### 1. Open WebUI Architecture
```
User Upload Image → Open WebUI → LiteLLM → Gemini API
                                    ↑
                            Không hỗ trợ vision format!
```

### 2. Vấn Đề Cụ Thể

#### A. Open WebUI sử dụng LiteLLM
- Open WebUI dùng **LiteLLM** làm proxy cho tất cả models
- LiteLLM có thể chưa hỗ trợ đầy đủ Gemini vision format
- Hoặc cấu hình Gemini trong Open WebUI chưa enable vision

#### B. Gemini Vision Format Requirements
```python
# Gemini cần format này:
{
    "contents": [{
        "parts": [
            {"text": "What's in this image?"},
            {
                "inline_data": {
                    "mime_type": "image/jpeg",
                    "data": "base64_image_data"
                }
            }
        ]
    }]
}

# Nhưng Open WebUI/LiteLLM có thể gửi format này:
{
    "messages": [{
        "role": "user",
        "content": [
            {"type": "text", "text": "What's in this image?"},
            {
                "type": "image_url",
                "image_url": {"url": "data:image/jpeg;base64,xxx"}
            }
        ]
    }]
}
```

#### C. Backend Gemini Client Không Hỗ Trợ Vision
File: [`backend/app/llm_clients/gemini_client.py`](backend/app/llm_clients/gemini_client.py)

```python
# Line 13: Chỉ dùng 'gemini-pro' (text-only)
self.model = genai.GenerativeModel('gemini-pro')

# Không có:
# - gemini-2.5-flash (vision support)
# - Image processing logic
# - Vision-specific parameters
```

## 🎯 Các Giải Pháp Có Thể

### Option 1: Fix Open WebUI Integration (Khuyến nghị)
**Cách:** Sửa cấu hình Gemini trong Open WebUI để enable vision

**Files cần check:**
- Open WebUI database (models table)
- Environment variables
- LiteLLM configuration

### Option 2: Patch Backend Gemini Client
**Cách:** Upgrade backend Gemini client để hỗ trợ vision

**Changes needed:**
```python
# Thay vì:
self.model = genai.GenerativeModel('gemini-pro')

# Dùng:
self.model = genai.GenerativeModel('gemini-2.5-flash')
# + Add image processing logic
```

### Option 3: Pipeline Approach (Đã làm)
**Cách:** Dùng pipeline để intercept và format lại requests

**Status:** Đã tạo nhưng có thể chưa đủ

## 🔧 Debug Steps

### Step 1: Check Open WebUI Gemini Config
```bash
# Check database
sqlite3 webui-data/webui.db "SELECT * FROM model WHERE id LIKE '%gemini%';"

# Check environment
grep -i gemini .env.catomanton

# Check logs
docker logs catomanton-webui | grep -i gemini | tail -10
```

### Step 2: Test Direct Gemini API
```bash
python3 debug_gemini_vision_issue.py
```

### Step 3: Check LiteLLM Support
```bash
# Check if LiteLLM supports Gemini vision
pip show litellm
# Check LiteLLM docs for Gemini vision support
```

## 🎯 Most Likely Solution

**Vấn đề:** Open WebUI chưa được cấu hình đúng để enable Gemini vision

**Fix:** 
1. Ensure Gemini models trong Open WebUI có vision capability enabled
2. Check LiteLLM version và Gemini vision support
3. Update model configuration trong database

## 🚀 Action Plan

### Immediate Actions:
1. ✅ Run debug script: `python3 debug_gemini_vision_issue.py`
2. ⏳ Check Open WebUI Gemini model configuration
3. ⏳ Verify LiteLLM Gemini vision support
4. ⏳ Fix configuration if needed

### If Config Fix Doesn't Work:
1. Upgrade backend Gemini client
2. Patch Open WebUI Gemini integration
3. Use pipeline as workaround

## 📊 Probability Analysis

| Root Cause | Probability | Fix Difficulty |
|------------|-------------|----------------|
| **Open WebUI config issue** | 80% | Easy |
| LiteLLM không hỗ trợ Gemini vision | 15% | Medium |
| Backend client issue | 5% | Hard |

**Most likely:** Chỉ cần fix cấu hình Open WebUI!