#!/usr/bin/env python3
"""
Đo thời gian từng component trong hệ thống
"""

import time
import requests
import json
from datetime import datetime

def test_component_timing():
    """Test thời gian từng component"""
    
    print("🧪 COMPONENT TIMING ANALYSIS")
    print("=" * 50)
    print(f"🕐 Started at: {datetime.now().strftime('%H:%M:%S')}")
    
    base_url = "http://localhost"
    
    # Test 1: Gemini API direct call
    print("\n1️⃣ TESTING GEMINI API DIRECT:")
    print("-" * 30)
    
    gemini_start = time.time()
    try:
        gemini_data = {
            "contents": [{
                "parts": [{"text": "Hello, this is a test message"}]
            }]
        }
        
        response = requests.post(
            "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent",
            headers={
                "Content-Type": "application/json",
                "x-goog-api-key": "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"
            },
            json=gemini_data,
            timeout=60
        )
        
        gemini_end = time.time()
        gemini_duration = gemini_end - gemini_start
        
        print(f"✅ Gemini API: {gemini_duration:.2f}s (Status: {response.status_code})")
        
        if response.status_code == 200:
            result = response.json()
            if 'candidates' in result:
                content = result['candidates'][0]['content']['parts'][0]['text']
                print(f"📝 Response length: {len(content)} chars")
        
    except Exception as e:
        gemini_end = time.time()
        gemini_duration = gemini_end - gemini_start
        print(f"❌ Gemini API error: {e} (Time: {gemini_duration:.2f}s)")
    
    # Test 2: Pipelines endpoint
    print("\n2️⃣ TESTING PIPELINES:")
    print("-" * 30)
    
    pipeline_start = time.time()
    try:
        response = requests.get(f"{base_url}:9099/models", timeout=30)
        pipeline_end = time.time()
        pipeline_duration = pipeline_end - pipeline_start
        
        print(f"✅ Pipelines health: {pipeline_duration:.2f}s (Status: {response.status_code})")
        
    except Exception as e:
        pipeline_end = time.time()
        pipeline_duration = pipeline_end - pipeline_start
        print(f"❌ Pipelines error: {e} (Time: {pipeline_duration:.2f}s)")
    
    # Test 3: MCPO Server
    print("\n3️⃣ TESTING MCPO SERVER:")
    print("-" * 30)
    
    mcpo_start = time.time()
    try:
        response = requests.get(f"{base_url}:5000/health", timeout=30)
        mcpo_end = time.time()
        mcpo_duration = mcpo_end - mcpo_start
        
        print(f"✅ MCPO health: {mcpo_duration:.2f}s (Status: {response.status_code})")
        
    except Exception as e:
        mcpo_end = time.time()
        mcpo_duration = mcpo_end - mcpo_start
        print(f"❌ MCPO error: {e} (Time: {mcpo_duration:.2f}s)")
    
    # Test 4: Qdrant Vector DB
    print("\n4️⃣ TESTING QDRANT:")
    print("-" * 30)
    
    qdrant_start = time.time()
    try:
        response = requests.get(f"{base_url}:6333/collections", timeout=30)
        qdrant_end = time.time()
        qdrant_duration = qdrant_end - qdrant_start
        
        print(f"✅ Qdrant: {qdrant_duration:.2f}s (Status: {response.status_code})")
        
        if response.status_code == 200:
            collections = response.json()
            print(f"📊 Collections: {len(collections.get('result', {}).get('collections', []))}")
        
    except Exception as e:
        qdrant_end = time.time()
        qdrant_duration = qdrant_end - qdrant_start
        print(f"❌ Qdrant error: {e} (Time: {qdrant_duration:.2f}s)")
    
    # Test 5: Embedding generation test
    print("\n5️⃣ TESTING EMBEDDING GENERATION:")
    print("-" * 30)
    
    embed_start = time.time()
    try:
        embed_data = {
            "model": "text-embedding-004",
            "input": "This is a test text for embedding generation"
        }
        
        response = requests.post(
            "https://generativelanguage.googleapis.com/v1beta/openai/embeddings",
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"
            },
            json=embed_data,
            timeout=30
        )
        
        embed_end = time.time()
        embed_duration = embed_end - embed_start
        
        print(f"✅ Embedding: {embed_duration:.2f}s (Status: {response.status_code})")
        
        if response.status_code == 200:
            result = response.json()
            if 'data' in result and len(result['data']) > 0:
                vector_len = len(result['data'][0]['embedding'])
                print(f"📊 Vector dimension: {vector_len}")
        
    except Exception as e:
        embed_end = time.time()
        embed_duration = embed_end - embed_start
        print(f"❌ Embedding error: {e} (Time: {embed_duration:.2f}s)")
    
    # Summary
    print("\n📊 TIMING SUMMARY:")
    print("=" * 50)
    print(f"🤖 Gemini API (direct): {gemini_duration:.2f}s")
    print(f"🔧 Pipelines: {pipeline_duration:.2f}s") 
    print(f"🛠️  MCPO Server: {mcpo_duration:.2f}s")
    print(f"💾 Qdrant DB: {qdrant_duration:.2f}s")
    print(f"🧠 Embedding: {embed_duration:.2f}s")
    
    total_overhead = pipeline_duration + mcpo_duration + qdrant_duration + embed_duration
    print(f"\n⚡ Infrastructure overhead: {total_overhead:.2f}s")
    print(f"🎯 Pure AI processing: {gemini_duration:.2f}s")
    
    # Analysis
    print(f"\n🔍 BOTTLENECK ANALYSIS:")
    print("-" * 30)
    
    if gemini_duration > 10:
        print(f"🐌 Gemini API is slow ({gemini_duration:.2f}s)")
    if pipeline_duration > 2:
        print(f"🐌 Pipelines are slow ({pipeline_duration:.2f}s)")
    if embed_duration > 3:
        print(f"🐌 Embedding generation is slow ({embed_duration:.2f}s)")
    if qdrant_duration > 1:
        print(f"🐌 Qdrant DB is slow ({qdrant_duration:.2f}s)")
    
    if total_overhead > gemini_duration:
        print(f"⚠️  Infrastructure overhead ({total_overhead:.2f}s) > AI processing ({gemini_duration:.2f}s)")
        print("💡 Consider optimizing pipeline configuration")

if __name__ == "__main__":
    test_component_timing()
