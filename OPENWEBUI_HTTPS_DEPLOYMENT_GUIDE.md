# 🚀 Open WebUI HTTPS Deployment Guide với Traefik

Hướng dẫn triển khai Open WebUI với HTTPS tự động sử dụng Traefik và Let's Encrypt cho domain `catomanton.duckdns.org`.

## 📋 Tổng quan

Deployment này bao gồm:
- **Open WebUI**: <PERSON><PERSON><PERSON>n chính cho AI chat
- **Traefik**: Reverse proxy với SSL tự động
- **Pipelines**: Backend service cho các tính năng nâng cao
- **Let's Encrypt**: SSL certificate miễn phí
- **Domain**: `catomanton.duckdns.org` (IP: *************9)

## 🔧 Yêu cầu hệ thống

- Docker và Docker Compose đã cài đặt
- Port 80, 443, 8080 available
- Domain `catomaton.duckdns.org` đã trỏ về server IP
- Gemini API key (đã có sẵn trong config)

## 📁 Cấu trúc file

```
AccA/
├── deploy-openwebui-traefik.yml    # Docker Compose chính
├── .env.traefik                    # Environment variables
├── deploy-openwebui-https.sh       # Script deploy tự động
├── traefik/
│   ├── traefik.yml                 # Cấu hình Traefik
│   └── acme/                       # SSL certificates storage
└── OPENWEBUI_HTTPS_DEPLOYMENT_GUIDE.md
```

## 🚀 Cách sử dụng

### Phương pháp 1: Script tự động (Khuyến nghị)

```bash
# Chạy script với domain catomaton
./deploy-openwebui-https.sh catomaton.duckdns.org <EMAIL>
```

### Phương pháp 2: Manual deployment

```bash
# 1. Kiểm tra cấu hình
cat .env.traefik

# 2. Tạo network
docker network create webui-network

# 3. Setup SSL storage
mkdir -p traefik/acme
touch traefik/acme/acme.json
chmod 600 traefik/acme/acme.json

# 4. Deploy services
docker-compose -f deploy-openwebui-traefik.yml --env-file .env.traefik up -d

# 5. Kiểm tra status
docker-compose -f deploy-openwebui-traefik.yml ps
```

## 🌐 Thông tin truy cập

### URLs chính
- **Main Site**: https://catomaton.duckdns.org
- **HTTP Redirect**: http://catomaton.duckdns.org → HTTPS
- **Traefik Dashboard**: http://*************9:8080
- **API Endpoint**: https://api.catomaton.duckdns.org/pipelines

### Thông tin DNS
- **Domain**: catomaton.duckdns.org
- **IP**: *************9
- **DNS Record**: A record pointing to *************9

## ⚙️ Cấu hình chi tiết

### Environment Variables (.env.traefik)
```ini
DOMAIN=catomaton.duckdns.org
SSL_EMAIL=<EMAIL>
WEBUI_SECRET_KEY=your-secret-key-here-change-this
GEMINI_API_KEY=AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM

# RAG Configuration với Gemini
RAG_EMBEDDING_ENGINE=openai
RAG_EMBEDDING_MODEL=text-embedding-004
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
RAG_TOP_K=10
```

### Traefik Configuration
- **Auto SSL**: Let's Encrypt với HTTP challenge
- **HTTP → HTTPS**: Tự động redirect
- **Security Headers**: HSTS, XSS protection, etc.
- **Dashboard**: Available tại port 8080

### Open WebUI Features
- **RAG với Gemini**: text-embedding-004 model
- **Pipelines**: Advanced backend features
- **Security**: Signup disabled, user role management
- **Web Search**: RAG web fetch enabled

## 🔍 Troubleshooting

### 1. Kiểm tra service status
```bash
docker-compose -f deploy-openwebui-traefik.yml ps
docker-compose -f deploy-openwebui-traefik.yml logs -f
```

### 2. Kiểm tra SSL certificate
```bash
docker-compose -f deploy-openwebui-traefik.yml logs traefik | grep -i acme
ls -la traefik/acme/acme.json
```

### 3. Test connectivity
```bash
# Test HTTP
curl -I http://catomaton.duckdns.org

# Test HTTPS
curl -I https://catomaton.duckdns.org

# Test domain resolution
nslookup catomaton.duckdns.org
```

### 4. Common issues

**SSL Certificate không tạo được:**
```bash
# Kiểm tra acme.json permissions
chmod 600 traefik/acme/acme.json

# Restart Traefik
docker-compose -f deploy-openwebui-traefik.yml restart traefik
```

**Domain không resolve:**
- Kiểm tra DNS settings tại DuckDNS
- Đảm bảo A record trỏ về *************9

**Port conflicts:**
```bash
# Kiểm tra ports đang sử dụng
netstat -tulpn | grep -E ':(80|443|8080)'

# Stop conflicting services
sudo systemctl stop nginx apache2
```

## 🛠️ Management Commands

### Quản lý services
```bash
# Start services
docker-compose -f deploy-openwebui-traefik.yml up -d

# Stop services
docker-compose -f deploy-openwebui-traefik.yml down

# Restart specific service
docker-compose -f deploy-openwebui-traefik.yml restart open-webui

# View logs
docker-compose -f deploy-openwebui-traefik.yml logs -f open-webui
```

### Backup và restore
```bash
# Backup volumes
docker run --rm -v open-webui-data:/data -v $(pwd):/backup alpine tar czf /backup/openwebui-backup.tar.gz -C /data .

# Restore volumes
docker run --rm -v open-webui-data:/data -v $(pwd):/backup alpine tar xzf /backup/openwebui-backup.tar.gz -C /data
```

### Update services
```bash
# Pull latest images
docker-compose -f deploy-openwebui-traefik.yml pull

# Recreate containers
docker-compose -f deploy-openwebui-traefik.yml up -d --force-recreate
```

## 🔒 Security Features

### SSL/TLS
- **Let's Encrypt**: Automatic certificate generation
- **HSTS**: HTTP Strict Transport Security
- **TLS 1.2+**: Modern encryption standards

### Headers Security
- **X-Frame-Options**: Clickjacking protection
- **X-XSS-Protection**: XSS filtering
- **X-Content-Type-Options**: MIME sniffing protection
- **Referrer-Policy**: Referrer information control

### Application Security
- **Signup Disabled**: Prevent unauthorized registration
- **User Roles**: Controlled access levels
- **Secret Key**: Session security

## 📊 Monitoring

### Health Checks
```bash
# Service health
docker-compose -f deploy-openwebui-traefik.yml ps

# Resource usage
docker stats

# Disk usage
docker system df
```

### Traefik Dashboard
- URL: http://*************9:8080
- Features: Routing rules, SSL status, service health

### Log Monitoring
```bash
# Real-time logs
docker-compose -f deploy-openwebui-traefik.yml logs -f

# Specific service logs
docker-compose -f deploy-openwebui-traefik.yml logs traefik
docker-compose -f deploy-openwebui-traefik.yml logs open-webui
```

## 🔄 Maintenance

### Regular Tasks
1. **Certificate Renewal**: Automatic via Let's Encrypt
2. **Image Updates**: Monthly `docker-compose pull`
3. **Log Rotation**: Configure Docker log limits
4. **Backup**: Weekly data backup

### Performance Optimization
```bash
# Clean unused resources
docker system prune -f

# Optimize images
docker image prune -f

# Monitor resource usage
docker stats --no-stream
```

## 📞 Support

### Quick Commands Reference
```bash
# Deploy
./deploy-openwebui-https.sh catomaton.duckdns.org

# Status check
docker-compose -f deploy-openwebui-traefik.yml ps

# Logs
docker-compose -f deploy-openwebui-traefik.yml logs -f

# Stop
docker-compose -f deploy-openwebui-traefik.yml down

# Update
docker-compose -f deploy-openwebui-traefik.yml pull && \
docker-compose -f deploy-openwebui-traefik.yml up -d --force-recreate
```

### Configuration Files
- **Main Config**: `deploy-openwebui-traefik.yml`
- **Environment**: `.env.traefik`
- **Traefik**: `traefik/traefik.yml`
- **Deploy Script**: `deploy-openwebui-https.sh`

---

## ✅ Deployment Checklist

- [ ] Domain `catomaton.duckdns.org` trỏ về *************9
- [ ] Docker và Docker Compose đã cài đặt
- [ ] Ports 80, 443, 8080 available
- [ ] Gemini API key configured
- [ ] SSL certificate storage setup
- [ ] Services deployed và running
- [ ] HTTPS access working
- [ ] Traefik dashboard accessible

**🎉 Deployment hoàn tất! Truy cập https://catomaton.duckdns.org để sử dụng Open WebUI.**