#!/usr/bin/env python3
"""
Reset Open WebUI Permissions
Fix database permissions and user roles
"""

import sqlite3
import os
import json
from datetime import datetime

def reset_webui_permissions():
    """Reset Open WebUI database permissions"""
    
    print("🔧 RESETTING OPEN WEBUI PERMISSIONS")
    print("===================================")
    
    db_path = "/home/<USER>/.local/lib/python3.12/site-packages/open_webui/data/webui.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if users table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user';")
        if cursor.fetchone():
            
            # Reset all users to admin role
            print("📋 Setting all users to admin role...")
            cursor.execute("UPDATE user SET role = 'admin' WHERE role != 'admin';")
            
            # Enable all users
            cursor.execute("UPDATE user SET is_active = 1;")
            
            # Remove any permission restrictions
            cursor.execute("UPDATE user SET profile = NULL WHERE profile LIKE '%permission%';")
            
            conn.commit()
            print("✅ User permissions reset successfully")
            
            # Show current users
            cursor.execute("SELECT id, name, email, role, is_active FROM user;")
            users = cursor.fetchall()
            print(f"\n📊 Current users ({len(users)}):")
            for user in users:
                print(f"   • {user[1]} ({user[2]}) - Role: {user[3]}, Active: {user[4]}")
        
        else:
            print("⚠️  No users table found - this might be a fresh installation")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error resetting permissions: {e}")
        return False

if __name__ == "__main__":
    reset_webui_permissions()
