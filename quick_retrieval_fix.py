#!/usr/bin/env python3
"""
Quick fix for Open WebUI retrieval bug
Directly patches the problematic line in the container
"""

import docker
import logging
import sys

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_retrieval_bug():
    """Apply quick fix for the retrieval bug"""
    client = docker.from_env()
    container_name = "catomanton-webui"
    
    try:
        container = client.containers.get(container_name)
        logger.info(f"Found container: {container_name}")
        
        # Create the patch script
        patch_script = '''
import re
import os

def apply_patch():
    file_path = "/app/backend/open_webui/retrieval/utils.py"
    
    # Create backup
    os.system(f"cp {file_path} {file_path}.backup")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find and replace the problematic line
    old_line = "zip(documents, scores.tolist() if not isinstance(scores, list) else scores)"
    new_line = "zip(documents, scores.tolist() if scores is not None and hasattr(scores, 'tolist') and not isinstance(scores, list) else (scores if scores is not None else [1.0] * len(documents)))"
    
    if old_line in content:
        content = content.replace(old_line, new_line)
        
        with open(file_path, 'w') as f:
            f.write(content)
        
        print("Patch applied successfully!")
        return True
    else:
        print("Target line not found - may already be patched")
        return False

if __name__ == "__main__":
    apply_patch()
'''
        
        # Write patch script to container
        with open('/tmp/patch_script.py', 'w') as f:
            f.write(patch_script)
        
        # Copy script to container
        with open('/tmp/patch_script.py', 'rb') as f:
            container.put_archive('/tmp/', f.read())
        
        # Execute the patch
        result = container.exec_run("python /tmp/patch_script.py")
        logger.info(f"Patch execution result: {result.output.decode()}")
        
        if result.exit_code == 0:
            logger.info("Restarting container to apply changes...")
            container.restart()
            logger.info("Container restarted successfully!")
            return True
        else:
            logger.error("Patch execution failed")
            return False
            
    except docker.errors.NotFound:
        logger.error(f"Container {container_name} not found")
        return False
    except Exception as e:
        logger.error(f"Error applying fix: {e}")
        return False

if __name__ == "__main__":
    if fix_retrieval_bug():
        print("✅ Fix applied successfully! Document upload should now work.")
        sys.exit(0)
    else:
        print("❌ Fix failed. Please check the logs.")
        sys.exit(1)