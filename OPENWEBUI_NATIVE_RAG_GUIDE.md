# 📚 Open WebUI Native RAG - Hướng Dẫn Sử Dụng

## 🎯 Tổng quan

Sau khi gỡ bỏ hệ thống pipeline tùy chỉnh, chún<PERSON> ta chuyển sang sử dụng **RAG tích hợp sẵn của Open WebUI**. Đ<PERSON><PERSON> là giải pháp đ<PERSON>, <PERSON>n định và mạnh mẽ hơn.

## ✅ Lợi ích của Native RAG

### 🔧 **Đơn giản hóa**
- ❌ Không cần external RAG API (port 8081)
- ❌ Không cần pipeline containers
- ❌ Không cần custom scripts
- ✅ Tích hợp trực tiếp trong Open WebUI

### 🚀 **Hiệu năng tốt hơn**
- <PERSON><PERSON> lý nhanh hơn (không qua API calls)
- Tìm kiếm semantic hiệu quả
- Hỗ trợ nhiều định dạng file

### 🛠️ **Tính năng mạnh mẽ**
- **Knowledge Collections**: Quản lý tài liệu theo nhóm
- **Hybrid Search**: BM25 + Vector similarity  
- **Multi-format support**: PDF, DOCX, TXT, MD, HTML
- **Real-time processing**: Upload và sử dụng ngay

## 🚀 Hướng dẫn triển khai

### Bước 1: Truy cập Open WebUI
```bash
# Mở browser tại:
http://localhost:3001
```

### Bước 2: Tạo Knowledge Base
1. **Click vào tab "Knowledge" (📚 icon)**
2. **Click "Create Knowledge Base"**
3. **Nhập thông tin:**
   - **Name**: `MobiFone Documents`
   - **Description**: `Tài liệu và quy định nội bộ MobiFone`
   - **Visibility**: `Private` (hoặc `Public` nếu muốn chia sẻ)

### Bước 3: Upload Documents
1. **Click "Upload Files"**
2. **Chọn tài liệu từ thư mục `migrated_docs/`** (đã được extract từ database cũ)
3. **Hoặc upload từ `sample_docs/`** để test
4. **Wait for processing** (thanh tiến trình sẽ hiện)

### Bước 4: Cấu hình RAG Settings
1. **Go to Settings → Knowledge**
2. **Cấu hình:**
   ```
   Chunk Size: 1000
   Chunk Overlap: 200
   Top K: 5
   Similarity Threshold: 0.3
   ```

## 📁 Tài liệu đã migrate

### 📊 **Tóm tắt Migration**
- ✅ **22 documents** đã được extract thành công
- ✅ **8 departments**: TTKTCN, AUTO, TTKT, TTTC, TTNS, TTBV, UNKNOWN
- ✅ **Files saved**: `migrated_docs/` folder

### 📋 **Danh sách documents chính:**

#### 🏢 **TTTC (Tài chính)**
- `Quy định thanh toán công tác phí - 2024.txt`
- `Hướng dẫn mua sắm TSCĐ.txt`

#### 👥 **TTNS (Nhân sự)**  
- `Quy trình xin nghỉ phép và chấm công.txt`
- `quy_che_lam_viec.txt`

#### 💻 **TTKTCN (CNTT)**
- `Hướng dẫn sử dụng VPN.txt`
- `Quy định về BYOD.txt`
- `huong_dan_erp.txt`

#### 🔒 **TTBV (Bảo vệ)**
- `Chính sách bảo mật thông tin.txt`

#### 📊 **TTKT (Kế toán)**
- `Quy trình kế toán doanh thu.txt`

#### 📑 **UNKNOWN (Văn bản đến)**
- `VB_DEN_94_Quy trình QL tài sản cố định...`
- `VB_DEN_93_Quy trình QL kho vật liệu...`
- `VB_DEN_90_QT thanh toán nội bộ...`
- `VB_DEN_92_Quy trình QL và thu hồi công nợ...`
- `VB_DEN_96_TQT Vốn và thẩm tra...`

## 💬 Cách sử dụng RAG

### 🔍 **Automatic RAG Mode**
1. **Create New Chat**
2. **Select Knowledge Base**: Choose "MobiFone Documents"
3. **Ask questions**:
   ```
   "Quy định về công tác phí có gì?"
   "Hướng dẫn sử dụng VPN như thế nào?"
   "Quy trình xin nghỉ phép ra sao?"
   "Chính sách BYOD là gì?"
   ```

### 🎯 **Advanced Search**
- **Department-specific**: "Trong tài liệu TTTC, quy định mua sắm TSCĐ như thế nào?"
- **Multi-document**: "So sánh quy trình thanh toán giữa các phòng ban"
- **Specific procedures**: "Các bước kế toán doanh thu chi tiết"

## 🔧 Advanced Configuration

### ⚙️ **RAG Settings Optimization**

```bash
# Trong Open WebUI Settings → Knowledge:

# For better accuracy:
Chunk Size: 800
Chunk Overlap: 150
Top K: 7
Similarity Threshold: 0.4

# For faster response:
Chunk Size: 600
Chunk Overlap: 100
Top K: 3
Similarity Threshold: 0.3
```

### 📊 **Knowledge Base Management**

#### **Organize by Department**
```
MobiFone Documents/
├── TTTC_Finance/
├── TTNS_HR/
├── TTKTCN_IT/
├── TTBV_Security/
└── Official_Procedures/
```

#### **Tag Documents**
- Add tags khi upload: `#policy #finance #hr #it`
- Easier search và organization

## 🎨 UI Features

### 📱 **Chat Interface**
- **Knowledge indicator**: Hiện tài liệu được sử dụng
- **Source citations**: Link đến original documents
- **Relevance scores**: Thể hiện độ liên quan

### 📚 **Knowledge Management**
- **Upload progress**: Real-time processing status
- **Document preview**: View content trước khi upload
- **Search within KB**: Tìm kiếm trong knowledge base
- **Usage analytics**: Xem documents nào được dùng nhiều

## 🔍 Troubleshooting

### ❓ **Common Issues**

#### **Upload fails**
```bash
✅ Check file formats: PDF, DOCX, TXT, MD, HTML
✅ File size limit: Usually < 50MB per file
✅ Encoding: UTF-8 for Vietnamese text
```

#### **Poor search results**
```bash
✅ Lower similarity threshold (0.3 → 0.2)
✅ Increase Top K (5 → 7)
✅ Add more context in questions
```

#### **Slow processing**
```bash
✅ Break large documents into smaller chunks
✅ Remove duplicate content
✅ Check system resources
```

### 🛠️ **System Check**

```bash
# Verify Open WebUI is running:
curl http://localhost:3001

# Check available models:
curl http://localhost:8010/api/v1/models

# System resources:
docker stats acca-open-webui-1
```

## 📈 Performance Optimization

### 🚀 **Best Practices**

#### **Document Preparation**
- ✅ Clean, well-formatted text
- ✅ Meaningful file names
- ✅ Remove headers/footers
- ✅ Consistent formatting

#### **Query Optimization**
- ✅ Specific questions work better
- ✅ Include context: "Trong quy định TTTC..."
- ✅ Use domain terminology
- ✅ Multiple questions for complex topics

#### **Knowledge Base Structure**
- ✅ Group related documents
- ✅ Update regularly
- ✅ Remove outdated content
- ✅ Monitor usage analytics

## 🎯 Success Metrics

### ✅ **Đã hoàn thành:**
- [x] Gỡ bỏ pipeline tùy chỉnh hoàn toàn
- [x] Stop external RAG API services
- [x] Migrate 22 documents thành công  
- [x] Open WebUI native RAG ready
- [x] Hướng dẫn chi tiết

### 🎊 **Kết quả:**
- **Đơn giản hóa**: Chỉ cần Open WebUI, không external services
- **Ổn định**: Native features, ít lỗi hơn
- **Hiệu năng**: Faster, better UX
- **Maintainable**: Easier updates và troubleshooting

## 🔗 Quick Links

- **Open WebUI**: http://localhost:3001
- **Backend API**: http://localhost:8010
- **Documents**: `./migrated_docs/` và `./sample_docs/`
- **Migration Summary**: `./migration_summary.json`

---

## 🎉 Summary

**Open WebUI Native RAG** đã sẵn sàng sử dụng! Không còn cần pipeline phức tạp, external APIs hay custom integrations. Chỉ cần upload documents và bắt đầu chat với knowledge base của bạn.

**🚀 Ready to go!** Open http://localhost:3001 và bắt đầu sử dụng RAG ngay bây giờ! 