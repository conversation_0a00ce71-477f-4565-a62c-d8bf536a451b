#!/usr/bin/env python3
"""
Optimize memory settings cho pipeline mem0-owui-gemini-3072-fixed
"""

import json
import os

def optimize_memory_settings():
    print("🔧 Optimizing memory settings...")
    
    valves_path = "webui-data/pipelines/mem0-owui-gemini-3072-fixed/valves.json"
    
    try:
        with open(valves_path, 'r') as f:
            valves = json.load(f)
        
        # Optimize memory settings
        valves.update({
            "max_memories_to_inject": 10,  # Tăng từ 5 lên 10
            "memory_relevance_threshold": 0.15,  # Gi<PERSON>m từ 0.2 xuống 0.15 (l<PERSON><PERSON> nhiều memory hơn)
            "auto_store_messages": True,
            "enable_debug_logging": True,
            "memory_window_size": 20,  # Thêm setting mới
            "max_memory_age_days": 30  # Thêm setting mới
        })
        
        with open(valves_path, 'w') as f:
            json.dump(valves, f, indent=2)
        
        print("✅ Updated valves.json with optimized settings:")
        print(f"  - max_memories_to_inject: 5 → 10")
        print(f"  - memory_relevance_threshold: 0.2 → 0.15")
        print(f"  - Added memory_window_size: 20")
        print(f"  - Added max_memory_age_days: 30")
        
        # Copy to container
        os.system(f"docker cp {valves_path} catomanton-webui:/app/backend/data/pipelines/mem0-owui-gemini-3072-fixed/valves.json")
        print("✅ Copied optimized settings to container")
        
        print("\n🔄 Restart OpenWebUI để apply settings:")
        print("docker restart catomanton-webui")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    optimize_memory_settings()