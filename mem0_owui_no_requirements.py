"""
title: AccA Mem0 Integration (No Requirements)
author: Custom for AccA Project
date: 2025-01-08
version: 1.0.1
license: MIT
description: Custom mem0 pipeline for AccA project (assumes dependencies pre-installed)
"""

import os
import json
import asyncio
from typing import ClassVar, List, Optional

# Check if dependencies are available
try:
    from pydantic import BaseModel, Field
    PYDANTIC_AVAILABLE = True
except ImportError:
    print("[AccA-Mem0] Pydantic not found. Using fallback implementation.")
    class BaseModel:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)
    def Field(default=None, description=""):
        return default
    PYDANTIC_AVAILABLE = False

try:
    from mem0 import AsyncMemory
    MEM0_AVAILABLE = True
    print("[AccA-Mem0] mem0ai loaded successfully")
except ImportError:
    print("[AccA-Mem0] mem0ai not found. Memory functions disabled.")
    MEM0_AVAILABLE = False
    class AsyncMemory:
        @classmethod
        async def from_config(cls, config):
            raise ImportError("mem0ai not installed")


class Pipeline:
    class Valves(BaseModel):
        # Pipeline configuration
        pipelines: List[str] = ["*"]
        priority: int = 0
        user_id: str = Field(
            default="default_user", 
            description="Default user ID for memory operations"
        )

        # Qdrant configuration (using existing setup)
        qdrant_host: str = Field(
            default="localhost", 
            description="Qdrant host"
        )
        qdrant_port: str = Field(
            default="6333", 
            description="Qdrant port"
        )
        collection_name: str = Field(
            default="mem0_gemini_768", 
            description="Collection for 768-dim Gemini embeddings"
        )

        # Gemini LLM configuration
        llm_provider: str = Field(default="gemini", description="LLM provider")
        llm_api_key: str = Field(
            default=os.getenv("GEMINI_API_KEY", ""), 
            description="Gemini API key"
        )
        llm_model: str = Field(
            default="gemini-2.5-flash", 
            description="Gemini model"
        )
        llm_temperature: float = Field(default=0.1, description="Temperature")
        llm_max_tokens: int = Field(default=1000, description="Max tokens")

        # Gemini Embedder configuration  
        embedder_provider: str = Field(default="gemini", description="Embedder provider")
        embedder_api_key: str = Field(
            default=os.getenv("GEMINI_API_KEY", ""), 
            description="Gemini API key for embeddings"
        )
        embedder_model: str = Field(
            default="text-embedding-004", 
            description="Embedding model"
        )
        embedder_dims: int = Field(default=768, description="Embedding dimensions")

        # Memory behavior
        max_memories: int = Field(default=5, description="Max memories to inject")
        relevance_threshold: float = Field(
            default=0.6, 
            description="Minimum relevance score (lowered for better recall)"
        )
        auto_store: bool = Field(default=True, description="Auto-store messages")
        debug_mode: bool = Field(default=False, description="Enable debug logging")

    def __init__(self):
        self.type = "filter"
        self.valves = self.Valves()
        self.m = None
        
        # Status check
        self.mem0_enabled = MEM0_AVAILABLE
        if not MEM0_AVAILABLE:
            print("[AccA-Mem0] Running in NO-MEMORY mode (mem0ai not available)")
        else:
            print("[AccA-Mem0] Memory functions enabled")

    def log(self, message: str, level: str = "INFO"):
        """Logging with level control"""
        if self.valves.debug_mode or level == "ERROR":
            print(f"[AccA-Mem0-{level}] {message}")

    async def on_startup(self):
        """Pipeline startup"""
        self.log("Pipeline starting up")

    async def on_shutdown(self):
        """Pipeline shutdown"""
        self.log("Pipeline shutting down")

    async def on_valves_updated(self):
        """Reinitialize when valves change"""
        self.log("Valves updated - reinitializing...")
        if self.mem0_enabled:
            try:
                self.m = await self.init_memory()
                self.log("Memory client reinitialized")
            except Exception as e:
                self.log(f"Failed to reinitialize memory: {e}", "ERROR")

    async def add_to_memory(self, user_id: str, message: dict):
        """Add message to memory"""
        if not self.mem0_enabled or not self.valves.auto_store:
            return
        
        try:
            await self.m.add(user_id=user_id, messages=[message])
            self.log(f"Stored memory for user {user_id}")
        except Exception as e:
            self.log(f"Failed to store memory: {e}", "ERROR")

    async def inlet(self, body: dict, user: Optional[dict] = None) -> dict:
        """Main filter - inject memory context"""
        
        # Skip if no memory available
        if not self.mem0_enabled:
            self.log("Skipping - mem0 not available")
            return body

        # Initialize memory client if needed
        if self.m is None:
            try:
                self.m = await self.init_memory()
                self.log("Memory client initialized")
            except Exception as e:
                self.log(f"Memory init failed: {e}", "ERROR")
                return body

        # Extract info
        messages = body.get("messages", [])
        if not messages:
            return body

        # Skip special requests
        if "metadata" in body and "task" in body.get("metadata", {}):
            return body

        # Get user ID
        current_user_id = self.valves.user_id
        if user and "id" in user:
            current_user_id = user["id"]

        # Find latest messages
        user_message = None
        assistant_message = None
        
        for msg in reversed(messages):
            if msg.get("role") == "user" and not user_message:
                user_message = msg.get("content")
            elif msg.get("role") == "assistant" and not assistant_message:
                assistant_message = msg.get("content")

        if not user_message:
            return body

        try:
            # Search memories
            self.log(f"Searching memories for user {current_user_id}")
            memories = await self.m.search(
                user_id=current_user_id,
                query=user_message,
                limit=self.valves.max_memories
            )

            # Store previous assistant message
            if assistant_message and self.valves.auto_store:
                asyncio.create_task(
                    self.add_to_memory(
                        current_user_id,
                        {"role": "assistant", "content": assistant_message}
                    )
                )

            # Store current user message  
            if self.valves.auto_store:
                asyncio.create_task(
                    self.add_to_memory(
                        current_user_id,
                        {"role": "user", "content": user_message}
                    )
                )

            # Filter relevant memories
            relevant_memories = []
            if memories and "results" in memories:
                for mem in memories["results"]:
                    score = mem.get("score", 1.0)
                    if score >= self.valves.relevance_threshold:
                        relevant_memories.append(mem)

            self.log(f"Found {len(relevant_memories)} relevant memories")

            # Inject memory context
            if relevant_memories:
                context = "\n\n📚 From previous conversations:\n"
                for i, mem in enumerate(relevant_memories, 1):
                    memory_text = mem.get('memory', str(mem))
                    context += f"{i}. {memory_text}\n"
                
                context += "\nUse this context to provide personalized responses.\n"

                # Add to system message
                system_msg = None
                for msg in messages:
                    if msg.get("role") == "system":
                        system_msg = msg
                        break

                if system_msg:
                    system_msg["content"] += context
                else:
                    messages.insert(0, {
                        "role": "system",
                        "content": f"You are a helpful AI with memory of past conversations.{context}"
                    })

                body["messages"] = messages
                self.log(f"Injected {len(relevant_memories)} memories")

        except Exception as e:
            self.log(f"Memory processing error: {e}", "ERROR")

        return body

    async def init_memory(self):
        """Initialize mem0 client"""
        if not self.mem0_enabled:
            raise ImportError("mem0ai not available")

        config = {
            "vector_store": {
                "provider": "qdrant",
                "config": {
                    "host": self.valves.qdrant_host,
                    "port": int(self.valves.qdrant_port),
                    "collection_name": self.valves.collection_name,
                },
            },
            "llm": {
                "provider": self.valves.llm_provider,
                "config": {
                    "api_key": self.valves.llm_api_key,
                    "model": self.valves.llm_model,
                    "temperature": self.valves.llm_temperature,
                    "max_tokens": self.valves.llm_max_tokens,
                },
            },
            "embedder": {
                "provider": self.valves.embedder_provider,
                "config": {
                    "api_key": self.valves.embedder_api_key,
                    "model": self.valves.embedder_model,
                    "embedding_dims": self.valves.embedder_dims,
                },
            },
        }

        self.log("Creating AsyncMemory client...")
        return await AsyncMemory.from_config(config) 