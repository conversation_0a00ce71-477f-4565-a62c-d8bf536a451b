#!/usr/bin/env python3
import socket
import ssl
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
import requests
import urllib.parse
import sys

class HTTPSProxyHandler(BaseHTTPRequestHandler):
    def log_message(self, format, *args):
        pass  # Suppress log messages
    
    def do_GET(self):
        try:
            # Forward the request to Open WebUI on localhost:3000
            target_url = f"http://localhost:3000{self.path}"
            
            # Forward headers
            headers = {}
            for header, value in self.headers.items():
                if header.lower() not in ['host', 'connection']:
                    headers[header] = value
            
            # Make request to Open WebUI
            response = requests.get(target_url, headers=headers, timeout=30)
            
            # Send response back
            self.send_response(response.status_code)
            
            # Forward response headers
            for header, value in response.headers.items():
                if header.lower() not in ['connection', 'transfer-encoding']:
                    self.send_header(header, value)
            
            # Add security headers for WebView
            self.send_header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
            self.send_header('X-Frame-Options', 'SAMEORIGIN')
            self.send_header('X-Content-Type-Options', 'nosniff')
            self.send_header('X-XSS-Protection', '1; mode=block')
            
            self.end_headers()
            
            # Send content
            self.wfile.write(response.content)
            
        except Exception as e:
            print(f"Error proxying request: {e}")
            self.send_error(502, "Bad Gateway")
    
    def do_POST(self):
        try:
            # Get request body
            content_length = int(self.headers.get('content-length', 0))
            body = self.rfile.read(content_length)
            
            # Forward the request to Open WebUI on localhost:3000
            target_url = f"http://localhost:3000{self.path}"
            
            # Forward headers
            headers = {}
            for header, value in self.headers.items():
                if header.lower() not in ['host', 'connection']:
                    headers[header] = value
            
            # Make request to Open WebUI
            response = requests.post(target_url, headers=headers, data=body, timeout=30)
            
            # Send response back
            self.send_response(response.status_code)
            
            # Forward response headers
            for header, value in response.headers.items():
                if header.lower() not in ['connection', 'transfer-encoding']:
                    self.send_header(header, value)
            
            # Add security headers for WebView
            self.send_header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
            self.send_header('X-Frame-Options', 'SAMEORIGIN')
            self.send_header('X-Content-Type-Options', 'nosniff')
            self.send_header('X-XSS-Protection', '1; mode=block')
            
            self.end_headers()
            
            # Send content
            self.wfile.write(response.content)
            
        except Exception as e:
            print(f"Error proxying POST request: {e}")
            self.send_error(502, "Bad Gateway")

def start_https_server():
    print("🔒 Starting HTTPS Proxy Server on port 3001...")
    print("📍 URL: https://**************:3001")
    print("🔗 Proxying to: http://localhost:3000 (Open WebUI)")
    print("🌐 WebView Compatible: YES")
    print("")
    
    try:
        # Create HTTP server
        httpd = HTTPServer(('0.0.0.0', 3001), HTTPSProxyHandler)
        
        # Wrap with SSL
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        context.load_cert_chain('/etc/nginx/ssl/openwebui.crt', '/etc/nginx/ssl/openwebui.key')
        
        httpd.socket = context.wrap_socket(httpd.socket, server_side=True)
        
        print("✅ HTTPS Proxy Server started successfully!")
        print("🔗 Access: https://**************:3001")
        print("")
        
        httpd.serve_forever()
        
    except PermissionError:
        print("❌ Permission denied. Please run with sudo:")
        print("sudo python3 https-redirect-server.py")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    start_https_server() 