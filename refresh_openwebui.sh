#!/bin/bash

echo "🔄 REFRESH OPEN WEBUI MODELS"
echo "============================"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_status "1. Checking backend models..."
echo "Available models:"
curl -s "http://localhost:8010/api/v1/models" | jq -r '.data[] | "  - " + .id'

print_status "2. Restarting Open WebUI to refresh cache..."
sudo docker restart acca-open-webui-1

print_status "3. Waiting for Open WebUI to start..."
sleep 15

print_status "4. Checking Open WebUI health..."
if curl -s "http://localhost:3001" | grep -q "Open WebUI"; then
    print_success "Open WebUI is running!"
    echo ""
    echo "🌐 Open your browser and go to: http://**************:3001"
    echo ""
    echo "📋 Available models should include:"
    echo "   - gemma-3n-e4b-litert (Gemma 3n E4B)"
    echo "   - tflite-enhanced-gpt-3.5-turbo (TensorFlow Lite Enhanced)"
    echo "   - tflite-enhanced-gemini-pro (TensorFlow Lite Enhanced)"
    echo ""
    echo "💡 If models still don't appear:"
    echo "   1. Click model dropdown in Open WebUI"
    echo "   2. Try refresh browser (Ctrl+F5)"
    echo "   3. Check browser DevTools Network tab for /api/v1/models calls"
else
    print_warning "Open WebUI might still be starting..."
fi

print_success "Refresh completed!" 