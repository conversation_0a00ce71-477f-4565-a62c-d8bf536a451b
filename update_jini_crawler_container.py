#!/usr/bin/env python3
"""
Update jina-crawler-mcp container with new jini_crawler code
"""

import subprocess
import time
import requests
import os
import shutil

def check_container_status():
    """Check if jina-crawler-mcp container is running"""
    try:
        result = subprocess.run(
            ["docker", "ps", "--filter", "name=jina-crawler-mcp", "--format", "{{.Status}}"],
            capture_output=True, text=True
        )
        return "Up" in result.stdout
    except:
        return False

def find_new_jini_server():
    """Find the new jini_crawler server code"""
    
    # Common locations to search
    search_paths = [
        "/home/<USER>/jina-crawler",
        "/home/<USER>/AccA/AccA/jina-crawler", 
        "/home/<USER>/AccA/AccA/jini-crawler",
        "/home/<USER>/jini-crawler"
    ]
    
    for path in search_paths:
        if os.path.exists(path):
            # Look for main server file
            for file in ["main.py", "api_server.py", "server.py", "app.py"]:
                full_path = os.path.join(path, file)
                if os.path.exists(full_path):
                    print(f"✅ Found server at: {full_path}")
                    return path
    
    print("❌ Could not find new jini_crawler server code")
    return None

def backup_old_code():
    """Backup old code from container"""
    
    print("📦 Backing up old code...")
    
    # Create backup directory
    backup_dir = f"jini_crawler_backup_{int(time.time())}"
    os.makedirs(backup_dir, exist_ok=True)
    
    # Copy current code from container
    subprocess.run([
        "docker", "cp", "jina-crawler-mcp:/app/.", backup_dir
    ], capture_output=True)
    
    print(f"✅ Backup created at: {backup_dir}")
    return backup_dir

def update_container_code(new_server_path):
    """Update container with new code"""
    
    print("🔄 Updating container with new code...")
    
    # Copy new code to container
    subprocess.run([
        "docker", "cp", f"{new_server_path}/.", "jina-crawler-mcp:/app/"
    ], capture_output=True)
    
    print("✅ New code copied to container")

def restart_container():
    """Restart the container"""
    
    print("🔄 Restarting container...")
    
    result = subprocess.run(
        ["docker", "restart", "jina-crawler-mcp"],
        capture_output=True, text=True
    )
    
    if result.returncode == 0:
        print("✅ Container restarted successfully")
        return True
    else:
        print(f"❌ Failed to restart container: {result.stderr}")
        return False

def wait_for_container():
    """Wait for container to be ready"""
    
    print("⏳ Waiting for container to be ready...")
    
    for i in range(30):  # Wait up to 30 seconds
        try:
            # Try to connect to container's internal service
            result = subprocess.run([
                "docker", "exec", "jina-crawler-mcp", 
                "curl", "-s", "http://localhost:8009/health"
            ], capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                print("✅ Container is ready!")
                return True
                
        except subprocess.TimeoutExpired:
            pass
            
        time.sleep(1)
        print(f"   Waiting... ({i+1}/30)")
    
    print("❌ Container did not become ready in time")
    return False

def test_new_endpoints():
    """Test if new endpoints are available"""
    
    print("🧪 Testing new endpoints...")
    
    # Test through OpenWebUI's expected URL
    test_urls = [
        "http://localhost:8009/openapi.json",
        "http://localhost:8009/health"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {url} - OK")
            else:
                print(f"❌ {url} - Status {response.status_code}")
        except Exception as e:
            print(f"❌ {url} - Error: {e}")

def create_simple_update_script():
    """Create a simple script to copy localhost server to container"""
    
    script_content = '''#!/bin/bash

echo "🔄 Updating jina-crawler-mcp container..."

# Find the process running on localhost:8009
PID=$(lsof -ti:8009 | head -1)
if [ -z "$PID" ]; then
    echo "❌ No process found on port 8009"
    exit 1
fi

# Get the working directory of that process
WORKDIR=$(pwdx $PID 2>/dev/null | cut -d: -f2 | xargs)
if [ -z "$WORKDIR" ]; then
    echo "❌ Could not find working directory"
    exit 1
fi

echo "📁 Found server at: $WORKDIR"

# Backup current container code
echo "📦 Creating backup..."
docker cp jina-crawler-mcp:/app ./jina_backup_$(date +%s)

# Copy new code to container
echo "🔄 Copying new code..."
docker cp "$WORKDIR/." jina-crawler-mcp:/app/

# Restart container
echo "🔄 Restarting container..."
docker restart jina-crawler-mcp

# Wait for container
echo "⏳ Waiting for container..."
sleep 10

# Test
echo "🧪 Testing..."
docker exec jina-crawler-mcp curl -s http://localhost:8009/health || echo "❌ Health check failed"

echo "✅ Update completed!"
'''
    
    with open("quick_update_jini.sh", "w") as f:
        f.write(script_content)
    
    os.chmod("quick_update_jini.sh", 0o755)
    print("✅ Created quick_update_jini.sh script")

def main():
    print("🚀 Updating jina-crawler-mcp container with new code...")
    
    # Check if container is running
    if not check_container_status():
        print("❌ jina-crawler-mcp container is not running")
        return
    
    print("✅ Container is running")
    
    # Try to find new server code
    new_server_path = find_new_jini_server()
    
    if not new_server_path:
        print("\n💡 Alternative approach:")
        print("Since we can't automatically find the new server code,")
        print("let's create a script to copy from the running localhost:8009 process")
        
        create_simple_update_script()
        
        print("\n📋 Manual steps:")
        print("1. Run: ./quick_update_jini.sh")
        print("2. Or manually copy your new jini_crawler code to container:")
        print("   docker cp /path/to/new/code/. jina-crawler-mcp:/app/")
        print("   docker restart jina-crawler-mcp")
        
        return
    
    # Backup old code
    backup_dir = backup_old_code()
    
    # Update container
    update_container_code(new_server_path)
    
    # Restart container
    if restart_container():
        
        # Wait for container to be ready
        if wait_for_container():
            
            # Test new endpoints
            test_new_endpoints()
            
            print("\n🎉 Update completed successfully!")
            print("📋 Next steps:")
            print("1. Check OpenWebUI at http://localhost:3000")
            print("2. Test jini_crawler tools in chat")
            print("3. If issues occur, restore from backup:")
            print(f"   docker cp {backup_dir}/. jina-crawler-mcp:/app/")
            print("   docker restart jina-crawler-mcp")
        else:
            print("❌ Container failed to start properly")
    else:
        print("❌ Failed to restart container")

if __name__ == "__main__":
    main()
