#!/usr/bin/env python3
"""
Re-embed Memories Script: Convert placeholder vectors to real 768D embeddings
"""

import asyncio
import requests
import json
import time
from typing import List, Dict, Any

try:
    from mem0 import AsyncMemory
    MEM0_AVAILABLE = True
except ImportError:
    print("❌ mem0 not available")
    MEM0_AVAILABLE = False

# Configuration
QDRANT_HOST = "qdrant"
QDRANT_PORT = 6333
GEMINI_API_KEY = "AIzaSyDHsg-8CCSEBUO9N3V0vaRsCSNq2iS4oec"
COLLECTION_NAME = "mem0_gemini_gemi_768"

class MemoryReembedder:
    def __init__(self):
        self.memory_client = None
        
    async def setup_memory_client(self):
        """Setup memory client for re-embedding"""
        if not MEM0_AVAILABLE:
            print("❌ mem0 not available")
            return False
            
        try:
            config = {
                "vector_store": {
                    "provider": "qdrant",
                    "config": {
                        "host": QDRANT_HOST,
                        "port": QDRANT_PORT,
                        "collection_name": COLLECTION_NAME,
                    },
                },
                "llm": {
                    "provider": "gemini",
                    "config": {
                        "api_key": GEMINI_API_KEY,
                        "model": "gemini-2.5-flash",
                        "temperature": 0.1,
                        "max_tokens": 1000,
                    },
                },
                "embedder": {
                    "provider": "gemini",
                    "config": {
                        "api_key": GEMINI_API_KEY,
                        "model": "text-embedding-004",
                        "embedding_dims": 768,
                    },
                },
            }
            
            self.memory_client = await AsyncMemory.from_config(config)
            print(f"✅ Memory client initialized for re-embedding")
            return True
            
        except Exception as e:
            print(f"❌ Failed to setup memory client: {e}")
            return False
    
    def get_memories_needing_reembedding(self, limit: int = 100, offset: str = None) -> tuple:
        """Get memories that need re-embedding"""
        try:
            payload = {
                "limit": limit,
                "with_payload": True,
                "with_vector": False,
                "filter": {
                    "must": [
                        {
                            "key": "needs_reembedding",
                            "match": {"value": True}
                        }
                    ]
                }
            }
            
            if offset:
                payload["offset"] = offset
            
            response = requests.post(
                f"http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{COLLECTION_NAME}/points/scroll",
                json=payload
            )
            
            if response.status_code != 200:
                print(f"❌ Error getting memories: {response.status_code}")
                return [], None
            
            data = response.json()
            points = data["result"]["points"]
            next_offset = data["result"].get("next_page_offset")
            
            memories = []
            for point in points:
                payload_data = point["payload"]
                content = payload_data.get("content", "")
                
                if content:
                    memory_data = {
                        "id": point["id"],
                        "content": content,
                        "user_id": payload_data.get("user_id", "default_user"),
                        "session_id": payload_data.get("session_id", ""),
                        "timestamp": payload_data.get("timestamp", "")
                    }
                    memories.append(memory_data)
            
            return memories, next_offset
            
        except Exception as e:
            print(f"❌ Error getting memories: {e}")
            return [], None
    
    async def reembed_memory_batch(self, memories: List[Dict[str, Any]]) -> int:
        """Re-embed a batch of memories"""
        if not memories:
            return 0
            
        print(f"🔄 Re-embedding {len(memories)} memories...")
        reembedded_count = 0
        
        for i, memory in enumerate(memories):
            try:
                # Delete old memory
                await self.memory_client.delete(memory_id=memory["id"])
                
                # Add memory again (will create new embedding)
                await self.memory_client.add(
                    messages=memory["content"],
                    user_id=memory["user_id"],
                    metadata={
                        "session_id": memory["session_id"],
                        "timestamp": memory["timestamp"],
                        "reembedded": True,
                        "original_id": memory["id"]
                    }
                )
                
                reembedded_count += 1
                
                # Rate limiting
                if i < len(memories) - 1:
                    await asyncio.sleep(0.5)  # 500ms delay
                
                if (i + 1) % 10 == 0:
                    print(f"   ✅ Re-embedded {i + 1}/{len(memories)} memories")
                    
            except Exception as e:
                if "429" in str(e) or "quota" in str(e).lower():
                    print(f"   ⏳ Rate limit hit, waiting 10 seconds...")
                    await asyncio.sleep(10)
                    # Retry once
                    try:
                        await self.memory_client.delete(memory_id=memory["id"])
                        await self.memory_client.add(
                            messages=memory["content"],
                            user_id=memory["user_id"],
                            metadata={
                                "session_id": memory["session_id"],
                                "timestamp": memory["timestamp"],
                                "reembedded": True,
                                "original_id": memory["id"]
                            }
                        )
                        reembedded_count += 1
                    except Exception as retry_e:
                        print(f"   ❌ Retry failed for memory {memory['id']}: {retry_e}")
                else:
                    print(f"   ❌ Error re-embedding memory {memory['id']}: {e}")
                continue
        
        print(f"✅ Successfully re-embedded {reembedded_count}/{len(memories)} memories")
        return reembedded_count
    
    def get_total_memories_needing_reembedding(self) -> int:
        """Get total count of memories needing re-embedding"""
        try:
            response = requests.post(
                f"http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{COLLECTION_NAME}/points/count",
                json={
                    "filter": {
                        "must": [
                            {
                                "key": "needs_reembedding",
                                "match": {"value": True}
                            }
                        ]
                    }
                }
            )
            
            if response.status_code == 200:
                return response.json()["result"]["count"]
            else:
                return 0
                
        except Exception as e:
            print(f"❌ Error getting count: {e}")
            return 0
    
    async def reembed_all_memories(self):
        """Re-embed all memories that need it"""
        print(f"🚀 Starting re-embedding process for collection: {COLLECTION_NAME}")
        
        # Setup memory client
        if not await self.setup_memory_client():
            return False
        
        # Get total count
        total_count = self.get_total_memories_needing_reembedding()
        print(f"📊 Total memories needing re-embedding: {total_count}")
        
        if total_count == 0:
            print("✅ No memories need re-embedding!")
            return True
        
        # Process in batches
        total_reembedded = 0
        offset = None
        batch_size = 10  # Small batches for rate limiting
        
        while True:
            print(f"\n📤 Getting batch (offset: {offset})...")
            memories, next_offset = self.get_memories_needing_reembedding(batch_size, offset)
            
            if not memories:
                break
            
            # Re-embed batch
            reembedded = await self.reembed_memory_batch(memories)
            total_reembedded += reembedded
            
            print(f"📊 Progress: {total_reembedded}/{total_count} memories re-embedded")
            
            if not next_offset:
                break
                
            offset = next_offset
            
            # Wait between batches
            print(f"⏳ Waiting 3 seconds before next batch...")
            await asyncio.sleep(3)
        
        print(f"\n🎉 Re-embedding completed!")
        print(f"📊 Total memories re-embedded: {total_reembedded}")
        
        return True

async def main():
    """Main re-embedding process"""
    reembedder = MemoryReembedder()
    success = await reembedder.reembed_all_memories()
    
    if success:
        print(f"\n✅ Re-embedding process completed successfully!")
        print(f"💡 All memories now have proper 768D embeddings")
        print(f"🚀 Memory search should work correctly now")
    else:
        print(f"\n❌ Re-embedding process failed")

if __name__ == "__main__":
    asyncio.run(main())
