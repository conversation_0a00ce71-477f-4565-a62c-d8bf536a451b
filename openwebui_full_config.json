{"version": "0.6.21", "general": {"default_locale": "en-US", "default_models": ["gemini-2.5-flash", "claude-3-5-sonnet"], "default_prompt_suggestions": [{"title": "Analyze Data", "content": "Help me analyze this data using pandas tools"}, {"title": "Web Search", "content": "Search for the latest information about"}, {"title": "Vietnamese Translation", "content": "Translate this text to Vietnamese"}, {"title": "Weather Info", "content": "What's the weather like in Ho Chi Minh City?"}]}, "ui": {"default_theme": "dark", "chat_bubble": true, "chat_direction": "LTR", "fluid_container": false, "landing_page_mode": "chat"}, "models": {"gemini-2.5-flash": {"name": "Gemini 2.5 Flash", "base_url": "http://localhost:11434", "api_key": "", "parameters": {"temperature": 0.7, "top_p": 0.9, "max_tokens": 4096}}, "claude-3-5-sonnet": {"name": "Claude 3.5 Sonnet", "base_url": "http://localhost:11434", "api_key": "", "parameters": {"temperature": 0.7, "top_p": 0.9, "max_tokens": 4096}}}, "audio": {"stt": {"engine": "whisper", "model": "base", "language": "auto"}, "tts": {"engine": "openai", "voice": "alloy", "model": "tts-1"}}, "images": {"generation": {"engine": "automatic1111", "enabled": false, "size": "512x512"}}, "rag": {"enabled": true, "embedding_engine": "sentence-transformers", "embedding_model": "all-MiniLM-L6-v2", "chunk_size": 1500, "chunk_overlap": 100, "vector_db": "chroma", "reranking_model": "", "top_k": 5, "relevance_threshold": 0.0}, "web": {"search": {"enabled": true, "engine": "brave", "searxng_query_url": "", "google_pse_api_key": "", "google_pse_engine_id": "", "brave_search_api_key": "BSAWh8paqNo8JFJaBmvd6WX7CqT2_Sk", "serpstack_api_key": "", "serper_api_key": "", "serply_api_key": "", "tavily_api_key": ""}}, "oauth": {"providers": {}}, "webhook": {"url": ""}, "mcpServers": {"mcpo_complete_proxy_8000": {"transport": {"type": "http", "url": "http://mcpo-complete-proxy-8000:8000"}, "description": "MCPO Complete Proxy - 12 MCP tools with namespace separation"}, "jina_crawler_8002": {"transport": {"type": "http", "url": "http://jina-crawler-mcp-proxy-8002:8002"}, "description": "<PERSON><PERSON> - Advanced web crawling with AI processing"}, "pandas_unified_8004": {"transport": {"type": "http", "url": "http://pandas-unified-server:8004"}, "description": "Pandas Server - Data analysis tools via HTTP"}, "document_processing": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/document_processing/server.py"], "env": {}, "description": "Document processing and analysis tools"}, "vietnamese_language": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/vietnamese_language/server.py"], "env": {}, "description": "Vietnamese language processing and translation"}, "web_automation": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/web_automation/server_playwright.py"], "env": {}, "description": "Web browser automation with Playwright"}, "time_utilities": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/time_utilities/server.py"], "env": {}, "description": "Time and timezone utilities"}, "weather_service": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/weather_service/server.py"], "env": {}, "description": "Weather information service"}, "filesystem": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/filesystem/server.py"], "env": {}, "description": "File system operations"}, "wikipedia": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/wikipedia/server.py"], "env": {}, "description": "Wikipedia search and information retrieval"}, "sqlite": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/sqlite/server.py"], "env": {}, "description": "SQLite database operations"}, "github": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/github/server.py"], "env": {}, "description": "GitHub repository search and operations"}, "brave_search": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/brave_search/server.py"], "env": {"BRAVE_API_KEY": "BSAWh8paqNo8JFJaBmvd6WX7CqT2_Sk"}, "description": "Brave Search API for web search"}, "gemini_search_engine": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/gemini_search_engine/server_with_grounding.py"], "env": {"GEMINI_API_KEY": "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM"}, "description": "Gemini-powered search engine"}, "gemini_cli_tools": {"command": "/usr/bin/python3", "args": ["/app/mcp-servers/gemini_cli_tools/server.py"], "env": {"GEMINI_API_KEY": "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM", "GEMINI_UNRESTRICTED_MODE": "false", "GEMINI_RBAC_ENABLED": "true"}, "description": "Gemini CLI tools with RBAC"}}, "environment": {"OPENAI_API_KEY": "", "ANTHROPIC_API_KEY": "", "GEMINI_API_KEY": "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM", "BRAVE_API_KEY": "BSAWh8paqNo8JFJaBmvd6WX7CqT2_Sk", "EMBEDDING_ENGINE": "sentence-transformers", "EMBEDDING_MODEL": "all-MiniLM-L6-v2", "RAG_EMBEDDING_ENGINE": "sentence-transformers", "RAG_EMBEDDING_MODEL": "all-MiniLM-L6-v2", "ENABLE_RAG_WEB_SEARCH": "true", "ENABLE_RAG_LOCAL_WEB_FETCH": "true", "RAG_WEB_SEARCH_ENGINE": "brave", "ENABLE_WEBSOCKET_SUPPORT": "true", "ENABLE_COMMUNITY_SHARING": "false", "WEBUI_AUTH": "false", "WEBUI_SECRET_KEY": "", "DEFAULT_USER_ROLE": "user", "ENABLE_SIGNUP": "true", "ENABLE_LOGIN_FORM": "true", "ENABLE_OAUTH_SIGNUP": "false", "OAUTH_MERGE_ACCOUNTS_BY_EMAIL": "false"}, "connections": {"ollama": {"base_url": "http://localhost:11434", "api_key": "", "verify_ssl": true}, "openai": {"base_url": "https://api.openai.com/v1", "api_key": "", "verify_ssl": true}, "anthropic": {"base_url": "https://api.anthropic.com", "api_key": "", "verify_ssl": true}, "gemini": {"base_url": "https://generativelanguage.googleapis.com/v1beta", "api_key": "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM", "verify_ssl": true}}, "admin": {"default_user_role": "user", "enable_signup": true, "enable_community_sharing": false, "enable_message_rating": true, "enable_model_filter": false, "model_filter_list": [], "webhook_url": ""}}