# 🚀 MCP OpenAPI Proxy (MCPO) - Complete Guide

## 📋 Tổng quan

**MCP OpenAPI Proxy (MCPO)** là giải pháp hoàn chỉnh để expose MCP tools dưới dạng HTTP API endpoints, cho phép Open WebUI và các LLM sử dụng tools một cách hiệu quả.

## 🎯 Tại sao cần MCPO?

### ❌ Vấn đề với MCP trực tiếp:
- Open WebUI không thể gọi MCP protocol trực tiếp
- LLM cần HTTP endpoints để sử dụng tools
- MCP servers thường chạy riêng biệt, khó integrate
- Không có standardized API format

### ✅ Giải pháp MCPO:
- Chuyển đổi MCP calls thành HTTP API calls
- Cung cấp OpenAPI specifications chuẩn
- Container-based deployment dễ quản lý
- Tương thích với mọi LLM trong Open WebUI
- Auto-discovery và tool registration

## 🏗️ Kiến trúc MC<PERSON>

```mermaid
graph TB
    subgraph "Open WebUI"
        A[LLM Chat Interface]
        B[Tool Registry]
    end
    
    subgraph "MCPO Container"
        C[FastAPI Server]
        D[OpenAPI Specs]
        E[Tool Endpoints]
        F[MCP Integration Layer]
    end
    
    subgraph "Real Implementation"
        G[Jina Crawler]
        H[TLS Bypass]
        I[Gemini AI]
        J[Search Engine]
    end
    
    A --> B
    B --> C
    C --> D
    C --> E
    E --> F
    F --> G
    F --> H
    F --> I
    F --> J
```

## 🛠️ Thành phần cốt lõi

### 1. **FastAPI Server với CORS**
```python
app = FastAPI(
    title="MCP OpenAPI Proxy",
    description="Automatically generated API from MCP Tool Schemas",
    version="1.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### 2. **OpenAPI 3.1.0 Specification**
```python
{
    "openapi": "3.1.0",  # QUAN TRỌNG: Phải là 3.1.0
    "info": {
        "title": "tool_name",
        "description": "Khi nào LLM nên sử dụng tool này",
        "version": "1.0.0"
    },
    "paths": {
        "/function_name": {
            "post": {
                "operationId": "function_name",  # QUAN TRỌNG!
                "summary": "Tóm tắt ngắn gọn",
                "description": "Hướng dẫn chi tiết khi nào sử dụng",
                # ... requestBody và responses
            }
        }
    }
}
```

### 3. **Tool Endpoints Pattern**
```python
@app.post("/tool_name/function_name")
async def function_name(request: RequestModel):
    if not component_available:
        return {"success": False, "error": "Component not available"}
    
    try:
        result = await real_implementation(request)
        return {
            "success": True,
            "result": result,
            "processing_time": time_taken
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }
```

## 🎨 Best Practices

### 1. **Naming Conventions**
- Container: `tool-name-mcpo-PORT`
- Endpoints: `/tool_name/function_name`
- operationId: `function_name`
- Request models: `FunctionNameRequest`

### 2. **Error Handling Pattern**
```python
{
    "success": boolean,
    "result": any,           # Khi success = True
    "error": string,         # Khi success = False
    "processing_time": float,
    "metadata": object       # Optional
}
```

### 3. **Description Guidelines**
- **Tool description**: Khi nào LLM nên sử dụng tool
- **Function description**: Cụ thể function làm gì
- **Parameter description**: Ý nghĩa và format của parameter
- **Examples**: Giá trị mẫu thực tế

### 4. **Container Requirements**
- Dockerfile với health check
- Docker compose với shared networks
- Environment variables cho configuration
- Restart policy: `unless-stopped`

## 📊 Ví dụ thực tế: Jina Crawler MCPO

### **Container**: `jina-crawler-mcp-proxy-8002`
### **URL**: `http://jina-crawler-mcp-proxy-8002:8002/jina_crawler/openapi.json`

### **Tools có sẵn:**
1. **✅ crawl_url** - Crawl website với TLS bypass + Gemini AI
2. **✅ search_web** - AI-powered web search
3. **✅ crawl_batch** - Batch crawl multiple websites
4. **✅ bypass_paywall** - Bypass paywall content
5. **✅ ai_search** - Advanced AI search với query refinement

### **Real Implementation:**
- **TLS Bypass**: Vượt qua mọi protection
- **Gemini AI**: Content processing và analysis
- **Multi-method**: HTTP, Selenium, Playwright
- **Error handling**: Graceful fallbacks

## 🚀 Deployment Workflow

### **1. Development**
```bash
# Tạo project từ template
./create_mcpo_project.sh my_tool 8003

# Customize implementation
vim my_tool_mcpo_server.py
```

### **2. Build & Deploy**
```bash
# Build container
./manage_my_tool_mcpo.sh build

# Start container
./manage_my_tool_mcpo.sh start

# Test connectivity
./manage_my_tool_mcpo.sh test
```

### **3. Integration với Open WebUI**
1. **Add tool URL**: `http://my-tool-mcpo-8003:8003/my_tool/openapi.json`
2. **Verify connection**: Check admin panel
3. **Test với LLM**: "Use my_tool to process this request"

## 🧪 Testing & Verification

### **Health Check**
```bash
curl http://localhost:8002/health
```

### **OpenAPI Spec**
```bash
curl http://localhost:8002/tool_name/openapi.json | jq
```

### **Tool Function**
```bash
curl -X POST "http://localhost:8002/tool_name/function_name" \
  -H "Content-Type: application/json" \
  -d '{"param": "value"}'
```

### **Container Connectivity**
```bash
docker exec open-webui-container curl http://tool-mcpo:8002/health
```

## 📚 Templates & Generators

### **MCPO Project Generator**
```bash
./create_mcpo_project.sh tool_name [port]
```

**Tạo tự động:**
- `tool_name_mcpo_server.py` - Server code
- `Dockerfile.tool_name-mcpo` - Container definition
- `docker-compose.tool_name-mcpo.yml` - Deployment config
- `manage_tool_name_mcpo.sh` - Management script
- `README_tool_name_mcpo.md` - Documentation

### **Base Template**: `mcpo_template.py`
- FastAPI setup với CORS
- OpenAPI specification template
- Request/response models
- Error handling patterns
- Health check endpoints

## 🔧 Troubleshooting

### **Common Issues:**

#### **1. LLM không thấy tool**
- ✅ Kiểm tra OpenAPI spec format
- ✅ Verify operationId có đúng không
- ✅ Check descriptions rõ ràng chưa

#### **2. Container không kết nối**
- ✅ Verify Docker networks
- ✅ Check port mapping
- ✅ Test từ Open WebUI container

#### **3. Tool không hoạt động**
- ✅ Check real implementation
- ✅ Verify error handling
- ✅ Test endpoints trực tiếp

#### **4. CORS errors**
- ✅ Ensure CORS middleware configured
- ✅ Check allow_origins settings

### **Debug Commands:**
```bash
# Check containers
docker ps | grep mcpo

# Test connectivity
docker exec open-webui-mcpo curl http://tool-mcpo:8002/health

# View logs
docker logs tool-mcpo-8002

# Check OpenAPI spec
curl http://localhost:8002/tool_name/openapi.json | jq '.paths'
```

## 🎯 Success Metrics

### **✅ MCPO hoạt động tốt khi:**
1. **Health endpoint** returns 200
2. **OpenAPI spec** valid JSON
3. **Tool endpoints** return expected responses
4. **Open WebUI** sees tools in admin panel
5. **LLM** automatically uses tools when appropriate
6. **Real implementation** processes requests correctly

## 🌟 Advanced Features

### **1. Multi-tool MCPO**
Một container có thể expose nhiều tools:
```python
# /tool1/function1, /tool1/function2
# /tool2/function1, /tool2/function2
```

### **2. Fallback Mechanisms**
```python
try:
    result = await advanced_method()
except:
    result = await fallback_method()
```

### **3. Caching & Performance**
```python
@lru_cache(maxsize=100)
async def cached_function():
    pass
```

### **4. Rate Limiting**
```python
from slowapi import Limiter
limiter = Limiter(key_func=get_remote_address)

@app.post("/endpoint")
@limiter.limit("10/minute")
async def limited_endpoint():
    pass
```

## 🎉 Kết luận

**MCPO pattern** cung cấp:
- ✅ **Standardized** tool integration
- ✅ **Scalable** architecture
- ✅ **Easy deployment** với containers
- ✅ **Real implementation** support
- ✅ **Error handling** và fallbacks
- ✅ **Auto-discovery** cho Open WebUI
- ✅ **Production-ready** solutions

**Với MCPO, bạn có thể tạo và deploy bất kỳ tool nào cho Open WebUI một cách dễ dàng và hiệu quả!**
