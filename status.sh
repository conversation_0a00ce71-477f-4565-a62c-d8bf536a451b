#!/bin/bash

echo "📊 Native Stack Status - Qwen2.5-Coder Agents"
echo "==============================================="

echo ""
echo "🤖 AI Models:"
if pgrep -f "qwen2.5-coder.*11435" >/dev/null; then
    echo "   ✅ Qwen2.5-Coder-7B-Instruct - Port 11435 (Active)"
else
    echo "   ❌ Qwen2.5-Coder - Not running"
fi

if pgrep -f "gemma.*11434" >/dev/null; then
    echo "   ✅ Gemma 3 4B - Port 11434 (Active)"
else
    echo "   ❌ Gemma 3 4B - Not running"
fi

echo ""
echo "🌐 Web Interface:"
if pgrep -f "open-webui" >/dev/null; then
    echo "   ✅ Open WebUI Native - Port 3000 (Active)"
else
    echo "   ❌ Open WebUI - Not running"
fi

echo ""
echo "🔗 API Endpoints:"
echo "   🤖 Qwen2.5-Coder: http://localhost:11435/v1"
echo "   🤖 Gemma 3 4B:    http://localhost:11434/v1"
echo "   🌐 Open WebUI:    http://localhost:3000"

echo ""
echo "🧪 API Tests:"
if curl -s http://localhost:11435/v1/models >/dev/null 2>&1; then
    echo "   ✅ Qwen API (11435) - Responding"
else
    echo "   ❌ Qwen API (11435) - Down"
fi

if curl -s http://localhost:11434/v1/models >/dev/null 2>&1; then
    echo "   ✅ Gemma API (11434) - Responding"
else
    echo "   ❌ Gemma API (11434) - Down"
fi

if curl -s http://localhost:3000 >/dev/null 2>&1; then
    echo "   ✅ WebUI (3000) - Responding"
else
    echo "   ❌ WebUI (3000) - Down"
fi

echo ""
echo "🔧 Agent Configuration:"
echo "   • Cline/Claude Dev: http://localhost:11435/v1"
echo "   • Open WebUI Setup:"
echo "     1. Go to http://localhost:3000"
echo "     2. Admin Settings → Connections → OpenAI"
echo "     3. Add: URL=http://localhost:11435/v1, Name=Qwen2.5-Coder"

echo ""
echo "📝 View Logs:"
echo "   • Qwen: tail -f /tmp/qwen-7b.log"
echo "   • WebUI: tail -f /tmp/open-webui.log"

echo ""
echo "🎯 Model Capabilities:"
echo "   • Code generation (92+ languages)"
echo "   • Function/Tool calling"
echo "   • Repository understanding"
echo "   • Fill-in-middle coding"
echo "   • 32K context window" 