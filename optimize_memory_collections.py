#!/usr/bin/env python3
"""
Script to optimize Qdrant memory collections safely
- Only removes empty collections
- Identifies memory vs codebase collections
- Provides recommendations for memory consolidation
"""
import requests
import json
from typing import List, Dict

QDRANT_URL = "http://localhost:6333"

def get_collections() -> List[Dict]:
    """Get all collections info"""
    response = requests.get(f"{QDRANT_URL}/collections")
    return response.json()["result"]["collections"]

def get_collection_info(collection_name: str) -> Dict:
    """Get detailed collection info"""
    response = requests.get(f"{QDRANT_URL}/collections/{collection_name}")
    return response.json()["result"]

def get_sample_point(collection_name: str) -> Dict:
    """Get a sample point to identify collection type"""
    try:
        response = requests.post(
            f"{QDRANT_URL}/collections/{collection_name}/points/scroll",
            headers={"Content-Type": "application/json"},
            json={"limit": 1}
        )
        result = response.json()
        if result.get("result", {}).get("points"):
            return result["result"]["points"][0]["payload"]
        return {}
    except:
        return {}

def identify_collection_type(payload: Dict) -> str:
    """Identify if collection contains memory or codebase data"""
    if not payload:
        return "empty"
    
    # Check for codebase indicators
    if "filePath" in payload and "codeChunk" in payload:
        return "codebase"
    
    # Check for memory indicators
    if "content" in payload and "user_id" in payload:
        return "memory"
    
    # Check for other patterns
    if "session_id" in payload or "timestamp" in payload:
        return "memory"
    
    return "unknown"

def delete_collection(collection_name: str):
    """Delete a collection"""
    response = requests.delete(f"{QDRANT_URL}/collections/{collection_name}")
    return response.json()

def main():
    print("🔍 Analyzing Qdrant Collections...")
    print("=" * 60)
    
    collections = get_collections()
    
    memory_collections = []
    codebase_collections = []
    empty_collections = []
    unknown_collections = []
    
    for collection in collections:
        name = collection["name"]
        info = get_collection_info(name)
        points_count = info["points_count"]
        vector_size = info["config"]["params"]["vectors"]["size"]
        
        print(f"\n📁 Collection: {name}")
        print(f"   Points: {points_count:,}")
        print(f"   Vector Size: {vector_size}")
        
        if points_count == 0:
            empty_collections.append(name)
            print(f"   Type: ❌ EMPTY")
        else:
            sample = get_sample_point(name)
            collection_type = identify_collection_type(sample)
            
            if collection_type == "memory":
                memory_collections.append((name, points_count))
                print(f"   Type: 🧠 MEMORY")
            elif collection_type == "codebase":
                codebase_collections.append((name, points_count))
                print(f"   Type: 💻 CODEBASE")
            else:
                unknown_collections.append((name, points_count))
                print(f"   Type: ❓ UNKNOWN")
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY:")
    print(f"🧠 Memory Collections: {len(memory_collections)}")
    for name, count in memory_collections:
        print(f"   - {name}: {count:,} memories")
    
    print(f"\n💻 Codebase Collections: {len(codebase_collections)}")
    for name, count in codebase_collections:
        print(f"   - {name}: {count:,} embeddings")
    
    print(f"\n❌ Empty Collections: {len(empty_collections)}")
    for name in empty_collections:
        print(f"   - {name}")
    
    print(f"\n❓ Unknown Collections: {len(unknown_collections)}")
    for name, count in unknown_collections:
        print(f"   - {name}: {count:,} points")
    
    # Safe cleanup recommendations
    print("\n" + "=" * 60)
    print("💡 RECOMMENDATIONS:")
    
    if empty_collections:
        print(f"\n🗑️  SAFE TO DELETE ({len(empty_collections)} empty collections):")
        for name in empty_collections:
            print(f"   - {name}")
        
        print("\n⚠️  To delete empty collections, run:")
        for name in empty_collections:
            print(f"   curl -X DELETE http://localhost:6333/collections/{name}")
    
    if len(memory_collections) > 1:
        total_memories = sum(count for _, count in memory_collections)
        print(f"\n🔄 MEMORY CONSOLIDATION OPPORTUNITY:")
        print(f"   - {len(memory_collections)} memory collections with {total_memories:,} total memories")
        print(f"   - Consider consolidating into 1 main collection")
        print(f"   - ⚠️  RISK: Potential duplicate memories")
        print(f"   - 💡 RECOMMENDATION: Manual review before merging")
    
    print(f"\n✅ CODEBASE COLLECTIONS (KEEP AS-IS):")
    print(f"   - {len(codebase_collections)} collections are codebase embeddings")
    print(f"   - These should NOT be merged or deleted")

if __name__ == "__main__":
    main()