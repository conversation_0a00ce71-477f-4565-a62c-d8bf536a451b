#!/bin/bash

echo "Starting the cleanup process..."

# Step 1: Remove all untracked directories
if [ -f ./remove-all-untracked-dirs-final.sh ]; then
    echo "Running remove-all-untracked-dirs-final.sh..."
    bash ./remove-all-untracked-dirs-final.sh
else
    echo "Warning: remove-all-untracked-dirs-final.sh not found."
fi

# Step 2: Remove large files from Git history
if [ -f ./remove-large-files-from-git-final.sh ]; then
    echo "Running remove-large-files-from-git-final.sh..."
    bash ./remove-large-files-from-git-final.sh
else
    echo "Warning: remove-large-files-from-git-final.sh not found."
fi

# Step 3: Remove nested Git repositories
if [ -f ./remove-nested-git-repos.sh ]; then
    echo "Running remove-nested-git-repos.sh..."
    bash ./remove-nested-git-repos.sh
else
    echo "Warning: remove-nested-git-repos.sh not found."
fi

# Step 4: Remove virtual environments from Git
if [ -f ./remove-venv-from-git.sh ]; then
    echo "Running remove-venv-from-git.sh..."
    bash ./remove-venv-from-git.sh
else
    echo "Warning: remove-venv-from-git.sh not found."
fi

echo "Cleanup process complete."