"""
Open WebUI Integration Adapter
==============================

This module provides the integration layer that allows our enhanced RAG system
to completely replace Open WebUI's built-in Knowledge RAG while maintaining
full backward compatibility.

This adapter intercepts and replaces the core RAG functions in Open WebUI's
retrieval.py and knowledge.py routers.
"""

import logging
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime

from fastapi import Request, HTTPException, status
from pydantic import BaseModel

# Import our enhanced RAG system
from openwebui_rag_replacement import enhanced_rag_system, EnhancedRAGKnowledgeSystem

# Import Open WebUI models and utilities
from open_webui.models.files import FileModel, Files
from open_webui.models.knowledge import Knowledges
from open_webui.retrieval.vector.factory import VECTOR_DB_CLIENT
from open_webui.constants import ERROR_MESSAGES

log = logging.getLogger(__name__)

class OpenWebUIRAGAdapter:
    """
    Adapter that replaces Open WebUI's RAG functions with our enhanced system.
    This ensures seamless integration without breaking existing functionality.
    """
    
    def __init__(self):
        self.enhanced_system = enhanced_rag_system
        self.original_functions = {}
        self._monkey_patch_applied = False
    
    def apply_monkey_patches(self):
        """
        Apply monkey patches to replace Open WebUI's RAG functions with our enhanced versions.
        This is the key mechanism that allows us to replace the entire RAG system.
        """
        if self._monkey_patch_applied:
            return
        
        try:
            # Import the modules we need to patch
            import open_webui.routers.retrieval as retrieval_module
            import open_webui.routers.knowledge as knowledge_module
            
            # Store original functions for potential rollback
            self.original_functions = {
                'process_file': getattr(retrieval_module, 'process_file', None),
                'query_doc_handler': getattr(retrieval_module, 'query_doc_handler', None),
                'query_collection_handler': getattr(retrieval_module, 'query_collection_handler', None),
                'save_docs_to_vector_db': getattr(retrieval_module, 'save_docs_to_vector_db', None),
            }
            
            # Replace with our enhanced functions
            retrieval_module.process_file = self.enhanced_process_file
            retrieval_module.query_doc_handler = self.enhanced_query_doc_handler
            retrieval_module.query_collection_handler = self.enhanced_query_collection_handler
            retrieval_module.save_docs_to_vector_db = self.enhanced_save_docs_to_vector_db
            
            self._monkey_patch_applied = True
            log.info("Enhanced RAG monkey patches applied successfully")
            
        except Exception as e:
            log.error(f"Failed to apply monkey patches: {str(e)}")
            raise
    
    def rollback_monkey_patches(self):
        """
        Rollback monkey patches to restore original functionality.
        """
        if not self._monkey_patch_applied:
            return
        
        try:
            import open_webui.routers.retrieval as retrieval_module
            
            # Restore original functions
            for func_name, original_func in self.original_functions.items():
                if original_func:
                    setattr(retrieval_module, func_name.split('_', 1)[-1], original_func)
            
            self._monkey_patch_applied = False
            log.info("Enhanced RAG monkey patches rolled back")
            
        except Exception as e:
            log.error(f"Failed to rollback monkey patches: {str(e)}")
    
    async def enhanced_process_file(self, request: Request, form_data, user):
        """
        Enhanced replacement for Open WebUI's process_file function.
        """
        try:
            # Get file from database
            file = Files.get_file_by_id(form_data.file_id)
            if not file:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=ERROR_MESSAGES.NOT_FOUND
                )
            
            # Check if file has content
            if not file.data or not file.data.get("content"):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=ERROR_MESSAGES.FILE_NOT_PROCESSED
                )
            
            # Process with our enhanced system
            result = await self.enhanced_system.process_document_enhanced(
                file_model=file,
                collection_name=form_data.collection_name,
                user_id=user.id
            )
            
            # Update file metadata to match Open WebUI expectations
            Files.update_file_metadata_by_id(
                form_data.file_id,
                {
                    "collection_name": form_data.collection_name,
                    "enhanced_rag": True,
                    "chunks_created": result["chunks_created"]
                }
            )
            
            # Return in Open WebUI expected format
            return {
                "status": True,
                "file_id": form_data.file_id,
                "collection_name": form_data.collection_name,
                "message": "File processed successfully with enhanced RAG"
            }
            
        except Exception as e:
            log.error(f"Enhanced process_file error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=str(e)
            )
    
    async def enhanced_query_doc_handler(self, request: Request, form_data, user):
        """
        Enhanced replacement for Open WebUI's query_doc_handler function.
        """
        try:
            # Query with our enhanced system
            results = await self.enhanced_system.query_knowledge_enhanced(
                collection_name=form_data.collection_name,
                query=form_data.query,
                k=form_data.k or 5,
                user_id=user.id
            )
            
            # Convert to Open WebUI expected format
            formatted_results = []
            for result in results["results"]:
                formatted_results.append({
                    "id": result["metadata"].get("chunk_id", ""),
                    "document": result["content"],
                    "metadata": result["metadata"],
                    "score": result["score"],
                    # Enhanced citation information
                    "citation": {
                        "source": result["citation"]["source"],
                        "file_id": result["citation"]["file_id"],
                        "confidence": result["citation"]["confidence"],
                        "keywords": result["citation"]["keywords"]
                    }
                })
            
            return formatted_results
            
        except Exception as e:
            log.error(f"Enhanced query_doc_handler error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=str(e)
            )
    
    async def enhanced_query_collection_handler(self, request: Request, form_data, user):
        """
        Enhanced replacement for Open WebUI's query_collection_handler function.
        """
        try:
            all_results = []
            
            # Query each collection with our enhanced system
            for collection_name in form_data.collection_names:
                results = await self.enhanced_system.query_knowledge_enhanced(
                    collection_name=collection_name,
                    query=form_data.query,
                    k=form_data.k or 5,
                    user_id=user.id
                )
                
                # Add collection context to results
                for result in results["results"]:
                    result["collection_name"] = collection_name
                    all_results.append(result)
            
            # Sort by score and limit results
            all_results.sort(key=lambda x: x["score"], reverse=True)
            if form_data.k:
                all_results = all_results[:form_data.k]
            
            # Convert to Open WebUI expected format
            formatted_results = []
            for result in all_results:
                formatted_results.append({
                    "id": result["metadata"].get("chunk_id", ""),
                    "document": result["content"],
                    "metadata": result["metadata"],
                    "score": result["score"],
                    "collection_name": result["collection_name"],
                    "citation": result["citation"]
                })
            
            return formatted_results
            
        except Exception as e:
            log.error(f"Enhanced query_collection_handler error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=str(e)
            )
    
    async def enhanced_save_docs_to_vector_db(
        self, 
        request: Request, 
        docs: List[Any], 
        collection_name: str, 
        overwrite: bool = False,
        add: bool = False,
        user=None
    ):
        """
        Enhanced replacement for Open WebUI's save_docs_to_vector_db function.
        """
        try:
            if overwrite and VECTOR_DB_CLIENT.has_collection(collection_name):
                VECTOR_DB_CLIENT.delete_collection(collection_name)
            
            # Create collection if it doesn't exist
            if not VECTOR_DB_CLIENT.has_collection(collection_name):
                VECTOR_DB_CLIENT.create_collection(collection_name)
            
            # Process documents with our enhanced system
            processed_count = 0
            for doc in docs:
                try:
                    # Extract content and metadata
                    content = doc.page_content if hasattr(doc, 'page_content') else str(doc)
                    metadata = doc.metadata if hasattr(doc, 'metadata') else {}
                    
                    # Create a temporary file model for processing
                    temp_file = FileModel(
                        id=metadata.get("file_id", str(uuid.uuid4())),
                        filename=metadata.get("source", "unknown"),
                        data={"content": content},
                        meta=metadata,
                        user_id=user.id if user else "system"
                    )
                    
                    # Process with enhanced system
                    result = await self.enhanced_system.process_document_enhanced(
                        file_model=temp_file,
                        collection_name=collection_name,
                        user_id=user.id if user else "system"
                    )
                    
                    processed_count += 1
                    
                except Exception as e:
                    log.error(f"Failed to process document: {str(e)}")
                    continue
            
            log.info(f"Enhanced save_docs_to_vector_db: Processed {processed_count}/{len(docs)} documents")
            return processed_count
            
        except Exception as e:
            log.error(f"Enhanced save_docs_to_vector_db error: {str(e)}")
            raise

# Global adapter instance
openwebui_adapter = OpenWebUIRAGAdapter()

def initialize_enhanced_rag_integration():
    """
    Initialize the enhanced RAG integration by applying monkey patches.
    This function should be called during Open WebUI startup.
    """
    try:
        openwebui_adapter.apply_monkey_patches()
        log.info("Enhanced RAG integration initialized successfully")
        return True
    except Exception as e:
        log.error(f"Failed to initialize enhanced RAG integration: {str(e)}")
        return False

def shutdown_enhanced_rag_integration():
    """
    Shutdown the enhanced RAG integration by rolling back monkey patches.
    """
    try:
        openwebui_adapter.rollback_monkey_patches()
        log.info("Enhanced RAG integration shutdown successfully")
        return True
    except Exception as e:
        log.error(f"Failed to shutdown enhanced RAG integration: {str(e)}")
        return False

# Configuration and feature flags
class EnhancedRAGConfig:
    """Configuration for enhanced RAG integration."""
    
    def __init__(self):
        self.enabled = True
        self.fallback_to_original = True
        self.log_performance_metrics = True
        self.cache_embeddings = True
        self.max_chunk_size = 1000
        self.overlap_size = 200
        self.top_k_default = 5
        self.confidence_threshold = 0.7
    
    def to_dict(self):
        return {
            "enabled": self.enabled,
            "fallback_to_original": self.fallback_to_original,
            "log_performance_metrics": self.log_performance_metrics,
            "cache_embeddings": self.cache_embeddings,
            "max_chunk_size": self.max_chunk_size,
            "overlap_size": self.overlap_size,
            "top_k_default": self.top_k_default,
            "confidence_threshold": self.confidence_threshold
        }

# Global configuration
enhanced_rag_config = EnhancedRAGConfig()

def get_enhanced_rag_config():
    """Get the current enhanced RAG configuration."""
    return enhanced_rag_config

def update_enhanced_rag_config(**kwargs):
    """Update enhanced RAG configuration."""
    for key, value in kwargs.items():
        if hasattr(enhanced_rag_config, key):
            setattr(enhanced_rag_config, key, value)
    
    log.info(f"Enhanced RAG configuration updated: {kwargs}")

# Performance monitoring
class PerformanceMonitor:
    """Monitor performance of enhanced RAG operations."""
    
    def __init__(self):
        self.metrics = {
            "queries_processed": 0,
            "documents_processed": 0,
            "average_query_time": 0.0,
            "average_processing_time": 0.0,
            "errors": 0,
            "cache_hits": 0,
            "cache_misses": 0
        }
        self.start_time = datetime.now()
    
    def record_query(self, duration: float):
        """Record a query operation."""
        self.metrics["queries_processed"] += 1
        self.metrics["average_query_time"] = (
            (self.metrics["average_query_time"] * (self.metrics["queries_processed"] - 1) + duration)
            / self.metrics["queries_processed"]
        )
    
    def record_processing(self, duration: float):
        """Record a document processing operation."""
        self.metrics["documents_processed"] += 1
        self.metrics["average_processing_time"] = (
            (self.metrics["average_processing_time"] * (self.metrics["documents_processed"] - 1) + duration)
            / self.metrics["documents_processed"]
        )
    
    def record_error(self):
        """Record an error."""
        self.metrics["errors"] += 1
    
    def record_cache_hit(self):
        """Record a cache hit."""
        self.metrics["cache_hits"] += 1
    
    def record_cache_miss(self):
        """Record a cache miss."""
        self.metrics["cache_misses"] += 1
    
    def get_metrics(self):
        """Get current performance metrics."""
        uptime = (datetime.now() - self.start_time).total_seconds()
        return {
            **self.metrics,
            "uptime_seconds": uptime,
            "queries_per_second": self.metrics["queries_processed"] / uptime if uptime > 0 else 0,
            "cache_hit_rate": (
                self.metrics["cache_hits"] / (self.metrics["cache_hits"] + self.metrics["cache_misses"])
                if (self.metrics["cache_hits"] + self.metrics["cache_misses"]) > 0 else 0
            )
        }
    
    def reset_metrics(self):
        """Reset all metrics."""
        self.metrics = {key: 0 if isinstance(value, (int, float)) else value for key, value in self.metrics.items()}
        self.start_time = datetime.now()

# Global performance monitor
performance_monitor = PerformanceMonitor()

def get_performance_metrics():
    """Get current performance metrics."""
    return performance_monitor.get_metrics()