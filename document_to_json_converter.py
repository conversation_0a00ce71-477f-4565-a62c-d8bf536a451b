#!/usr/bin/env python3
"""
Document to JSON Converter for Vietnamese RAG System
Convert documents and tables to structured JSON format for JSON Fast Search
Leverages existing LLMSherpa table processing capabilities
"""

import os
import json
import logging
import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class JSONDocument:
    """Structured document for JSON Fast Search"""
    id: str
    title: str
    content: str
    content_type: str  # "text", "table", "mixed"
    keywords: List[str]
    categories: List[str]
    metadata: Dict[str, Any]
    tables: List[Dict[str, Any]]
    text_sections: List[Dict[str, Any]]
    created_at: str
    file_path: str
    confidence_score: float

@dataclass
class JSONTable:
    """Structured table for JSON Fast Search"""
    id: str
    title: str
    headers: List[str]
    rows: List[List[str]]
    table_type: str
    summary: str
    keywords: List[str]
    metadata: Dict[str, Any]
    source_document: str
    page_number: int
    confidence_score: float

class DocumentToJSONConverter:
    """Convert documents to structured JSON for fast search"""
    
    def __init__(self):
        # Try to import existing processors
        self.table_processor = None
        try:
            from llmsherpa_table_processor import LLMSherpaTableProcessor
            self.table_processor = LLMSherpaTableProcessor()
            logger.info("✅ LLMSherpa table processor loaded")
        except ImportError:
            logger.warning("⚠️  LLMSherpa processor not available, using basic processing")
        
        # Vietnamese stop words and patterns
        self.vietnamese_stop_words = {
            'là', 'của', 'được', 'có', 'trong', 'và', 'với', 'để', 'từ', 'về',
            'theo', 'như', 'khi', 'bởi', 'do', 'vì', 'nếu', 'mà', 'để', 'cho',
            'từng', 'đã', 'sẽ', 'đang', 'các', 'những', 'này', 'đó', 'ở', 'tại'
        }
        
        # English stop words
        self.english_stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'from', 'as', 'is', 'was', 'are', 'were', 'be',
            'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will',
            'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that'
        }
        
        # Combined stop words
        self.stop_words = self.vietnamese_stop_words | self.english_stop_words
        
        # Vietnamese keyword patterns
        self.vietnamese_patterns = {
            'financial_terms': [
                'doanh thu', 'bán hàng', 'tài sản', 'cố định', 'chi phí', 'lợi nhuận',
                'thuế', 'vat', 'phí', 'vnđ', 'vnd', 'đồng', 'triệu', 'tỷ', 'nghìn',
                'thành tiền', 'tổng cộng', 'tổng tiền', 'đơn giá', 'số lượng'
            ],
            'business_terms': [
                'công ty', 'doanh nghiệp', 'khách hàng', 'nhà cung cấp', 'đối tác',
                'hợp đồng', 'báo cáo', 'kế hoạch', 'chiến lược', 'mục tiêu',
                'phòng ban', 'nhân viên', 'quản lý', 'giám đốc', 'trưởng phòng'
            ],
            'process_terms': [
                'quy trình', 'thủ tục', 'bước', 'giai đoạn', 'thực hiện', 'hoàn thành',
                'kiểm tra', 'đánh giá', 'phê duyệt', 'chấp thuận', 'từ chối'
            ],
            'time_terms': [
                'ngày', 'tháng', 'năm', 'tuần', 'giờ', 'phút', 'thời gian',
                'deadline', 'hạn chót', 'kế hoạch', 'lịch trình', 'thời điểm'
            ]
        }
        
        # Category mapping
        self.category_patterns = {
            'financial': ['tài chính', 'kế toán', 'ngân sách', 'chi phí', 'doanh thu', 'lợi nhuận'],
            'hr': ['nhân sự', 'tuyển dụng', 'lương', 'bảo hiểm', 'đào tạo', 'nhân viên'],
            'process': ['quy trình', 'thủ tục', 'hướng dẫn', 'procedure', 'workflow'],
            'legal': ['hợp đồng', 'luật', 'quy định', 'pháp lý', 'điều khoản'],
            'technical': ['kỹ thuật', 'công nghệ', 'hệ thống', 'phần mềm', 'technical'],
            'report': ['báo cáo', 'thống kê', 'phân tích', 'đánh giá', 'report']
        }

    def convert_document_to_json(self, file_path: str) -> JSONDocument:
        """Convert a document to structured JSON format"""
        try:
            logger.info(f"🔄 Converting document to JSON: {Path(file_path).name}")
            
            # Extract text content
            text_content = self._extract_text_content(file_path)
            
            # Process tables
            table_chunks = []
            if self.table_processor:
                try:
                    table_chunks = self.table_processor.process_pdf_with_llmsherpa(file_path)
                except Exception as e:
                    logger.warning(f"LLMSherpa processing failed, using basic table extraction: {e}")
                    table_chunks = self._basic_table_extraction(file_path)
            else:
                table_chunks = self._basic_table_extraction(file_path)
            
            # Extract keywords from content
            all_content = text_content
            for chunk in table_chunks:
                if hasattr(chunk, 'content'):
                    all_content += " " + chunk.content
            
            keywords = self._extract_keywords(all_content)
            categories = self._determine_categories(all_content)
            
            # Create text sections
            text_sections = self._create_text_sections(text_content, file_path)
            
            # Convert table chunks to JSON format
            json_tables = []
            for i, chunk in enumerate(table_chunks):
                json_table = self._convert_table_chunk_to_json(chunk, file_path, i)
                json_tables.append(asdict(json_table))
            
            # Generate document ID
            doc_id = self._generate_document_id(file_path)
            
            # Determine content type
            content_type = "mixed" if json_tables else "text"
            if json_tables and not text_content.strip():
                content_type = "table"
            
            # Calculate overall confidence
            confidence_score = self._calculate_document_confidence(text_content, table_chunks)
            
            # Create JSON document
            json_doc = JSONDocument(
                id=doc_id,
                title=self._extract_title(file_path, text_content),
                content=text_content,
                content_type=content_type,
                keywords=keywords,
                categories=categories,
                metadata={
                    'file_size': os.path.getsize(file_path) if os.path.exists(file_path) else 0,
                    'file_extension': Path(file_path).suffix.lower(),
                    'total_tables': len(json_tables),
                    'total_text_sections': len(text_sections),
                    'has_vietnamese': self._has_vietnamese_content(all_content),
                    'processing_method': 'llmsherpa' if self.table_processor else 'basic'
                },
                tables=json_tables,
                text_sections=text_sections,
                created_at=datetime.now().isoformat(),
                file_path=str(file_path),
                confidence_score=confidence_score
            )
            
            logger.info(f"✅ Converted document: {len(json_tables)} tables, {len(text_sections)} text sections")
            return json_doc
            
        except Exception as e:
            logger.error(f"❌ Error converting document to JSON: {e}")
            # Return empty document structure
            return self._create_empty_document(file_path, str(e))

    def _extract_text_content(self, file_path: str) -> str:
        """Extract text content from document"""
        try:
            file_ext = Path(file_path).suffix.lower()
            
            if file_ext == '.pdf':
                return self._extract_pdf_text(file_path)
            elif file_ext in ['.txt', '.md']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            elif file_ext in ['.docx', '.doc']:
                return self._extract_docx_text(file_path)
            else:
                logger.warning(f"Unsupported file type for text extraction: {file_ext}")
                return ""
        except Exception as e:
            logger.error(f"Error extracting text from {file_path}: {e}")
            return ""

    def _extract_pdf_text(self, file_path: str) -> str:
        """Extract text from PDF using PyMuPDF"""
        try:
            import fitz
            
            doc = fitz.open(file_path)
            text_content = ""
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                text_content += page.get_text() + "\n"
            
            doc.close()
            return text_content.strip()
            
        except ImportError:
            logger.warning("PyMuPDF not available, trying basic PDF text extraction")
            return ""
        except Exception as e:
            logger.error(f"Error extracting PDF text: {e}")
            return ""

    def _extract_docx_text(self, file_path: str) -> str:
        """Extract text from DOCX using python-docx"""
        try:
            from docx import Document
            
            doc = Document(file_path)
            text_content = ""
            
            for paragraph in doc.paragraphs:
                text_content += paragraph.text + "\n"
            
            return text_content.strip()
            
        except ImportError:
            logger.warning("python-docx not available")
            return ""
        except Exception as e:
            logger.error(f"Error extracting DOCX text: {e}")
            return ""

    def _basic_table_extraction(self, file_path: str) -> List[Dict[str, Any]]:
        """Basic table extraction when LLMSherpa is not available"""
        tables = []
        
        try:
            text_content = self._extract_text_content(file_path)
            
            # Look for table-like patterns in text
            lines = text_content.split('\n')
            current_table = []
            
            for line in lines:
                if self._looks_like_table_row(line):
                    current_table.append(line)
                else:
                    if len(current_table) >= 3:  # At least 3 rows
                        table_data = self._parse_table_lines(current_table)
                        if table_data:
                            tables.append({
                                'content': '\n'.join(current_table),
                                'table_structure': table_data,
                                'confidence_score': 0.6,
                                'table_type': 'detected_table',
                                'metadata': {'extraction_method': 'basic_text_pattern'}
                            })
                    current_table = []
            
            # Don't forget the last table
            if len(current_table) >= 3:
                table_data = self._parse_table_lines(current_table)
                if table_data:
                    tables.append({
                        'content': '\n'.join(current_table),
                        'table_structure': table_data,
                        'confidence_score': 0.6,
                        'table_type': 'detected_table',
                        'metadata': {'extraction_method': 'basic_text_pattern'}
                    })
            
        except Exception as e:
            logger.error(f"Basic table extraction failed: {e}")
        
        return tables

    def _looks_like_table_row(self, line: str) -> bool:
        """Check if line looks like a table row"""
        line = line.strip()
        if len(line) < 10:
            return False
        
        # Check for separators
        separators = ['\t', '|', '  ', '...', '--', '::']
        has_separators = any(sep in line for sep in separators)
        
        # Check for Vietnamese table indicators
        vietnamese_indicators = ['STT', 'Tên', 'Số', 'Ngày', 'Thời gian', 'Mã', 'Loại']
        has_indicators = any(indicator in line for indicator in vietnamese_indicators)
        
        # Check for numbers and mixed content
        has_numbers = bool(re.search(r'\d+', line))
        
        return has_separators and (has_indicators or has_numbers)

    def _parse_table_lines(self, lines: List[str]) -> Dict[str, Any]:
        """Parse table lines into structured data"""
        try:
            headers = []
            rows = []
            
            # First line as headers
            if lines:
                first_line = lines[0]
                headers = self._split_table_row(first_line)
            
            # Remaining lines as rows
            for line in lines[1:]:
                row_data = self._split_table_row(line)
                if row_data:
                    rows.append(row_data)
            
            return {
                'headers': headers,
                'rows': rows
            }
        except Exception as e:
            logger.error(f"Error parsing table lines: {e}")
            return {'headers': [], 'rows': []}

    def _split_table_row(self, line: str) -> List[str]:
        """Split table row into cells"""
        # Try different separators
        separators = ['\t', '|', '  ', '...', '--']
        
        for sep in separators:
            if sep in line:
                cells = [cell.strip() for cell in line.split(sep)]
                cells = [cell for cell in cells if cell]  # Remove empty cells
                if len(cells) >= 2:
                    return cells
        
        # Fallback: split by multiple spaces
        cells = re.split(r'\s{2,}', line.strip())
        return [cell.strip() for cell in cells if cell.strip()]

    def _extract_keywords(self, content: str) -> List[str]:
        """Extract meaningful keywords from content"""
        keywords = set()
        content_lower = content.lower()
        
        # Extract Vietnamese financial, business, and process terms
        for category, terms in self.vietnamese_patterns.items():
            for term in terms:
                if term in content_lower:
                    keywords.add(term)
        
        # Extract important words (length > 3, not stop words)
        words = re.findall(r'\b[a-zA-ZÀ-ỹ]{4,}\b', content)
        for word in words:
            if word.lower() not in self.stop_words and len(word) > 3:
                keywords.add(word.lower())
        
        # Extract numbers with units
        number_patterns = [
            r'\d+(?:\.\d{3})*(?:,\d{2})?\s*(?:vnđ|vnd|đồng|triệu|tỷ)',
            r'\d+(?:,\d+)?%',
            r'\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}'  # dates
        ]
        
        for pattern in number_patterns:
            matches = re.findall(pattern, content_lower)
            for match in matches:
                keywords.add(match)
        
        # Limit to most important keywords
        return list(keywords)[:30]

    def _determine_categories(self, content: str) -> List[str]:
        """Determine document categories based on content"""
        categories = []
        content_lower = content.lower()
        
        for category, patterns in self.category_patterns.items():
            match_count = sum(1 for pattern in patterns if pattern in content_lower)
            if match_count >= 2:  # At least 2 pattern matches
                categories.append(category)
        
        # Default category if none detected
        if not categories:
            categories.append('general')
        
        return categories

    def _create_text_sections(self, text_content: str, file_path: str) -> List[Dict[str, Any]]:
        """Create text sections for search"""
        sections = []
        
        if not text_content.strip():
            return sections
        
        # Split into paragraphs
        paragraphs = [p.strip() for p in text_content.split('\n\n') if p.strip()]
        
        for i, paragraph in enumerate(paragraphs):
            if len(paragraph) > 50:  # Only meaningful paragraphs
                section = {
                    'id': f"text_section_{i}",
                    'content': paragraph,
                    'keywords': self._extract_keywords(paragraph)[:10],
                    'length': len(paragraph),
                    'section_index': i
                }
                sections.append(section)
        
        return sections

    def _convert_table_chunk_to_json(self, table_chunk: Any, file_path: str, table_index: int) -> JSONTable:
        """Convert table chunk to JSONTable format"""
        # Handle different table chunk formats
        if hasattr(table_chunk, 'table_structure'):
            # LLMSherpa TableChunk format
            table_structure = table_chunk.table_structure
            headers = table_structure.get('headers', [])
            rows = table_structure.get('rows', [])
            content = getattr(table_chunk, 'content', '')
            table_type = getattr(table_chunk, 'table_type', 'general_table')
            confidence = getattr(table_chunk, 'confidence_score', 0.7)
            metadata = getattr(table_chunk, 'metadata', {})
        else:
            # Basic dictionary format
            table_structure = table_chunk.get('table_structure', {})
            headers = table_structure.get('headers', [])
            rows = table_structure.get('rows', [])
            content = table_chunk.get('content', '')
            table_type = table_chunk.get('table_type', 'general_table')
            confidence = table_chunk.get('confidence_score', 0.7)
            metadata = table_chunk.get('metadata', {})
        
        # Generate table ID
        table_id = f"table_{hashlib.md5(f'{file_path}_{table_index}'.encode()).hexdigest()[:8]}"
        
        # Extract keywords from table content
        table_keywords = self._extract_keywords(content)[:15]
        
        # Create summary
        summary = self._create_table_summary(headers, rows, table_type)
        
        return JSONTable(
            id=table_id,
            title=f"Bảng {table_type.replace('_', ' ').title()}",
            headers=headers,
            rows=rows,
            table_type=table_type,
            summary=summary,
            keywords=table_keywords,
            metadata=metadata,
            source_document=str(file_path),
            page_number=metadata.get('page_number', 0),
            confidence_score=confidence
        )

    def _create_table_summary(self, headers: List[str], rows: List[List[str]], table_type: str) -> str:
        """Create a meaningful summary of the table"""
        summary_parts = []
        
        # Table structure info
        summary_parts.append(f"Bảng {table_type.replace('_', ' ')} với {len(headers)} cột và {len(rows)} hàng")
        
        # Column names
        if headers:
            summary_parts.append(f"Các cột: {', '.join(headers[:5])}")
            if len(headers) > 5:
                summary_parts[-1] += f" và {len(headers) - 5} cột khác"
        
        # Sample data
        if rows and len(rows) > 0:
            first_row = rows[0]
            non_empty_cells = [cell for cell in first_row if cell and str(cell).strip()]
            if non_empty_cells:
                summary_parts.append(f"Dữ liệu mẫu: {', '.join(map(str, non_empty_cells[:3]))}")
        
        return ". ".join(summary_parts)

    def _extract_title(self, file_path: str, content: str) -> str:
        """Extract or generate document title"""
        # Use filename as base
        base_title = Path(file_path).stem.replace('_', ' ').replace('-', ' ')
        
        # Try to find a better title in content
        lines = content.split('\n')
        for line in lines[:10]:  # Check first 10 lines
            line = line.strip()
            if len(line) > 10 and len(line) < 100:
                # Check if it looks like a title
                if any(word in line.lower() for word in ['báo cáo', 'kế hoạch', 'hướng dẫn', 'quy trình']):
                    return line
        
        return base_title.title()

    def _generate_document_id(self, file_path: str) -> str:
        """Generate unique document ID"""
        file_info = f"{file_path}_{os.path.getmtime(file_path) if os.path.exists(file_path) else 0}"
        return hashlib.md5(file_info.encode()).hexdigest()

    def _calculate_document_confidence(self, text_content: str, table_chunks: List[Any]) -> float:
        """Calculate overall document processing confidence"""
        confidence = 0.0
        
        # Text content quality
        if text_content and len(text_content.strip()) > 100:
            confidence += 0.4
        elif text_content:
            confidence += 0.2
        
        # Table processing confidence
        if table_chunks:
            total_confidence = 0
            for chunk in table_chunks:
                if hasattr(chunk, 'confidence_score'):
                    total_confidence += chunk.confidence_score
                else:
                    total_confidence += chunk.get('confidence_score', 0.6)
            table_confidence = total_confidence / len(table_chunks)
            confidence += table_confidence * 0.6
        else:
            confidence += 0.3  # No tables is also valid
        
        return min(1.0, confidence)

    def _has_vietnamese_content(self, content: str) -> bool:
        """Check if content contains Vietnamese characters"""
        vietnamese_chars = 'àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ'
        return any(char in content.lower() for char in vietnamese_chars)

    def _create_empty_document(self, file_path: str, error_message: str) -> JSONDocument:
        """Create empty document structure for failed conversions"""
        return JSONDocument(
            id=self._generate_document_id(file_path),
            title=Path(file_path).stem,
            content="",
            content_type="error",
            keywords=[],
            categories=["error"],
            metadata={'error': error_message},
            tables=[],
            text_sections=[],
            created_at=datetime.now().isoformat(),
            file_path=str(file_path),
            confidence_score=0.0
        )

    def save_json_document(self, json_doc: JSONDocument, output_dir: str = "json_knowledge_base") -> str:
        """Save JSON document to file"""
        try:
            # Create output directory
            os.makedirs(output_dir, exist_ok=True)
            
            # Generate filename
            filename = f"{json_doc.id}.json"
            output_path = os.path.join(output_dir, filename)
            
            # Save to file
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(asdict(json_doc), f, ensure_ascii=False, indent=2)
            
            logger.info(f"💾 Saved JSON document: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"❌ Error saving JSON document: {e}")
            return ""

    def convert_directory_to_json(self, input_dir: str, output_dir: str = "json_knowledge_base") -> Dict[str, Any]:
        """Convert all documents in directory to JSON format"""
        results = {
            'total_files': 0,
            'successful_conversions': 0,
            'failed_conversions': 0,
            'total_tables': 0,
            'total_text_sections': 0,
            'converted_files': [],
            'failed_files': []
        }
        
        # Find all document files
        supported_extensions = ['.pdf', '.docx', '.doc', '.txt', '.md']
        input_path = Path(input_dir)
        
        for ext in supported_extensions:
            for file_path in input_path.glob(f"**/*{ext}"):
                results['total_files'] += 1
                
                try:
                    # Convert to JSON
                    json_doc = self.convert_document_to_json(str(file_path))
                    
                    # Save JSON document
                    output_path = self.save_json_document(json_doc, output_dir)
                    
                    if output_path:
                        results['successful_conversions'] += 1
                        results['total_tables'] += len(json_doc.tables)
                        results['total_text_sections'] += len(json_doc.text_sections)
                        results['converted_files'].append({
                            'input_file': str(file_path),
                            'output_file': output_path,
                            'tables': len(json_doc.tables),
                            'confidence': json_doc.confidence_score
                        })
                    else:
                        results['failed_conversions'] += 1
                        results['failed_files'].append(str(file_path))
                        
                except Exception as e:
                    logger.error(f"❌ Failed to convert {file_path}: {e}")
                    results['failed_conversions'] += 1
                    results['failed_files'].append(str(file_path))
        
        # Save conversion summary
        summary_path = os.path.join(output_dir, "conversion_summary.json")
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📊 Conversion Summary:")
        logger.info(f"   Total files: {results['total_files']}")
        logger.info(f"   Successful: {results['successful_conversions']}")
        logger.info(f"   Failed: {results['failed_conversions']}")
        logger.info(f"   Total tables: {results['total_tables']}")
        logger.info(f"   Total text sections: {results['total_text_sections']}")
        
        return results


if __name__ == "__main__":
    # Example usage
    converter = DocumentToJSONConverter()
    
    # Convert single document
    if len(os.sys.argv) > 1:
        input_file = os.sys.argv[1]
        if os.path.isfile(input_file):
            print(f"🔄 Converting single document: {input_file}")
            json_doc = converter.convert_document_to_json(input_file)
            output_path = converter.save_json_document(json_doc)
            print(f"✅ Conversion complete: {output_path}")
        elif os.path.isdir(input_file):
            print(f"🔄 Converting directory: {input_file}")
            results = converter.convert_directory_to_json(input_file)
            print(f"✅ Directory conversion complete")
    else:
        print("Usage: python document_to_json_converter.py <file_or_directory>")
        print("Example: python document_to_json_converter.py sample_document.pdf")
        print("Example: python document_to_json_converter.py /path/to/documents/") 