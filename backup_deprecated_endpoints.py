#!/usr/bin/env python3
"""
Backup deprecated image endpoints before removal
"""

import os
import shutil
from datetime import datetime

def backup_file(source_path, backup_dir):
    """Backup a file to the backup directory"""
    if os.path.exists(source_path):
        filename = os.path.basename(source_path)
        backup_path = os.path.join(backup_dir, filename)
        shutil.copy2(source_path, backup_path)
        print(f"✅ Backed up: {source_path} -> {backup_path}")
        return True
    else:
        print(f"⚠️  File not found: {source_path}")
        return False

def main():
    """Backup deprecated image processing endpoints"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"deprecated_endpoints_backup_{timestamp}"
    
    # Create backup directory
    os.makedirs(backup_dir, exist_ok=True)
    print(f"📁 Created backup directory: {backup_dir}")
    
    # Files to backup
    deprecated_files = [
        "backend/app/api/gemma3n.py",
        "backend/app/api/v1/endpoints/ai_edge_provider.py", 
        "backend/app/api/tflite.py"
    ]
    
    print("\n🔄 Backing up deprecated endpoints...")
    
    backed_up = 0
    for file_path in deprecated_files:
        if backup_file(file_path, backup_dir):
            backed_up += 1
    
    # Create a README for the backup
    readme_content = f"""# Deprecated Image Endpoints Backup

Created: {datetime.now().isoformat()}

## Files Backed Up:
{chr(10).join(f"- {f}" for f in deprecated_files)}

## Reason for Removal:
These endpoints were tied to deprecated models (Gemma3n, AI Edge, TFLite) that are no longer in use.
They have been replaced with a simplified image handler at:
- backend/app/api/v1/endpoints/image_handler.py

## New Endpoints:
- POST /api/v1/images/upload - Upload and process images
- POST /api/v1/images/analyze - Analyze uploaded images  
- GET /api/v1/images/stats - Get processing statistics
- GET /api/v1/images/health - Health check

## Restoration:
If needed, these files can be restored from this backup directory.
"""
    
    with open(os.path.join(backup_dir, "README.md"), "w") as f:
        f.write(readme_content)
    
    print(f"\n📋 Backup completed: {backed_up}/{len(deprecated_files)} files backed up")
    print(f"📁 Backup location: {backup_dir}")
    print("✅ Ready to remove deprecated endpoints")

if __name__ == "__main__":
    main()