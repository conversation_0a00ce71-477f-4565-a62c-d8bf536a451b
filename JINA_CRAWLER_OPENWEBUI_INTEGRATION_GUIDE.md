# Jina Crawler + Open WebUI Integration Guide

## Tổng quan

Hướng dẫn này giải thích cách tích hợp Jina Crawler với Open WebUI thông qua HTTP endpoints thay vì MCP protocol. Gi<PERSON>i pháp này dựa trên mô hình thành công của MCPO server.

## Vấn đề đã giải quyết

- **Vấn đề gốc**: LLM không thể nhìn thấy jina_crawler tools dù đã kết nối thành công với Open WebUI
- **Nguyên nhân**: Open WebUI cần HTTP endpoints để gọi tools, không phải MCP protocol trực tiếp
- **Gi<PERSON>i pháp**: Tạo HTTP wrapper cho jina_crawler tương tự như mcpo_complete_server.py

## Kiến trúc giải pháp

```
┌─────────────────┐    HTTP API    ┌──────────────────────┐    Function Call    ┌─────────────┐
│   Open WebUI    │ ──────────────► │ Jina Crawler HTTP    │ ─────────────────► │ JiniCrawler │
│                 │                │ Server (Port 8001)   │                    │ Components  │
└─────────────────┘                └──────────────────────┘                    └─────────────┘
```

## Các thành phần chính

### 1. Jina Crawler HTTP Server (`jina_crawler_http_server.py`)
- FastAPI server expose jina_crawler functions qua HTTP
- Chạy trên port 8001
- Cung cấp OpenAPI specs cho từng tool
- CORS được cấu hình để cho phép Open WebUI truy cập

### 2. Open WebUI Function (`openwebui_jina_crawler_function.py`)
- Function có thể import vào Open WebUI
- Gọi HTTP endpoints của Jina Crawler
- Xử lý response và format cho LLM

### 3. Configuration Scripts
- `configure_openwebui_jina_http.py`: Cấu hình tự động
- `start_jina_crawler_for_openwebui.sh`: Script khởi động tổng hợp

## Cài đặt và sử dụng

### Bước 1: Khởi động Jina Crawler HTTP Server

```bash
# Cách 1: Khởi động manual
python3 jina_crawler_http_server.py

# Cách 2: Sử dụng script tự động
./start_jina_crawler_for_openwebui.sh
```

### Bước 2: Kiểm tra server hoạt động

```bash
# Health check
curl http://localhost:8001/health

# Test crawl
curl -X POST "http://localhost:8001/jina_crawler/crawl" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://dantri.com.vn", "max_content_length": 5000}'
```

### Bước 3: Cấu hình Open WebUI

1. **Chạy script cấu hình**:
   ```bash
   python3 configure_openwebui_jina_http.py
   ```

2. **Thêm Function vào Open WebUI**:
   - Vào Admin Panel > Functions
   - Tạo function mới
   - Copy code từ `openwebui_jina_crawler_function.py`
   - Enable function

### Bước 4: Test integration

Trong Open WebUI chat, sử dụng:
```
jina_crawler_tool('https://dantri.com.vn')
```

## API Endpoints

### Health Check
```
GET /health
```

### Crawl Single URL
```
POST /jina_crawler/crawl
{
  "url": "https://example.com",
  "method": "tls_bypass",
  "process_content": true,
  "max_content_length": 10000
}
```

### Crawl Multiple URLs
```
POST /jina_crawler/crawl_batch
{
  "urls": ["https://example1.com", "https://example2.com"],
  "method": "tls_bypass",
  "process_content": true,
  "max_content_length": 10000
}
```

### AI Search
```
POST /jina_crawler/ai_search
{
  "query": "latest AI news",
  "max_sources": 10,
  "enable_query_refinement": true
}
```

### Bypass Paywall
```
POST /jina_crawler/bypass_paywall
{
  "url": "https://paywall-site.com/article",
  "max_content_length": 50000
}
```

## Tính năng

### ✅ Đã hoạt động
- [x] HTTP wrapper cho JiniCrawler
- [x] OpenAPI specification
- [x] CORS configuration
- [x] Health check endpoint
- [x] Single URL crawling
- [x] Batch URL crawling
- [x] AI search integration
- [x] Paywall bypass
- [x] Open WebUI function
- [x] Automatic configuration
- [x] Error handling

### 🔧 Cải tiến so với MCP
- **Đơn giản hơn**: HTTP API thay vì MCP protocol
- **Tương thích tốt**: Hoạt động với mọi LLM trong Open WebUI
- **Dễ debug**: HTTP requests có thể test bằng curl
- **Hiệu suất cao**: Batch processing với Gemini
- **Linh hoạt**: Có thể mở rộng thêm endpoints

## Troubleshooting

### Lỗi thường gặp

1. **Server không khởi động được**
   ```bash
   # Kiểm tra port đã được sử dụng
   lsof -i :8001
   
   # Kill process nếu cần
   pkill -f jina_crawler_http_server.py
   ```

2. **Import error**
   ```bash
   # Cài đặt dependencies
   pip install fastapi uvicorn pydantic aiohttp beautifulsoup4 requests tls-client fake-useragent
   ```

3. **Open WebUI không thấy function**
   - Kiểm tra function đã được enable
   - Restart Open WebUI
   - Kiểm tra logs trong browser console

### Debug commands

```bash
# Xem logs
tail -f jina_crawler_http.log

# Test endpoints
curl http://localhost:8001/docs  # Swagger UI
curl http://localhost:8001/jina_crawler/openapi.json

# Check processes
ps aux | grep jina_crawler
```

## So sánh với MCPO

| Aspect | MCPO | Jina Crawler HTTP |
|--------|------|-------------------|
| Protocol | HTTP | HTTP |
| Port | 8000 | 8001 |
| Tools | 12 mock tools | 4 real tools |
| Processing | Mock responses | Real AI processing |
| Performance | Fast (mock) | Slower (real processing) |
| Functionality | Limited | Full web crawling |

## Kết luận

Giải pháp này thành công giải quyết vấn đề kết nối giữa Jina Crawler và Open WebUI bằng cách:

1. **Sử dụng HTTP thay vì MCP**: Tương tự như MCPO thành công
2. **Tạo wrapper layer**: Chuyển đổi MCP calls thành HTTP calls
3. **Cung cấp OpenAPI specs**: Cho phép Open WebUI hiểu được tools
4. **Tự động hóa cấu hình**: Scripts giúp setup dễ dàng

Bây giờ LLM có thể sử dụng đầy đủ tính năng của Jina Crawler thông qua Open WebUI interface.

## Files quan trọng

- `jina_crawler_http_server.py` - HTTP server chính
- `openwebui_jina_crawler_function.py` - Function cho Open WebUI
- `configure_openwebui_jina_http.py` - Script cấu hình
- `start_jina_crawler_for_openwebui.sh` - Script khởi động
- `openwebui_jina_crawler_config.json` - Cấu hình tools
