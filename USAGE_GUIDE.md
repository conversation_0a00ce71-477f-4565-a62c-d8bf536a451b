# 🚀 AccA RAG Pipeline - Hướng dẫn sử dụng

## 📋 Tổng quan

AccA RAG Pipeline là hệ thống RAG (Retrieval-Augmented Generation) tùy chỉnh cho Open WebUI, đượ<PERSON> thiết kế để:
- Thay thế cơ chế RAG mặc định của Open WebUI
- Sử dụng Qdrant Vector Database có sẵn trên VPS
- Xử lý tài liệu với nhiều bảng biểu
- Tối ưu hóa cho Gemini API với async processing

## 🏗️ Kiến trúc

```
Open WebUI → AccA RAG Pipeline → Qdrant Vector DB
                ↓
    Gemini API (embedding + LLM)
```

## 📦 Cài đặt

### 1. Prerequisites

```bash
# Kiểm tra Python version (yêu cầu >= 3.8)
python3 --version

# Kiểm tra Qdrant đang chạy
curl http://**********:6333/collections
```

### 2. Cài đặt dependencies

```bash
# Cài đặt basic requirements
pip install -r requirements_enhanced.txt

# Hoặc cài đặt từng package
pip install httpx aiohttp pydantic pandas PyPDF2 python-docx openpyxl

# Optional: Advanced table processing
pip install camelot-py[cv] pdfplumber tabulate
```

### 3. Setup environment

```bash
# Tạo file .env
cat > .env << EOF
GEMINI_API_KEY=your_gemini_api_key_here
QDRANT_HOST=**********
QDRANT_PORT=6333
EOF

# Source environment
source .env
```

### 4. Auto setup (Recommended)

```bash
# Chạy script setup tự động
chmod +x setup_rag_pipeline.sh
./setup_rag_pipeline.sh
```

## 🎯 Cách sử dụng

### A. 📄 Upload và xử lý documents

#### 1. Basic Usage

```python
import asyncio
from acca_rag_document_pipeline import Pipeline

# Initialize pipeline
pipeline = Pipeline()
await pipeline.on_startup()

# Process document
with open("financial_report.pdf", "rb") as f:
    file_content = f.read()

result = await pipeline.process_document_enhanced(
    file_content=file_content,
    filename="financial_report.pdf", 
    user_id="user123"
)

print(f"Processed: {result['chunks_count']} chunks, {result['tables_count']} tables")
```

#### 2. Batch Processing

```python
import os
from pathlib import Path

async def process_folder(folder_path: str, user_id: str):
    pipeline = Pipeline()
    await pipeline.on_startup()
    
    results = []
    for file_path in Path(folder_path).glob("*.pdf"):
        with open(file_path, "rb") as f:
            content = f.read()
        
        result = await pipeline.process_document_enhanced(
            file_content=content,
            filename=file_path.name,
            user_id=user_id
        )
        results.append(result)
    
    return results

# Usage
results = await process_folder("/path/to/documents", "user123")
```

### B. 🔍 Search và RAG Query

#### 1. Basic RAG Search

```python
# Search for relevant documents
query = "What was the revenue growth in Q4?"
user_id = "user123"

# Get relevant chunks
relevant_chunks = await pipeline.search_documents(
    query=query,
    user_id=user_id,
    max_results=5,
    relevance_threshold=0.7
)

# Use with LLM
context = "\n".join([chunk['content'] for chunk in relevant_chunks])
prompt = f"""
Context from documents:
{context}

Question: {query}
Answer based on the context above:
"""
```

#### 2. Table-Aware Search

```python
from acca_rag_table_integration import RAGPipelineWithTables

# Initialize table-aware pipeline
rag_pipeline = RAGPipelineWithTables(
    max_workers=15,
    table_processing_mode="enhanced"
)

# Search with table awareness
results = await rag_pipeline.search_with_table_awareness(
    query="Show me financial performance data",
    user_id="user123",
    max_results=5
)

# Results include table metadata
for result in results:
    print(f"Content: {result['content']}")
    print(f"Type: {result['metadata']['chunk_type']}")
    if result['metadata']['chunk_type'] == 'table':
        print(f"Table info: {result['metadata']['table_rows']}x{result['metadata']['table_columns']}")
```

### C. 🔗 Tích hợp với Open WebUI

#### 1. Cài đặt Pipeline trong Open WebUI

```bash
# Copy pipeline files to Open WebUI pipelines directory
cp acca_rag_document_pipeline.py /path/to/open-webui/pipelines/
cp acca_rag_upload_handler.py /path/to/open-webui/pipelines/
```

#### 2. Configuration trong Open WebUI

```python
# Trong Open WebUI admin panel:
# 1. Go to Admin Panel → Pipelines
# 2. Add new pipeline: acca_rag_document_pipeline.py
# 3. Configure valves:

VALVES_CONFIG = {
    "qdrant_host": "**********",
    "qdrant_port": "6333", 
    "openai_api_key": "your_gemini_key",
    "collection_name": "acca_rag_documents_v2",
    "max_workers": 15,
    "rate_limit_rpm": 1400,
    "chunk_size": 400,
    "max_chunk_tokens": 1800,
    "extract_tables": True,
    "table_processing_mode": "enhanced"
}
```

#### 3. Document Upload Handler

```python
# Trong Open WebUI, khi user upload file:
from acca_rag_upload_handler import DocumentUploadHandler

handler = DocumentUploadHandler()
await handler.initialize()

# Process uploaded file
result = await handler.process_upload(
    file_content=uploaded_file_content,
    filename=uploaded_filename,
    user_id=current_user_id,
    metadata={
        "upload_time": datetime.now().isoformat(),
        "source": "open_webui_upload"
    }
)
```

### D. 📊 Advanced Table Processing

#### 1. Specific Table Extraction

```python
from table_processor_advanced import AdvancedTableProcessor

# Initialize table processor
table_processor = AdvancedTableProcessor(processing_mode="enhanced")

# Extract tables from file
with open("complex_report.pdf", "rb") as f:
    tables = table_processor.extract_tables_from_file(f.read(), "complex_report.pdf")

# Analyze tables
for table in tables:
    print(f"Table type: {table['type']}")
    print(f"Size: {table['rows']}x{table['columns']}")
    print(f"Extraction method: {table['extraction_method']}")
    print(f"Content preview:\n{table['processed_content'][:200]}...")
```

#### 2. Table Metadata Analysis

```python
from enhanced_table_metadata import EnhancedTableMetadataProcessor

# Generate comprehensive metadata
metadata_processor = EnhancedTableMetadataProcessor()

for table in tables:
    metadata = metadata_processor.generate_comprehensive_metadata(
        table=table,
        source_file="complex_report.pdf",
        user_id="user123"
    )
    
    print(f"Table ID: {metadata['table_id']}")
    print(f"Content type: {metadata['content_classification']['primary_type']}")
    print(f"Quality grade: {metadata['quality']['quality_grade']}")
    print(f"Requires chunking: {metadata['chunking']['requires_chunking']}")
```

## ⚙️ Configuration Options

### Pipeline Valves (Main Settings)

```python
class Valves(BaseModel):
    # Qdrant Settings
    qdrant_host: str = "**********"
    qdrant_port: str = "6333"
    collection_name: str = "acca_rag_documents_v2"
    
    # Gemini API Settings  
    openai_api_base: str = "https://generativelanguage.googleapis.com/v1beta/openai"
    openai_api_key: str = os.getenv("GEMINI_API_KEY", "")
    embedding_model: str = "text-embedding-004"
    
    # Performance Settings
    max_workers: int = 15  # 10-20 recommended
    rate_limit_rpm: int = 1400  # Below 1500 limit
    batch_size: int = 50
    
    # Document Processing
    chunk_size: int = 400  # words
    chunk_overlap: int = 50
    max_chunk_tokens: int = 1800  # Below 2048 limit
    max_chunks_per_doc: int = 200
    
    # Table Processing
    extract_tables: bool = True
    table_processing_mode: str = "enhanced"  # markdown, text, structured, enhanced
    
    # RAG Behavior
    max_relevant_chunks: int = 5
    relevance_threshold: float = 0.7
    
    # Supported file types
    supported_extensions: List[str] = [".txt", ".pdf", ".docx", ".md", ".json", ".csv", ".xlsx"]
```

### Performance Tuning

```python
# For high-volume processing
VALVES_HIGH_PERFORMANCE = {
    "max_workers": 20,
    "batch_size": 100,
    "rate_limit_rpm": 1450,
    "chunk_size": 300,
    "max_chunk_tokens": 1600
}

# For quality-focused processing  
VALVES_HIGH_QUALITY = {
    "max_workers": 10,
    "batch_size": 25,
    "table_processing_mode": "enhanced",
    "chunk_size": 500,
    "max_chunk_tokens": 1900,
    "relevance_threshold": 0.8
}
```

## 🧪 Testing

### 1. Run basic tests

```bash
# Test pipeline functionality
python test_rag_pipeline.py

# Expected output:
# ✅ Pipeline initialization
# ✅ Qdrant connection
# ✅ Document processing
# ✅ Table extraction  
# ✅ Embedding generation
# ✅ Vector storage
# ✅ RAG search
```

### 2. Test with sample documents

```python
# Test với file có nhiều bảng biểu
test_files = [
    "financial_report.pdf",     # Tables with numbers
    "product_catalog.xlsx",     # Excel sheets
    "research_paper.docx",      # Academic tables
    "data_export.csv"          # Raw CSV data
]

for file in test_files:
    result = await pipeline.process_document_enhanced(
        file_content=open(file, "rb").read(),
        filename=file,
        user_id="test_user"
    )
    print(f"{file}: {result['chunks_count']} chunks, {result['tables_count']} tables")
```

## 📈 Monitoring & Performance

### 1. Performance Stats

```python
# Get performance statistics
stats = await pipeline.get_performance_stats()
print(f"""
Performance Stats:
- Total embeddings: {stats['total_embeddings']}
- Average time per embedding: {stats['avg_time_per_embedding']:.2f}s
- Success rate: {stats['success_rate']:.1%}
- Total processing time: {stats['total_time']:.2f}s
""")
```

### 2. Qdrant Collection Stats

```python
# Check collection status
import httpx

async with httpx.AsyncClient() as client:
    response = await client.get(f"http://{qdrant_host}:{qdrant_port}/collections/{collection_name}")
    if response.status_code == 200:
        data = response.json()
        print(f"Collection size: {data['result']['points_count']} documents")
        print(f"Collection status: {data['result']['status']}")
```

## 🚨 Troubleshooting

### Common Issues

#### 1. API Rate Limiting
```
Error: Rate limit exceeded
Solution: Reduce max_workers or rate_limit_rpm
```

#### 2. Memory Issues
```
Error: Out of memory during processing
Solution: Reduce batch_size or chunk_size
```

#### 3. Qdrant Connection Failed
```
Error: Connection refused to Qdrant
Solution: Check qdrant_host/port and ensure Qdrant is running
```

#### 4. Table Extraction Failed
```
Error: No tables found in PDF
Solution: Try different extraction methods or check PDF quality
```

### Debug Mode

```python
# Enable debug logging
pipeline.valves.enable_debug = True
pipeline.valves.log_performance = True

# Check extraction capabilities
from table_processor_advanced import AdvancedTableProcessor
processor = AdvancedTableProcessor()
print("Available capabilities:", processor.capabilities)
```

## 🎯 Best Practices

### 1. Document Preparation
- ✅ Ensure PDFs are text-based, not scanned images
- ✅ Tables should have clear borders and structure
- ✅ File names should be descriptive
- ✅ Large files should be split before processing

### 2. Performance Optimization
- ✅ Use batch processing for multiple documents
- ✅ Monitor API rate limits
- ✅ Adjust chunk_size based on document type
- ✅ Use appropriate table_processing_mode

### 3. Quality Assurance
- ✅ Review extracted tables for accuracy
- ✅ Test search relevance with sample queries
- ✅ Monitor embedding quality scores
- ✅ Validate metadata completeness

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Run the test suite: `python test_rag_pipeline.py`
3. Enable debug mode for detailed logs
4. Check Qdrant and Gemini API status

---

**Happy RAG-ing! 🚀📊** 