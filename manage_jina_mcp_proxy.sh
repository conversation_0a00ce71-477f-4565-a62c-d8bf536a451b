#!/bin/bash

# Jina Crawler MCP Proxy Management Script
# Manages the containerized MCP proxy server for Open WebUI integration

CONTAINER_NAME="jina-crawler-mcp-proxy-8002"
COMPOSE_FILE="docker-compose.jina-mcp-proxy.yml"

show_help() {
    echo "🐳 Jina Crawler MCP Proxy Management"
    echo "===================================="
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     - Start the MCP proxy container"
    echo "  stop      - Stop the MCP proxy container"
    echo "  restart   - Restart the MCP proxy container"
    echo "  status    - Show container status"
    echo "  logs      - Show container logs"
    echo "  test      - Test container connectivity"
    echo "  health    - Check health endpoint"
    echo "  build     - Rebuild the container"
    echo "  clean     - Stop and remove container"
    echo "  help      - Show this help message"
}

start_container() {
    echo "🚀 Starting Jina Crawler MCP Proxy container..."
    docker compose -f "$COMPOSE_FILE" up -d
    
    if [ $? -eq 0 ]; then
        echo "✅ Container started successfully"
        sleep 3
        test_connectivity
    else
        echo "❌ Failed to start container"
        return 1
    fi
}

stop_container() {
    echo "🛑 Stopping Jina Crawler MCP Proxy container..."
    docker compose -f "$COMPOSE_FILE" down
    
    if [ $? -eq 0 ]; then
        echo "✅ Container stopped successfully"
    else
        echo "❌ Failed to stop container"
        return 1
    fi
}

restart_container() {
    echo "🔄 Restarting Jina Crawler MCP Proxy container..."
    stop_container
    sleep 2
    start_container
}

show_status() {
    echo "📊 Container Status:"
    echo "==================="
    
    if docker ps -a --filter "name=${CONTAINER_NAME}" --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        docker ps -a --filter "name=${CONTAINER_NAME}" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        echo ""
        
        if docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
            echo "🌐 Endpoints:"
            echo "  - Health: http://localhost:8002/health"
            echo "  - API Docs: http://localhost:8002/jina_crawler/docs"
            echo "  - OpenAPI: http://localhost:8002/jina_crawler/openapi.json"
            echo ""
            echo "🔗 For Open WebUI:"
            echo "  - Container URL: http://jina-crawler-mcp-proxy-8002:8002/jina_crawler/openapi.json"
            echo "  - Host URL: http://localhost:8002/jina_crawler/openapi.json"
        fi
    else
        echo "❌ Container does not exist"
        echo "Run '$0 start' to create and start the container"
    fi
}

show_logs() {
    echo "📋 Container Logs:"
    echo "=================="
    
    if docker ps -a --filter "name=${CONTAINER_NAME}" --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        docker logs "$CONTAINER_NAME" --tail 50 -f
    else
        echo "❌ Container does not exist"
    fi
}

test_connectivity() {
    echo "🧪 Testing Container Connectivity:"
    echo "=================================="
    
    # Test from host
    echo "1. Testing from host..."
    if curl -s "http://localhost:8002/health" > /dev/null; then
        echo "   ✅ Host → Container: OK"
    else
        echo "   ❌ Host → Container: FAILED"
    fi
    
    # Test from Open WebUI container
    echo "2. Testing from Open WebUI container..."
    if docker exec open-webui-mcpo curl -s "http://${CONTAINER_NAME}:8002/health" > /dev/null 2>&1; then
        echo "   ✅ Open WebUI → Jina MCP Proxy: OK"
    else
        echo "   ❌ Open WebUI → Jina MCP Proxy: FAILED"
    fi
    
    echo ""
    echo "🔗 Network Information:"
    docker inspect "$CONTAINER_NAME" --format='{{range $k, $v := .NetworkSettings.Networks}}{{$k}}: {{$v.IPAddress}} {{end}}' 2>/dev/null || echo "Container not running"
}

check_health() {
    echo "🏥 Health Check:"
    echo "================"
    
    if docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        echo "Container Status: ✅ Running"
        echo ""
        echo "Health Endpoint Response:"
        curl -s "http://localhost:8002/health" | python3 -m json.tool 2>/dev/null || echo "Failed to get health status"
        echo ""
        echo "Available Tools:"
        curl -s "http://localhost:8002/jina_crawler/openapi.json" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    paths = data.get('paths', {})
    for path, info in paths.items():
        post_info = info.get('post', {})
        print(f'  - {post_info.get(\"operationId\", path)}: {post_info.get(\"summary\", \"N/A\")}')
except:
    print('Failed to parse OpenAPI spec')
" 2>/dev/null
    else
        echo "❌ Container is not running"
    fi
}

build_container() {
    echo "🔨 Building Jina Crawler MCP Proxy container..."
    docker compose -f "$COMPOSE_FILE" build
    
    if [ $? -eq 0 ]; then
        echo "✅ Container built successfully"
    else
        echo "❌ Failed to build container"
        return 1
    fi
}

clean_container() {
    echo "🧹 Cleaning up Jina Crawler MCP Proxy container..."
    docker compose -f "$COMPOSE_FILE" down --rmi all --volumes
    
    if [ $? -eq 0 ]; then
        echo "✅ Container cleaned up successfully"
    else
        echo "❌ Failed to clean up container"
        return 1
    fi
}

# Main script logic
case "${1:-help}" in
    start)
        start_container
        ;;
    stop)
        stop_container
        ;;
    restart)
        restart_container
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    test)
        test_connectivity
        ;;
    health)
        check_health
        ;;
    build)
        build_container
        ;;
    clean)
        clean_container
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
