import argparse
import time
import random
import statistics

import numpy as np
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, SearchParams, PointStruct


def benchmark_dimension(client: QdrantClient, dimension: int, num_points: int, num_queries: int,
                        top_k: int, hnsw_ef: int, batch_size: int) -> None:
    collection_name = f"bench_dim{dimension}_{int(time.time())}"

    client.recreate_collection(
        collection_name=collection_name,
        vectors_config=VectorParams(size=dimension, distance=Distance.COSINE),
    )

    rng = np.random.default_rng(42)
    vectors = rng.random((num_points, dimension), dtype=np.float32)
    ids = list(range(num_points))

    for start in range(0, num_points, batch_size):
        end = min(start + batch_size, num_points)
        points = [
            PointStruct(id=int(ids[i]), vector=vectors[i].tolist())
            for i in range(start, end)
        ]
        client.upsert(collection_name=collection_name, points=points, wait=True)

    # Warm up
    for _ in range(min(10, num_queries)):
        idx = random.randrange(num_points)
        client.search(
            collection_name=collection_name,
            query_vector=vectors[idx].tolist(),
            limit=top_k,
            search_params=SearchParams(hnsw_ef=hnsw_ef),
        )

    timings_ms = []
    for _ in range(num_queries):
        idx = random.randrange(num_points)
        t0 = time.perf_counter()
        client.search(
            collection_name=collection_name,
            query_vector=vectors[idx].tolist(),
            limit=top_k,
            search_params=SearchParams(hnsw_ef=hnsw_ef),
        )
        t1 = time.perf_counter()
        timings_ms.append((t1 - t0) * 1000.0)

    timings_ms_sorted = sorted(timings_ms)
    p50 = statistics.median(timings_ms)
    p95 = timings_ms_sorted[max(0, int(0.95 * len(timings_ms_sorted)) - 1)]
    avg = statistics.mean(timings_ms)

    print(f"dim={dimension} n={num_points} q={num_queries} ef={hnsw_ef} top_k={top_k} p50_ms={p50:.2f} p95_ms={p95:.2f} avg_ms={avg:.2f}")

    try:
        client.delete_collection(collection_name)
    except Exception:
        pass


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--host", default="qdrant")
    parser.add_argument("--port", type=int, default=6333)
    parser.add_argument("--n", type=int, default=3000)
    parser.add_argument("--q", type=int, default=100)
    parser.add_argument("--ef", type=int, default=128)
    parser.add_argument("--top_k", type=int, default=10)
    parser.add_argument("--batch_size", type=int, default=500)
    args = parser.parse_args()

    client = QdrantClient(host=args.host, port=args.port, timeout=300)

    for dim in (768, 3072):
        benchmark_dimension(
            client=client,
            dimension=dim,
            num_points=args.n,
            num_queries=args.q,
            top_k=args.top_k,
            hnsw_ef=args.ef,
            batch_size=args.batch_size,
        )


if __name__ == "__main__":
    main()




