# 🚀 Hướng Dẫn Upload Pipeline - AccA Memory Integration

## ❌ Lỗi "No Function class found"

Bạn đang cố upload Pipeline vào section **Functions**. Đây là lỗi phổ biến!

**Pipeline ≠ Function** trong Open WebUI:
- **Functions**: Có class `Function` 
- **Pipelines**: Có class `Pipeline` ✅ (file của chúng ta)

## ✅ Cách Upload Đúng

### Bước 1: Vào Đúng Section
1. Mở **Open WebUI Admin Panel** 
2. Vào **Settings** → **Admin Settings**
3. **⚠️ CHỌN TAB "PIPELINES"** (không phải "Functions"!)
4. Click **"Upload Pipeline"**

### Bước 2: Upload File
- **File để upload**: `acca_mem0_pipeline_final.py`
- **Không upload vào Functions!**

### Bước 3: <PERSON><PERSON><PERSON> (<PERSON><PERSON>)
<PERSON><PERSON> <PERSON>hi upload thành công:

#### 🔧 **Core Settings**
- **Pipelines**: `["*"]` (áp dụng cho tất cả models)
- **Priority**: `0` (chạy trước các pipeline khác)

#### 🗄️ **Qdrant Settings** (đã tối ưu sẵn)
- **Host**: `localhost` 
- **Port**: `6333`
- **Collection**: `mem0_gemini_768`

#### 🤖 **Gemini API** (đã auto-config)
- **API Key**: Tự động lấy từ `GEMINI_API_KEY`
- **Model**: `gemini-2.5-flash`
- **Embedder**: `text-embedding-004`

#### 🧠 **Memory Behavior**
- **Max Memories**: `5` (số memories inject vào context)
- **Relevance Threshold**: `0.6` (đã giảm để recall tốt hơn)
- **Auto Store**: `true` (tự động lưu messages)
- **Debug Mode**: `false` (bật nếu cần troubleshoot)

### Bước 4: Enable Pipeline
1. Tìm **"AccA Memory Pipeline"** trong danh sách
2. Toggle **Enable** 
3. Đảm bảo **Priority = 0** (chạy đầu tiên)

## 🧪 Test Hoạt Động

### Test Cơ Bản
1. Tạo conversation mới
2. Nói: *"Xin chào, tôi tên David và tôi làm việc tại MobiFone"*
3. AI phản hồi bình thường
4. Trong conversation khác hoặc sau này, nói: *"Bạn còn nhớ tôi làm ở đâu không?"*
5. AI sẽ nhớ MobiFone! 🎉

### Debug Mode
Nếu không hoạt động:
1. Vào **Pipeline Settings** → **Valves**
2. Bật **Debug Mode**: `true`
3. Check **Open WebUI logs** để xem debug messages

## 🔧 Troubleshooting

### "No Function class found"
- ✅ **Giải pháp**: Upload vào **Pipelines**, không phải Functions

### "Pipeline không xuất hiện"
- Check **Admin Panel** → **Pipelines** tab
- Đảm bảo file upload thành công
- Refresh browser

### "Memory không hoạt động"
1. Enable **Debug Mode** trong valves
2. Check logs:
   - `[AccA-Memory] Pipeline initialized with memory support` ✅
   - `[AccA-Memory-ERROR]` ❌ (check Qdrant/Gemini API)

### "Lỗi Dependencies"
- File final đã handle gracefully
- Sẽ chạy mà không crash ngay cả khi thiếu dependencies
- Check logs để xem status

## 📋 Files Summary

| File | Mục đích | Status |
|------|----------|---------|
| `acca_mem0_pipeline_final.py` | **✅ UPLOAD FILE NÀY** | Production ready |
| `mem0_owui_no_requirements.py` | Alternative version | Backup |
| `mem0_owui_custom_pipeline.py` | Original version | Development |

## 🎯 Expected Results

### ✅ Success Indicators
- Pipeline xuất hiện trong **Pipelines** list
- Status: **Enabled** 
- Logs: `[AccA-Memory] Pipeline initialized with memory support`
- Memory injection hoạt động trong conversations

### 🎉 Final Success
Khi thành công, bạn sẽ có:
- 🧠 **AI nhớ thông tin** qua nhiều conversations
- 🔄 **Context dài hạn** - AI biết bạn là ai, làm gì
- 🎯 **Personalized responses** dựa trên lịch sử chat
- 🚀 **Seamless experience** - hoàn toàn tự động

---

## 📞 Support

Nếu vẫn gặp vấn đề:
1. Check **Qdrant** running: `curl http://localhost:6333/`
2. Check **Gemini API**: `echo $GEMINI_API_KEY`
3. Enable **Debug Mode** và check logs
4. Thử restart **Open WebUI** service

**File upload chính**: `acca_mem0_pipeline_final.py` 🚀 