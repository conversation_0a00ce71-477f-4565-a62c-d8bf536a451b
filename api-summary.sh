#!/bin/bash

echo "🔑 Qwen2.5-Coder API Access Summary"
echo "==================================="

# Read the latest API key
LATEST_KEY=$(tail -1 /opt/llama-cpp/api-keys.txt 2>/dev/null | awk '{print $5}')
PUBLIC_IP=$(curl -s ifconfig.me)

echo ""
echo "🌐 Server Information:"
echo "   • Public IP: $PUBLIC_IP"
echo "   • Local IP: $(hostname -I | awk '{print $1}')"

echo ""
echo "🔑 API Credentials:"
echo "   • API Key: $LATEST_KEY"
echo "   • Base URL: http://$PUBLIC_IP:11435/v1"
echo "   • Model: qwen2.5-coder-7b-instruct"

echo ""
echo "🧪 Connection Tests:"

# Test local
if curl -s http://localhost:11435/v1/models >/dev/null 2>&1; then
    echo "   ✅ Local (localhost:11435) - Working"
else
    echo "   ❌ Local (localhost:11435) - Failed"
fi

# Test external (from server)
echo "   🔍 Testing external access..."
timeout 10 curl -s http://$PUBLIC_IP:11435/v1/models >/dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "   ✅ External ($PUBLIC_IP:11435) - Working"
else
    echo "   ⚠️  External ($PUBLIC_IP:11435) - May need cloud firewall config"
fi

echo ""
echo "📋 Usage Examples:"

echo ""
echo "🐍 Python (với OpenAI client):"
cat << EOF
import openai

client = openai.OpenAI(
    base_url="http://$PUBLIC_IP:11435/v1",
    api_key="$LATEST_KEY"
)

response = client.chat.completions.create(
    model="qwen2.5-coder-7b-instruct",
    messages=[
        {"role": "user", "content": "Write a Python function to calculate factorial"}
    ],
    max_tokens=500,
    temperature=0.3
)

print(response.choices[0].message.content)
EOF

echo ""
echo "📱 cURL Command:"
cat << EOF
curl -X POST http://$PUBLIC_IP:11435/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer $LATEST_KEY" \\
  -d '{
    "model": "qwen2.5-coder-7b-instruct",
    "messages": [
      {"role": "user", "content": "Hello from external client!"}
    ],
    "max_tokens": 100,
    "temperature": 0.3
  }'
EOF

echo ""
echo ""
echo "🔧 Advanced Usage:"

echo ""
echo "🛠️ Function Calling (Tool Usage):"
cat << EOF
{
  "model": "qwen2.5-coder-7b-instruct",
  "messages": [
    {"role": "user", "content": "Calculate 15 + 27"}
  ],
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "calculate",
        "description": "Perform basic math calculations",
        "parameters": {
          "type": "object",
          "properties": {
            "expression": {"type": "string"}
          }
        }
      }
    }
  ],
  "max_tokens": 200
}
EOF

echo ""
echo "📝 Code Completion:"
cat << EOF
{
  "model": "qwen2.5-coder-7b-instruct", 
  "messages": [
    {"role": "user", "content": "Complete this function:\\n\\ndef fibonacci(n):\\n    # Complete the implementation"}
  ],
  "max_tokens": 300,
  "temperature": 0.1
}
EOF

echo ""
echo "🔒 Security Notes:"
echo "   • API key lưu tại: /opt/llama-cpp/api-keys.txt"
echo "   • Revoke key: Xóa dòng tương ứng trong file"
echo "   • Monitor logs: tail -f /tmp/qwen-7b.log"

echo ""
echo "⚠️  Cloud Provider Setup:"
echo "   • Oracle Cloud: Security Lists → Add Ingress Rule port 11435"
echo "   • AWS: Security Groups → Add Inbound Rule port 11435"
echo "   • GCP: Firewall Rules → Add Rule for port 11435"

echo ""
echo "🎯 Model Features:"
echo "   • Code generation (92+ languages)"
echo "   • Function/Tool calling"
echo "   • Repository-level understanding"
echo "   • Fill-in-middle completion"
echo "   • 32K context window"
echo "   • ARM64 optimized performance" 