# 🚀 Hướng dẫn tích hợp Jini Crawler với Open WebUI

## ✅ Trạng thái hiện tại
- **Server**: `jini_crawler` đang chạy trên port `8009`
- **Status**: ✅ Tương thích hoàn toàn với Open WebUI
- **OpenAPI**: ✅ Đã cấu hình đúng format mcpo
- **Tools**: 9 công cụ crawling mạnh mẽ sẵn sàng sử dụng

## 🔗 Tích hợp với Open WebUI

### Bước 1: Thêm OpenAPI Server
1. Mở Open WebUI admin panel
2. Đi tới **Settings** → **External API**
3. Thêm server mới:
   - **Name**: `jini_crawler`
   - **URL**: `http://localhost:8009`
   - **Type**: `OpenAPI Server`

### Bước 2: Verify Connection
1. Click **Test Connection** để kiểm tra
2. Bạn sẽ thấy thông báo: ✅ "Connection successful"
3. Server sẽ hiển thị: `jina_crawler MCP Server - Advanced web crawler with AI processing`

### Bước 3: Refresh Tools
1. Đi tới **Tools** section
2. Click **Refresh Tools** 
3. Bạn sẽ thấy 9 tools mới của jini_crawler:
   - 🌐 `crawl_url` - Smart web crawler with AI summarization
   - 📰 `crawl_url_full_article` - Full article extraction with paywall bypass
   - 🔍 `search_google` - Google search with result processing
   - 🧠 `ai_search` - AI-powered search with smart results
   - 📊 `ai_search_streaming` - Streaming AI search results
   - 🎯 `smart_crawl` - Smart crawling with content optimization
   - 📈 `get_trending_topics` - Get trending topics from various sources
   - ❤️ `health_check` - Server health monitoring
   - 📋 `get_crawler_stats` - Crawler performance statistics

## 🎯 Cách sử dụng

### Trong Chat
Bây giờ bạn có thể sử dụng các lệnh như:
```
@jini_crawler crawl_url https://example.com
@jini_crawler search_google "AI technology 2024"
@jini_crawler ai_search "latest news about Tesla"
```

### Auto-detection
Open WebUI sẽ tự động gợi ý sử dụng jini_crawler khi:
- Bạn đề cập đến URLs
- Bạn yêu cầu search thông tin
- Bạn cần crawl web content

## 🔧 API Endpoints Available

| Endpoint | Description |
|----------|-------------|
| `GET /` | Server information |
| `GET /health` | Health check |
| `GET /openapi.json` | OpenAPI schema |
| `GET /docs` | Swagger UI documentation |
| `POST /crawl_url` | Crawl và summarize URL |
| `POST /crawl_url_full_article` | Extract full article với paywall bypass |
| `POST /search_google` | Google search |
| `POST /ai_search` | AI-powered search |
| `POST /smart_crawl` | Smart crawling |

## 🛠️ Troubleshooting

### Nếu tools không hiển thị:
1. Kiểm tra server status: `curl http://localhost:8009/health`
2. Verify OpenAPI schema: `curl http://localhost:8009/openapi.json`
3. Restart jini_crawler container: `docker restart jina-crawler-mcp`
4. Refresh Open WebUI tools

### Nếu connection failed:
1. Đảm bảo server đang chạy: `docker ps | grep 8009`
2. Kiểm tra firewall/network settings
3. Test bằng browser: `http://localhost:8009/docs`

## 📊 Test Script
Chạy script test để verify integration:
```bash
python3 test_jini_crawler_openwebui_integration.py
```

## 🎉 Kết quả mong đợi
Sau khi tích hợp thành công:
- ✅ 9 jini_crawler tools xuất hiện trong Open WebUI
- ✅ Auto-suggestion hoạt động khi mention URLs
- ✅ Crawling và search functions hoạt động smoothly
- ✅ Real-time streaming results cho AI search

---

**🔥 jini_crawler hiện đã sẵn sàng để sử dụng với Open WebUI!**
**Enjoy powerful web crawling và AI search capabilities!** 🚀