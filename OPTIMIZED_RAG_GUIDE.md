# 🚀 Hướng Dẫn Tối Ưu RAG cho Open WebUI

**Dựa trên bài viết Medium**: [Open WebUI tutorial — Supercharge Your Local AI with RAG and Custom Knowledge Bases](https://medium.com/@hautel.alex2000/open-webui-tutorial-supercharging-your-local-ai-with-rag-and-custom-knowledge-bases-334d272c8c40)

## 📋 Tổng Quan Cải Tiến

Phiên bản tối ưu này bao gồm những cải tiến quan trọng so với setup RAG cơ bản:

### 🎯 **Những Tối Ưu Chính**

| Thành Phần | Cũ | Mới (Tối Ưu) | Lý Do |
|------------|----|--------------|----- |
| **Document Processing** | Basic extraction | Apache Tika | Xử lý tốt hơn PDF, DOCX, PPT |
| **Text Splitting** | Character-based | Token-based (Tiktoken) | Chính xác hơn với LLM |
| **Chunk <PERSON>ze** | 1000 chars | 500 tokens | Cân bằng tối ưu context/precision |
| **Embedding Model** | all-MiniLM-L6-v2 | nomic-embed-text | Performance tốt hơn |
| **Search Method** | Dense only | Hybrid (Dense + Sparse) | Kết hợp semantic + keyword |
| **Re-ranking** | Không có | BGE re-ranker-v2-m3 | Cải thiện relevance |
| **Top K** | 5 | 20 → 10 (sau re-rank) | Coverage tốt hơn |

## 🏗️ Kiến Trúc Hệ Thống

```mermaid
graph TD
    A[User Query] --> B[Open WebUI]
    B --> C[Apache Tika<br/>Document Processing]
    B --> D[Hybrid Search]
    
    D --> E[Dense Search<br/>Vector Similarity]
    D --> F[Sparse Search<br/>Keyword Matching]
    
    E --> G[Top 20 Results]
    F --> G
    
    G --> H[BGE Re-ranker<br/>Relevance Scoring]
    H --> I[Top 10 Final Results]
    
    I --> J[LLM Context]
    J --> K[Generated Response]
    
    style C fill:#e1f5fe
    style H fill:#f3e5f5
    style I fill:#e8f5e8
```

## 🚀 Triển Khai Nhanh

### Bước 1: Chạy Script Tự Động
```bash
# Cấp quyền thực thi
chmod +x setup_optimized_rag.sh

# Chạy script setup
./setup_optimized_rag.sh
```

### Bước 2: Cấu Hình Manual (Nếu Cần)

Nếu script tự động không hoạt động, hãy làm theo các bước sau:

#### 2.1 Khởi Động Services
```bash
docker-compose down
docker-compose up -d
```

#### 2.2 Cài Đặt Embedding Model
```bash
ollama pull nomic-embed-text:latest
```

## ⚙️ Cấu Hình Chi Tiết trong Open WebUI

### 📁 **Document Settings** (Settings → Admin Settings → Documents)

```yaml
Content Extraction:
  Engine: "Tika"
  Tika URL: "http://tika:9998"

Text Splitting:
  Splitter: "Token (Tiktoken)"  # ✅ Token-based thay vì Character
  Chunk Size: 500               # ✅ Tối ưu cho LLM
  Chunk Overlap: 100            # ✅ 20% overlap
```

### 🎯 **Embedding Settings**

```yaml
Embedding:
  Engine: "Ollama"
  Model: "nomic-embed-text:latest"  # ✅ Tốt hơn all-MiniLM-L6-v2
  Batch Size: 1
```

### 🔍 **Retrieval Settings**

```yaml
Hybrid Search: ✅ Enabled       # ✅ Kết hợp Dense + Sparse
Top K: 20                       # ✅ Tăng coverage ban đầu
Top K Reranker: 10             # ✅ Lọc lại với re-ranker
Relevance Threshold: 0.1        # ✅ Cân bằng precision/recall
Re-ranking Model: "BAAI/bge-reranker-v2-m3"  # ✅ State-of-the-art
```

## 📚 Tạo Knowledge Base Tối Ưu

### Bước 1: Chuẩn Bị Dữ Liệu

**💡 Theo Best Practices từ Medium:**

```bash
# Tạo thư mục organized documents
mkdir -p knowledge_base/{
  company_docs,
  technical_manuals,
  policies,
  faqs
}

# Ví dụ: Tải Open WebUI docs làm test case
wget https://github.com/open-webui/docs/archive/refs/heads/main.zip
unzip main.zip
# Sử dụng docs-main/docs/ làm knowledge base
```

### Bước 2: Tạo Knowledge Base

1. **Workspace** → **Knowledge** → **+ Create Knowledge Base**
2. **Name**: `Optimized Knowledge Base`
3. **Purpose**: `Enhanced RAG with Tika + Hybrid Search`
4. **Upload**: Chọn thư mục docs đã chuẩn bị

### Bước 3: Tạo Custom Model

1. **Workspace** → **Models** → **+ Add New Model**
2. **Name**: `GPT-4 + Optimized RAG`
3. **Base Model**: Chọn model 7B-14B parameters
4. **Knowledge Source**: Chọn knowledge base đã tạo
5. **System Prompt**: Sử dụng template tối ưu

## 🎯 System Prompt Tối Ưu

```markdown
You are a specialized AI assistant with access to curated knowledge sources through an optimized RAG system.

## Response Guidelines:
1. **Use retrieved context EXCLUSIVELY** - Never rely on general knowledge when documentation is available
2. **Always cite sources** with both document title and relevance score
3. **State limitations clearly** when information is not found in the knowledge base
4. **Prioritize accuracy over completeness** - Better to acknowledge gaps than provide uncertain information

## Context Usage:
- Analyze retrieved chunks by relevance score (0.8+ = high confidence)
- Cross-reference multiple sources when available
- Highlight conflicting information if found

## Citation Format:
<<Sources: [Document Title](Relevance: 0.XX), [Document Title](Relevance: 0.XX)>>

Remember: Your responses are grounded in retrieved evidence, not general training data.
```

## 📊 Monitoring & Optimization

### Kiểm Tra Performance

```bash
# Kiểm tra services status
docker-compose ps

# Test Tika service
curl http://localhost:9998/version

# Test Open WebUI
curl http://localhost:3001/api/health
```

### Điều Chỉnh Tham Số Theo Use Case

| Use Case | Chunk Size | Top K | Relevance Threshold |
|----------|------------|-------|-------------------- |
| **Technical Docs** | 300 tokens | 25 | 0.15 |
| **General Knowledge** | 500 tokens | 20 | 0.1 |
| **Legal Documents** | 200 tokens | 30 | 0.2 |
| **Research Papers** | 800 tokens | 15 | 0.1 |

## 🚨 Troubleshooting

### Vấn Đề Thường Gặp

**1. Tika không khởi động**
```bash
# Kiểm tra logs
docker logs tika

# Restart service
docker-compose restart tika
```

**2. Embedding model không tải được**
```bash
# Kiểm tra Ollama
ollama list
ollama pull nomic-embed-text:latest
```

**3. Re-ranking chậm**
```bash
# Giảm Top K nếu performance kém
# Trong UI: Top K từ 20 → 15
# Top K Reranker từ 10 → 8
```

## 🎉 Kết Quả Mong Đợi

Sau khi setup xong, bạn sẽ có:

✅ **Độ chính xác cao hơn** - Hybrid search + re-ranking  
✅ **Xử lý document tốt hơn** - Apache Tika  
✅ **Chunking thông minh** - Token-based  
✅ **Embedding quality cao** - Nomic model  
✅ **Response có citation** - Truy vết nguồn  
✅ **Performance tối ưu** - Cấu hình balanced  

## 📖 Tài Liệu Tham Khảo

- [Medium Article - Original Guide](https://medium.com/@hautel.alex2000/open-webui-tutorial-supercharging-your-local-ai-with-rag-and-custom-knowledge-bases-334d272c8c40)
- [Open WebUI RAG Documentation](https://docs.openwebui.com/tutorials/tips/rag-tutorial/)
- [MTEB Leaderboard - Embedding Models](https://huggingface.co/spaces/mteb/leaderboard)
- [BGE Re-ranking Models](https://huggingface.co/BAAI/bge-reranker-v2-m3)

---

**💡 Pro Tip**: Bắt đầu với cấu hình mặc định, sau đó fine-tune dần theo specific use case của bạn! 