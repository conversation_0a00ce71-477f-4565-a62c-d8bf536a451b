# 🚀 Hướng dẫn tạo MCP OpenAPI Proxy cho Open WebUI

## 📋 Tổng quan

**MCP OpenAPI Proxy (MCPO)** là pattern để expose MCP tools dưới dạng HTTP API endpoints, cho phép Open WebUI và các LLM có thể sử dụng tools một cách dễ dàng.

## 🎯 Tại sao cần MCPO?

### Vấn đề với MCP trực tiếp:
- Open WebUI không thể gọi MCP protocol trực tiếp
- LLM cần HTTP endpoints để sử dụng tools
- MCP servers thường chạy riêng biệt, khó integrate

### Giải pháp MCPO:
- Chuyển đổi MCP calls thành HTTP API calls
- Cung cấp OpenAPI specifications cho tools
- Container-based deployment dễ quản lý
- Tương thích với mọi LLM trong Open WebUI

## 🏗️ Kiến trúc MCPO

```mermaid
graph LR
    A[Open WebUI] --> B[MCPO Container]
    B --> C[Tool Functions]
    B --> D[OpenAPI Specs]
    C --> E[Actual Implementation]
    
    subgraph "MCPO Container"
        B
        C
        D
    end
```

## 📝 Cấu trúc cơ bản

### 1. FastAPI Server Structure

```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

app = FastAPI(
    title="MCP OpenAPI Proxy",
    description="Automatically generated API from MCP Tool Schemas",
    version="1.0"
)

# CORS middleware - QUAN TRỌNG!
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### 2. Root Endpoint Pattern

```python
@app.get("/")
async def root():
    return {
        "openapi": "3.1.0",
        "info": {
            "title": "MCP OpenAPI Proxy",
            "description": "Available tools:\n- [tool_name](/tool_name/docs)",
            "version": "1.0"
        },
        "paths": {}
    }
```

### 3. Tool Documentation Endpoint

```python
@app.get("/tool_name/docs")
async def tool_docs():
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <link type="text/css" rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css">
        <title>tool_name - Swagger UI</title>
    </head>
    <body>
        <div id="swagger-ui"></div>
        <script src="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js"></script>
        <script>
        const ui = SwaggerUIBundle({
            url: '/tool_name/openapi.json',
            "dom_id": "#swagger-ui",
            "layout": "BaseLayout",
            "deepLinking": true
        })
        </script>
    </body>
    </html>
    """
```

## 🔧 OpenAPI Specification Pattern

### Cấu trúc OpenAPI chuẩn:

```python
@app.get("/tool_name/openapi.json")
async def tool_openapi():
    return {
        "openapi": "3.1.0",  # Quan trọng: phải là 3.1.0
        "info": {
            "title": "tool_name",
            "description": "Tool description with usage instructions",
            "version": "1.0.0"
        },
        "paths": {
            "/function_name": {
                "post": {
                    "summary": "Function summary",
                    "description": "When to use this function",
                    "operationId": "function_name",  # QUAN TRỌNG!
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "param1": {
                                            "type": "string",
                                            "description": "Parameter description",
                                            "example": "example_value"
                                        }
                                    },
                                    "required": ["param1"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Success response",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "success": {"type": "boolean"},
                                            "result": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
```

## 🎨 Tool Implementation Pattern

### Request Models:

```python
from pydantic import BaseModel
from typing import Optional, List

class ToolRequest(BaseModel):
    param1: str
    param2: Optional[int] = 100
    param3: Optional[List[str]] = []
```

### Tool Endpoints:

```python
@app.post("/tool_name/function_name")
async def function_name(request: ToolRequest):
    """Tool function implementation"""
    try:
        # Actual implementation here
        result = process_request(request)
        
        return {
            "success": True,
            "result": result,
            "processing_time": 1.5
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "result": None
        }
```

## 🐳 Container Deployment

### Dockerfile Pattern:

```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
RUN pip install --no-cache-dir fastapi uvicorn pydantic

# Copy server code
COPY mcpo_server.py .

# Expose port
EXPOSE 8002

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8002/health || exit 1

# Start command
CMD ["python", "mcpo_server.py"]
```

### Docker Compose Pattern:

```yaml
version: '3.8'

services:
  tool-mcpo:
    build:
      context: .
      dockerfile: Dockerfile.mcpo
    container_name: tool-mcpo-8002
    ports:
      - "8002:8002"
    networks:
      - acca-network
      - unified-mcpo-network
      - gemini-network
    environment:
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

networks:
  acca-network:
    external: true
  unified-mcpo-network:
    external: true
  gemini-network:
    external: true
```

## 📋 Checklist tạo MCPO

### ✅ Server Requirements:
- [ ] FastAPI với CORS middleware
- [ ] Health endpoint (`/health`)
- [ ] Root endpoint với tool list
- [ ] Tool docs endpoint (`/tool_name/docs`)
- [ ] OpenAPI spec endpoint (`/tool_name/openapi.json`)
- [ ] Tool function endpoints (`/tool_name/function_name`)

### ✅ OpenAPI Requirements:
- [ ] OpenAPI version 3.1.0
- [ ] Clear tool descriptions
- [ ] operationId cho mỗi function
- [ ] Request/response schemas
- [ ] Examples trong parameters
- [ ] Usage instructions trong descriptions

### ✅ Container Requirements:
- [ ] Dockerfile với health check
- [ ] Docker compose với networks
- [ ] Port mapping (8002 recommended)
- [ ] Restart policy
- [ ] Environment variables

### ✅ Integration Requirements:
- [ ] Container trong cùng Docker networks với Open WebUI
- [ ] Test connectivity từ Open WebUI container
- [ ] OpenAPI endpoint accessible
- [ ] Tool endpoints working

## 🧪 Testing Pattern

### Test Script Template:

```bash
#!/bin/bash

CONTAINER_NAME="tool-mcpo-8002"
BASE_URL="http://localhost:8002"

echo "🧪 Testing MCPO Container"
echo "========================="

# Test 1: Health check
echo "1. Health check..."
curl -s "$BASE_URL/health" | python3 -m json.tool

# Test 2: OpenAPI spec
echo "2. OpenAPI spec..."
curl -s "$BASE_URL/tool_name/openapi.json" | head -20

# Test 3: Tool function
echo "3. Tool function..."
curl -X POST "$BASE_URL/tool_name/function_name" \
  -H "Content-Type: application/json" \
  -d '{"param1": "test_value"}'

# Test 4: Container connectivity
echo "4. Container connectivity..."
docker exec open-webui-container curl -s "http://$CONTAINER_NAME:8002/health"
```

## 🎯 Best Practices

### 1. **Naming Conventions:**
- Container: `tool-name-mcpo-PORT`
- Endpoints: `/tool_name/function_name`
- operationId: `function_name`

### 2. **Error Handling:**
```python
try:
    result = process_request(request)
    return {"success": True, "result": result}
except Exception as e:
    return {"success": False, "error": str(e)}
```

### 3. **Response Format:**
```python
{
    "success": boolean,
    "result": any,
    "error": string (if failed),
    "processing_time": float,
    "metadata": object (optional)
}
```

### 4. **Description Guidelines:**
- **Tool description**: Khi nào LLM nên sử dụng tool
- **Function description**: Cụ thể function làm gì
- **Parameter description**: Ý nghĩa và format của parameter
- **Examples**: Giá trị mẫu thực tế

### 5. **Network Configuration:**
- Luôn join các networks chung với Open WebUI
- Test connectivity từ Open WebUI container
- Sử dụng container hostname cho internal calls

## 🚀 Deployment Workflow

### 1. **Development:**
```bash
# Create MCPO server
python mcpo_server.py

# Test locally
curl http://localhost:8002/health
```

### 2. **Containerization:**
```bash
# Build container
docker build -f Dockerfile.mcpo -t tool-mcpo .

# Run container
docker run -d --name tool-mcpo-8002 -p 8002:8002 tool-mcpo
```

### 3. **Integration:**
```bash
# Test from Open WebUI container
docker exec open-webui curl http://tool-mcpo-8002:8002/health

# Add to Open WebUI
# URL: http://tool-mcpo-8002:8002/tool_name/openapi.json
```

### 4. **Verification:**
```bash
# Test with LLM prompt
"Use the tool_name to process this request: [request]"
```

## 📚 Examples

### Minimal MCPO Example:
```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

app = FastAPI(title="Simple MCPO")
app.add_middleware(CORSMiddleware, allow_origins=["*"])

class SimpleRequest(BaseModel):
    text: str

@app.get("/health")
async def health():
    return {"status": "healthy"}

@app.get("/simple_tool/openapi.json")
async def openapi():
    return {
        "openapi": "3.1.0",
        "info": {"title": "simple_tool", "version": "1.0.0"},
        "paths": {
            "/process": {
                "post": {
                    "operationId": "process_text",
                    "summary": "Process text",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "text": {"type": "string"}
                                    },
                                    "required": ["text"]
                                }
                            }
                        }
                    }
                }
            }
        }
    }

@app.post("/simple_tool/process")
async def process_text(request: SimpleRequest):
    return {
        "success": True,
        "result": f"Processed: {request.text}",
        "length": len(request.text)
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
```

## 🎉 Kết luận

MCPO pattern cho phép:
- ✅ Dễ dàng expose MCP tools cho Open WebUI
- ✅ Standardized API format
- ✅ Container-based deployment
- ✅ Scalable và maintainable
- ✅ Compatible với mọi LLM

**Với pattern này, bạn có thể tạo MCPO cho bất kỳ tool nào và integrate với Open WebUI một cách dễ dàng!**

## 🛠️ Quick Start với Template

### Sử dụng MCPO Generator:

```bash
# Tạo project mới
./create_mcpo_project.sh my_tool 8003

# Customize tool functions
vim my_tool_mcpo_server.py

# Build và deploy
./manage_my_tool_mcpo.sh build
./manage_my_tool_mcpo.sh start

# Test
./manage_my_tool_mcpo.sh test
```

### Files được tạo tự động:
- `my_tool_mcpo_server.py` - Server code
- `Dockerfile.my_tool-mcpo` - Container definition
- `docker-compose.my_tool-mcpo.yml` - Deployment config
- `manage_my_tool_mcpo.sh` - Management script
- `README_my_tool_mcpo.md` - Documentation

## 📚 Tài liệu tham khảo

### Templates có sẵn:
- `mcpo_template.py` - Base template
- `create_mcpo_project.sh` - Project generator
- `jina_crawler_mcp_proxy_server.py` - Working example

### Ví dụ thực tế:
- **Jina Crawler MCPO**: Container `jina-crawler-mcp-proxy-8002`
- **URL**: `http://jina-crawler-mcp-proxy-8002:8002/jina_crawler/openapi.json`
- **Tools**: crawl_url, search_web, crawl_batch

## 🎯 Troubleshooting

### Common Issues:
1. **LLM không thấy tool**: Kiểm tra OpenAPI spec format
2. **Container không kết nối**: Verify Docker networks
3. **Tool không hoạt động**: Check operationId và descriptions
4. **CORS errors**: Ensure CORS middleware configured

### Debug Commands:
```bash
# Check container
docker ps | grep mcpo

# Test connectivity
docker exec open-webui-mcpo curl http://tool-mcpo:8002/health

# View OpenAPI spec
curl http://localhost:8002/tool_name/openapi.json | jq
```
