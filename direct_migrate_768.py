#!/usr/bin/env python3
"""
Direct Migration Script: Copy memories without re-embedding
Create 768-dim collection and copy data structure from 3072-dim collection
"""

import requests
import json
import time
from typing import List, Dict, Any

# Configuration
QDRANT_HOST = "qdrant"
QDRANT_PORT = 6333

# Source collection (3072 dimensions)
SOURCE_COLLECTION = "mem0_gemini_3072_fixed"

# Target collection (768 dimensions)
TARGET_COLLECTION = "mem0_gemini_gemi_768"

def create_target_collection():
    """Create target collection with 768 dimensions"""
    print(f"🔧 Creating target collection: {TARGET_COLLECTION}")
    
    collection_config = {
        "vectors": {
            "size": 768,
            "distance": "Cosine"
        },
        "optimizers_config": {
            "default_segment_number": 2
        },
        "replication_factor": 1
    }
    
    try:
        response = requests.put(
            f"http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{TARGET_COLLECTION}",
            json=collection_config
        )
        
        if response.status_code in [200, 201]:
            print(f"✅ Created collection: {TARGET_COLLECTION}")
            return True
        else:
            print(f"❌ Failed to create collection: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error creating collection: {e}")
        return False

def get_collection_info(collection_name: str) -> Dict[str, Any]:
    """Get collection information"""
    try:
        response = requests.get(f"http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{collection_name}")
        if response.status_code == 200:
            return response.json()["result"]
        else:
            return None
    except Exception as e:
        print(f"❌ Error getting collection info: {e}")
        return None

def export_memories_batch(collection_name: str, offset: str = None, limit: int = 100) -> tuple:
    """Export memories in batches"""
    try:
        payload = {
            "limit": limit,
            "with_payload": True,
            "with_vector": False
        }
        
        if offset:
            payload["offset"] = offset
        
        response = requests.post(
            f"http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{collection_name}/points/scroll",
            json=payload
        )
        
        if response.status_code != 200:
            print(f"❌ Error scrolling collection: {response.status_code}")
            return [], None
        
        data = response.json()
        points = data["result"]["points"]
        next_page_offset = data["result"].get("next_page_offset")
        
        memories = []
        for point in points:
            payload_data = point["payload"]
            
            # Extract memory content
            content = payload_data.get("content", "")
            if content:
                memory_data = {
                    "id": point["id"],
                    "content": content,
                    "user_id": payload_data.get("user_id", "default_user"),
                    "session_id": payload_data.get("session_id", ""),
                    "timestamp": payload_data.get("timestamp", ""),
                    "dimensions": 768  # Update to new dimensions
                }
                memories.append(memory_data)
        
        return memories, next_page_offset
        
    except Exception as e:
        print(f"❌ Error exporting batch: {e}")
        return [], None

def create_dummy_vector(size: int = 768) -> List[float]:
    """Create a dummy vector for placeholder"""
    import random
    return [random.uniform(-1, 1) for _ in range(size)]

def import_memories_batch(memories: List[Dict[str, Any]]) -> int:
    """Import memories to target collection"""
    if not memories:
        return 0
    
    points = []
    for memory in memories:
        # Create point with dummy vector (will be replaced by proper embedding later)
        point = {
            "id": memory["id"],
            "vector": create_dummy_vector(768),
            "payload": {
                "content": memory["content"],
                "user_id": memory["user_id"],
                "session_id": memory["session_id"],
                "timestamp": memory["timestamp"],
                "dimensions": memory["dimensions"],
                "migrated_from": SOURCE_COLLECTION,
                "needs_reembedding": True  # Flag for later re-embedding
            }
        }
        points.append(point)
    
    try:
        response = requests.put(
            f"http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{TARGET_COLLECTION}/points",
            json={"points": points}
        )
        
        if response.status_code == 200:
            return len(points)
        else:
            print(f"❌ Error importing batch: {response.status_code} - {response.text}")
            return 0
            
    except Exception as e:
        print(f"❌ Error importing batch: {e}")
        return 0

def migrate_all_memories():
    """Migrate all memories from source to target collection"""
    print(f"🔄 Starting migration: {SOURCE_COLLECTION} -> {TARGET_COLLECTION}")
    
    # Check source collection
    source_info = get_collection_info(SOURCE_COLLECTION)
    if not source_info:
        print(f"❌ Source collection {SOURCE_COLLECTION} not found")
        return False
    
    total_points = source_info["points_count"]
    print(f"📦 Source collection: {total_points} points")
    
    # Create target collection
    if not create_target_collection():
        return False
    
    # Migrate in batches
    total_migrated = 0
    offset = None
    batch_size = 100
    
    while True:
        print(f"📤 Exporting batch (offset: {offset})...")
        memories, next_offset = export_memories_batch(SOURCE_COLLECTION, offset, batch_size)
        
        if not memories:
            break
        
        print(f"📥 Importing {len(memories)} memories...")
        imported = import_memories_batch(memories)
        total_migrated += imported
        
        print(f"✅ Migrated {total_migrated}/{total_points} memories")
        
        if not next_offset:
            break
            
        offset = next_offset
        time.sleep(0.1)  # Small delay to avoid overwhelming the system
    
    return total_migrated

def verify_migration():
    """Verify migration results"""
    print(f"\n📊 Verifying migration results...")
    
    # Check target collection
    target_info = get_collection_info(TARGET_COLLECTION)
    if target_info:
        print(f"✅ Target collection: {target_info['points_count']} points, {target_info['config']['params']['vectors']['size']} dims")
        
        # Get a sample point to verify structure
        try:
            response = requests.post(
                f"http://{QDRANT_HOST}:{QDRANT_PORT}/collections/{TARGET_COLLECTION}/points/scroll",
                json={"limit": 1, "with_payload": True, "with_vector": False}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data["result"]["points"]:
                    sample = data["result"]["points"][0]
                    print(f"📋 Sample migrated point:")
                    print(f"   - ID: {sample['id']}")
                    print(f"   - User ID: {sample['payload'].get('user_id', 'N/A')}")
                    print(f"   - Content length: {len(sample['payload'].get('content', ''))}")
                    print(f"   - Needs re-embedding: {sample['payload'].get('needs_reembedding', False)}")
        except Exception as e:
            print(f"❌ Error getting sample: {e}")
    else:
        print(f"❌ Target collection not found")

def main():
    """Main migration process"""
    print("🚀 Direct Memory Migration (3072D -> 768D)")
    print("⚠️  Note: This creates placeholder vectors. Re-embedding needed later.")
    print(f"📍 Qdrant: {QDRANT_HOST}:{QDRANT_PORT}")
    
    # Run migration
    total_migrated = migrate_all_memories()
    
    if total_migrated > 0:
        print(f"\n🎉 Migration completed!")
        print(f"📊 Total memories migrated: {total_migrated}")
        
        # Verify results
        verify_migration()
        
        print(f"\n💡 Next steps:")
        print(f"   1. Deploy the new 768D pipeline")
        print(f"   2. The pipeline will automatically re-embed memories with proper vectors")
        print(f"   3. Test the new configuration")
        print(f"   4. Monitor performance improvements")
        
        return True
    else:
        print("❌ Migration failed")
        return False

if __name__ == "__main__":
    main()
