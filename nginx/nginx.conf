events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=upload:10m rate=2r/s;
    limit_req_zone $binary_remote_addr zone=models:10m rate=5r/s;

    # Upstream servers
    upstream enhanced_backend {
        server enhanced-backend:8000;
    }

    upstream llama_server {
        server llama-server:11434;
    }

    upstream open_webui {
        server open-webui:8080;
    }

    upstream multi_model_gateway {
        server multi-model-gateway:8000;
    }

    upstream model_manager {
        server model-manager:8000;
    }

    # Main server block
    server {
        listen 80;
        server_name _;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";

        # Open WebUI (Main interface)
        location / {
            proxy_pass http://open_webui;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket support
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # Enhanced Backend API
        location /api/v1/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://enhanced_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts for AI processing
            proxy_connect_timeout 120s;
            proxy_send_timeout 120s;
            proxy_read_timeout 300s;
        }

        # Enhanced RAG endpoints
        location /enhanced-rag/ {
            limit_req zone=upload burst=5 nodelay;
            
            proxy_pass http://enhanced_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Extended timeouts for document processing
            proxy_connect_timeout 180s;
            proxy_send_timeout 180s;
            proxy_read_timeout 600s;
        }

        # Model Manager API
        location /model-manager/ {
            limit_req zone=models burst=10 nodelay;
            
            rewrite ^/model-manager/(.*) /$1 break;
            proxy_pass http://model_manager;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Extended timeouts for model downloads
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 1800s;  # 30 minutes for large model downloads
        }

        # Direct llama.cpp server access
        location /llama/ {
            rewrite ^/llama/(.*) /$1 break;
            proxy_pass http://llama_server;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Multi-model gateway
        location /gateway/ {
            rewrite ^/gateway/(.*) /$1 break;
            proxy_pass http://multi_model_gateway;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health checks
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Status page
        location /status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow **********/16;  # Docker network
            deny all;
        }
    }
} 