# Oracle Autonomous Database Configuration
# Generated for AI Assistant Platform - Oracle Integration

# =============================================================================
# ORACLE DATABASE CONNECTION
# =============================================================================

# Oracle Database Credentials
ORACLE_USER=ADMIN
ORACLE_PASSWORD=Twilv0zera@123

# Oracle DSN (from tnsnames.ora - using high performance connection)
ORACLE_DSN=(description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.ap-singapore-2.oraclecloud.com))(connect_data=(service_name=g872ed23dd62a8b_raxcblotwgf3qzgh_high.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))

# Wallet Configuration
ORACLE_WALLET_LOCATION=./oracle_wallet/Wallet_SWIV8HV5Y96IWO2T/Wallet_RAXCBLOTWGF3QZGH
ORACLE_WALLET_PASSWORD=Twilv0zera@123

# =============================================================================
# OCI (Oracle Cloud Infrastructure) SETTINGS
# =============================================================================

# Autonomous Database OCID
AUTONOMOUS_DB_OCID=ocid1.autonomousdatabase.oc1.ap-singapore-2.anqwcljrrftircqankbftno6ftnjtvhkkxjx7bvqwhg25svpj4snsx7r6roq

# OCI Configuration (optional - for advanced features)
OCI_CONFIG_FILE=~/.oci/config
OCI_PROFILE=DEFAULT

# =============================================================================
# CONNECTION POOL SETTINGS
# =============================================================================

# Pool configuration for production
ORACLE_POOL_MIN=2
ORACLE_POOL_MAX=10
ORACLE_POOL_INCREMENT=1

# =============================================================================
# AI ASSISTANT INTEGRATION SETTINGS
# =============================================================================

# Oracle AI Features (Oracle 19C limitations)
USE_ORACLE_23AI_FEATURES=false
ENABLE_VECTOR_SEARCH=false
ENABLE_JSON_SEARCH=true
ENABLE_FULL_TEXT_SEARCH=true

# Hybrid approach (Oracle + PostgreSQL)
USE_HYBRID_APPROACH=true
EXTERNAL_VECTOR_DB=postgresql

# Service Ports
ORACLE_RAG_SERVICE_PORT=8030
ORACLE_INTEGRATION_PORT=8025

# =============================================================================
# LOGGING AND MONITORING
# =============================================================================

# Log levels
ORACLE_LOG_LEVEL=INFO
ORACLE_LOG_FILE=oracle_integration.log

# Health check settings
HEALTH_CHECK_INTERVAL=30
CONNECTION_TIMEOUT=30

# =============================================================================
# AI/ML SPECIFIC SETTINGS
# =============================================================================

# Embedding storage method for Oracle 19C
EMBEDDING_STORAGE_METHOD=json_clob
EMBEDDING_CHUNK_SIZE=1000

# Tables configuration
DOCUMENTS_TABLE=AI_DOCUMENTS
EMBEDDINGS_TABLE=AI_EMBEDDINGS
CONVERSATIONS_TABLE=AI_CONVERSATIONS
KNOWLEDGE_BASE_TABLE=AI_KNOWLEDGE_BASE
