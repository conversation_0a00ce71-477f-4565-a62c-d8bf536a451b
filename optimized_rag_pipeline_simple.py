"""
Simple RAG Optimization Pipeline for Open WebUI
Simplified version without Vietnamese-specific dependencies
"""

import sys
import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from simple_rag_pipeline_integration import SimpleRAGPipelineManager, SimplePipelineCompatibilityLayer
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure simple_rag_pipeline_integration.py is in the same directory")
    # Fallback minimal implementation
    class SimplePipelineCompatibilityLayer:
        def __init__(self):
            self.type = "filter"
            self.name = "Simple RAG System (Fallback)"
            self.description = "Simple RAG system - fallback mode"
        
        async def inlet(self, body: Dict[str, Any], user: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
            return body

class Pipeline:
    """Open WebUI Pipeline Class"""
    
    def __init__(self):
        try:
            # Initialize the simple RAG pipeline manager
            self.manager = SimpleRAGPipelineManager()
            self.pipeline = self.manager.get_pipeline()
            
            # Pipeline metadata
            self.type = "filter"
            self.name = "Simple RAG Optimization System"
            self.description = """
            🚀 Simple RAG optimization system
            
            Features:
            • Basic text processing and keyword extraction
            • Context-aware conversation chunking
            • Simple keyword-based retrieval
            • In-memory conversation storage
            • Conversation context enhancement
            
            Simplified version for reliable operation without external dependencies.
            """
            
            print(f"[Simple RAG] Pipeline initialized successfully")
            
        except Exception as e:
            print(f"Pipeline initialization error: {e}")
            # Fallback initialization
            self.pipeline = SimplePipelineCompatibilityLayer()
            self.type = self.pipeline.type
            self.name = self.pipeline.name
            self.description = self.pipeline.description
    
    async def inlet(self, body: Dict[str, Any], user: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process incoming requests through the simple RAG system
        
        Args:
            body: Request body from Open WebUI
            user: User information (optional)
            
        Returns:
            Processed body with enhanced RAG capabilities
        """
        try:
            # Use the simple pipeline
            result = await self.pipeline.inlet(body, user)
            return result
            
        except Exception as e:
            print(f"Pipeline processing error: {e}")
            # Fallback: return original body
            return body
    
    def get_status(self) -> Dict[str, Any]:
        """Get pipeline status information"""
        try:
            return self.pipeline.get_status()
        except:
            return {
                "status": "active",
                "type": self.type,
                "name": self.name,
                "features": [
                    "Basic text processing",
                    "Context-aware chunking",
                    "Keyword-based retrieval",
                    "In-memory storage"
                ]
            }

# Create pipeline instance for Open WebUI
pipeline = Pipeline()

# Export required attributes
type = pipeline.type
name = pipeline.name
description = pipeline.description

# Export required methods
async def inlet(body, user=None):
    return await pipeline.inlet(body, user)