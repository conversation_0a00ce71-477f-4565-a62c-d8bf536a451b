"""
Simple Pipeline Auth Disabler
Only disable authentication for pipeline server (port 9099)
"""

import subprocess
import time
import requests

def check_pipeline_status():
    """Check current pipeline server status"""
    print("📊 Checking pipeline server status...")
    
    try:
        response = requests.get("http://localhost:9099/", timeout=5)
        print(f"✅ Pipeline server: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ Pipeline server not responding: {e}")
        return False

def restart_pipeline_without_auth():
    """Restart pipeline server without authentication"""
    print("🔄 Restarting pipeline server without auth...")
    
    # Kill current pipeline process
    subprocess.run(
        "docker exec pipelines pkill -f 'uvicorn' || true",
        shell=True, capture_output=True
    )
    
    # Start pipeline server without auth requirements
    start_cmd = """
docker exec -d pipelines bash -c '
export WEBUI_AUTH=false
export ENABLE_SIGNUP=true
export DEFAULT_USER_ROLE=admin
cd /app
python -m uvicorn main:app --host 0.0.0.0 --port 9099 --reload
'
"""
    
    result = subprocess.run(start_cmd, shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Pipeline server restarted without auth")
    else:
        print(f"⚠️ Restart result: {result.stderr}")
    
    # Wait for server to start
    print("⏳ Waiting for pipeline server...")
    time.sleep(5)

def test_pipeline_endpoints():
    """Test pipeline endpoints after auth disable"""
    print("🧪 Testing pipeline endpoints...")
    
    endpoints = [
        ("Pipeline Status", "http://localhost:9099/"),
        ("Pipeline Models", "http://localhost:9099/models"),
        ("RAG Valves", "http://localhost:9099/enhanced-rag-v1/valves/spec")
    ]
    
    for name, url in endpoints:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: OK (no auth needed)")
            elif response.status_code == 403:
                print(f"❌ {name}: Still requires auth")
            else:
                print(f"⚠️ {name}: Status {response.status_code}")
        except Exception as e:
            print(f"❌ {name}: {str(e)}")

def main():
    """Main function"""
    print("🔓 DISABLE PIPELINE AUTH ONLY")
    print("=" * 40)
    
    # Check current status
    if not check_pipeline_status():
        print("❌ Pipeline server not running, starting container first...")
        subprocess.run("docker start pipelines", shell=True)
        time.sleep(3)
    
    # Restart without auth
    restart_pipeline_without_auth()
    
    # Test endpoints
    test_pipeline_endpoints()
    
    print("\n🎯 DONE!")
    print("✅ Pipeline server should now work without auth")
    print("✅ Try: curl http://localhost:9099/models")

if __name__ == "__main__":
    main()