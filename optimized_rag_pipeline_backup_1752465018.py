"""
Standalone Enhanced RAG Pipeline with Rich Metadata and <PERSON><PERSON>
Self-contained implementation for Open WebUI
"""

import json
import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel

class Pipeline:
    """Standalone Enhanced RAG Pipeline with metadata support and valves"""
    
    class Valves(BaseModel):
        """Configuration valves for Enhanced RAG"""
        ENABLE_METADATA: bool = True
        ENABLE_TIMESTAMPS: bool = True
        ENABLE_SESSION_TRACKING: bool = True
        ENABLE_TAGS: bool = True
        ENABLE_FEEDBACK: bool = True
        MAX_MEMORIES: int = 1000
        RETRIEVAL_TOP_K: int = 5
        METADATA_BOOST_RECENT: float = 2.0
        METADATA_BOOST_POSITIVE: float = 1.8
        METADATA_BOOST_SAME_SESSION: float = 1.3
        DEBUG_MODE: bool = False
        
    def __init__(self):
        self.name = "Enhanced RAG with Metadata & Valves"
        self.valves = self.Valves()
        self.memories = []
        self.current_session_id = str(uuid.uuid4())
        
    async def on_startup(self):
        """Initialize the enhanced RAG system"""
        print(f"🚀 Starting {self.name}")
        print("📊 Features: timestamp, session_id, source, tags, user_feedback, valves")
        print(f"🔧 Valves configured: metadata={self.valves.ENABLE_METADATA}, debug={self.valves.DEBUG_MODE}")
        
    async def on_shutdown(self):
        """Cleanup on shutdown"""
        print(f"🛑 Shutting down {self.name}")
        
    def add_memory(self, 
                   content: str,
                   source: str = "user",
                   tags: List[str] = None,
                   context: Dict[str, Any] = None,
                   user_feedback: Optional[str] = None):
        """Add memory with full metadata"""
        
        memory = {
            "id": str(uuid.uuid4()),
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "unix_timestamp": int(time.time()),
            "session_id": self.current_session_id,
            "source": source,
            "tags": tags or [],
            "context": context or {},
            "user_feedback": user_feedback,
            "relevance_score": 0.0,
            "access_count": 0,
            "last_accessed": None,
            "created_by": "standalone_enhanced_rag"
        }
        
        self.memories.append(memory)
        
        # Keep only recent memories if limit exceeded
        if len(self.memories) > self.valves.MAX_MEMORIES:
            self.memories = self.memories[-self.valves.MAX_MEMORIES:]
            
        return memory["id"]
    
    def extract_tags(self, text: str) -> List[str]:
        """Extract tags from text content"""
        if not self.valves.ENABLE_TAGS:
            return []
            
        tags = []
        
        # Technical tags
        tech_keywords = {
            "rag": ["rag", "retrieval", "vector", "embedding"],
            "docker": ["docker", "container", "image"],
            "python": ["python", "pip", "import"],
            "debugging": ["error", "bug", "fix", "debug"],
            "deployment": ["deploy", "install", "setup"],
            "api": ["api", "endpoint", "request", "response"],
            "database": ["database", "db", "query", "sql"],
            "ai": ["ai", "llm", "model", "gemini"],
            "pipeline": ["pipeline", "valve", "config"]
        }
        
        text_lower = text.lower()
        for tag, keywords in tech_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                tags.append(tag)
        
        # Sentiment tags
        if any(word in text_lower for word in ["chuẩn", "ngon", "tốt", "ok", "good"]):
            tags.append("positive")
        elif any(word in text_lower for word in ["sai", "lỗi", "ngu", "tệ", "bad", "error"]):
            tags.append("negative")
            
        return tags
    
    def retrieve_relevant_context(self, query: str, limit: int = None) -> List[Dict]:
        """Retrieve relevant memories with metadata awareness"""
        
        if not self.memories:
            return []
            
        limit = limit or self.valves.RETRIEVAL_TOP_K
        
        # Simple keyword matching
        query_words = set(query.lower().split())
        scored_memories = []
        
        for memory in self.memories:
            content_words = set(memory["content"].lower().split())
            overlap = len(query_words.intersection(content_words))
            
            if overlap > 0:
                score = overlap
                
                # Apply metadata boosts if enabled
                if self.valves.ENABLE_METADATA:
                    # Boost recent memories
                    age_hours = (time.time() - memory["unix_timestamp"]) / 3600
                    if age_hours < 1:
                        score *= self.valves.METADATA_BOOST_RECENT
                    elif age_hours < 24:
                        score *= 1.5
                        
                    # Boost memories with positive feedback
                    if memory.get("user_feedback") and "chuẩn" in memory["user_feedback"].lower():
                        score *= self.valves.METADATA_BOOST_POSITIVE
                        
                    # Boost same session memories
                    if memory["session_id"] == self.current_session_id:
                        score *= self.valves.METADATA_BOOST_SAME_SESSION
                
                memory["relevance_score"] = score
                scored_memories.append(memory)
                
                # Update access tracking
                memory["access_count"] += 1
                memory["last_accessed"] = datetime.now().isoformat()
        
        # Sort by relevance and return top results
        scored_memories.sort(key=lambda x: x["relevance_score"], reverse=True)
        return scored_memories[:limit]
        
    def pipe(self, user_message: str, model_id: str, messages: list, body: dict) -> str:
        """Main pipeline processing with enhanced metadata"""
        
        try:
            # Extract context from Open WebUI
            context = {
                "model_id": model_id,
                "message_count": len(messages),
                "user_id": body.get("user", {}).get("id", "unknown"),
                "chat_id": body.get("chat_id", "unknown"),
                "valves_config": {
                    "metadata_enabled": self.valves.ENABLE_METADATA,
                    "debug_mode": self.valves.DEBUG_MODE,
                    "max_memories": self.valves.MAX_MEMORIES
                }
            }
            
            # Extract tags from input
            tags = self.extract_tags(user_message)
            
            # Store user input
            user_memory_id = self.add_memory(
                content=user_message,
                source="user",
                tags=tags,
                context=context
            )
            
            # Retrieve relevant context
            relevant_memories = self.retrieve_relevant_context(user_message)
            
            # Generate response
            response = self.generate_response(user_message, relevant_memories)
            
            # Store AI response
            response_memory_id = self.add_memory(
                content=response,
                source="ai",
                tags=tags + ["response"],
                context={"responding_to": user_memory_id}
            )
            
            # Format output based on valves
            if not self.valves.ENABLE_METADATA:
                return response
            
            # Enhanced response with metadata
            enhanced_response = f"""🧠 Enhanced RAG Response:

{response}

📊 Metadata:
- Session: {self.current_session_id[:8]}...
- Timestamp: {datetime.now().strftime('%H:%M:%S %d/%m/%Y')}
- Tags: {', '.join(tags) if tags else 'None'}
- Relevant memories: {len(relevant_memories)}
- Total memories: {len(self.memories)}
"""

            if self.valves.DEBUG_MODE:
                enhanced_response += f"""
🔧 Debug Info:
- User Memory ID: {user_memory_id[:8]}...
- Response Memory ID: {response_memory_id[:8]}...
- Valves: metadata={self.valves.ENABLE_METADATA}, tags={self.valves.ENABLE_TAGS}
- Context: {json.dumps(context, indent=2)}
"""
            
            # Add memory details if enabled and available
            if self.valves.ENABLE_METADATA and relevant_memories:
                enhanced_response += "\n💡 Relevant memories:\n"
                
                for i, memory in enumerate(relevant_memories[:3], 1):
                    timestamp = memory['timestamp'][:19].replace('T', ' ')
                    source_icon = {"user": "👤", "ai": "🤖", "file": "📄", "system": "⚙️"}.get(memory['source'], "❓")
                    tags_str = ', '.join([f"#{tag}" for tag in memory['tags'][:3]]) if memory['tags'] else 'No tags'
                    feedback = memory.get('user_feedback', 'No feedback')
                    
                    enhanced_response += f"""
{i}. {source_icon} [{timestamp}] Score: {memory['relevance_score']:.2f}
   Tags: {tags_str}
   Feedback: {feedback}
   Content: {memory['content'][:100]}{'...' if len(memory['content']) > 100 else ''}
"""
            
            return enhanced_response
            
        except Exception as e:
            error_msg = f"❌ Enhanced RAG Error: {str(e)}"
            if self.valves.DEBUG_MODE:
                import traceback
                error_msg += f"\n🔧 Traceback:\n{traceback.format_exc()}"
            return error_msg
    
    def generate_response(self, user_input: str, relevant_memories: List[Dict]) -> str:
        """Generate response using relevant context"""
        
        if not relevant_memories:
            return f"Tôi hiểu bạn hỏi về: {user_input}. Tuy nhiên tôi chưa có đủ context từ memories để trả lời chi tiết."
        
        # Build context from memories
        context_parts = []
        for memory in relevant_memories[:3]:  # Use top 3 memories
            timestamp = datetime.fromisoformat(memory["timestamp"]).strftime("%H:%M %d/%m")
            source_icon = {"user": "👤", "ai": "🤖", "file": "📄", "system": "⚙️"}.get(memory["source"], "❓")
            tags_str = " ".join([f"#{tag}" for tag in memory["tags"][:3]]) if memory["tags"] else ""
            
            context_parts.append(f"{source_icon} [{timestamp}] {tags_str}\n{memory['content'][:200]}{'...' if len(memory['content']) > 200 else ''}")
        
        context = "\n\n".join(context_parts)
        
        return f"""Dựa trên context với metadata:

{context}

Trả lời cho câu hỏi: {user_input}

[Enhanced RAG response với đầy đủ timestamp, session tracking, tags, và metadata như LLM yêu cầu]"""

    def get_debug_info(self) -> Dict[str, Any]:
        """Get debug information about current state"""
        return {
            "total_memories": len(self.memories),
            "current_session": self.current_session_id,
            "valves_config": {
                "metadata_enabled": self.valves.ENABLE_METADATA,
                "timestamps_enabled": self.valves.ENABLE_TIMESTAMPS,
                "session_tracking": self.valves.ENABLE_SESSION_TRACKING,
                "tags_enabled": self.valves.ENABLE_TAGS,
                "feedback_enabled": self.valves.ENABLE_FEEDBACK,
                "debug_mode": self.valves.DEBUG_MODE
            },
            "recent_memories": self.memories[-5:] if self.memories else [],
            "system_status": "active"
        }
        
    def add_feedback(self, memory_id: str, feedback: str) -> bool:
        """Add user feedback to memory"""
        if not self.valves.ENABLE_FEEDBACK:
            return False
            
        for memory in self.memories:
            if memory["id"] == memory_id:
                memory["user_feedback"] = feedback
                memory["last_updated"] = datetime.now().isoformat()
                return True
        return False