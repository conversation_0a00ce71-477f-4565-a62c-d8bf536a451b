#!/usr/bin/env python3
"""
Open WebUI External RAG Connector
Connects Open WebUI to the high-performance external RAG server
"""

import asyncio
import json
import logging
from typing import List, Dict, Any, Optional
import httpx
from pathlib import Path

logger = logging.getLogger(__name__)

class OpenWebUIExternalRAGConnector:
    """Connector for Open WebUI to use external RAG server"""
    
    def __init__(self, external_rag_url: str = "http://localhost:8020"):
        self.external_rag_url = external_rag_url.rstrip('/')
        self.client = httpx.AsyncClient(timeout=30.0)
        
    async def health_check(self) -> bool:
        """Check if external RAG server is healthy"""
        try:
            response = await self.client.get(f"{self.external_rag_url}/health")
            return response.status_code == 200
        except Exception as e:
            logger.error(f"External RAG health check failed: {e}")
            return False
    
    async def upload_document(
        self,
        file_path: str,
        collection_name: str = "default",
        chunk_size: Optional[int] = None,
        chunk_overlap: Optional[int] = None
    ) -> Dict[str, Any]:
        """Upload document to external RAG server"""
        try:
            file_path = Path(file_path)
            
            # Prepare form data
            form_data = {
                "collection_name": collection_name
            }
            
            if chunk_size:
                form_data["chunk_size"] = chunk_size
            if chunk_overlap:
                form_data["chunk_overlap"] = chunk_overlap
            
            # Upload file
            with open(file_path, 'rb') as f:
                files = {"file": (file_path.name, f, "application/octet-stream")}
                
                response = await self.client.post(
                    f"{self.external_rag_url}/upload",
                    data=form_data,
                    files=files
                )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Upload failed: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "message": f"Upload failed: {response.status_code}",
                    "document_id": "",
                    "chunks_created": 0,
                    "processing_time": 0
                }
                
        except Exception as e:
            logger.error(f"Document upload error: {e}")
            return {
                "success": False,
                "message": f"Error: {str(e)}",
                "document_id": "",
                "chunks_created": 0,
                "processing_time": 0
            }
    
    async def search_documents(
        self,
        query: str,
        collection_name: str = "default",
        top_k: int = 10,
        similarity_threshold: float = 0.7,
        hybrid_search: bool = True
    ) -> Dict[str, Any]:
        """Search documents in external RAG server"""
        try:
            search_request = {
                "query": query,
                "collection_name": collection_name,
                "top_k": top_k,
                "similarity_threshold": similarity_threshold,
                "hybrid_search": hybrid_search
            }
            
            response = await self.client.post(
                f"{self.external_rag_url}/search",
                json=search_request
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Search failed: {response.status_code} - {response.text}")
                return {
                    "results": [],
                    "total_results": 0,
                    "processing_time": 0,
                    "query": query
                }
                
        except Exception as e:
            logger.error(f"Search error: {e}")
            return {
                "results": [],
                "total_results": 0,
                "processing_time": 0,
                "query": query
            }
    
    async def list_collections(self) -> Dict[str, Any]:
        """List all collections in external RAG server"""
        try:
            response = await self.client.get(f"{self.external_rag_url}/collections")
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"List collections failed: {response.status_code}")
                return {"collections": []}
                
        except Exception as e:
            logger.error(f"List collections error: {e}")
            return {"collections": []}
    
    async def delete_collection(self, collection_name: str) -> Dict[str, Any]:
        """Delete a collection from external RAG server"""
        try:
            response = await self.client.delete(
                f"{self.external_rag_url}/collections/{collection_name}"
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "message": f"Delete failed: {response.status_code}",
                    "success": False
                }
                
        except Exception as e:
            logger.error(f"Delete collection error: {e}")
            return {
                "message": f"Error: {str(e)}",
                "success": False
            }
    
    async def format_context_for_openwebui(
        self,
        search_results: List[Dict[str, Any]],
        max_context_length: int = 4000
    ) -> str:
        """Format search results as context for Open WebUI"""
        if not search_results:
            return ""
        
        context_parts = []
        current_length = 0
        
        for i, result in enumerate(search_results):
            content = result.get("content", "")
            score = result.get("score", 0)
            
            # Add source information
            source_info = f"[Source {i+1} - Score: {score:.3f}]\n{content}\n"
            
            if current_length + len(source_info) > max_context_length:
                break
                
            context_parts.append(source_info)
            current_length += len(source_info)
        
        return "\n".join(context_parts)
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

# Open WebUI Integration Functions
class OpenWebUIRAGIntegration:
    """Integration class for Open WebUI RAG functionality"""
    
    def __init__(self, external_rag_url: str = "http://localhost:8020"):
        self.connector = OpenWebUIExternalRAGConnector(external_rag_url)
        
    async def query_with_context(
        self,
        query: str,
        collection_name: str = "default",
        top_k: int = 10,
        similarity_threshold: float = 0.7
    ) -> Dict[str, Any]:
        """Query with context formatting for Open WebUI"""
        
        # Search for relevant documents
        search_results = await self.connector.search_documents(
            query=query,
            collection_name=collection_name,
            top_k=top_k,
            similarity_threshold=similarity_threshold,
            hybrid_search=True
        )
        
        # Format context
        context = await self.connector.format_context_for_openwebui(
            search_results.get("results", [])
        )
        
        return {
            "context": context,
            "sources": search_results.get("results", []),
            "total_results": search_results.get("total_results", 0),
            "processing_time": search_results.get("processing_time", 0),
            "query": query
        }
    
    async def upload_and_index(
        self,
        file_path: str,
        collection_name: str = "default"
    ) -> Dict[str, Any]:
        """Upload and index document for Open WebUI"""
        
        result = await self.connector.upload_document(
            file_path=file_path,
            collection_name=collection_name
        )
        
        if result.get("success"):
            logger.info(
                f"✅ Document indexed: {file_path} -> "
                f"{result.get('chunks_created')} chunks in {result.get('processing_time'):.2f}s"
            )
        else:
            logger.error(f"❌ Document indexing failed: {result.get('message')}")
        
        return result
    
    async def get_collections_info(self) -> Dict[str, Any]:
        """Get collections information for Open WebUI admin"""
        collections_data = await self.connector.list_collections()
        
        # Format for Open WebUI display
        formatted_collections = []
        for collection in collections_data.get("collections", []):
            formatted_collections.append({
                "name": collection.get("name"),
                "documents": collection.get("document_count", 0),
                "chunks": collection.get("chunk_count", 0),
                "created": collection.get("created_at"),
                "status": "active"
            })
        
        return {
            "collections": formatted_collections,
            "total_collections": len(formatted_collections)
        }

# Test and demonstration functions
async def test_external_rag_integration():
    """Test the external RAG integration"""
    print("🧪 Testing External RAG Integration")
    print("=" * 50)
    
    integration = OpenWebUIRAGIntegration()
    
    try:
        # Health check
        health = await integration.connector.health_check()
        print(f"🏥 Health Check: {'✅ Healthy' if health else '❌ Unhealthy'}")
        
        if not health:
            print("External RAG server is not running. Please start it first.")
            return
        
        # List collections
        collections_info = await integration.get_collections_info()
        print(f"📚 Collections: {collections_info['total_collections']}")
        for collection in collections_info['collections']:
            print(f"  - {collection['name']}: {collection['documents']} docs, {collection['chunks']} chunks")
        
        # Test search
        print("\n🔍 Testing Search...")
        query = "What is artificial intelligence?"
        result = await integration.query_with_context(query, top_k=3)
        
        print(f"Query: {query}")
        print(f"Results: {result['total_results']}")
        print(f"Processing time: {result['processing_time']:.3f}s")
        
        if result['context']:
            print(f"Context preview: {result['context'][:200]}...")
        else:
            print("No context found (no documents indexed)")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await integration.connector.close()

if __name__ == "__main__":
    asyncio.run(test_external_rag_integration()) 