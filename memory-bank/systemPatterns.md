# System Patterns: AI Assistant Platform - Enhanced RAG Architecture

## 🧠 **CURRENT ARCHITECTURE: Enhanced RAG with Metada<PERSON> Pattern**

### **Core Architecture Achievement**
The AI Assistant Platform features an **Enhanced RAG System with rich metadata** that successfully addresses LLM feedback about "text trần trụi" by providing comprehensive context tracking and configurable valves.

```
┌─────────────────────────────────────────────────────────────┐
│        AI Assistant Platform - Enhanced RAG Architecture    │
│           (Metadata-Rich Processing with <PERSON>ves)           │
├─────────────────┬─────────────────┬─────────────────────────┤
│  User Interface │  Enhanced RAG   │    Memory Storage       │
│  - Open WebUI   │  - Pipeline     │  - Rich Metadata        │
│  - Port 3000    │  - Port 9099    │  - In-Memory            │
│  - Chat UI      │  - 11 Valves    │  - Session Isolation    │
└─────────────────┴─────────────────┴─────────────────────────┘
                           │
              ┌─────────────────────────┐
              │ Enhanced RAG Pipeline   │
              │                         │
              │ ┌─────────────────────┐ │
              │ │ Metadata System     │ │
              │ │ - Timestamps        │ │
              │ │ - Session tracking  │ │
              │ │ - Status: ✅ Active │ │
              │ └─────────────────────┘ │
              │           +             │
              │ ┌─────────────────────┐ │
              │ │ Valves Config       │ │
              │ │ - 11 parameters     │ │
              │ │ - Real-time tuning  │ │
              │ │ - Status: ✅ Working│ │
              │ └─────────────────────┘ │
              │           +             │
              │ ┌─────────────────────┐ │
              │ │ Vietnamese Support  │ │
              │ │ - Language optimize │ │
              │ │ - Tag extraction    │ │
              │ │ - Status: ✅ Active │ │
              │ └─────────────────────┘ │
              └─────────────────────────┘
```

## 🧠 **ENHANCED RAG PATTERNS - METADATA SYSTEM**

### **1. Enhanced RAG Architecture Pattern**
```python
# Enhanced RAG with Metadata System Pattern
class EnhancedRAGPipeline:
    def __init__(self):
        self.name = "Enhanced RAG with Metadata & Valves"
        self.valves = self.Valves()
        self.memories = []
        self.current_session_id = str(uuid.uuid4())
        
    def pipeline_status(self):
        return {
            "enhanced_rag": "✅ Fully Operational",     # Metadata working
            "valves_config": "✅ 11 Parameters Active", # Real-time tuning
            "metadata_system": "✅ Rich Context",       # Timestamps, sessions, etc.
            "vietnamese_support": "✅ Optimized"        # Language processing
        }
```

### **2. Metadata Tracking Pattern**
```python
# Rich Metadata System Pattern
class MetadataSystem:
    def __init__(self):
        self.timestamp_format = "ISO + Unix"
        self.session_tracking = "UUID-based"
        self.source_attribution = ["user", "ai", "file", "system"]
        
    def create_memory(self, content, source="user"):
        return {
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "unix_timestamp": int(time.time()),
            "session_id": self.current_session_id,
            "source": source,
            "tags": self.extract_tags(content),
            "user_feedback": None,
            "relevance_score": 0.0
        }
```

### **3. Valves Configuration Pattern**
```python
# Configurable Valves System Pattern
class Valves(BaseModel):
    ENABLE_METADATA: bool = True
    RETRIEVAL_TOP_K: int = 5
    MAX_MEMORIES: int = 1000
    RECENT_MEMORY_BOOST: float = 1.2
    POSITIVE_FEEDBACK_BOOST: float = 1.3
    SAME_SESSION_BOOST: float = 1.1
    ENABLE_VIETNAMESE_PROCESSING: bool = True
    ENABLE_AUTO_TAGGING: bool = True
    DEBUG_MODE: bool = False
    MEMORY_CLEANUP_THRESHOLD: int = 1200
    ENABLE_FEEDBACK_LEARNING: bool = True
    
    def get_config_summary(self):
        return {
            "metadata_enabled": self.ENABLE_METADATA,
            "retrieval_count": self.RETRIEVAL_TOP_K,
            "memory_limit": self.MAX_MEMORIES,
            "vietnamese_support": self.ENABLE_VIETNAMESE_PROCESSING,
            "debug_active": self.DEBUG_MODE
        }
```

### **4. Vietnamese Language Processing Pattern**
```python
# Vietnamese Language Optimization Pattern
class VietnameseProcessor:
    def __init__(self):
        self.business_terms = [
            "hợp đồng", "báo cáo", "kế hoạch", "dự án", "khách hàng",
            "doanh thu", "lợi nhuận", "chi phí", "ngân sách", "đầu tư"
        ]
        self.stop_words = ["và", "của", "trong", "với", "để", "là", "có"]
        
    def extract_vietnamese_tags(self, content: str) -> List[str]:
        tags = []
        content_lower = content.lower()
        
        # Extract business terms
        for term in self.business_terms:
            if term in content_lower:
                tags.append(term)
                
        # Extract key phrases (2-3 words)
        words = content_lower.split()
        for i in range(len(words) - 1):
            phrase = " ".join(words[i:i+2])
            if len(phrase) > 5 and not any(stop in phrase for stop in self.stop_words):
                tags.append(phrase)
                
        return list(set(tags))[:10]  # Limit to 10 most relevant tags
```

## 🧠 **ENHANCED RAG PATTERNS - ADVANCED FEATURES**

### **1. Metadata-Aware Retrieval Pattern**
```python
# Enhanced Retrieval with Metadata Scoring
class MetadataAwareRetrieval:
    def __init__(self, valves):
        self.valves = valves
        self.current_session_id = str(uuid.uuid4())
        
    def retrieve_relevant_context(self, query: str, memories: List[dict]) -> List[dict]:
        if not memories:
            return []
            
        scored_memories = []
        current_time = time.time()
        
        for memory in memories:
            # Base relevance score
            score = self.calculate_relevance(query, memory["content"])
            
            # Metadata boosts
            if self.valves.ENABLE_METADATA:
                # Recent memory boost
                time_diff = current_time - memory.get("unix_timestamp", 0)
                if time_diff < 3600:  # Within 1 hour
                    score *= self.valves.RECENT_MEMORY_BOOST
                    
                # Same session boost
                if memory.get("session_id") == self.current_session_id:
                    score *= self.valves.SAME_SESSION_BOOST
                    
                # Positive feedback boost
                if memory.get("user_feedback") == "positive":
                    score *= self.valves.POSITIVE_FEEDBACK_BOOST
                    
            scored_memories.append((memory, score))
            
        # Sort by score and return top K
        scored_memories.sort(key=lambda x: x[1], reverse=True)
        return [mem for mem, score in scored_memories[:self.valves.RETRIEVAL_TOP_K]]
```

## 🔄 **SESSION MANAGEMENT PATTERN**

### **1. Session Isolation and Tracking**
```python
# Session-Aware Memory Management
class SessionManager:
    def __init__(self):
        self.sessions = {}
        self.current_session_id = str(uuid.uuid4())
        
    def create_new_session(self) -> str:
        session_id = str(uuid.uuid4())
        self.sessions[session_id] = {
            "created_at": datetime.now().isoformat(),
            "memories": [],
            "context": {}
        }
        self.current_session_id = session_id
        return session_id
        
    def add_memory_to_session(self, session_id: str, memory: dict):
        if session_id not in self.sessions:
            self.sessions[session_id] = {"memories": [], "context": {}}
        self.sessions[session_id]["memories"].append(memory)
        
    def get_session_context(self, session_id: str) -> List[dict]:
        return self.sessions.get(session_id, {}).get("memories", [])
```

## 📱 **FEEDBACK LEARNING PATTERN**

### **1. User Feedback Collection and Learning**
```python
class FeedbackLearningSystem:
    def __init__(self):
        self.feedback_history = []
        self.learning_enabled = True
        
    def collect_feedback(self, memory_id: str, feedback: str, user_comment: str = ""):
        feedback_entry = {
            "memory_id": memory_id,
            "feedback": feedback,  # "positive", "negative", "neutral"
            "user_comment": user_comment,
            "timestamp": datetime.now().isoformat(),
            "unix_timestamp": int(time.time())
        }
        self.feedback_history.append(feedback_entry)
        
        # Update memory with feedback
        self.update_memory_feedback(memory_id, feedback)
        
    def update_memory_feedback(self, memory_id: str, feedback: str):
        for memory in self.memories:
            if memory.get("id") == memory_id:
                memory["user_feedback"] = feedback
                memory["feedback_timestamp"] = datetime.now().isoformat()
                break
                
    def get_feedback_stats(self) -> dict:
        if not self.feedback_history:
            return {"positive": 0, "negative": 0, "neutral": 0}
            
        stats = {"positive": 0, "negative": 0, "neutral": 0}
        for feedback in self.feedback_history:
            feedback_type = feedback.get("feedback", "neutral")
            stats[feedback_type] = stats.get(feedback_type, 0) + 1
            
        return stats
```

### **2. Memory Cleanup and Optimization Pattern**
```python
# Intelligent Memory Management
class MemoryOptimizer:
    def __init__(self, valves):
        self.valves = valves
        self.cleanup_threshold = valves.MEMORY_CLEANUP_THRESHOLD
        
    def cleanup_old_memories(self, memories: List[dict]) -> List[dict]:
        if len(memories) <= self.valves.MAX_MEMORIES:
            return memories
            
        # Sort by relevance and recency
        current_time = time.time()
        scored_memories = []
        
        for memory in memories:
            score = 0
            
            # Recency score (more recent = higher score)
            time_diff = current_time - memory.get("unix_timestamp", 0)
            recency_score = max(0, 1 - (time_diff / (7 * 24 * 3600)))  # 7 days decay
            
            # Feedback score
            feedback_score = 0
            if memory.get("user_feedback") == "positive":
                feedback_score = 1.0
            elif memory.get("user_feedback") == "negative":
                feedback_score = -0.5
                
            # Combined score
            score = recency_score + feedback_score
            scored_memories.append((memory, score))
            
        # Keep top memories
        scored_memories.sort(key=lambda x: x[1], reverse=True)
        return [mem for mem, score in scored_memories[:self.valves.MAX_MEMORIES]]
```

## 🔧 **DEPLOYMENT PATTERNS**

### **1. Standalone Pipeline Deployment Pattern**
```python
# Self-Contained Pipeline Deployment
class StandaloneDeployment:
    def __init__(self):
        self.container_name = "pipelines"
        self.target_path = "/app/pipelines/optimized_rag_pipeline.py"
        self.source_file = "standalone_enhanced_rag_pipeline.py"
        
    def deploy_to_container(self):
        # Step 1: Copy standalone file to container
        copy_command = f"docker cp {self.source_file} {self.container_name}:{self.target_path}"
        subprocess.run(copy_command, shell=True, check=True)
        
        # Step 2: Restart container to load new pipeline
        restart_command = f"docker restart {self.container_name}"
        subprocess.run(restart_command, shell=True, check=True)
        
        # Step 3: Verify deployment
        self.verify_deployment()
        
    def verify_deployment(self):
        # Check if pipeline loads successfully
        logs_command = f"docker logs {self.container_name} --tail 20"
        result = subprocess.run(logs_command, shell=True, capture_output=True, text=True)
        
        if "Enhanced RAG with Metadata & Valves" in result.stdout:
            print("✅ Enhanced RAG Pipeline deployed successfully")
        else:
            print("❌ Deployment verification failed")
```

### **2. Valves API Integration Pattern**
```python
# Valves Configuration API
class ValvesAPI:
    def __init__(self, pipeline_url="http://localhost:9099"):
        self.base_url = pipeline_url
        self.pipeline_name = "optimized_rag_pipeline"
        
    def get_valves_spec(self):
        url = f"{self.base_url}/{self.pipeline_name}/valves/spec"
        response = requests.get(url)
        return response.json()
        
    def get_current_valves(self):
        url = f"{self.base_url}/{self.pipeline_name}/valves"
        response = requests.get(url)
        return response.json()
        
    def update_valves(self, new_config: dict):
        url = f"{self.base_url}/{self.pipeline_name}/valves"
        response = requests.post(url, json=new_config)
        return response.json()
        
    def optimize_for_vietnamese(self):
        config = {
            "ENABLE_VIETNAMESE_PROCESSING": True,
            "ENABLE_AUTO_TAGGING": True,
            "RETRIEVAL_TOP_K": 7,
            "RECENT_MEMORY_BOOST": 1.3
        }
        return self.update_valves(config)
```

## ☁️ **GOOGLE DRIVE INTEGRATION PATTERNS**

### **1. Hybrid Caching Pattern**
```python
# Local + Cloud Storage Pattern
class GoogleDriveSync:
    def __init__(self):
        self.local_cache = JSONCache()
        self.drive_api = GoogleDriveAPI()
        self.cache_ttl = 3600  # 1 hour
        
    def get_document(self, doc_id):
        # Check local cache first
        if self.local_cache.is_valid(doc_id):
            return self.local_cache.get(doc_id)
            
        # Fetch from Google Drive if needed
        doc = self.drive_api.download(doc_id)
        self.local_cache.store(doc_id, doc)
        return doc
```

### **2. Export/Import Pattern**
```python
# Google Drive Compatible Format
def export_to_drive_format(knowledge_base):
    return {
        "format_version": "1.0",
        "export_timestamp": datetime.now().isoformat(),
        "documents": knowledge_base.documents,
        "indexes": knowledge_base.indexes,
        "metadata": {
            "total_documents": len(knowledge_base.documents),
            "vietnamese_optimized": True,
            "search_engine": "json_fast_search"
        }
    }
```

## 🎯 **PERFORMANCE PATTERNS**

### **1. Memory Management Pattern**
```python
# Efficient Memory Usage Pattern
class MemoryOptimizedSearch:
    def __init__(self):
        self.max_memory = 50 * 1024 * 1024  # 50MB limit
        self.indexes = {}
        self.documents = {}
        
    def lazy_load_document(self, doc_id):
        # Load documents only when needed
        if doc_id not in self.documents:
            self.documents[doc_id] = self.load_from_storage(doc_id)
        return self.documents[doc_id]
```

### **2. Search Optimization Pattern**
```python
# Cached Search Results Pattern
from functools import lru_cache

class OptimizedSearch:
    @lru_cache(maxsize=1000)
    def search(self, query):
        # Cache frequent searches
        return self._perform_search(query)
        
    def _perform_search(self, query):
        # Actual search implementation
        keywords = self.extract_keywords(query)
        return self.find_matching_documents(keywords)
```

## 🌐 **DEPLOYMENT PATTERNS**

### **1. Production Deployment Pattern**
```yaml
# Dual System Deployment Configuration
services:
  json_search:
    image: python:3.9-slim
    environment:
      - SEARCH_ENGINE=json_fast_search
      - MEMORY_LIMIT=50MB
      - CACHE_SIZE=15MB
    volumes:
      - ./json_knowledge_base:/data
      
  traditional_rag:
    image: openwebui:latest
    environment:
      - RAG_EMBEDDING_MODEL=gemini-embedding-exp-03-07
      - RAG_EMBEDDING_DIMENSIONS=3072
    volumes:
      - ./vector_store:/vectorstore
      
  router:
    image: nginx:alpine
    depends_on:
      - json_search
      - traditional_rag
```

### **2. Migration Pattern**
```python
# Gradual Migration Strategy
class MigrationManager:
    def migrate_to_json(self, documents):
        for doc in documents:
            # Convert document to JSON format
            json_doc = self.convert_document(doc)
            
            # Validate conversion quality
            if self.validate_conversion(doc, json_doc):
                self.store_json_document(json_doc)
            else:
                self.log_conversion_issue(doc)
```

## 🏆 **ENHANCED RAG ARCHITECTURE SUMMARY**

### **Pattern Integration Excellence**
- **Metadata System**: Rich context tracking with timestamps, sessions, sources, tags
- **Valves Configuration**: 11 real-time configurable parameters for system tuning
- **Vietnamese Optimization**: Language-specific processing and tag extraction
- **Session Management**: UUID-based isolation for conversation context
- **Feedback Learning**: User feedback collection and scoring system
- **Memory Management**: Smart cleanup and optimization algorithms

### **Engineering Patterns Applied**
- **Metadata Enhancement**: Transform "text trần trụi" into rich contextual information
- **Real-time Configuration**: Valves system for dynamic parameter adjustment
- **Session Isolation**: UUID-based conversation context separation
- **Language Optimization**: Vietnamese-specific processing and understanding
- **Feedback Integration**: Learning system for continuous improvement

**ARCHITECTURE STATUS**: ✅ **ENHANCED RAG WITH METADATA IMPLEMENTED**
**PERFORMANCE**: 🧠 **RICH CONTEXT WITH CONFIGURABLE INTELLIGENCE**
**QUALITY**: ✅ **METADATA-RICH WITH VIETNAMESE EXCELLENCE**

```mermaid
graph TD
    subgraph "ARM64 VPS"
        subgraph "Docker Backend (localhost:8010)"
            A[FastAPI Application] --> B{OpenAI-Compatible API Layer}
            B --> C[Gemma3n MediaPipe Service]
            C --> D["MediaPipe LLMInference<br>(gemma-3n-E4B-it-int4.task)"]
        end
        subgraph "Docker Open WebUI (localhost:3001)"
            E[Web UI] --> F{OpenAI API Connection}
        end
    end

    F --> B

    style D fill:#d4edda,stroke:#155724
```

## Core Design Patterns

### 1. MediaPipe LLM Inference Pattern
**Implementation**: The core logic is handled by the `mediapipe.tasks.genai.LlmInference` class.

```python
# MediaPipe Implementation Pattern
import mediapipe as mp
from mediapipe.tasks.python.genai import llm_inference

class Gemma3nMediaPipeService:
    def __init__(self):
        self.llm = None
        self._initialize_model()

    def _initialize_model(self):
        # Path to the .task model bundle
        model_path = "backend/app/models/tflite/gemma3n/gemma-3n-E4B-it-int4.task"

        # LlmInferenceOptions configuration
        options = llm_inference.LlmInference.Options(
            model_path=model_path,
            # Other options like temperature, top_k can be set here
        )
        self.llm = llm_inference.LlmInference(options=options)

    def generate_response(self, prompt: str):
        if not self.llm:
            self._initialize_model()
        
        # Generate response using the MediaPipe LLM
        response = self.llm.generate(prompt)
        return response
```

**Key Characteristics:**
- **Simplicity**: The `.task` model file encapsulates all necessary components (model weights, tokenizer, etc.).
- **Abstraction**: MediaPipe handles the complexities of tokenization, tensor management, and inference.
- **Efficiency**: Optimized for on-device and edge inference.

### 2. Service Layer Pattern
**Modular Architecture**: Clean separation of concerns.

```mermaid
graph TD
    subgraph "FastAPI Application"
        A[API Layer] --> B[Service Layer]
        B --> C[Model Layer]
    end

    subgraph "API Layer (OpenAI Compatible)"
        D[/api/v1/chat/completions]
    end

    subgraph "Service Layer"
        E[Gemma3nMediaPipeService]
    end
    
    subgraph "Model Layer"
        F[MediaPipe LLMInference Task]
    end

    A --> D
    B --> E
    C --> F
```

## Component Relationships

### API Integration Flow
```mermaid
graph TD
    A[Open WebUI] -- HTTP Request --> B[FastAPI Backend]
    B -- Service Call --> C[Gemma3nMediaPipeService]
    C -- generate() --> D[MediaPipe LLMInference]
    D -- Response --> C
    C -- OpenAI-Compatible JSON --> B
    B -- HTTP Response --> A

```
