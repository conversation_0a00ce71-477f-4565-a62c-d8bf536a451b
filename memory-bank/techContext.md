# Technology Context: AI Assistant Platform - Enhanced RAG with Metadata

## 🧠 **LATEST ADDITION: ENHANCED RAG WITH METADATA TECHNOLOGY STACK**

### **Enhanced RAG Architecture (NEW)**
- **Core Engine**: Standalone Python pipeline with rich metadata
- **Storage Format**: In-memory with comprehensive metadata tracking
- **Processing Algorithm**: Metadata-aware retrieval with scoring boosts
- **Memory Management**: Smart cleanup with configurable limits
- **Performance**: Real-time configuration with 11 valves system

### **Enhanced RAG Technical Stack**
```yaml
# Enhanced RAG Configuration
PIPELINE_ENGINE: standalone_enhanced_rag    # Self-contained implementation
STORAGE_FORMAT: in_memory_metadata         # Rich context tracking
METADATA_SYSTEM: comprehensive            # Timestamp, session, source, tags, feedback
SCORING_ALGORITHM: metadata_aware         # Relevance + metadata boosts
MEMORY_USAGE: configurable               # MAX_MEMORIES valve (default: 1000)
RETRIEVAL_SPEED: instant                 # In-memory processing

# Vietnamese Language Optimization
VIETNAMESE_PROCESSING: enabled           # ENABLE_VIETNAMESE_PROCESSING valve
BUSINESS_TERMS: auto_extraction         # Vietnamese business term detection
TAG_EXTRACTION: automatic               # ENABLE_AUTO_TAGGING valve
FEEDBACK_LEARNING: enabled              # ENABLE_FEEDBACK_LEARNING valve

# Valves Configuration System
VALVES_COUNT: 11                        # Real-time configurable parameters
VALVES_ENDPOINT: /valves/spec           # API endpoint for configuration
REAL_TIME_CONFIG: enabled               # Dynamic parameter adjustment
DEBUG_MODE: configurable                # DEBUG_MODE valve
```

### **Enhanced RAG vs Simple RAG Performance**
```
Enhanced RAG Architecture:
- Data Structure: In-memory with rich metadata
- Memory: Configurable (MAX_MEMORIES valve)
- Storage: Session-aware with UUID isolation
- Processing: Metadata-aware retrieval with scoring boosts
- Speed: Instant (in-memory processing)
- Context: Rich metadata (timestamp, session, source, tags, feedback)

Simple RAG Architecture:
- Data Structure: Basic text storage
- Memory: Fixed capacity
- Storage: Simple text without context
- Processing: Basic relevance scoring
- Speed: Fast but limited context
- Context: "text trần trụi" - no metadata
```

### **Enhanced RAG Processing Pipeline**
```python
# Enhanced RAG Memory Processing
User Input → Memory Creation → Metadata Enrichment → Storage → Retrieval

Components:
1. Memory Creator: Rich metadata generation with timestamps
2. Session Manager: UUID-based session isolation
3. Tag Extractor: Vietnamese business term detection
4. Source Tracker: User/AI/File/System attribution
5. Feedback Collector: User feedback learning system
6. Relevance Scorer: Metadata-aware scoring with boosts
7. Memory Optimizer: Smart cleanup and management
```

---

## 🧠 **ENHANCED RAG CONFIGURATION - METADATA SYSTEM**

### **Current Enhanced RAG Configuration**
- **Pipeline**: `standalone_enhanced_rag_pipeline.py` (Self-contained)
- **Valves**: 11 configurable parameters for real-time tuning
- **Metadata**: Comprehensive tracking (timestamp, session, source, tags, feedback)
- **Performance**: Instant in-memory processing
- **Quality**: Rich context addressing LLM "text trần trụi" complaint
- **Usage**: Production deployment on port 9099

### **Enhanced RAG System Architecture**
```yaml
# Enhanced RAG Pipeline Configuration
RAG_PIPELINE_ENGINE: standalone_enhanced        # Self-contained implementation
RAG_PIPELINE_NAME: "Enhanced RAG with Metadata & Valves"
RAG_PIPELINE_PORT: 9099                        # Docker container port
RAG_VALVES_ENDPOINT: /optimized_rag_pipeline/valves/spec
RAG_CONTAINER_NAME: pipelines                  # Docker container

# Metadata System Configuration
METADATA_TRACKING: comprehensive              # Full context tracking
TIMESTAMP_FORMAT: iso_plus_unix               # ISO + Unix timestamp
SESSION_MANAGEMENT: uuid_based                # UUID session isolation
SOURCE_ATTRIBUTION: four_types               # user/ai/file/system
TAG_SYSTEM: auto_plus_manual                 # Auto-extraction + manual
FEEDBACK_SYSTEM: learning_enabled            # User feedback collection

# Valves Configuration (11 Parameters)
ENABLE_METADATA: true                        # Metadata features toggle
RETRIEVAL_TOP_K: 5                          # Number of memories to retrieve
MAX_MEMORIES: 1000                          # Maximum memories to store
RECENT_MEMORY_BOOST: 1.2                    # Boost for recent memories
POSITIVE_FEEDBACK_BOOST: 1.3                # Boost for positive feedback
SAME_SESSION_BOOST: 1.1                     # Boost for same session
ENABLE_VIETNAMESE_PROCESSING: true           # Vietnamese language features
ENABLE_AUTO_TAGGING: true                    # Automatic tag extraction
DEBUG_MODE: false                           # Enhanced logging
MEMORY_CLEANUP_THRESHOLD: 1200              # Auto-cleanup threshold
ENABLE_FEEDBACK_LEARNING: true              # Feedback learning system
```

---

## 🚀 **COMPLETE TECHNOLOGY STACK**

### **Mobile Application (Flutter WebView Wrapper)**
- **Framework**: Flutter 3.x (Dart)
- **Architecture**: WebView Wrapper Pattern
- **Backend Integration**: Dual search system support
- **Performance**: Benefits from instant JSON search
- **Build Target**: Android APK (19.5MB optimized)

### **Backend Integration (ENHANCED WITH METADATA RAG)**
- **Primary Server**: VPS with Enhanced RAG Pipeline
- **Web Framework**: Open WebUI with Enhanced RAG integration
- **Protocol**: HTTP/WebSocket with valves API
- **RAG Options**: Enhanced RAG with metadata + configurable valves
- **Network Security**: HTTPS with nginx reverse proxy

## 📊 **ENHANCED RAG TECHNICAL IMPLEMENTATION**

### **Core Data Structures**
```python
# Enhanced RAG Memory Format
memory_structure = {
    "content": "User or AI message content",
    "timestamp": "2025-01-14T03:20:00.000Z",              # ISO format
    "unix_timestamp": 1736823600,                         # Unix timestamp
    "session_id": "550e8400-e29b-41d4-a716-************", # UUID
    "source": "user",                                     # user/ai/file/system
    "tags": ["hợp đồng", "báo cáo", "dự án"],            # Auto-extracted
    "user_feedback": None,                                # positive/negative/neutral
    "relevance_score": 0.0,                              # Calculated score
    "id": "mem_550e8400",                                # Memory identifier
    "feedback_timestamp": None                            # When feedback given
}

# Valves Configuration Structure
valves_config = {
    "ENABLE_METADATA": True,
    "RETRIEVAL_TOP_K": 5,
    "MAX_MEMORIES": 1000,
    "RECENT_MEMORY_BOOST": 1.2,
    "POSITIVE_FEEDBACK_BOOST": 1.3,
    "SAME_SESSION_BOOST": 1.1,
    "ENABLE_VIETNAMESE_PROCESSING": True,
    "ENABLE_AUTO_TAGGING": True,
    "DEBUG_MODE": False,
    "MEMORY_CLEANUP_THRESHOLD": 1200,
    "ENABLE_FEEDBACK_LEARNING": True
}
```

### **Vietnamese Language Processing**
```python
# Enhanced RAG Vietnamese Optimization
vietnamese_business_terms = [
    'hợp đồng', 'báo cáo', 'kế hoạch', 'dự án', 'khách hàng',
    'doanh thu', 'lợi nhuận', 'chi phí', 'ngân sách', 'đầu tư',
    'tuyển dụng', 'nhân sự', 'lương', 'thưởng', 'công việc',
    'phòng ban', 'quản lý', 'điều hành', 'kinh doanh', 'quy trình'
]

vietnamese_stop_words = [
    'và', 'của', 'trong', 'với', 'để', 'là', 'có', 'được',
    'sẽ', 'đã', 'này', 'đó', 'các', 'những', 'nhiều', 'một'
]

def extract_vietnamese_tags(content: str) -> List[str]:
    """Extract Vietnamese business terms and key phrases"""
    tags = []
    content_lower = content.lower()
    
    # Extract business terms
    for term in vietnamese_business_terms:
        if term in content_lower:
            tags.append(term)
            
    # Extract key phrases (2-3 words)
    words = content_lower.split()
    for i in range(len(words) - 1):
        phrase = " ".join(words[i:i+2])
        if len(phrase) > 5 and not any(stop in phrase for stop in vietnamese_stop_words):
            tags.append(phrase)
            
    return list(set(tags))[:10]  # Limit to 10 most relevant tags
```

### **Enhanced RAG Scoring Algorithm**
```python
# Metadata-Aware Scoring System
def calculate_relevance_with_metadata_boosts(query: str, memory: dict, valves) -> float:
    # Base relevance score (content similarity)
    base_score = calculate_base_relevance(query, memory["content"])
    
    if not valves.ENABLE_METADATA:
        return base_score
        
    current_time = time.time()
    boosted_score = base_score
    
    # Recent memory boost
    time_diff = current_time - memory.get("unix_timestamp", 0)
    if time_diff < 3600:  # Within 1 hour
        boosted_score *= valves.RECENT_MEMORY_BOOST
        
    # Same session boost
    if memory.get("session_id") == self.current_session_id:
        boosted_score *= valves.SAME_SESSION_BOOST
        
    # Positive feedback boost
    if memory.get("user_feedback") == "positive":
        boosted_score *= valves.POSITIVE_FEEDBACK_BOOST
        
    # Tag relevance boost
    query_lower = query.lower()
    for tag in memory.get("tags", []):
        if tag.lower() in query_lower:
            boosted_score *= 1.15  # Tag match boost
            
    return boosted_score

def calculate_base_relevance(query: str, content: str) -> float:
    """Calculate base content similarity"""
    query_words = set(query.lower().split())
    content_words = set(content.lower().split())
    
    if not query_words or not content_words:
        return 0.0
        
    intersection = query_words & content_words
    union = query_words | content_words
    
    return len(intersection) / len(union) if union else 0.0
```

## 🔧 **DOCKER DEPLOYMENT TECHNOLOGY**

### **Container Integration Architecture**
```python
# Docker Container Integration
CONTAINER_NAME: pipelines
CONTAINER_PORT: 9099
TARGET_PATH: /app/pipelines/optimized_rag_pipeline.py
SOURCE_FILE: standalone_enhanced_rag_pipeline.py
DEPLOYMENT_METHOD: docker_cp_and_restart

# Deployment Configuration
COPY_COMMAND: docker cp {source} {container}:{target}
RESTART_COMMAND: docker restart {container}
LOGS_COMMAND: docker logs {container} --tail 20
VERIFICATION_METHOD: pipeline_loading_check
```

### **Performance Optimizations**
```python
# Memory Management
MEMORY_STORAGE: in_memory          # Fast access to memories
MEMORY_CLEANUP: smart_algorithm    # Based on relevance and age
MEMORY_LIMIT: configurable         # MAX_MEMORIES valve
SESSION_ISOLATION: uuid_based      # Separate session contexts

# Retrieval Optimizations
METADATA_INDEXING: enabled         # Fast metadata-based filtering
SCORING_CACHE: disabled            # Real-time scoring for accuracy
FEEDBACK_LEARNING: enabled         # Continuous improvement
VALVES_CONFIG: real_time          # Dynamic parameter adjustment
```

## 📱 **MOBILE TECHNICAL IMPLEMENTATION - ENHANCED**

### **Application Architecture (Enhanced Backend Options)**
```
┌─────────────────────────────────────────────────┐
│              Flutter Application                │
├─────────────┬─────────────┬─────────────────────┤
│ main.dart   │ Screens     │ Native Integration  │
│ - App Entry │ - Splash    │ - GPU Acceleration  │
│ - Material  │ - Loading   │ - Status Bar        │
│ - Routes    │ - WebView   │ - Connectivity      │
└─────────────┴─────────────┴─────────────────────┘
                     │
            ┌─────────────────────────┐
            │   Enhanced VPS Backend  │
            │   **************:3001   │
            │   - Open WebUI          │
            │   - JSON Fast Search    │
            │   - Traditional RAG     │
            │   - Dual Search API     │
            └─────────────────────────┘
```

### **WebView Integration (Enhanced RAG Support)**
```dart
WebViewController configuration:
- JavaScriptMode.unrestricted          // Full JS support
- Mobile browser user agent            // Compatibility
- NavigationDelegate with progress     // Loading states
- onPageFinished → Robot logo injection
- onPageFinished → GPU optimization
- Backend: Enhanced RAG with metadata
- RAG Features: Rich context, valves configuration, Vietnamese support
```

## 🎨 **DEVELOPMENT TOOLS CREATED**

### **Enhanced RAG Development Kit**
```bash
# Core Development Files
enhanced_rag_with_metadata.py      # Core RAG logic with metadata
standalone_enhanced_rag_pipeline.py # Self-contained pipeline
deploy_enhanced_rag.py             # Automated deployment script
ENHANCED_RAG_METADATA_GUIDE.md     # Documentation and usage guide

# Legacy Development Files (Superseded)
simple_rag_optimization_system.py  # Simple RAG without metadata
simple_rag_pipeline_integration.py # Basic integration layer
optimized_rag_pipeline_simple.py   # Original simple pipeline

# Deployment Tools
auto_deploy_rag_to_container.py    # Container deployment automation
verify_rag_deployment.py           # Deployment verification
RAG_USAGE_GUIDE.md                 # Usage documentation

# Development Tools
docker_integration_script.py       # Docker container management
memory_bank_updater.py             # Memory bank maintenance
valves_configuration_tool.py       # Valves management utility
```

## 🏆 **TECHNOLOGY STACK SUMMARY**

### **Enhanced RAG Architecture Deployed**
- **Metadata System**: Rich context tracking (timestamp, session, source, tags, feedback)
- **Valves Configuration**: 11 real-time configurable parameters
- **Vietnamese Optimization**: Language-specific processing and tag extraction
- **Session Management**: UUID-based conversation isolation
- **Feedback Learning**: User feedback collection and scoring system
- **Production Ready**: Deployed and operational on port 9099

### **Development Efficiency Achieved**
- **Standalone Implementation**: Self-contained pipeline without external dependencies
- **Docker Integration**: Automated deployment with container management
- **Real-time Configuration**: Valves API for dynamic parameter adjustment
- **Vietnamese Support**: Optimized for Vietnamese business content
- **Metadata Intelligence**: Rich context addressing LLM "text trần trụi" complaint

**TECHNOLOGY STATUS**: ✅ **ENHANCED RAG WITH METADATA COMPLETED**
**PERFORMANCE**: 🧠 **RICH CONTEXT WITH CONFIGURABLE INTELLIGENCE**
**COMPATIBILITY**: ✅ **MOBILE + WEB + DOCKER INTEGRATION READY**
