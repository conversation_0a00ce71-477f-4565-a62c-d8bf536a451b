#!/usr/bin/env python3
"""
Open WebUI Table Integration
Tích hợp xử lý bảng biểu tự động vào Open WebUI knowledge upload process
"""

import os
import sys
import json
import tempfile
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
import asyncio

# Import table processors
try:
    from simple_table_rag import extract_tables_simple, format_table_for_rag
    SIMPLE_PROCESSOR_AVAILABLE = True
except ImportError:
    SIMPLE_PROCESSOR_AVAILABLE = False

try:
    from enhanced_table_processor import EnhancedTableProcessor
    ENHANCED_PROCESSOR_AVAILABLE = True
except ImportError:
    ENHANCED_PROCESSOR_AVAILABLE = False

# Import Open WebUI RAG components
try:
    from backend.app.services.openwebui_rag_service import OpenWebUIRAGService
    OPENWEBUI_RAG_AVAILABLE = True
except ImportError:
    OPENWEBUI_RAG_AVAILABLE = False

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OpenWebUITableIntegration:
    """Tích hợp xử lý bảng biểu vào Open WebUI"""
    
    def __init__(self):
        self.simple_available = SIMPLE_PROCESSOR_AVAILABLE
        self.enhanced_available = ENHANCED_PROCESSOR_AVAILABLE
        self.openwebui_available = OPENWEBUI_RAG_AVAILABLE
        
        # Initialize processors
        self.enhanced_processor = None
        if self.enhanced_available:
            self.enhanced_processor = EnhancedTableProcessor()
        
        # Initialize Open WebUI RAG service
        self.rag_service = None
        if self.openwebui_available:
            self.rag_service = OpenWebUIRAGService()
        
        logger.info(f"Table integration initialized:")
        logger.info(f"  Simple processor: {self.simple_available}")
        logger.info(f"  Enhanced processor: {self.enhanced_available}")
        logger.info(f"  Open WebUI RAG: {self.openwebui_available}")

    async def process_document_with_tables(self, file_path: str, filename: str, collection_id: str = None) -> Dict[str, Any]:
        """
        Xử lý document với table extraction tự động
        Đây là function hook vào Open WebUI upload process
        """
        try:
            logger.info(f"🔄 Processing document with table extraction: {filename}")
            
            # Step 1: Extract tables from document
            tables = await self._extract_tables_from_document(file_path, filename)
            
            # Step 2: Process tables into RAG-ready format
            table_chunks = []
            if tables:
                table_chunks = self._create_table_chunks(tables, filename)
                logger.info(f"📊 Extracted {len(tables)} tables → {len(table_chunks)} chunks")
            
            # Step 3: Upload original document to Open WebUI
            original_result = None
            if self.rag_service and collection_id:
                original_result = await self.rag_service.process_document(
                    collection_id, file_path, filename
                )
            
            # Step 4: Add table chunks to collection
            table_results = []
            for i, chunk in enumerate(table_chunks):
                # Create a temporary file for each table chunk
                table_filename = f"{Path(filename).stem}_table_{i+1}.txt"
                
                with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp:
                    tmp.write(chunk['content'])
                    tmp_path = tmp.name
                
                try:
                    if self.rag_service and collection_id:
                        table_result = await self.rag_service.process_document(
                            collection_id, tmp_path, table_filename
                        )
                        table_results.append(table_result)
                finally:
                    # Clean up temp file
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
            
            return {
                'success': True,
                'filename': filename,
                'tables_found': len(tables),
                'table_chunks_created': len(table_chunks),
                'original_document': original_result,
                'table_documents': table_results,
                'enhanced_processing': True
            }
            
        except Exception as e:
            logger.error(f"❌ Error processing {filename}: {e}")
            return {
                'success': False,
                'filename': filename,
                'error': str(e),
                'enhanced_processing': False
            }

    async def _extract_tables_from_document(self, file_path: str, filename: str) -> List[Dict[str, Any]]:
        """Extract tables using available processors"""
        tables = []
        
        # Try enhanced processor first for best results
        if self.enhanced_available and self.enhanced_processor:
            try:
                logger.info("📊 Using Enhanced Table Processor...")
                table_chunks = self.enhanced_processor.process_document_tables(file_path)
                
                for chunk in table_chunks:
                    tables.append({
                        'type': 'enhanced_table',
                        'content': chunk.content,
                        'confidence': getattr(chunk, 'confidence_score', 0.8),
                        'metadata': getattr(chunk, 'metadata', {}),
                        'table_type': getattr(chunk, 'table_type', 'unknown')
                    })
                
                if tables:
                    return tables
                    
            except Exception as e:
                logger.warning(f"Enhanced processor failed: {e}")
        
        # Fallback to simple processor
        if self.simple_available:
            try:
                logger.info("📋 Using Simple Table Processor...")
                simple_tables = extract_tables_simple(file_path)
                
                for table in simple_tables:
                    tables.append({
                        'type': table.get('type', 'simple_table'),
                        'content': table.get('structured_text', ''),
                        'confidence': 0.7,
                        'metadata': {
                            'headers': table.get('headers', []),
                            'rows': len(table.get('rows', [])),
                            'source': table.get('file', filename)
                        },
                        'table_type': table.get('type', 'data_table')
                    })
                
            except Exception as e:
                logger.warning(f"Simple processor failed: {e}")
        
        return tables

    def _create_table_chunks(self, tables: List[Dict], source_filename: str) -> List[Dict[str, Any]]:
        """Create RAG-optimized chunks from extracted tables"""
        chunks = []
        
        for i, table in enumerate(tables):
            # Create enhanced content for RAG
            content = f"BẢNG {i+1} từ {source_filename}\n"
            content += "=" * 50 + "\n\n"
            
            # Add table metadata
            if 'table_type' in table:
                content += f"Loại bảng: {table['table_type']}\n"
            
            if 'confidence' in table:
                content += f"Độ tin cậy: {table['confidence']:.2f}\n"
            
            content += "\n"
            
            # Add table content
            content += table['content']
            
            # Add searchable metadata
            metadata = table.get('metadata', {})
            if metadata.get('headers'):
                content += f"\n\nCác cột trong bảng: {', '.join(metadata['headers'])}"
            
            chunks.append({
                'id': f"{source_filename}_table_{i+1}",
                'content': content,
                'metadata': {
                    'type': 'table',
                    'source_file': source_filename,
                    'table_index': i + 1,
                    'table_type': table.get('table_type', 'unknown'),
                    'confidence': table.get('confidence', 0.7),
                    **metadata
                }
            })
        
        return chunks

# Monkey patch cho Open WebUI
def patch_openwebui_upload():
    """
    Monkey patch Open WebUI's document upload to include table processing
    Chỉ gọi function này khi muốn hook vào Open WebUI
    """
    try:
        # Import Open WebUI modules
        from backend.app.api.v1.endpoints.openwebui_rag import upload_document_to_collection
        
        # Store original function
        original_upload = upload_document_to_collection
        
        # Create integration instance
        integration = OpenWebUITableIntegration()
        
        async def enhanced_upload_document_to_collection(collection_id: str, file):
            """Enhanced upload with table processing"""
            logger.info(f"🔄 Enhanced upload triggered for {file.filename}")
            
            # Save uploaded file temporarily
            with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as tmp:
                content = await file.read()
                tmp.write(content)
                tmp_path = tmp.name
            
            try:
                # Process with table extraction
                result = await integration.process_document_with_tables(
                    tmp_path, file.filename, collection_id
                )
                
                if result['success']:
                    logger.info(f"✅ Enhanced processing complete: {result['tables_found']} tables found")
                    return {
                        "status": "success",
                        "message": f"Document processed with {result['tables_found']} tables extracted",
                        "tables_found": result['tables_found'],
                        "enhanced": True
                    }
                else:
                    # Fallback to original upload
                    logger.warning("Enhanced processing failed, using original upload")
                    return await original_upload(collection_id, file)
                    
            finally:
                # Clean up temp file
                if os.path.exists(tmp_path):
                    os.unlink(tmp_path)
        
        # Replace the original function
        import backend.app.api.v1.endpoints.openwebui_rag
        backend.app.api.v1.endpoints.openwebui_rag.upload_document_to_collection = enhanced_upload_document_to_collection
        
        logger.info("🔌 Open WebUI upload patched with table processing")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to patch Open WebUI: {e}")
        return False

# Standalone integration for testing
async def test_integration():
    """Test the integration with a sample file"""
    integration = OpenWebUITableIntegration()
    
    # Test với file có bảng
    test_files = [
        "sample_table.xlsx",
        "sample_table.csv", 
        "document_with_tables.pdf"
    ]
    
    for test_file in test_files:
        if Path(test_file).exists():
            logger.info(f"🧪 Testing with {test_file}")
            result = await integration.process_document_with_tables(test_file, test_file)
            print(json.dumps(result, indent=2, ensure_ascii=False))
            break
    else:
        logger.warning("No test files found")

def create_integration_config():
    """Tạo config file để enable/disable table integration"""
    config = {
        "table_integration": {
            "enabled": True,
            "processors": {
                "simple": SIMPLE_PROCESSOR_AVAILABLE,
                "enhanced": ENHANCED_PROCESSOR_AVAILABLE
            },
            "settings": {
                "min_confidence": 0.6,
                "auto_patch_openwebui": False,  # Set True để tự động patch
                "create_separate_chunks": True,
                "include_metadata": True
            }
        }
    }
    
    with open("table_integration_config.json", "w", encoding="utf-8") as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    logger.info("📄 Created table_integration_config.json")

def main():
    print("🔗 Open WebUI Table Integration")
    print("=" * 40)
    
    integration = OpenWebUITableIntegration()
    
    print(f"📊 Available processors:")
    print(f"  Simple: {integration.simple_available}")
    print(f"  Enhanced: {integration.enhanced_available}")
    print(f"  Open WebUI RAG: {integration.openwebui_available}")
    
    print(f"\n💡 Usage options:")
    print(f"1. Manual integration:")
    print(f"   python openwebui_table_integration.py patch")
    print(f"")
    print(f"2. Test integration:")
    print(f"   python openwebui_table_integration.py test")
    print(f"")
    print(f"3. Config generation:")
    print(f"   python openwebui_table_integration.py config")
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "patch":
            success = patch_openwebui_upload()
            if success:
                print("✅ Open WebUI patched successfully!")
                print("Now upload files through Open WebUI will automatically process tables")
            else:
                print("❌ Patching failed")
                
        elif command == "test":
            asyncio.run(test_integration())
            
        elif command == "config":
            create_integration_config()
        
        else:
            print(f"❌ Unknown command: {command}")

if __name__ == "__main__":
    main() 