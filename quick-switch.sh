#!/bin/bash

# Quick Model Switch Commands
# Sử dụng nhanh: ./quick-switch.sh gemma4b

case $1 in
    "gemma4b"|"1")
        echo "🔄 <PERSON><PERSON><PERSON><PERSON> sang Gemma 4B..."
        ./model-manager.sh switch 1
        ;;
    "qwen7b"|"2")
        echo "🔄 <PERSON><PERSON><PERSON><PERSON> sang Qwen 7B Coder..."
        ./model-manager.sh switch 2
        ;;
    "qwen14b"|"3")
        echo "🔄 <PERSON><PERSON><PERSON><PERSON> sang Qwen 14B Coder..."
        ./model-manager.sh switch 3
        ;;
    "gemma12b"|"4")
        echo "🔄 <PERSON><PERSON><PERSON><PERSON> sang Gemma 12B..."
        ./model-manager.sh switch 4
        ;;
    "nano"|"5")
        echo "🔄 <PERSON><PERSON><PERSON><PERSON> sang Jan Nano..."
        ./model-manager.sh switch 5
        ;;
    "status"|"s")
        ./model-manager.sh status
        ;;
    "stop")
        ./model-manager.sh stop
        ;;
    *)
        echo "🔄 Quick Model Switch"
        echo "Sử dụng: $0 <model>"
        echo ""
        echo "Models:"
        echo "  gemma4b  (1) - <PERSON> 4B (2.4GB)"
        echo "  qwen7b   (2) - <PERSON>wen 7B Coder (4.4GB)"
        echo "  qwen14b  (3) - <PERSON>wen 14B Coder (8.4GB)"
        echo "  gemma12b (4) - <PERSON> 12B (6.8GB)"
        echo "  nano     (5) - Jan Nano (4.0GB)"
        echo ""
        echo "Commands:"
        echo "  status   (s) - Xem trạng thái"
        echo "  stop         - Dừng tất cả"
        ;;
esac 