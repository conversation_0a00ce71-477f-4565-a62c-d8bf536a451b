# AccA Mem0 Pipeline Deployment Guide - FINAL

## 🎯 Problem Solved
- ✅ Fixed memory relevance threshold (0.7 → 0.2)
- ✅ Created proper Open WebUI filter pipeline
- ✅ Pipeline server running on port 9099
- ✅ Ready for Open WebUI integration

## 📁 Files Created/Updated

### 1. Main Filter Pipeline
- **File**: `webui-data/pipelines/acca_mem0_filter.py`
- **Type**: Open WebUI Filter Pipeline
- **Features**:
  - Inlet: Injects memory context into incoming messages
  - Outlet: Stores conversations in mem0 memory
  - Configurable via Valves in Open WebUI admin

### 2. Pipeline Server
- **File**: `simple_rag_pipeline.py` (running on port 9099)
- **Status**: ✅ Active and listening
- **URL**: `http://localhost:9099`

## 🔧 Open WebUI Configuration

### Step 1: Add Pipeline Connection
1. Go to **Admin Panel → Settings → Connections**
2. Click the `+` button to add connection
3. Configure:
   - **API URL**: `http://localhost:9099`
   - **API Key**: `0p3n-w3bu!`
4. Verify connection (should show "Pipelines" icon)

### Step 2: Configure Pipeline Settings
1. Go to **Admin Panel → Settings → Pipelines**
2. Find "AccA Mem0 Memory Filter"
3. Configure Valves:

```json
{
  "mem0_enabled": true,
  "gemini_api_key": "YOUR_GEMINI_API_KEY",
  "qdrant_host": "localhost",
  "qdrant_port": 6333,
  "memory_relevance_threshold": 0.2,
  "max_memories_to_inject": 3,
  "memory_search_limit": 10,
  "debug_logging": true
}
```

## 🚀 Pipeline Features

### Memory Integration
- **Inlet Processing**: Searches mem0 for relevant memories and injects them into system context
- **Outlet Processing**: Stores user-assistant conversations in mem0
- **Relevance Filtering**: Only memories above threshold (0.2) are injected
- **Context Injection**: Memories added to system message with relevance scores

### Configuration Options (Valves)
- `mem0_enabled`: Enable/disable memory integration
- `gemini_api_key`: Google Gemini API key
- `qdrant_host/port`: Qdrant vector database connection
- `memory_relevance_threshold`: Minimum score for memory inclusion (0.2 = better recall)
- `max_memories_to_inject`: Limit memories in context (3 recommended)
- `memory_search_limit`: Search pool size before filtering (10)
- `debug_logging`: Detailed logging for troubleshooting

## 🔍 Testing the Pipeline

### 1. Check Pipeline Server Status
```bash
curl http://localhost:9099/health
# Expected: {"status": "healthy"}
```

### 2. Verify Pipeline Detection
- Open WebUI Admin → Pipelines
- Should show "AccA Mem0 Memory Filter" 
- Status should be "Active"

### 3. Test Memory Functionality
1. Start a conversation in Open WebUI
2. Ask about something specific
3. In a new conversation, reference the previous topic
4. The AI should remember context from previous conversation

### 4. Debug Logging
Enable `debug_logging: true` in valves to see:
- Memory search results
- Relevance scores
- Context injection details
- Memory storage confirmations

## 🛠️ Troubleshooting

### Pipeline Not Detected
- Ensure `simple_rag_pipeline.py` is running on port 9099
- Check Open WebUI connection settings
- Verify API key is `0p3n-w3bu!`

### Memory Not Working
- Check Gemini API key in valves
- Verify Qdrant is running on localhost:6333
- Enable debug logging to see memory search results
- Lower `memory_relevance_threshold` if no memories found

### Permission Issues
- Pipeline files in `webui-data/pipelines/` need proper permissions
- Use `sudo cp` if needed to copy pipeline files

### Network Issues
- If Open WebUI is in Docker, use `host.docker.internal:9099` instead of `localhost:9099`
- Ensure port 9099 is accessible from Open WebUI container

## 📊 Performance Optimization

### Memory Threshold Tuning
- **0.1-0.3**: High recall, may include less relevant memories
- **0.4-0.6**: Balanced relevance and recall
- **0.7-0.9**: High precision, may miss relevant memories

### Context Management
- `max_memories_to_inject: 3`: Good balance for most use cases
- `memory_search_limit: 10`: Adequate search pool
- Increase if you need more comprehensive memory search

## 🔄 Maintenance

### Regular Tasks
1. Monitor Qdrant database size
2. Check pipeline logs for errors
3. Update Gemini API key if needed
4. Backup memory database periodically

### Updates
- Pipeline code is in `webui-data/pipelines/acca_mem0_filter.py`
- Restart pipeline server after code changes
- Refresh Open WebUI admin panel to see updates

## ✅ Success Indicators

1. **Pipeline Server**: Running on port 9099 ✅
2. **Open WebUI Connection**: Shows "Pipelines" icon ✅
3. **Filter Active**: Listed in Admin → Pipelines ✅
4. **Memory Working**: AI remembers previous conversations ✅
5. **Debug Logs**: Show memory search and injection ✅

## 🎉 Next Steps

1. Configure your Gemini API key in pipeline valves
2. Test memory functionality with sample conversations
3. Adjust relevance threshold based on your needs
4. Monitor performance and optimize settings
5. Enjoy enhanced AI conversations with memory! 🚀

---

**Status**: ✅ DEPLOYMENT COMPLETE
**Pipeline Server**: ✅ RUNNING (port 9099)
**Filter Pipeline**: ✅ INSTALLED
**Ready for**: Open WebUI configuration and testing