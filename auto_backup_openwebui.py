#!/usr/bin/env python3
"""
Auto Backup Script cho Open WebUI
Tự động backup dữ liệu Open WebUI định kỳ
"""

import subprocess
import os
import shutil
from datetime import datetime
import schedule
import time

class OpenWebUIBackup:
    def __init__(self):
        self.container_name = "open-webui-ssl"
        self.volume_name = "acca_open_webui_data"
        self.volume_path = f"/var/lib/docker/volumes/{self.volume_name}/_data"
        self.backup_base_dir = "webui_backups"
        
    def log(self, message):
        """Log với timestamp"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def create_backup_dir(self):
        """Tạo thư mục backup với timestamp"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = f"{self.backup_base_dir}/backup_{timestamp}"
        os.makedirs(backup_dir, exist_ok=True)
        return backup_dir
        
    def backup_database(self, backup_dir):
        """Backup SQLite database"""
        self.log("🗄️ Backup database...")
        
        try:
            # Backup database chính
            subprocess.run(f"sudo cp {self.volume_path}/webui.db {backup_dir}/webui.db", shell=True, check=True)
            
            # Export sang SQL format
            sql_file = f"{backup_dir}/webui_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
            subprocess.run(f"sudo sqlite3 {self.volume_path}/webui.db .dump > {sql_file}", shell=True, check=True)
            
            self.log(f"✅ Database backup hoàn thành: {sql_file}")
            return True
        except subprocess.CalledProcessError as e:
            self.log(f"❌ Lỗi backup database: {e}")
            return False
            
    def backup_files(self, backup_dir):
        """Backup các file và thư mục quan trọng"""
        self.log("📁 Backup files và folders...")
        
        folders_to_backup = ['cache', 'uploads', 'vector_db']
        
        for folder in folders_to_backup:
            source_path = f"{self.volume_path}/{folder}"
            dest_path = f"{backup_dir}/{folder}"
            
            try:
                if os.path.exists(source_path):
                    subprocess.run(f"sudo cp -r {source_path} {dest_path}", shell=True, check=True)
                    self.log(f"✅ Backup {folder} thành công")
                else:
                    self.log(f"⚠️ Thư mục {folder} không tồn tại")
            except subprocess.CalledProcessError as e:
                self.log(f"❌ Lỗi backup {folder}: {e}")
                
    def backup_container_config(self, backup_dir):
        """Backup cấu hình container"""
        self.log("⚙️ Backup cấu hình container...")
        
        try:
            # Export container config
            config_file = f"{backup_dir}/container_config.json"
            subprocess.run(f"docker inspect {self.container_name} > {config_file}", shell=True, check=True)
            
            # Export docker-compose equivalent
            compose_file = f"{backup_dir}/docker_run_command.sh"
            with open(compose_file, 'w') as f:
                f.write(f"""#!/bin/bash
# Docker run command để recreate container
docker run -d --name {self.container_name} \\
  --network webui-network \\
  -p 3000:8080 \\
  -e PIPELINE_URL=http://pipelines:9099 \\
  -v {self.volume_name}:/app/backend/data \\
  ghcr.io/open-webui/open-webui:main
""")
            
            self.log("✅ Backup cấu hình container thành công")
        except subprocess.CalledProcessError as e:
            self.log(f"❌ Lỗi backup cấu hình: {e}")
            
    def cleanup_old_backups(self, keep_days=7):
        """Xóa backup cũ hơn X ngày"""
        self.log(f"🧹 Dọn dẹp backup cũ hơn {keep_days} ngày...")
        
        try:
            # Tìm và xóa backup cũ
            find_cmd = f"find {self.backup_base_dir} -name 'backup_*' -type d -mtime +{keep_days}"
            result = subprocess.run(find_cmd, shell=True, capture_output=True, text=True)
            
            if result.stdout.strip():
                old_backups = result.stdout.strip().split('\n')
                for backup in old_backups:
                    shutil.rmtree(backup)
                    self.log(f"🗑️ Đã xóa backup cũ: {backup}")
            else:
                self.log("✅ Không có backup cũ cần xóa")
                
        except Exception as e:
            self.log(f"❌ Lỗi khi dọn dẹp backup: {e}")
            
    def get_backup_stats(self, backup_dir):
        """Lấy thống kê backup"""
        try:
            result = subprocess.run(f"du -sh {backup_dir}", shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                size = result.stdout.split()[0]
                self.log(f"📊 Kích thước backup: {size}")
                
            # Đếm số file
            result = subprocess.run(f"find {backup_dir} -type f | wc -l", shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                file_count = result.stdout.strip()
                self.log(f"📊 Số file backup: {file_count}")
                
        except Exception as e:
            self.log(f"⚠️ Không thể lấy thống kê backup: {e}")
            
    def run_backup(self):
        """Chạy backup hoàn chỉnh"""
        try:
            self.log("🎯 BẮT ĐẦU BACKUP OPEN WEBUI")
            self.log("=" * 50)
            
            # Tạo thư mục backup
            backup_dir = self.create_backup_dir()
            self.log(f"📁 Thư mục backup: {backup_dir}")
            
            # Kiểm tra container đang chạy
            result = subprocess.run(f"docker ps | grep {self.container_name}", shell=True, capture_output=True)
            if result.returncode != 0:
                self.log("⚠️ Container không đang chạy, backup có thể không đầy đủ")
                
            # Thực hiện backup
            success = True
            success &= self.backup_database(backup_dir)
            self.backup_files(backup_dir)
            self.backup_container_config(backup_dir)
            
            # Thống kê
            self.get_backup_stats(backup_dir)
            
            # Dọn dẹp backup cũ
            self.cleanup_old_backups()
            
            if success:
                self.log("=" * 50)
                self.log("🎉 BACKUP HOÀN THÀNH THÀNH CÔNG!")
                self.log(f"📁 Backup location: {backup_dir}")
                self.log("✅ Database đã được backup")
                self.log("✅ Files và folders đã được backup")
                self.log("✅ Cấu hình container đã được backup")
            else:
                self.log("⚠️ Backup hoàn thành với một số lỗi")
                
        except Exception as e:
            self.log(f"❌ LỖI TRONG QUÁ TRÌNH BACKUP: {e}")
            
    def schedule_backups(self):
        """Lên lịch backup tự động"""
        self.log("⏰ Thiết lập lịch backup tự động...")
        
        # Backup hàng ngày lúc 2:00 AM
        schedule.every().day.at("02:00").do(self.run_backup)
        
        # Backup mỗi 6 tiếng
        schedule.every(6).hours.do(self.run_backup)
        
        self.log("✅ Đã thiết lập lịch backup:")
        self.log("   - Hàng ngày lúc 2:00 AM")
        self.log("   - Mỗi 6 tiếng")
        
        # Chạy backup ngay lập tức
        self.log("🚀 Chạy backup đầu tiên...")
        self.run_backup()
        
        # Vòng lặp chờ
        self.log("⏳ Đang chờ lịch backup tiếp theo...")
        while True:
            schedule.run_pending()
            time.sleep(60)  # Kiểm tra mỗi phút

def main():
    """Hàm main"""
    backup = OpenWebUIBackup()
    
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--schedule":
        backup.schedule_backups()
    else:
        backup.run_backup()

if __name__ == "__main__":
    main()