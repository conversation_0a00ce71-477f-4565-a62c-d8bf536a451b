"""
title: AccA Memory Pipeline (Official mem0-owui)
author: AccA Project (based on <PERSON><PERSON><PERSON>)
date: 2025-01-08
version: 1.1
license: MIT
description: Official mem0-owui pipeline customized for AccA infrastructure
requirements: mem0ai==0.1.114, pydantic==2.10.6
"""

import os
from typing import ClassVar, List, Optional
from pydantic import BaseModel, Field, model_validator
import asyncio

# Safe imports with fallbacks
try:
    from schemas import OpenAIChatMessage
except ImportError:
    # Fallback if schemas not available
    class OpenAIChatMessage:
        pass

try:
    from mem0 import AsyncMemory
    MEM0_AVAILABLE = True
except ImportError:
    MEM0_AVAILABLE = False
    class AsyncMemory:
        @classmethod
        async def from_config(cls, config):
            raise ImportError("mem0ai not installed")


class Pipeline:
    class Valves(BaseModel):
        pipelines: List[str] = ["*"]
        priority: int = 0
        user_id: str = Field(
            default="default_user", description="Default user ID for memory operations"
        )

        # Vector store config (AccA optimized)
        qdrant_host: str = Field(
            default="localhost", description="Qdrant vector database host"
        )
        qdrant_port: str = Field(
            default="6333", description="Qdrant vector database port"
        )
        collection_name: str = Field(
            default="mem0_gemini_768", description="AccA Qdrant collection for Gemini embeddings"
        )

        # LLM config (Gemini for AccA)
        llm_provider: str = Field(
            default="gemini", description="LLM provider"
        )
        llm_api_key: str = Field(
            default=os.getenv("GEMINI_API_KEY", ""), description="Gemini API key"
        )
        llm_model: str = Field(
            default="gemini-2.5-flash", description="Gemini model name"
        )
        llm_base_url: str = Field(
            default="", description="LLM API base URL (not needed for Gemini)"
        )
        llm_temperature: float = Field(
            default=0.1, description="LLM temperature"
        )
        llm_max_tokens: int = Field(
            default=1000, description="LLM max tokens"
        )

        # Embedder config (Gemini for AccA)
        embedder_provider: str = Field(
            default="gemini", description="Embedding provider"
        )
        embedder_api_key: str = Field(
            default=os.getenv("GEMINI_API_KEY", ""), description="Gemini API key for embeddings"
        )
        embedder_model: str = Field(
            default="text-embedding-004", description="Gemini embedding model"
        )
        embedder_dims: int = Field(
            default=768, description="Embedding dimensions for Gemini"
        )

        # Memory behavior
        max_memories: int = Field(
            default=5, description="Maximum memories to inject into context"
        )
        relevance_threshold: float = Field(
            default=0.6, description="Minimum relevance score for memory inclusion"
        )
        auto_store_messages: bool = Field(
            default=True, description="Automatically store conversation messages"
        )
        enable_debug: bool = Field(
            default=False, description="Enable debug logging"
        )

    def __init__(self):
        self.type = "filter"
        self.valves = self.Valves()
        self.m = None  # Initialize memory client to None
        self.memory_available = MEM0_AVAILABLE
        
        # Log initialization status
        if self.memory_available:
            print("[AccA-Memory-Official] Pipeline initialized with memory support")
        else:
            print("[AccA-Memory-Official] Pipeline initialized - memory disabled (mem0ai not found)")

    def debug_log(self, message: str):
        """Debug logging with AccA prefix"""
        if self.valves.enable_debug:
            print(f"[AccA-Memory-DEBUG] {message}")

    async def on_valves_updated(self):
        """Called when pipeline valves are updated"""
        self.debug_log("Valves updated - reinitializing memory client")
        if self.memory_available:
            try:
                print("[AccA-Memory-Official] Initializing mem0 client")
                self.m = await self.init_mem_zero()
                print("[AccA-Memory-Official] mem0 client initialized successfully")
            except Exception as e:
                print(f"[AccA-Memory-ERROR] Failed to initialize mem0: {e}")

    async def on_startup(self):
        """Called when pipeline starts"""
        print(f"[AccA-Memory-Official] Pipeline startup: {__name__}")
        # Auto-initialize memory client
        if self.memory_available and self.m is None:
            await self.on_valves_updated()

    async def on_shutdown(self):
        """Called when pipeline shuts down"""
        print(f"[AccA-Memory-Official] Pipeline shutdown: {__name__}")

    async def add_message_to_mem0(self, user_id: str, message: dict):
        """Add message to memory store"""
        if not self.memory_available or not self.valves.auto_store_messages:
            return
            
        try:
            if self.m:
                await self.m.add(user_id=user_id, messages=[message])
                self.debug_log(f"Added message to mem0 for user: {user_id}")
        except Exception as e:
            print(f"[AccA-Memory-ERROR] Failed to store message: {e}")

    async def inlet(self, body: dict, user: Optional[dict] = None) -> dict:
        """
        Main pipeline method - processes requests before they go to the LLM
        Injects relevant memories into the conversation context
        """
        
        # Skip if memory not available
        if not self.memory_available:
            self.debug_log("Memory not available - passing through")
            return body

        # Initialize memory client if needed
        if self.m is None:
            try:
                print("[AccA-Memory-Official] Initializing mem0 client on-demand")
                self.m = await self.init_mem_zero()
                self.debug_log("Memory client initialized on-demand")
            except Exception as e:
                print(f"[AccA-Memory-ERROR] Memory initialization failed: {e}")
                return body

        self.debug_log("Processing request with official mem0-owui integration")

        # Extract messages
        messages = body.get("messages", [])
        if not messages:
            self.debug_log("No messages found in request")
            return body

        # Skip tasks and special requests
        metadata = body.get("metadata", {})
        if "task" in metadata:
            self.debug_log("Skipping task request")
            return body

        # Determine user ID
        current_user_id = self.valves.user_id
        if user and "id" in user:
            current_user_id = user["id"]
        
        self.debug_log(f"Processing for user: {current_user_id}")

        # Find user and assistant messages
        user_message = None
        assistant_message = None
        
        for msg in reversed(messages):
            if msg.get("role") == "user" and not user_message:
                user_message = msg.get("content", "")
            elif msg.get("role") == "assistant" and not assistant_message:
                assistant_message = msg.get("content", "")

        if not user_message:
            self.debug_log("No user message found")
            return body

        try:
            # Search for relevant memories
            self.debug_log(f"Searching memories for query: {user_message[:50]}...")
            
            memory_results = await self.m.search(
                user_id=current_user_id, 
                query=user_message,
                limit=self.valves.max_memories
            )

            # Store previous assistant message
            if assistant_message and self.valves.auto_store_messages:
                asyncio.create_task(
                    self.add_message_to_mem0(
                        current_user_id,
                        {"role": "assistant", "content": assistant_message}
                    )
                )

            # Store current user message
            if self.valves.auto_store_messages:
                asyncio.create_task(
                    self.add_message_to_mem0(
                        current_user_id,
                        {"role": "user", "content": user_message}
                    )
                )

            self.debug_log(f"Retrieved memories: {memory_results}")

            # Filter memories by relevance and inject context
            if memory_results and "results" in memory_results:
                relevant_memories = []
                for memory in memory_results["results"]:
                    score = memory.get("score", 1.0)
                    if score >= self.valves.relevance_threshold:
                        relevant_memories.append(memory)

                self.debug_log(f"Found {len(relevant_memories)} relevant memories")

                if relevant_memories:
                    # Create memory context
                    memory_context = "\n\n📚 Relevant memories from previous conversations:\n"
                    for i, memory in enumerate(relevant_memories, 1):
                        memory_text = memory.get('memory', str(memory))
                        memory_context += f"  {i}. {memory_text}\n"
                    
                    memory_context += "\nUse this context to provide more personalized and informed responses.\n"

                    # Find or create system message
                    system_message = None
                    for msg in messages:
                        if msg.get("role") == "system":
                            system_message = msg
                            break

                    if system_message:
                        # Append to existing system message
                        system_message["content"] += memory_context
                        self.debug_log("Added memory context to existing system message")
                    else:
                        # Create new system message
                        messages.insert(0, {
                            "role": "system",
                            "content": f"You are a helpful AI assistant with access to conversation history.{memory_context}"
                        })
                        self.debug_log("Created new system message with memory context")

                    # Update the request body
                    body["messages"] = messages
                    
                    self.debug_log(f"Successfully injected {len(relevant_memories)} memories into context")

        except Exception as e:
            print(f"[AccA-Memory-ERROR] Error processing memories: {e}")
            # Continue without memory context on error

        return body

    async def init_mem_zero(self):
        """Initialize the mem0 AsyncMemory client with AccA configuration"""
        if not self.memory_available:
            raise ImportError("mem0ai package not available")

        config = {
            "vector_store": {
                "provider": "qdrant",
                "config": {
                    "host": self.valves.qdrant_host,
                    "port": int(self.valves.qdrant_port),
                    "collection_name": self.valves.collection_name,
                },
            },
            "llm": {
                "provider": self.valves.llm_provider,
                "config": {
                    "api_key": self.valves.llm_api_key,
                    "model": self.valves.llm_model,
                    "temperature": self.valves.llm_temperature,
                    "max_tokens": self.valves.llm_max_tokens,
                },
            },
            "embedder": {
                "provider": self.valves.embedder_provider,
                "config": {
                    "api_key": self.valves.embedder_api_key,
                    "model": self.valves.embedder_model,
                    "embedding_dims": self.valves.embedder_dims,
                },
            },
        }

        self.debug_log("Creating AsyncMemory client with AccA configuration")
        
        try:
            return await AsyncMemory.from_config(config)
        except Exception as e:
            print(f"[AccA-Memory-ERROR] Failed to create memory client: {e}")
            print(f"[AccA-Memory-HINT] Check Qdrant connection and Gemini API key")
            raise 