#!/bin/bash

# Add all available models to Open WebUI
# Author: AI Assistant
# Date: $(date)

set -e

echo "🔍 Adding all available models to Open WebUI..."

MODELS_DIR="/opt/llama-cpp/models"
HOST_IP=$(hostname -I | awk '{print $1}')
LLAMACPP_PORT="11434"

# Create model switching script
echo "📝 Creating model switching capability..."

cat > /opt/llama-cpp/switch-model.sh << 'EOF'
#!/bin/bash

MODEL_NAME="$1"
MODELS_DIR="/opt/llama-cpp/models"

if [ -z "$MODEL_NAME" ]; then
    echo "Available models:"
    ls -1 $MODELS_DIR/*.gguf | xargs -I {} basename {} .gguf
    echo ""
    echo "Usage: $0 <model-name>"
    exit 1
fi

MODEL_FILE="$MODELS_DIR/${MODEL_NAME}.gguf"
if [ ! -f "$MODEL_FILE" ]; then
    MODEL_FILE=$(find $MODELS_DIR -name "*${MODEL_NAME}*" -type f | head -1)
    if [ -z "$MODEL_FILE" ]; then
        echo "❌ Model not found: $MODEL_NAME"
        exit 1
    fi
fi

echo "🔄 Switching to model: $(basename $MODEL_FILE)"

# Stop current service
sudo systemctl stop llama-cpp-optimized || true

# Update service file with new model
sudo sed -i "s|--model [^ ]*|--model $MODEL_FILE|" /etc/systemd/system/llama-cpp-optimized.service

# Reload and restart
sudo systemctl daemon-reload
sudo systemctl start llama-cpp-optimized

echo "✅ Model switched to: $(basename $MODEL_FILE)"
echo "🦙 API available at: http://localhost:11434"
EOF

chmod +x /opt/llama-cpp/switch-model.sh

# List all available models
echo "📦 Available models in $MODELS_DIR:"
if [ -d "$MODELS_DIR" ]; then
    for model in "$MODELS_DIR"/*.gguf; do
        if [ -f "$model" ]; then
            model_name=$(basename "$model")
            model_size=$(du -h "$model" | cut -f1)
            echo "  📄 $model_name ($model_size)"
        fi
    done
else
    echo "❌ Models directory not found: $MODELS_DIR"
    exit 1
fi

echo ""
echo "🔧 Current model in service:"
grep -o '\--model [^[:space:]]*' /etc/systemd/system/llama-cpp-optimized.service | cut -d' ' -f2

echo ""
echo "✅ Model switching setup complete!"
echo ""
echo "📖 Usage examples:"
echo "  # List available models"
echo "  /opt/llama-cpp/switch-model.sh"
echo ""
echo "  # Switch to specific model"
echo "  /opt/llama-cpp/switch-model.sh gemma-3-4b-it-q4_k_m"
echo "  /opt/llama-cpp/switch-model.sh qwen"
echo ""
echo "🌐 Open WebUI should now detect the current model automatically!"
echo "📱 Access Open WebUI at: http://localhost:3000" 