# 🤖 Gemini 2.5 Flash Lite Prompts trong Jina Crawler

## 📋 **<PERSON><PERSON>c Prompt đ<PERSON><PERSON><PERSON> sử dụng**

### **1. HTML to Markdown (Chính)**
```text
Convert the following HTML content to clean, well-formatted Markdown. Preserve Vietnamese text exactly, maintain proper formatting, and extract the main content while removing navigation, ads, and irrelevant elements.

HTML Content:
{content}

Markdown Output:
```

### **2. Summarize**
```text
Summarize the following Vietnamese content in a concise manner while preserving key information:

Content:
{content}

Summary:
```

### **3. Clean**
```text
Clean and format the following Vietnamese content to readable Markdown:

Content:
{content}

Cleaned Markdown:
```

## ⚙️ **Cấu hình API**

### **Model**: `gemini-2.5-flash-lite`
### **Generation Config**:
```json
{
  "temperature": 0.0,
  "maxOutputTokens": 2048,
  "topK": 1,
  "topP": 1.0
}
```

## 🎯 **Quy trình xử lý**

1. **Crawl HTML** → BeautifulSoup cleaning → **Extract main content**
2. **Truncate content** (max 8000 chars) → **Apply html_to_markdown prompt**
3. **Call Gemini API** → **Return Markdown output**

## 🇻🇳 **Tối ưu cho tiếng Việt**

### **Smart Truncation**:
- Tìm vị trí kết thúc câu tiếng Việt hợp lý
- Các dấu kết thúc: `. `, `.\n`, `? `, `?\n`, `! `, `!\n`, v.v.

### **Vietnamese Sentence Endings**:
```python
vietnamese_endings = [
    '. ', '.\n', '? ', '?\n', '! ', '!\n',
    '." ', '."', '?" ', '?"', '!" ', '!"',
    '.)', '.)', '?)', '?)', '!)', '!)'
]
```

## 📊 **Thông số hiệu suất**

- **Max content length**: 8000 characters
- **Max output tokens**: 2048 tokens
- **Processing time**: ~2-3 seconds (sub-second API call)
- **Temperature**: 0.0 (deterministic output)

## 🧪 **Test với nội dung mẫu**

### **Input HTML**:
```html
<h1>Tin tức công nghệ</h1>
<p>Trí tuệ nhân tạo đang phát triển mạnh mẽ.</p>
```

### **Output Markdown**:
```markdown
# Tin tức công nghệ

Trí tuệ nhân tạo đang phát triển mạnh mẽ.
```

## 🔧 **Retry Logic**

### **Exponential Backoff**:
```python
for attempt in range(max_retries + 1):
    try:
        # API call
    except Exception:
        wait_time = 2 ** attempt  # 1s, 2s, 4s, 8s...
        await asyncio.sleep(wait_time)
```

## 🚀 **Ưu điểm**

1. **Tốc độ**: Sub-second processing với Gemini Flash Lite
2. **Chất lượng**: Bảo tồn chính xác văn bản tiếng Việt
3. **Độ tin cậy**: Retry logic và error handling
4. **Tối ưu**: Smart truncation cho tiếng Việt
5. **Tương thích**: Output Markdown sạch sẽ

## 📈 **Use Cases**

### **1. News Processing**:
- Tóm tắt tin tức từ các trang như dantri.com.vn, vnexpress.net
- Trích xuất nội dung chính, loại bỏ quảng cáo

### **2. Content Transformation**:
- Chuyển HTML → Markdown cho AI processing
- Chuẩn hóa định dạng nội dung

### **3. Research & Analysis**:
- Xử lý hàng loạt trang web
- Trích xuất thông tin có cấu trúc

---

**📝 Note**: Prompt chính `html_to_markdown` được sử dụng trong 90% các trường hợp để chuyển đổi nội dung web thành định dạng Markdown sạch sẽ, sẵn sàng cho các tác vụ AI tiếp theo.