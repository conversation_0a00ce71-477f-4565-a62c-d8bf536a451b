#!/bin/bash
# Aggressive Configuration Injection Service

DB_PATH="/home/<USER>/open-webui/backend/data/webui.db"

while true; do
    sleep 10
    
    # Check current config
    CURRENT_ENGINE=$(sqlite3 "$DB_PATH" "SELECT json_extract(data, '$.embedding.engine') FROM config ORDER BY updated_at DESC LIMIT 1;" 2>/dev/null)
    
    if [ "$CURRENT_ENGINE" != "openai" ]; then
        echo "$(date): 🚨 Config corrupted! Engine: $CURRENT_ENGINE - Forcing fix..."
        
        # Force correct config
        sqlite3 "$DB_PATH" "
            UPDATE config SET 
                data = json_patch(data, '{"embedding": {"engine": "openai", "model": "text-embedding-004", "url": "https://generativelanguage.googleapis.com/v1beta", "key": "AIzaSyAfW1U1L2K_eW7TVwRpYN1r8G9W-wXix4A", "dimension": 768}}'),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = (SELECT MAX(id) FROM config);
        " 2>/dev/null
        
        echo "$(date): ✅ Configuration forcefully corrected"
    else
        echo "$(date): ✅ Configuration OK"
    fi
done
