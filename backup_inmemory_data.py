#!/usr/bin/env python3
"""
Backup script to extract current in-memory data from the running pipeline
before switching to Qdrant persistence.
"""

import requests
import json
import datetime
import os

def backup_pipeline_memories():
    """
    Try to extract current memories from the running pipeline
    by making a test query to trigger memory search
    """
    print("🔄 Attempting to backup current in-memory data...")
    
    # Try to get pipeline status/info
    try:
        # Check if pipeline is accessible via Open WebUI API
        webui_url = "http://localhost:3000"  # Open WebUI default port
        
        # Create a backup directory
        backup_dir = "memory_backup"
        os.makedirs(backup_dir, exist_ok=True)
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"{backup_dir}/inmemory_backup_{timestamp}.json"
        
        print(f"📁 Created backup directory: {backup_dir}")
        print(f"📄 Backup file: {backup_file}")
        
        # Since we can't directly access the in-memory data,
        # let's document what we know about the current state
        backup_info = {
            "backup_timestamp": timestamp,
            "backup_date": datetime.datetime.now().isoformat(),
            "pipeline_file": "webui-data/pipelines/mem0-owui-gemini-3072-fixed/mem0-owui-gemini-3072-fixed.py",
            "current_storage": "in-memory dictionary (self.memories = {})",
            "issue": "Data will be lost on container restart",
            "solution": "Migrate to Qdrant persistence",
            "qdrant_collections_before_fix": {
                "mem0_collections": "655+ memories (last updated July 17th)",
                "ws_collections": "30k+ document embeddings",
                "total_qdrant_memories": "30,608"
            },
            "webui_db_state": {
                "chat_records": 182,
                "last_chat_timestamp": "July 15th"
            },
            "pipeline_logs_evidence": [
                "Recent 'Stored memory' entries in acca-pipelines logs",
                "Recent 'Searching memories' entries",
                "Confirms active memory processing in RAM"
            ],
            "risk_assessment": "HIGH - All recent conversations (July 18-21) stored only in RAM",
            "recommended_action": "Immediate migration to Qdrant persistence"
        }
        
        # Save backup info
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(backup_info, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Backup information saved to: {backup_file}")
        print("⚠️  Note: In-memory data cannot be directly extracted")
        print("📋 Documented current state and migration plan")
        
        return backup_file
        
    except Exception as e:
        print(f"❌ Error during backup: {e}")
        return None

if __name__ == "__main__":
    backup_file = backup_pipeline_memories()
    if backup_file:
        print(f"\n🎯 Next step: Fix pipeline to use Qdrant persistence")
        print(f"📁 Backup saved: {backup_file}")
    else:
        print("\n❌ Backup failed, but proceeding with fix is still recommended")