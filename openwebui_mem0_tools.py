#!/usr/bin/env python3
"""
Open WebUI Mem0 Integration Tools
Tạo các function/tool để Open WebUI có thể sử dụng Mem0 memory system
"""

import json
import os
from typing import Dict, Any, List, Optional
from mem0 import Memory
import requests

class OpenWebUIMem0Integration:
    def __init__(self):
        self.memory = None
        self._initialize_memory()
    
    def _initialize_memory(self):
        """Khởi tạo Mem0 memory system với Gemini API"""
        try:
            # Kiểm tra file cấu hình trước
            config_files = [
                "backend/app/config/mem0_config.json",
                "data/mem0_config.json"
            ]
            
            config = None
            for config_file in config_files:
                if os.path.exists(config_file):
                    with open(config_file, 'r') as f:
                        config = json.load(f)
                        print(f"✅ Loaded config from {config_file}")
                        break
            
            # Nếu không có config file, tạo config mặc định với Gemini
            if not config:
                gemini_api_key = os.getenv("GOOGLE_AI_API_KEY") or os.getenv("GEMINI_API_KEY")
                openai_api_key = os.getenv("OPENAI_API_KEY")
                
                if gemini_api_key:
                    # Ưu tiên Gemini API
                    config = {
                        "llm": {
                            "provider": "google_ai",
                            "config": {
                                "model": "gemini-2.5-flash",
                                "api_key": gemini_api_key,
                                "temperature": 0.1
                            }
                        },
                        "embedder": {
                            "provider": "google_ai",
                            "config": {
                                "model": "text-embedding-004",
                                "api_key": gemini_api_key,
                                "dimensions": 768
                            }
                        }
                    }
                    print("✅ Using Gemini API for Mem0")
                elif openai_api_key:
                    # Fallback to OpenAI
                    config = {
                        "embedder": {
                            "provider": "openai",
                            "config": {
                                "model": "text-embedding-3-small",
                                "api_key": openai_api_key
                            }
                        }
                    }
                    print("✅ Using OpenAI API for Mem0")
                else:
                    raise Exception("No API key found. Set GOOGLE_AI_API_KEY or OPENAI_API_KEY")
            
            self.memory = Memory.from_config(config)
            print("✅ Mem0 initialized successfully")
            
        except Exception as e:
            print(f"❌ Mem0 initialization failed: {e}")
            self.memory = None

# Open WebUI Functions

def add_memory(text: str, user_id: str = "default") -> str:
    """
    Thêm memory mới vào hệ thống
    """
    try:
        integration = OpenWebUIMem0Integration()
        if not integration.memory:
            return "❌ Mem0 chưa được khởi tạo. Vui lòng kiểm tra cấu hình."
        
        result = integration.memory.add(text, user_id=user_id)
        
        if result:
            return f"✅ **Đã lưu memory thành công**\n📝 Nội dung: {text[:100]}{'...' if len(text) > 100 else ''}"
        else:
            return "❌ Không thể lưu memory"
            
    except Exception as e:
        return f"❌ Lỗi khi lưu memory: {str(e)}"

def search_memories(query: str, user_id: str = "default", limit: int = 5) -> str:
    """
    Tìm kiếm memories liên quan
    """
    try:
        integration = OpenWebUIMem0Integration()
        if not integration.memory:
            return "❌ Mem0 chưa được khởi tạo. Vui lòng kiểm tra cấu hình."
        
        results = integration.memory.search(query, user_id=user_id, limit=limit)
        
        if results and "results" in results and results["results"]:
            response = f"🔍 **Tìm thấy {len(results['results'])} memories liên quan:**\n\n"
            
            for i, memory in enumerate(results["results"], 1):
                memory_text = memory.get("memory", "")
                score = memory.get("score", 0)
                response += f"**{i}.** {memory_text} (độ liên quan: {score:.2f})\n\n"
            
            return response
        else:
            return f"🔍 Không tìm thấy memories liên quan đến: '{query}'"
            
    except Exception as e:
        return f"❌ Lỗi khi tìm kiếm memories: {str(e)}"

def get_all_memories(user_id: str = "default") -> str:
    """
    Lấy tất cả memories của user
    """
    try:
        integration = OpenWebUIMem0Integration()
        if not integration.memory:
            return "❌ Mem0 chưa được khởi tạo. Vui lòng kiểm tra cấu hình."
        
        results = integration.memory.get_all(user_id=user_id)
        
        if results:
            response = f"📚 **Tất cả memories ({len(results)} items):**\n\n"
            
            for i, memory in enumerate(results, 1):
                memory_text = memory.get("memory", "")
                created_at = memory.get("created_at", "")
                response += f"**{i}.** {memory_text}\n📅 {created_at}\n\n"
            
            return response
        else:
            return "📚 Chưa có memories nào được lưu."
            
    except Exception as e:
        return f"❌ Lỗi khi lấy memories: {str(e)}"

def delete_memory(memory_id: str, user_id: str = "default") -> str:
    """
    Xóa một memory cụ thể
    """
    try:
        integration = OpenWebUIMem0Integration()
        if not integration.memory:
            return "❌ Mem0 chưa được khởi tạo. Vui lòng kiểm tra cấu hình."
        
        result = integration.memory.delete(memory_id=memory_id)
        
        if result:
            return f"✅ Đã xóa memory ID: {memory_id}"
        else:
            return f"❌ Không thể xóa memory ID: {memory_id}"
            
    except Exception as e:
        return f"❌ Lỗi khi xóa memory: {str(e)}"

def chat_with_memory(message: str, user_id: str = "default") -> str:
    """
    Chat với AI sử dụng memory context
    """
    try:
        integration = OpenWebUIMem0Integration()
        if not integration.memory:
            return "❌ Mem0 chưa được khởi tạo. Vui lòng kiểm tra cấu hình."
        
        # Tìm memories liên quan
        relevant_memories = integration.memory.search(message, user_id=user_id, limit=3)
        
        context = ""
        if relevant_memories and "results" in relevant_memories and relevant_memories["results"]:
            context = "🧠 **Memories liên quan:**\n"
            for memory in relevant_memories["results"]:
                context += f"- {memory.get('memory', '')}\n"
            context += "\n"
        
        # Lưu tin nhắn hiện tại vào memory
        integration.memory.add(message, user_id=user_id)
        
        return f"{context}💬 **Tin nhắn của bạn đã được lưu vào memory:** {message}"
        
    except Exception as e:
        return f"❌ Lỗi trong chat với memory: {str(e)}"

# Tool definitions cho Open WebUI
OPENWEBUI_TOOLS = [
    {
        "name": "add_memory",
        "description": "Thêm thông tin vào memory system",
        "parameters": {
            "type": "object",
            "properties": {
                "text": {"type": "string", "description": "Nội dung cần lưu vào memory"},
                "user_id": {"type": "string", "description": "ID của user (mặc định: 'default')"}
            },
            "required": ["text"]
        }
    },
    {
        "name": "search_memories",
        "description": "Tìm kiếm thông tin trong memory",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "Từ khóa tìm kiếm"},
                "user_id": {"type": "string", "description": "ID của user (mặc định: 'default')"},
                "limit": {"type": "integer", "description": "Số lượng kết quả tối đa (mặc định: 5)"}
            },
            "required": ["query"]
        }
    },
    {
        "name": "chat_with_memory",
        "description": "Chat với AI sử dụng memory context",
        "parameters": {
            "type": "object",
            "properties": {
                "message": {"type": "string", "description": "Tin nhắn chat"},
                "user_id": {"type": "string", "description": "ID của user (mặc định: 'default')"}
            },
            "required": ["message"]
        }
    }
]

if __name__ == "__main__":
    # Test functions
    print("🧪 Testing Mem0 integration...")
    
    # Test initialization
    integration = OpenWebUIMem0Integration()
    if integration.memory:
        print("✅ Mem0 integration test successful")
        
        # Test add memory
        result = add_memory("Test memory from integration", "test_user")
        print(f"Add memory result: {result}")
        
        # Test search
        result = search_memories("test", "test_user")
        print(f"Search result: {result}")
        
    else:
        print("❌ Mem0 integration test failed")
        print("💡 Cần cấu hình API key trong environment hoặc config file")