#!/bin/bash

# REMOVE ALL UNTRACKED DIRS: Remove all untracked directories from git tracking
set -e

echo "🧹 Removing all untracked directories from Git tracking..."

# Check if .git_disabled exists
if [ ! -d ".git_disabled" ]; then
    echo "❌ Error: .git_disabled directory not found"
    exit 1
fi

# Temporarily enable git repository
echo "🔌 Enabling git repository..."
mv .git_disabled .git

# Find and remove all untracked directories from git tracking
echo "🗑️  Removing untracked directories from git tracking..."
find . -type d \( -name "venv" -o -name "*_env" -o -name "node_modules" -o -name "__pycache__" -o -name ".git" \) | grep -v "^./.git" | while read -r dir; do
    echo "   - Removing $dir"
    git rm -r --cached "$dir" 2>/dev/null || true
    git reset HEAD "$dir" 2>/dev/null || true
done

# Add updated .gitignore to git
echo "📝 Adding updated .gitignore to git..."
cat >> .gitignore << 'EOF'

# Ignore all virtual environments, node_modules, __pycache__, and nested git repos
**/venv/
**/*_env/
**/node_modules/
**/__pycache__/
**/.git/
EOF
git add .gitignore

# Commit the changes
echo "💾 Committing changes..."
git commit -m "🧹 Remove all untracked directories from git tracking

- Remove all virtual environments (venv, *_env) from git tracking
- Remove all node_modules directories from git tracking
- Remove all __pycache__ directories from git tracking
- Remove all nested .git repositories from git tracking
- Keep local files intact
- Update .gitignore to prevent future tracking"

# Disable git repository again
echo "🔌 Disabling git repository..."
mv .git .git_disabled

echo ""
echo "✅ All untracked directories removed from git tracking!"
echo ""
echo "📊 Current status:"
echo "• ✅ All untracked directories are no longer tracked by git"
echo "• ✅ Local files are preserved"
echo "• ✅ .gitignore is updated to prevent future tracking"
echo ""
echo "🔧 To verify the changes, you can:"
echo "   1. Rename .git_disabled back to .git temporarily"
echo "   2. Run 'git status' to see that untracked directories are no longer listed"
echo "   3. Rename .git back to .git_disabled"