#!/usr/bin/env python3
"""
MobiFone RAG Performance Optimization Script
"""

import asyncio
import aiosqlite
import numpy as np
import pickle
import json
from typing import Dict, List, Any, Optional
import time

class MobiFoneRAGOptimizer:
    """Optimize MobiFone RAG for faster responses"""
    
    def __init__(self, db_path: str = "mobifone_rag.db"):
        self.db_path = db_path
        self.cache = {}  # Simple in-memory cache
        self.cache_ttl = 300  # 5 minutes
        
    async def add_search_index(self):
        """Add database indexes for faster searches"""
        
        print("🔧 Adding database indexes for optimization...")
        
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # Add indexes for common search patterns
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_department ON mobifone_documents(department)",
                    "CREATE INDEX IF NOT EXISTS idx_doc_type ON mobifone_documents(document_type)",
                    "CREATE INDEX IF NOT EXISTS idx_title ON mobifone_documents(title)",
                    "CREATE INDEX IF NOT EXISTS idx_content_length ON mobifone_documents(LENGTH(content))",
                    "CREATE INDEX IF NOT EXISTS idx_last_updated ON mobifone_documents(last_updated)",
                ]
                
                for index_sql in indexes:
                    await db.execute(index_sql)
                    
                await db.commit()
                print("✅ Database indexes added successfully")
                return True
                
        except Exception as e:
            print(f"❌ Error adding indexes: {e}")
            return False
    
    async def precompute_document_vectors(self):
        """Precompute and cache document vector norms for faster similarity"""
        
        print("🔧 Precomputing document vector norms...")
        
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # Check if norm column exists
                cursor = await db.execute("PRAGMA table_info(mobifone_documents)")
                columns = await cursor.fetchall()
                has_norm = any(col[1] == 'embedding_norm' for col in columns)
                
                if not has_norm:
                    # Add norm column
                    await db.execute("ALTER TABLE mobifone_documents ADD COLUMN embedding_norm REAL")
                    
                    # Compute norms for existing documents
                    cursor = await db.execute("SELECT doc_id, embedding FROM mobifone_documents WHERE embedding IS NOT NULL")
                    rows = await cursor.fetchall()
                    
                    for doc_id, embedding_blob in rows:
                        if embedding_blob:
                            embedding = pickle.loads(embedding_blob)
                            norm = float(np.linalg.norm(embedding))
                            
                            await db.execute(
                                "UPDATE mobifone_documents SET embedding_norm = ? WHERE doc_id = ?",
                                (norm, doc_id)
                            )
                    
                    await db.commit()
                    print(f"✅ Computed norms for {len(rows)} documents")
                else:
                    print("✅ Document norms already exist")
                    
                return True
                
        except Exception as e:
            print(f"❌ Error precomputing norms: {e}")
            return False
    
    async def create_content_summaries(self):
        """Create short summaries for faster preview"""
        
        print("🔧 Creating content summaries...")
        
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # Check if summary column exists
                cursor = await db.execute("PRAGMA table_info(mobifone_documents)")
                columns = await cursor.fetchall()
                has_summary = any(col[1] == 'content_summary' for col in columns)
                
                if not has_summary:
                    # Add summary column
                    await db.execute("ALTER TABLE mobifone_documents ADD COLUMN content_summary TEXT")
                    
                    # Create summaries for existing documents
                    cursor = await db.execute("SELECT doc_id, content FROM mobifone_documents")
                    rows = await cursor.fetchall()
                    
                    for doc_id, content in rows:
                        if content:
                            # Simple summary: first 300 characters + keywords
                            summary = content[:300]
                            if len(content) > 300:
                                summary += "..."
                            
                            await db.execute(
                                "UPDATE mobifone_documents SET content_summary = ? WHERE doc_id = ?",
                                (summary, doc_id)
                            )
                    
                    await db.commit()
                    print(f"✅ Created summaries for {len(rows)} documents")
                else:
                    print("✅ Content summaries already exist")
                    
                return True
                
        except Exception as e:
            print(f"❌ Error creating summaries: {e}")
            return False
    
    async def optimize_database(self):
        """Full database optimization"""
        
        print("🚀 Starting MobiFone RAG Database Optimization...")
        
        tasks = [
            self.add_search_index(),
            self.precompute_document_vectors(),
            self.create_content_summaries()
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        success_count = sum(1 for r in results if r is True)
        print(f"\n✅ Optimization completed: {success_count}/3 tasks successful")
        
        # Run VACUUM to optimize database file
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("VACUUM")
                print("✅ Database vacuumed for optimal performance")
        except Exception as e:
            print(f"❌ Error vacuuming database: {e}")
    
    async def benchmark_search_performance(self):
        """Benchmark search performance"""
        
        print("\n📊 Benchmarking search performance...")
        
        test_queries = [
            "quy định TSCĐ",
            "công tác phí",
            "quy trình CCDC",
            "hướng dẫn",
            "chính sách nhân sự"
        ]
        
        from mobifone_sqlite_rag import MobiFoneSQLiteRAG
        
        rag = MobiFoneSQLiteRAG(self.db_path)
        await rag.initialize()
        
        total_time = 0
        for query in test_queries:
            start_time = time.time()
            results = await rag.search_documents(query, max_results=3)
            end_time = time.time()
            
            search_time = end_time - start_time
            total_time += search_time
            
            print(f"   Query: '{query}' - {search_time:.3f}s - {len(results)} results")
        
        avg_time = total_time / len(test_queries)
        print(f"\n📈 Average search time: {avg_time:.3f}s")
        
        if avg_time < 2.0:
            print("✅ Performance is good!")
        elif avg_time < 5.0:
            print("⚠️ Performance is acceptable")
        else:
            print("❌ Performance needs improvement")

async def main():
    """Main optimization function"""
    
    optimizer = MobiFoneRAGOptimizer()
    
    # Run optimizations
    await optimizer.optimize_database()
    
    # Benchmark performance
    await optimizer.benchmark_search_performance()
    
    print("\n🎯 Optimization Recommendations:")
    print("1. Restart the web interface to pick up optimizations")
    print("2. Consider adding more specific indexes for frequent queries")
    print("3. Monitor response times and adjust timeout as needed")
    print("4. Cache frequently asked questions")

if __name__ == "__main__":
    asyncio.run(main()) 