-- LobeChat Oracle Database Setup Script
-- This script will run when Oracle container starts for the first time

connect system/LobeChat2024@localhost:1521/XE;

-- Create LobeChat database/schema
CREATE USER lobe_user IDENTIFIED BY lobe_password;
ALTER USER lobe_user QUOTA UNLIMITED ON USERS;

-- <PERSON> necessary privileges
GRANT CONNECT, CREATE SESSION TO lobe_user;
GRANT CREATE TABLE, CREATE SEQUENCE, CREATE TRIGGER TO lobe_user;
GRANT CREATE PROCEDURE, CREATE VIEW, CREATE SYNONYM TO lobe_user;
GRANT ALTER ANY TABLE, DROP ANY TABLE TO lobe_user;
GRANT SELECT ANY TABLE, INSERT ANY TABLE, UPDATE ANY TABLE, DELETE ANY TABLE TO lobe_user;

-- Create tablespace for LobeChat (optional but recommended)
CREATE TABLESPACE LOBECHAT_DATA
  DATAFILE '/opt/oracle/oradata/XE/lobechat_data.dbf' 
  SIZ<PERSON> 100M 
  AUTOEXTEND ON 
  NEXT 10M 
  MAXSIZE UNLIMITED;

-- Assign tablespace to user
ALTER USER lobe_user DEFAULT TABLESPACE LOBECHAT_DATA;

-- Connect as lobe_user and create initial schema
connect lobe_user/lobe_password@localhost:1521/XE;

-- Create sequences
CREATE SEQUENCE user_seq START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE message_seq START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE conversation_seq START WITH 1 INCREMENT BY 1 NOCACHE;
CREATE SEQUENCE file_seq START WITH 1 INCREMENT BY 1 NOCACHE;

-- Users table
CREATE TABLE users (
    id VARCHAR2(36) PRIMARY KEY,
    email VARCHAR2(255) UNIQUE NOT NULL,
    username VARCHAR2(100),
    avatar VARCHAR2(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Sessions table  
CREATE TABLE sessions (
    id VARCHAR2(36) PRIMARY KEY,
    user_id VARCHAR2(36) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_session_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Conversations table
CREATE TABLE conversations (
    id VARCHAR2(36) PRIMARY KEY,
    user_id VARCHAR2(36) NOT NULL,
    title VARCHAR2(500),
    model VARCHAR2(100),
    config CLOB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_conversation_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Messages table
CREATE TABLE messages (
    id VARCHAR2(36) PRIMARY KEY,
    conversation_id VARCHAR2(36) NOT NULL,
    role VARCHAR2(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content CLOB,
    metadata CLOB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_message_conversation FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE
);

-- Files table for file uploads
CREATE TABLE files (
    id VARCHAR2(36) PRIMARY KEY,
    user_id VARCHAR2(36) NOT NULL,
    filename VARCHAR2(500) NOT NULL,
    mimetype VARCHAR2(100),
    size_bytes NUMBER,
    s3_key VARCHAR2(1000),
    url VARCHAR2(1000),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_file_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- API Keys table (encrypted)
CREATE TABLE api_keys (
    id VARCHAR2(36) PRIMARY KEY,
    user_id VARCHAR2(36) NOT NULL,
    provider VARCHAR2(50) NOT NULL,
    encrypted_key CLOB NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_apikey_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Models table
CREATE TABLE models (
    id VARCHAR2(36) PRIMARY KEY,
    name VARCHAR2(100) NOT NULL,
    provider VARCHAR2(50) NOT NULL,
    config CLOB,
    enabled NUMBER(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Knowledge base collections
CREATE TABLE knowledge_collections (
    id VARCHAR2(36) PRIMARY KEY,
    user_id VARCHAR2(36) NOT NULL,
    name VARCHAR2(200) NOT NULL,
    description CLOB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_knowledge_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Knowledge base documents
CREATE TABLE knowledge_documents (
    id VARCHAR2(36) PRIMARY KEY,
    collection_id VARCHAR2(36) NOT NULL,
    title VARCHAR2(500),
    content CLOB,
    embeddings BLOB,
    metadata CLOB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_knowledge_collection FOREIGN KEY (collection_id) REFERENCES knowledge_collections(id) ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_updated_at ON conversations(updated_at);
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);
CREATE INDEX idx_files_user_id ON files(user_id);
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_sessions_expires_at ON sessions(expires_at);
CREATE INDEX idx_knowledge_collections_user_id ON knowledge_collections(user_id);
CREATE INDEX idx_knowledge_documents_collection_id ON knowledge_documents(collection_id);

-- Insert default models
INSERT INTO models (id, name, provider, config, enabled) VALUES 
(SYS_GUID(), 'gpt-4o-mini', 'openai', '{"context_length": 128000, "max_tokens": 4096}', 1);

INSERT INTO models (id, name, provider, config, enabled) VALUES 
(SYS_GUID(), 'gpt-4o', 'openai', '{"context_length": 128000, "max_tokens": 4096}', 1);

INSERT INTO models (id, name, provider, config, enabled) VALUES 
(SYS_GUID(), 'claude-3-haiku', 'anthropic', '{"context_length": 200000, "max_tokens": 4096}', 1);

COMMIT;

-- Grant additional Oracle-specific permissions
GRANT EXECUTE ON SYS.DBMS_LOB TO lobe_user;
GRANT EXECUTE ON SYS.DBMS_CRYPTO TO lobe_user;

-- Create triggers for updated_at timestamps
CREATE OR REPLACE TRIGGER trg_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
BEGIN
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER trg_conversations_updated_at
    BEFORE UPDATE ON conversations
    FOR EACH ROW
BEGIN
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER trg_messages_updated_at
    BEFORE UPDATE ON messages
    FOR EACH ROW
BEGIN
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER trg_apikeys_updated_at
    BEFORE UPDATE ON api_keys
    FOR EACH ROW
BEGIN
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER trg_knowledge_collections_updated_at
    BEFORE UPDATE ON knowledge_collections
    FOR EACH ROW
BEGIN
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

COMMIT;

-- Log completion
INSERT INTO dual SELECT 'LobeChat Oracle setup completed at ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') FROM dual; 