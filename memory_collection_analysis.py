#!/usr/bin/env python3
"""
Memory Collection Analysis Script
Tạo file MD chi tiết về memories trong Qdrant collection
"""

import json
import requests
from datetime import datetime
import uuid

def get_collection_memories(collection_name, limit=50):
    """Get memories from Qdrant collection with detailed analysis"""
    
    # Qdrant connection
    qdrant_url = "http://localhost:6333"
    
    try:
        # Get collection info
        info_response = requests.get(f"{qdrant_url}/collections/{collection_name}")
        if info_response.status_code != 200:
            return None, f"Collection {collection_name} not found"
        
        collection_info = info_response.json()
        
        # Scroll through all points
        scroll_response = requests.post(
            f"{qdrant_url}/collections/{collection_name}/points/scroll",
            json={
                "limit": limit,
                "with_payload": True,
                "with_vector": False  # Don't need vectors for analysis
            }
        )
        
        if scroll_response.status_code != 200:
            return None, f"Failed to scroll collection: {scroll_response.text}"
        
        scroll_data = scroll_response.json()
        points = scroll_data.get("result", {}).get("points", [])
        
        return {
            "collection_info": collection_info["result"],
            "points": points,
            "total_retrieved": len(points)
        }, None
        
    except Exception as e:
        return None, f"Error: {str(e)}"

def analyze_memory_content(points):
    """Analyze memory content and categorize"""
    
    categories = {
        "user_memories": [],
        "system_messages": [],
        "code_related": [],
        "conversations": [],
        "other": []
    }
    
    for point in points:
        payload = point.get("payload", {})
        
        # Extract key fields
        memory_data = {
            "id": point.get("id"),
            "payload": payload
        }
        
        # Categorize based on content
        if "user_id" in payload and "memory" in payload:
            categories["user_memories"].append(memory_data)
        elif "text" in payload and any(keyword in str(payload.get("text", "")).lower() 
                                     for keyword in ["system", "assistant", "ai"]):
            categories["system_messages"].append(memory_data)
        elif any(keyword in str(payload).lower() 
                for keyword in ["code", "function", "class", "import", "def"]):
            categories["code_related"].append(memory_data)
        elif "text" in payload or "content" in payload:
            categories["conversations"].append(memory_data)
        else:
            categories["other"].append(memory_data)
    
    return categories

def format_timestamp(timestamp):
    """Format timestamp for display"""
    if not timestamp:
        return "N/A"
    
    try:
        if isinstance(timestamp, (int, float)):
            dt = datetime.fromtimestamp(timestamp)
        else:
            dt = datetime.fromisoformat(str(timestamp).replace('Z', '+00:00'))
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except:
        return str(timestamp)

def create_markdown_report(collection_name, data, categories):
    """Create detailed markdown report"""
    
    collection_info = data["collection_info"]
    total_points = data["total_retrieved"]
    
    md_content = f"""# 📊 Memory Collection Analysis Report

## 🔍 Collection Overview
- **Collection Name:** `{collection_name}`
- **Dimensions:** {collection_info.get('config', {}).get('params', {}).get('vectors', {}).get('size', 'Unknown')}
- **Distance Metric:** {collection_info.get('config', {}).get('params', {}).get('vectors', {}).get('distance', 'Unknown')}
- **Total Points Analyzed:** {total_points}
- **Generated:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 📈 Memory Categories Summary

| Category | Count | Description |
|----------|-------|-------------|
| 👤 User Memories | {len(categories['user_memories'])} | Personal user information and preferences |
| 🤖 System Messages | {len(categories['system_messages'])} | System/AI generated content |
| 💻 Code Related | {len(categories['code_related'])} | Programming and technical content |
| 💬 Conversations | {len(categories['conversations'])} | General conversation content |
| ❓ Other | {len(categories['other'])} | Uncategorized content |

---

"""

    # Add detailed sections for each category
    for category_name, items in categories.items():
        if not items:
            continue
            
        category_titles = {
            "user_memories": "👤 User Memories",
            "system_messages": "🤖 System Messages", 
            "code_related": "💻 Code Related",
            "conversations": "💬 Conversations",
            "other": "❓ Other Content"
        }
        
        md_content += f"## {category_titles.get(category_name, category_name.title())}\n\n"
        
        for i, item in enumerate(items[:20], 1):  # Limit to first 20 items per category
            payload = item["payload"]
            
            md_content += f"### {i}. Memory ID: `{item['id']}`\n\n"
            
            # Extract and display key fields
            key_fields = ["memory", "text", "content", "user_id", "session_id", "timestamp", "created_at"]
            
            for field in key_fields:
                if field in payload:
                    value = payload[field]
                    if field in ["timestamp", "created_at"]:
                        value = format_timestamp(value)
                    elif isinstance(value, str) and len(value) > 200:
                        value = value[:200] + "..."
                    
                    md_content += f"- **{field.title()}:** {value}\n"
            
            # Show other payload fields
            other_fields = {k: v for k, v in payload.items() if k not in key_fields}
            if other_fields:
                md_content += f"- **Other Fields:** {list(other_fields.keys())}\n"
            
            md_content += "\n---\n\n"
        
        if len(items) > 20:
            md_content += f"*... and {len(items) - 20} more items in this category*\n\n"
    
    # Add raw data section (sample)
    md_content += f"""## 🔧 Raw Data Sample

<details>
<summary>Click to view raw payload structure (first 3 items)</summary>

```json
{json.dumps(data["points"][:3], indent=2, ensure_ascii=False)}
```

</details>

## 📋 Collection Technical Details

```json
{json.dumps(collection_info, indent=2)}
```

---
*Report generated by Memory Collection Analysis Script*
"""
    
    return md_content

def main():
    collection_name = "mem0_gemini_3072_fixed"
    
    print(f"🔍 Analyzing collection: {collection_name}")
    
    # Get collection data
    data, error = get_collection_memories(collection_name, limit=100)
    if error:
        print(f"❌ Error: {error}")
        return
    
    print(f"📊 Retrieved {data['total_retrieved']} memories")
    
    # Analyze content
    categories = analyze_memory_content(data["points"])
    
    # Create markdown report
    md_content = create_markdown_report(collection_name, data, categories)
    
    # Save to file
    filename = f"memory_analysis_{collection_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(md_content)
    
    print(f"✅ Report saved to: {filename}")
    print(f"📈 Summary:")
    for category, items in categories.items():
        if items:
            print(f"  - {category}: {len(items)} items")

if __name__ == "__main__":
    main()