"""
AI Search Parallel Optimization
G<PERSON><PERSON><PERSON> thời gian AI search từ 15-20s xuống 8-10s bằng parallel processing
"""

import asyncio
import time
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class OptimizedSearchResult:
    """Optimized search result with timing info"""
    query: str
    results: List[Dict[str, Any]]
    total_time: float
    phase_timings: Dict[str, float]
    success: bool
    error: Optional[str] = None

class AISearchParallelOptimizer:
    """
    🚀 AI Search Parallel Optimizer
    
    Optimizations:
    1. Parallel query refinement + initial search
    2. Parallel multi-source search (Google + DDG + Brave)
    3. Parallel crawling with smart batching
    4. Cached results for common queries
    5. Early termination for fast results
    """
    
    def __init__(self, ai_search_engine):
        self.ai_search_engine = ai_search_engine
        self.query_cache = {}  # Simple in-memory cache
        self.cache_ttl = 300   # 5 minutes cache
        
    async def optimized_search(
        self, 
        query: str,
        enable_query_refinement: bool = True,
        max_crawl_urls: int = 5,  # Limit crawling for speed
        enable_caching: bool = True
    ) -> OptimizedSearchResult:
        """
        Optimized AI search with parallel processing
        
        Performance targets:
        - Simple queries: 5-8s
        - Complex queries: 8-12s
        - Cached queries: 1-3s
        """
        
        start_time = time.time()
        phase_timings = {}
        
        # Check cache first
        if enable_caching:
            cached_result = self._get_cached_result(query)
            if cached_result:
                logger.info(f"🚀 Cache hit for query: {query[:50]}...")
                return OptimizedSearchResult(
                    query=query,
                    results=cached_result['results'],
                    total_time=time.time() - start_time,
                    phase_timings={'cache_lookup': time.time() - start_time},
                    success=True
                )
        
        try:
            # === PHASE 1: PARALLEL QUERY PROCESSING ===
            phase1_start = time.time()
            
            # Run query refinement and initial search prep in parallel
            tasks = []
            
            if enable_query_refinement:
                refinement_task = asyncio.create_task(
                    self._safe_query_refinement(query),
                    name="query_refinement"
                )
                tasks.append(refinement_task)
            
            # Prepare search services in parallel
            search_prep_task = asyncio.create_task(
                self._prepare_search_services(),
                name="search_prep"
            )
            tasks.append(search_prep_task)
            
            # Execute phase 1 tasks
            phase1_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Parse results
            refined_query = query
            search_services_ready = False
            
            for i, result in enumerate(phase1_results):
                if isinstance(result, Exception):
                    logger.warning(f"Phase 1 task {tasks[i].get_name()} failed: {result}")
                    continue
                
                task_name = tasks[i].get_name()
                if task_name == "query_refinement" and result:
                    refined_query = result
                elif task_name == "search_prep":
                    search_services_ready = result
            
            phase_timings['phase1_parallel_prep'] = time.time() - phase1_start
            logger.info(f"✅ Phase 1 completed in {phase_timings['phase1_parallel_prep']:.2f}s")
            
            # === PHASE 2: PARALLEL MULTI-SOURCE SEARCH ===
            phase2_start = time.time()
            
            search_results = await self._parallel_multi_source_search(refined_query)
            
            phase_timings['phase2_parallel_search'] = time.time() - phase2_start
            logger.info(f"✅ Phase 2 completed in {phase_timings['phase2_parallel_search']:.2f}s")
            
            if not search_results:
                return OptimizedSearchResult(
                    query=query,
                    results=[],
                    total_time=time.time() - start_time,
                    phase_timings=phase_timings,
                    success=False,
                    error="No search results found"
                )
            
            # === PHASE 3: SMART PARALLEL CRAWLING ===
            phase3_start = time.time()
            
            # Select top URLs for crawling (limit for speed)
            top_urls = [r['url'] for r in search_results[:max_crawl_urls]]
            
            crawl_results = await self._smart_parallel_crawling(top_urls, query)
            
            phase_timings['phase3_parallel_crawling'] = time.time() - phase3_start
            logger.info(f"✅ Phase 3 completed in {phase_timings['phase3_parallel_crawling']:.2f}s")
            
            # === PHASE 4: FAST SYNTHESIS ===
            phase4_start = time.time()
            
            # Use lightweight synthesis for speed
            final_results = await self._fast_content_synthesis(
                query, search_results, crawl_results
            )
            
            phase_timings['phase4_synthesis'] = time.time() - phase4_start
            logger.info(f"✅ Phase 4 completed in {phase_timings['phase4_synthesis']:.2f}s")
            
            total_time = time.time() - start_time
            
            # Cache successful results
            if enable_caching and final_results:
                self._cache_result(query, final_results)
            
            return OptimizedSearchResult(
                query=query,
                results=final_results,
                total_time=total_time,
                phase_timings=phase_timings,
                success=True
            )
            
        except Exception as e:
            logger.error(f"❌ Optimized search error: {e}")
            return OptimizedSearchResult(
                query=query,
                results=[],
                total_time=time.time() - start_time,
                phase_timings=phase_timings,
                success=False,
                error=str(e)
            )
    
    async def _safe_query_refinement(self, query: str) -> Optional[str]:
        """Safe query refinement with timeout"""
        try:
            if hasattr(self.ai_search_engine, 'query_refinement'):
                return await asyncio.wait_for(
                    self.ai_search_engine.query_refinement.refine_query(query),
                    timeout=3  # Quick timeout for speed
                )
        except Exception as e:
            logger.warning(f"Query refinement failed: {e}")
        return query
    
    async def _prepare_search_services(self) -> bool:
        """Prepare search services in parallel"""
        try:
            if hasattr(self.ai_search_engine, 'multi_source_search'):
                await self.ai_search_engine.multi_source_search.initialize()
                return True
        except Exception as e:
            logger.warning(f"Search service prep failed: {e}")
        return False
    
    async def _parallel_multi_source_search(self, query: str) -> List[Dict[str, Any]]:
        """Parallel multi-source search with timeout"""
        try:
            if hasattr(self.ai_search_engine, 'multi_source_search'):
                # Use shorter timeout for speed
                search_response = await asyncio.wait_for(
                    self.ai_search_engine.multi_source_search.search(query),
                    timeout=8  # Reduced from default
                )
                
                if search_response.success:
                    return [
                        {
                            'title': r.title,
                            'url': r.url,
                            'snippet': r.snippet,
                            'source': r.source
                        }
                        for r in search_response.results
                    ]
        except Exception as e:
            logger.error(f"Multi-source search failed: {e}")
        
        return []
    
    async def _smart_parallel_crawling(self, urls: List[str], query: str) -> List[Dict[str, Any]]:
        """Smart parallel crawling with batching and timeout"""
        if not urls:
            return []
        
        try:
            # Batch crawling with smaller batches for speed
            batch_size = 3  # Smaller batches
            crawl_results = []
            
            for i in range(0, len(urls), batch_size):
                batch_urls = urls[i:i + batch_size]
                
                # Crawl batch with timeout
                batch_result = await asyncio.wait_for(
                    self.ai_search_engine.batch_crawler.crawl_urls(batch_urls, query),
                    timeout=6  # Reduced timeout per batch
                )
                
                if batch_result:
                    successful_results = self.ai_search_engine.batch_crawler.get_successful_results(batch_result)
                    crawl_results.extend(successful_results)
                
                # Early termination if we have enough results
                if len(crawl_results) >= 3:
                    break
            
            return crawl_results
            
        except Exception as e:
            logger.error(f"Smart crawling failed: {e}")
            return []
    
    async def _fast_content_synthesis(
        self, 
        query: str, 
        search_results: List[Dict[str, Any]], 
        crawl_results: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Fast content synthesis with reduced complexity"""
        try:
            # Use lightweight synthesis
            if hasattr(self.ai_search_engine, 'content_synthesis'):
                synthesis_response = await asyncio.wait_for(
                    self.ai_search_engine.content_synthesis.synthesize_content(
                        query, crawl_results[:3]  # Limit for speed
                    ),
                    timeout=4  # Quick synthesis
                )
                
                if synthesis_response.success:
                    return [{
                        'title': f"AI Search Results for: {query}",
                        'content': synthesis_response.synthesized_content,
                        'sources': search_results[:5],  # Top 5 sources
                        'type': 'ai_synthesized'
                    }]
            
            # Fallback: return search results directly
            return search_results[:5]
            
        except Exception as e:
            logger.error(f"Fast synthesis failed: {e}")
            return search_results[:5]  # Fallback to search results
    
    def _get_cached_result(self, query: str) -> Optional[Dict[str, Any]]:
        """Get cached result if available and not expired"""
        cache_key = query.lower().strip()
        
        if cache_key in self.query_cache:
            cached_data = self.query_cache[cache_key]
            
            # Check if cache is still valid
            if time.time() - cached_data['timestamp'] < self.cache_ttl:
                return cached_data
            else:
                # Remove expired cache
                del self.query_cache[cache_key]
        
        return None
    
    def _cache_result(self, query: str, results: List[Dict[str, Any]]):
        """Cache search results"""
        cache_key = query.lower().strip()
        
        self.query_cache[cache_key] = {
            'results': results,
            'timestamp': time.time()
        }
        
        # Simple cache cleanup (keep only last 100 queries)
        if len(self.query_cache) > 100:
            oldest_key = min(self.query_cache.keys(), 
                           key=lambda k: self.query_cache[k]['timestamp'])
            del self.query_cache[oldest_key]

# Usage example:
"""
# In your AI search integration:
optimizer = AISearchParallelOptimizer(ai_search_engine)

# Fast search with optimization
result = await optimizer.optimized_search(
    query="latest AI developments 2024",
    enable_query_refinement=True,
    max_crawl_urls=3,  # Limit for speed
    enable_caching=True
)

print(f"Search completed in {result.total_time:.2f}s")
print(f"Phase timings: {result.phase_timings}")
"""
