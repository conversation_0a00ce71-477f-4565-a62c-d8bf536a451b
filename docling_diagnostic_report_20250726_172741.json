{"timestamp": "2025-07-26T17:27:39.999949", "docling_server_status": {"running": true, "health_check": true, "endpoints_available": ["/extract_tables", "/extract_text", "/extract_content"], "error": null, "process_info": "root     3099658  0.0  0.2 218208 60028 ?        Ssl  16:33   0:00 python /app/servers/jina_crawler/real_smoldocling_integration.py\nroot     3133682 96.1  9.9 3980320 2433376 ?     Ssl  17:17  10:00 python docling_server.py\nubuntu   3139731 73.3  0.1  38924 30128 pts/30   S+   17:27   0:00 python3 docling_log_checker.py", "health_data": {"docling_available": true, "endpoints": ["/extract_tables", "/extract_text", "/extract_content", "/health"], "service": "docling-content-extractor", "status": "healthy", "version": "1.1.0"}}, "openwebui_status": {"running": false, "accessible": false, "docling_integration": false, "error": "HTTPConnectionPool(host='localhost', port=8888): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xfe99435b4c20>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, "docling_logs": {"log_files_found": ["./docling.log"], "recent_errors": [], "processing_attempts": ["INFO:__main__:DoclingTableExtractor initialized (converter to be created per-request).", "🚀 Starting Docling Table Extraction Server...", "- POST /extract_tables - Extract from uploaded file", "- POST /extract_tables_from_path - Extract from file path"], "common_issues": ["docling_error"], "log_analysis": {"./docling.log": {"total_lines": 18, "recent_lines": 18, "error_count": 0, "last_modified": **********.9467037}}, "system_logs": "-- No entries --\n"}, "file_processing_test": {"test_files": [{"name": "test_vietnamese.txt", "path": "/tmp/tmpyzdwnbyf.txt", "mime_type": "text/plain", "type": "text"}, {"name": "test_data.json", "path": "/tmp/tmpf9ygxo17.json", "mime_type": "application/json", "type": "json"}], "docling_direct_test": {"test_vietnamese.txt": {"file": "test_vietnamese.txt", "status_code": 200, "success": true, "response_size": 197, "error": null, "content_extracted": false, "tables_found": 0, "text_length": 0}, "test_data.json": {"file": "test_data.json", "status_code": 200, "success": true, "response_size": 203, "error": null, "content_extracted": false, "tables_found": 0, "text_length": 0}}, "openwebui_integration_test": {}, "errors": []}, "network_connectivity": {"localhost_5001": true, "localhost_8888": false, "port_listening": {"5001": true, "8888": false, "3000": true}, "firewall_status": null}, "environment_check": {"python_version": "3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]", "working_directory": "/home/<USER>/AccA/AccA", "environment_variables": {"DOCLING_SERVER_URL": "Not set", "RAG_CONTENT_EXTRACTION_ENGINE": "Not set", "DISABLE_OCR": "Not set", "TESSDATA_PREFIX": "Not set", "OPENAI_API_KEY": "Not set", "GEMINI_API_KEY": "AIzaSyDHsg-8CCSEBUO9N3V0vaRsCSNq2iS4oec"}, "disk_space": {"free_gb": 17.97, "total_gb": 144.26, "usage_percent": 87.54}, "memory_usage": {"total_gb": 23.42, "available_gb": 14.53}}, "recommendations": ["⚠️  WARNING: RAG_CONTENT_EXTRACTION_ENGINE không được set thành 'docling'", "⚠️  WARNING: DOCLING_SERVER_URL không được set. Nên set thành 'http://localhost:5001'"]}