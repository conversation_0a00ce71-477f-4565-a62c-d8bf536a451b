swiv8hv5y96iwo2t_high = (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.ap-singapore-2.oraclecloud.com))(connect_data=(service_name=g872ed23dd62a8b_swiv8hv5y96iwo2t_high.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))

swiv8hv5y96iwo2t_low = (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.ap-singapore-2.oraclecloud.com))(connect_data=(service_name=g872ed23dd62a8b_swiv8hv5y96iwo2t_low.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))

swiv8hv5y96iwo2t_medium = (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.ap-singapore-2.oraclecloud.com))(connect_data=(service_name=g872ed23dd62a8b_swiv8hv5y96iwo2t_medium.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))

swiv8hv5y96iwo2t_tp = (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.ap-singapore-2.oraclecloud.com))(connect_data=(service_name=g872ed23dd62a8b_swiv8hv5y96iwo2t_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))

swiv8hv5y96iwo2t_tpurgent = (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.ap-singapore-2.oraclecloud.com))(connect_data=(service_name=g872ed23dd62a8b_swiv8hv5y96iwo2t_tpurgent.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))



