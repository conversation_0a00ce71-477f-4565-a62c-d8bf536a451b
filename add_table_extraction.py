#!/usr/bin/env python3

import subprocess
import sys

def run_command(cmd, description):
    """Run a command and return success status"""
    print(f"🔧 {description}")
    print(f"   Command: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("   ✅ Success")
            return True
        else:
            print(f"   ❌ Failed (exit code: {result.returncode})")
            if result.stderr:
                print(f"   Error: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False

def main():
    print("🔧 ADDING TABLE EXTRACTION TO DOCLING LOADER")
    print("=" * 50)
    
    # Step 1: Add table extraction method and enhance load method
    print("\n🔄 STEP 1: Add table extraction functionality")
    
    # Create the enhancement script
    enhancement_script = '''
import re

# Read the current file
with open('/app/backend/open_webui/retrieval/loaders/main.py', 'r') as f:
    lines = f.readlines()

# Find the DoclingLoader class and add table extraction method
new_lines = []
in_docling_class = False
load_method_found = False

for i, line in enumerate(lines):
    if 'class DoclingLoader:' in line:
        in_docling_class = True
        new_lines.append(line)
        continue
    
    if in_docling_class and line.strip().startswith('def load('):
        # Add the table extraction method before load method
        new_lines.append('    def _extract_table_text(self, tables):\\n')
        new_lines.append('        """Extract readable text from Docling table structure"""\\n')
        new_lines.append('        table_texts = []\\n')
        new_lines.append('        \\n')
        new_lines.append('        for table in tables:\\n')
        new_lines.append('            if isinstance(table, dict) and "markdown" in table:\\n')
        new_lines.append('                markdown_str = table["markdown"]\\n')
        new_lines.append('                # Extract text values using regex\\n')
        new_lines.append('                text_matches = re.findall(r"text=\\'([^\\']*)\\'", markdown_str)\\n')
        new_lines.append('                \\n')
        new_lines.append('                if text_matches:\\n')
        new_lines.append('                    # Remove duplicates while preserving order\\n')
        new_lines.append('                    seen = set()\\n')
        new_lines.append('                    unique_texts = []\\n')
        new_lines.append('                    for text in text_matches:\\n')
        new_lines.append('                        if text not in seen and text.strip():\\n')
        new_lines.append('                            seen.add(text)\\n')
        new_lines.append('                            unique_texts.append(text)\\n')
        new_lines.append('                    \\n')
        new_lines.append('                    if unique_texts:\\n')
        new_lines.append('                        # Format as readable table\\n')
        new_lines.append('                        table_text = "TABLE DATA:\\\\n" + "\\\\n".join(f"- {text}" for text in unique_texts)\\n')
        new_lines.append('                        table_texts.append(table_text)\\n')
        new_lines.append('        \\n')
        new_lines.append('        return table_texts\\n')
        new_lines.append('\\n')
        load_method_found = True
    
    # Modify the load method to include table extraction
    if load_method_found and 'text_parts.extend(table_content)' in line:
        # Already enhanced, skip
        new_lines.append(line)
    elif load_method_found and 'text = "\\\\n\\\\n".join(str(item) for item in content if item)' in line:
        # Replace the simple text assignment with enhanced version
        new_lines.append('            # ENHANCED: Handle content, tables, and images\\n')
        new_lines.append('            text_parts = []\\n')
        new_lines.append('            \\n')
        new_lines.append('            # 1. Main content (text)\\n')
        new_lines.append('            main_text = "<No text content found>"\\n')
        new_lines.append('            if "content" in result:\\n')
        new_lines.append('                content = result["content"]\\n')
        new_lines.append('                if isinstance(content, list):\\n')
        new_lines.append('                    main_text = "\\\\n\\\\n".join(str(item) for item in content if item)\\n')
        new_lines.append('                elif isinstance(content, str):\\n')
        new_lines.append('                    main_text = content\\n')
        new_lines.append('            elif "document" in result:\\n')
        new_lines.append('                document_data = result.get("document", {})\\n')
        new_lines.append('                text = document_data.get("md_content", main_text)\\n')
        new_lines.append('            \\n')
        new_lines.append('            if main_text != "<No text content found>":\\n')
        new_lines.append('                text_parts.append(main_text)\\n')
        new_lines.append('            \\n')
        new_lines.append('            # 2. Extract tables\\n')
        new_lines.append('            if "tables" in result and result["tables"]:\\n')
        new_lines.append('                table_texts = self._extract_table_text(result["tables"])\\n')
        new_lines.append('                text_parts.extend(table_texts)\\n')
        new_lines.append('            \\n')
        new_lines.append('            # 3. Handle images (placeholder for now)\\n')
        new_lines.append('            if "images" in result and result["images"]:\\n')
        new_lines.append('                image_text = f"[DOCUMENT CONTAINS {len(result[\\'images\\'])} IMAGES]"\\n')
        new_lines.append('                text_parts.append(image_text)\\n')
        new_lines.append('            \\n')
        new_lines.append('            # Combine all parts\\n')
        new_lines.append('            if text_parts:\\n')
        new_lines.append('                text = "\\\\n\\\\n".join(text_parts)\\n')
        new_lines.append('            else:\\n')
        new_lines.append('                text = "<No content found>"\\n')
        
        # Skip the original lines until we find the metadata line
        skip_until_metadata = True
        continue
    elif 'metadata = {"Content-Type": self.mime_type}' in line and load_method_found:
        skip_until_metadata = False
        new_lines.append(line)
        load_method_found = False
        in_docling_class = False
    elif not skip_until_metadata:
        new_lines.append(line)

# Write back to file
with open('/app/backend/open_webui/retrieval/loaders/main.py', 'w') as f:
    f.writelines(new_lines)

print("✅ Table extraction added to DoclingLoader")
'''
    
    # Write enhancement script to temp file
    with open('/tmp/add_table_extraction.py', 'w') as f:
        f.write(enhancement_script)
    
    # Step 2: Copy script to container
    if not run_command(
        "docker cp /tmp/add_table_extraction.py catomanton-webui:/tmp/add_table_extraction.py",
        "Copy table extraction script to container"
    ):
        return False
    
    # Step 3: Run the enhancement script
    print("\n🔄 STEP 2: Apply table extraction enhancement")
    if not run_command(
        "docker exec catomanton-webui python3 /tmp/add_table_extraction.py",
        "Running table extraction script"
    ):
        return False
    
    # Step 4: Restart Open WebUI
    print("\n🔄 STEP 3: Restart Open WebUI")
    if not run_command(
        "docker restart catomanton-webui",
        "Restarting Open WebUI container"
    ):
        print("❌ Failed to restart container")
        return False
    
    print("\n✅ TABLE EXTRACTION ENHANCEMENT COMPLETED!")
    print("🔄 Open WebUI is restarting...")
    print("📊 The DoclingLoader now extracts both text content AND table data")
    print("🖼️  Image detection is also added")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)