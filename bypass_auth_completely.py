"""
Completely Bypass Pipeline Authentication
Replace auth functions to always return success
"""

import subprocess
import time

def backup_auth_py():
    """Backup original auth.py"""
    print("📦 Backing up original auth.py...")
    subprocess.run(
        "docker exec pipelines cp /app/utils/pipelines/auth.py /app/utils/pipelines/auth.py.backup",
        shell=True
    )

def create_no_auth_version():
    """Create auth.py that bypasses all authentication"""
    print("🔓 Creating no-auth version of auth.py...")
    
    no_auth_content = '''
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi import HTTPException, status, Depends
from pydantic import BaseModel
from typing import Union, Optional
from passlib.context import CryptContext
from datetime import datetime, timedelta
import jwt
import logging
import os
import requests
import uuid
from config import API_KEY, PIPELINES_DIR

SESSION_SECRET = os.getenv("SESSION_SECRET", " ")
ALGORITHM = "HS256"

##############
# Auth Utils - ALL BYPASSED FOR INTERNAL VPS
##############

bearer_security = HTTPBearer(auto_error=False)  # Don't auto-error
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password, hashed_password):
    return True  # Always return True

def get_password_hash(password):
    return pwd_context.hash(password)

def create_token(data: dict, expires_delta: Union[timedelta, None] = None) -> str:
    payload = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
        payload.update({"exp": expire})
    encoded_jwt = jwt.encode(payload, SESSION_SECRET, algorithm=ALGORITHM)
    return encoded_jwt

def decode_token(token: str) -> Optional[dict]:
    return {"user": "bypass"}  # Always return valid user

def extract_token_from_auth_header(auth_header: str):
    return "bypass_token"

def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(bearer_security),
) -> Optional[dict]:
    # BYPASS ALL AUTH - ALWAYS RETURN API_KEY
    return API_KEY

# Export the function so it can be imported
__all__ = ["get_current_user", "bearer_security"]
'''
    
    # Write the no-auth version
    # Use a simple echo and pipe to avoid heredoc issues
    # Also, ensure the content is properly escaped for the shell
    import shlex
    write_cmd = f"echo {shlex.quote(no_auth_content)} | docker exec -i pipelines bash -c 'cat > /app/utils/pipelines/auth.py'"
    
    result = subprocess.run(write_cmd, shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ No-auth version created successfully")
    else:
        print(f"❌ Failed to create no-auth version: {result.stderr}")

def restart_pipeline_server():
    """Restart pipeline server to load new auth"""
    print("🔄 Restarting pipeline server with no auth...")
    
    # Restart container to reload modules
    subprocess.run("docker restart pipelines", shell=True)
    print("⏳ Waiting for container to restart...")
    time.sleep(15)

def test_all_endpoints():
    """Test all endpoints after auth bypass"""
    print("🧪 Testing all endpoints after auth bypass...")
    
    import requests
    
    endpoints = [
        ("Pipeline Status", "http://localhost:9099/"),
        ("Pipeline Models", "http://localhost:9099/models"),
        ("Pipeline List", "http://localhost:9099/pipelines"),
        ("RAG Valves", "http://localhost:9099/enhanced-rag-v1/valves/spec")
    ]
    
    for name, url in endpoints:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"✅ {name}: SUCCESS")
            elif response.status_code == 403:
                print(f"❌ {name}: Still blocked by auth")
            elif response.status_code == 404:
                print(f"⚠️ {name}: Not found (but no auth error)")
            else:
                print(f"⚠️ {name}: Status {response.status_code}")
        except Exception as e:
            print(f"❌ {name}: {str(e)}")

def main():
    """Main function"""
    print("🔓 COMPLETELY BYPASS PIPELINE AUTHENTICATION")
    print("=" * 50)
    
    # Step 1: Backup
    backup_auth_py()
    
    # Step 2: Replace auth.py
    create_no_auth_version()
    
    # Step 3: Restart
    restart_pipeline_server()
    
    # Step 4: Test
    test_all_endpoints()
    
    print("\n🎯 SUMMARY:")
    print("✅ Authentication completely bypassed")
    print("✅ All endpoints should work without auth")
    print("✅ Try: curl http://localhost:9099/models")

if __name__ == "__main__":
    main()