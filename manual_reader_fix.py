#!/usr/bin/env python3
"""
Manually add reader mode fallback to the correct location
"""

import subprocess

def manual_reader_fix():
    """Manually add reader mode fallback"""
    
    print("🔧 Manually adding reader mode fallback...")
    
    # Read current jini_crawler.py
    result = subprocess.run([
        'docker', 'exec', 'jina-crawler-mcp', 'cat', '/app/jini_crawler.py'
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ Error reading jini_crawler.py: {result.stderr}")
        return False
    
    lines = result.stdout.split('\n')
    
    # Find the line with "return None" after TLS bypass error
    insert_index = -1
    for i, line in enumerate(lines):
        if "❌ TLS bypass error:" in line and i + 1 < len(lines) and "return None" in lines[i + 1]:
            insert_index = i + 1  # Insert after "return None"
            break
    
    if insert_index == -1:
        print("❌ Could not find insertion point")
        return False
    
    # Reader mode fallback code
    reader_code = [
        "",
        "        # 🚀 READER MODE FALLBACK for 403/blocked sites",
        "        if not html_content and status in [403, 429, 503]:",
        "            logger.info(f\"📖 Trying Reader Mode fallback for blocked URL: {url}\")",
        "            try:",
        "                # Use Googlebot user agent to bypass blocks",
        "                reader_headers = {",
        "                    'User-Agent': 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',",
        "                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',",
        "                    'Referer': 'https://www.google.com/',",
        "                }",
        "                ",
        "                async with self.session.get(url, headers=reader_headers, timeout=30) as response:",
        "                    if response.status == 200:",
        "                        html_content = await response.text()",
        "                        status = 200",
        "                        logger.info(f\"✅ Reader Mode successful for {url}\")",
        "                    else:",
        "                        logger.warning(f\"⚠️ Reader Mode also failed with status {response.status}\")",
        "                        ",
        "            except Exception as e:",
        "                logger.error(f\"❌ Reader Mode error: {e}\")",
        ""
    ]
    
    # Insert reader mode code
    lines[insert_index:insert_index] = reader_code
    
    # Write modified content
    modified_content = '\n'.join(lines)
    with open('/tmp/jini_crawler_manual_reader.py', 'w') as f:
        f.write(modified_content)
    
    # Copy back to container
    copy_result = subprocess.run([
        'docker', 'cp', '/tmp/jini_crawler_manual_reader.py', 'jina-crawler-mcp:/app/jini_crawler.py'
    ], capture_output=True, text=True)
    
    if copy_result.returncode != 0:
        print(f"❌ Error copying fixed file: {copy_result.stderr}")
        return False
    
    print("✅ Reader mode fallback manually added")
    return True

def main():
    """Main function"""
    print("🚀 Manually adding reader mode fallback...")
    
    if manual_reader_fix():
        print("\n🎉 Reader mode fallback manually added!")
        print("🔄 Restarting container...")
        
        # Restart container
        subprocess.run(['docker', 'restart', 'jina-crawler-mcp'])
        print("✅ Container restarted")
        
        print("\n📊 Reader mode should now work for 403 errors!")
    else:
        print("\n❌ Failed to add reader mode fallback")

if __name__ == "__main__":
    main()
